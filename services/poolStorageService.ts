import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { encryptForStorage, decryptFromStorage } from './contentService';

// Keys for AsyncStorage
const POOL_STORAGE_KEY_PREFIX = 'taptrap_question_pool_';
const USED_IDS_STORAGE_KEY_PREFIX = 'taptrap_used_question_ids_';
const GLOBAL_USED_IDS_KEY = 'taptrap_global_used_question_ids';
const STORAGE_VERSION = 'v1';

// Helper to generate storage keys based on categories
const getPoolKey = (categories: string[]): string => {
  // Sort categories to ensure consistent key generation
  const sortedCategories = [...categories].sort().join('_');
  return `${POOL_STORAGE_KEY_PREFIX}${sortedCategories || 'all'}`;
};

const getUsedIdsKey = (categories: string[]): string => {
  // Sort categories to ensure consistent key generation
  const sortedCategories = [...categories].sort().join('_');
  return `${USED_IDS_STORAGE_KEY_PREFIX}${sortedCategories || 'all'}`;
};

// Types for the pool items
export interface PoolItem {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
  type: 'question' | 'challenge';
  category: string;
}

/**
 * Save the current question pool to AsyncStorage
 * @param pool The current pool of questions/challenges
 * @param categories Array of categories this pool belongs to
 * @returns Promise<boolean> indicating success or failure
 */
export const saveQuestionPool = async (pool: PoolItem[], categories: string[] = []): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Pool storage not available on web platform');
      return false;
    }

    // Validate pool before saving
    if (!Array.isArray(pool)) {
      console.error('Invalid pool structure, not saving to storage');
      return false;
    }

    // Generate storage key based on categories
    const storageKey = getPoolKey(categories);

    console.log(`Saving question pool with ${pool.length} items to AsyncStorage with key ${storageKey}`);

    // Encrypt and save the pool
    const encryptedPool = encryptForStorage(pool);
    await AsyncStorage.setItem(storageKey, encryptedPool);

    return true;
  } catch (error) {
    console.error('Error saving question pool to storage:', error);
    return false;
  }
};

/**
 * Load the question pool from AsyncStorage
 * @param categories Array of categories to load the pool for
 * @returns Promise<PoolItem[] | null> The saved pool or null if not found
 */
export const loadQuestionPool = async (categories: string[] = []): Promise<PoolItem[] | null> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Pool storage not available on web platform');
      return null;
    }

    // Generate storage key based on categories
    const storageKey = getPoolKey(categories);

    // Get stored pool from AsyncStorage
    const storedPool = await AsyncStorage.getItem(storageKey);

    if (!storedPool) {
      // console.log(`No saved question pool found in AsyncStorage for key ${storageKey}`);
      return null;
    }

    try {
      // Decrypt and parse the pool
      const decryptedPool = decryptFromStorage(storedPool);

      if (Array.isArray(decryptedPool)) {
        console.log(`Loaded question pool with ${decryptedPool.length} items from AsyncStorage for key ${storageKey}`);
        return decryptedPool;
      } else {
        console.warn(`Decoded pool has invalid structure for key ${storageKey}, ignoring`);
        return null;
      }
    } catch (error) {
      console.error(`Error decoding saved pool for key ${storageKey}:`, error);

      // Clear invalid data
      await AsyncStorage.removeItem(storageKey);
      return null;
    }
  } catch (error) {
    console.error('Error loading question pool from storage:', error);
    return null;
  }
};

/**
 * Save the set of used question IDs to AsyncStorage
 * @param usedIds Set of used question IDs
 * @param categories Array of categories these IDs belong to
 * @returns Promise<boolean> indicating success or failure
 */
export const saveUsedQuestionIds = async (usedIds: Set<string>, categories: string[] = []): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Used IDs storage not available on web platform');
      return false;
    }

    // Generate storage key based on categories
    const storageKey = getUsedIdsKey(categories);

    console.log(`Saving ${usedIds.size} used question IDs to AsyncStorage with key ${storageKey}`);

    // Convert Set to Array for storage
    const usedIdsArray = Array.from(usedIds);

    // Encrypt and save the used IDs
    const encryptedIds = encryptForStorage(usedIdsArray);
    await AsyncStorage.setItem(storageKey, encryptedIds);

    return true;
  } catch (error) {
    console.error('Error saving used question IDs to storage:', error);
    return false;
  }
};

/**
 * Load the set of used question IDs from AsyncStorage
 * @param categories Array of categories to load the used IDs for
 * @returns Promise<Set<string> | null> The saved used IDs or null if not found
 */
export const loadUsedQuestionIds = async (categories: string[] = []): Promise<Set<string> | null> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Used IDs storage not available on web platform');
      return null;
    }

    // Generate storage key based on categories
    const storageKey = getUsedIdsKey(categories);

    // Get stored used IDs from AsyncStorage
    const storedIds = await AsyncStorage.getItem(storageKey);

    if (!storedIds) {
      // console.log(`No saved used question IDs found in AsyncStorage for key ${storageKey}`);
      return null;
    }

    try {
      // Decrypt and parse the used IDs
      const decryptedIds = decryptFromStorage(storedIds);

      if (Array.isArray(decryptedIds)) {
        console.log(`Loaded ${decryptedIds.length} used question IDs from AsyncStorage for key ${storageKey}`);
        return new Set(decryptedIds);
      } else {
        console.warn(`Decoded used IDs have invalid structure for key ${storageKey}, ignoring`);
        return null;
      }
    } catch (error) {
      console.error(`Error decoding saved used IDs for key ${storageKey}:`, error);

      // Clear invalid data
      await AsyncStorage.removeItem(storageKey);
      return null;
    }
  } catch (error) {
    console.error('Error loading used question IDs from storage:', error);
    return null;
  }
};

/**
 * Save the set of used question IDs to global storage (independent of categories/modes)
 * @param usedIds Set of used question IDs
 * @returns Promise<boolean> indicating success or failure
 */
export const saveGlobalUsedQuestionIds = async (usedIds: Set<string>): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Global used IDs storage not available on web platform');
      return false;
    }

    console.log(`Saving ${usedIds.size} used question IDs to global AsyncStorage`);

    // Convert Set to Array for storage
    const usedIdsArray = Array.from(usedIds);

    // Encrypt and save the used IDs
    const encryptedIds = encryptForStorage(usedIdsArray);
    await AsyncStorage.setItem(GLOBAL_USED_IDS_KEY, encryptedIds);

    return true;
  } catch (error) {
    console.error('Error saving global used question IDs to storage:', error);
    return false;
  }
};

/**
 * Load the set of used question IDs from global storage (independent of categories/modes)
 * @returns Promise<Set<string> | null> The saved used IDs or null if not found
 */
export const loadGlobalUsedQuestionIds = async (): Promise<Set<string> | null> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Global used IDs storage not available on web platform');
      return null;
    }

    // Get stored used IDs from AsyncStorage
    const storedIds = await AsyncStorage.getItem(GLOBAL_USED_IDS_KEY);

    if (!storedIds) {
      console.log('No saved global used question IDs found in AsyncStorage');
      return null;
    }

    try {
      // Decrypt and parse the used IDs
      const decryptedIds = decryptFromStorage(storedIds);

      if (Array.isArray(decryptedIds)) {
        console.log(`Loaded ${decryptedIds.length} global used question IDs from AsyncStorage`);
        return new Set(decryptedIds);
      } else {
        console.warn('Decoded global used IDs have invalid structure, ignoring');
        return null;
      }
    } catch (error) {
      console.error('Error decoding saved global used IDs:', error);

      // Clear invalid data
      await AsyncStorage.removeItem(GLOBAL_USED_IDS_KEY);
      return null;
    }
  } catch (error) {
    console.error('Error loading global used question IDs from storage:', error);
    return null;
  }
};

/**
 * Clear all pool-related data from AsyncStorage
 * @param categories Optional array of categories to clear. If not provided, clears all pools.
 * @returns Promise<boolean> indicating success or failure
 */
/**
 * Update all stored pools with new content from the API
 * This function:
 * 1. Updates text fields in existing items
 * 2. Adds new content items that weren't previously in the pool (only for matching categories)
 * 3. Removes content items that no longer exist in the API response or are marked as "Inactive"
 * 4. Keeps existing items that are still present (maintaining their used/unused status)
 *
 * @param freshContent The new content from the API
 * @returns Promise<boolean> indicating success or failure
 */
export const updatePoolItemsText = async (freshContent: any): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Pool storage not available on web platform');
      return false;
    }

    console.log('Updating all stored pools with fresh content...');

    // Get all AsyncStorage keys
    const allKeys = await AsyncStorage.getAllKeys();

    // Filter for pool keys only
    const poolKeys = allKeys.filter(key => key.startsWith(POOL_STORAGE_KEY_PREFIX));

    if (poolKeys.length === 0) {
      console.log('No stored pools found to update');
      return true;
    }

    console.log(`Found ${poolKeys.length} stored pools to check for updates`);

    // Define types for our content maps
    type ContentItem = {
      id: string;
      text_en: string;
      text_es: string;
      text_dom: string;
      category: string;
      status?: string;
      [key: string]: any; // Allow other properties
    };

    type CategoryMap = {
      [category: string]: Map<string, ContentItem>;
    };

    // Create category-specific maps for questions and dares
    const freshContentMaps: {
      questions: CategoryMap;
      dares: CategoryMap;
    } = {
      questions: {
        casual_questions: new Map<string, ContentItem>(),
        mild_questions: new Map<string, ContentItem>(),
        spicy_questions: new Map<string, ContentItem>(),
        no_limits_questions: new Map<string, ContentItem>(),
        couple_questions: new Map<string, ContentItem>()
      },
      dares: {
        casual_dares: new Map<string, ContentItem>(),
        mild_dares: new Map<string, ContentItem>(),
        spicy_dares: new Map<string, ContentItem>(),
        no_limits_dares: new Map<string, ContentItem>(),
        couple_dares: new Map<string, ContentItem>()
      }
    };

    // Populate the maps with content from each category
    if (freshContent.questions) {
      Object.keys(freshContent.questions).forEach(category => {
        const items = freshContent.questions[category];
        if (Array.isArray(items)) {
          items.forEach(item => {
            if (item.id && item.status !== 'Inactive') {
              // Add category to the item for easier reference
              freshContentMaps.questions[category].set(item.id, {
                ...item,
                category
              });
            }
          });
        }
      });
    }

    if (freshContent.dares) {
      Object.keys(freshContent.dares).forEach(category => {
        const items = freshContent.dares[category];
        if (Array.isArray(items)) {
          items.forEach(item => {
            if (item.id && item.status !== 'Inactive') {
              // Add category to the item for easier reference
              freshContentMaps.dares[category].set(item.id, {
                ...item,
                category
              });
            }
          });
        }
      });
    }

    // Log the number of items in each category
    let totalQuestions = 0;
    let totalDares = 0;
    Object.keys(freshContentMaps.questions).forEach(category => {
      const count = freshContentMaps.questions[category].size;
      totalQuestions += count;
      console.log(`- ${category}: ${count} active questions`);
    });
    Object.keys(freshContentMaps.dares).forEach(category => {
      const count = freshContentMaps.dares[category].size;
      totalDares += count;
      console.log(`- ${category}: ${count} active dares`);
    });
    console.log(`Created lookup maps with ${totalQuestions} active questions and ${totalDares} active dares`);

    // Track statistics for logging
    let totalPoolsUpdated = 0;
    let totalItemsUpdated = 0;
    let totalItemsAdded = 0;
    let totalItemsRemoved = 0;

    // Process each pool
    for (const poolKey of poolKeys) {
      // Load the pool
      const storedPool = await AsyncStorage.getItem(poolKey);
      if (!storedPool) continue;

      try {
        // Decrypt the pool
        const pool = decryptFromStorage(storedPool);

        if (!Array.isArray(pool)) {
          console.warn(`Pool with key ${poolKey} has invalid structure, skipping`);
          continue;
        }

        // Extract categories from the pool key
        // The key format is: taptrap_question_pool_category1_category2_...
        const keyParts = poolKey.replace(POOL_STORAGE_KEY_PREFIX, '').split('_');
        const poolCategories = keyParts.filter(part => part !== 'all' && part !== '');

        console.log(`Processing pool for categories: ${poolCategories.join(', ') || 'all'}`);

        let poolUpdated = false;
        let itemsUpdatedInPool = 0;
        let itemsAddedToPool = 0;
        let itemsRemovedFromPool = 0;

        // Step 1: Update existing items and mark items for removal
        const updatedPool = [];
        const existingIds = new Set();

        for (let i = 0; i < pool.length; i++) {
          const item = pool[i];

          // Find the fresh item in the appropriate category map
          let freshItem = null;
          if (item.type === 'question') {
            // Check if the item's category exists in our fresh content
            if (freshContentMaps.questions[item.category]) {
              freshItem = freshContentMaps.questions[item.category].get(item.id);
            }
          } else if (item.type === 'challenge') {
            // Check if the item's category exists in our fresh content
            if (freshContentMaps.dares[item.category]) {
              freshItem = freshContentMaps.dares[item.category].get(item.id);
            }
          }

          // If the item exists in fresh content, update it and keep it
          if (freshItem) {
            existingIds.add(item.id);

            // Check if any text fields have changed
            if (
              item.text_en !== freshItem.text_en ||
              item.text_es !== freshItem.text_es ||
              item.text_dom !== freshItem.text_dom
            ) {
              // Update the text fields but preserve other properties
              const updatedItem = {
                ...item,
                text_en: freshItem.text_en,
                text_es: freshItem.text_es,
                text_dom: freshItem.text_dom
              };

              updatedPool.push(updatedItem);
              poolUpdated = true;
              itemsUpdatedInPool++;
            } else {
              // No changes needed, keep the item as is
              updatedPool.push(item);
            }
          } else {
            // Item no longer exists in fresh content or is inactive, remove it
            poolUpdated = true;
            itemsRemovedFromPool++;
            // Don't add to updatedPool
          }
        }

        // Step 2: Add new items that weren't in the pool before, but only for the categories in this pool
        const newItems: PoolItem[] = [];

        // Helper function to check if a category should be included in this pool
        const shouldIncludeCategory = (category: string): boolean => {
          // If poolCategories is empty or contains 'all', include all categories
          if (poolCategories.length === 0 || poolCategories.includes('all')) {
            return true;
          }
          // Otherwise, only include if the category is in poolCategories
          return poolCategories.includes(category);
        };

        // Add new questions for matching categories
        Object.keys(freshContentMaps.questions).forEach(category => {
          if (shouldIncludeCategory(category)) {
            freshContentMaps.questions[category].forEach((item, id) => {
              if (!existingIds.has(id)) {
                newItems.push({
                  id: item.id,
                  text_en: item.text_en,
                  text_es: item.text_es,
                  text_dom: item.text_dom,
                  type: 'question',
                  category: item.category
                });
                itemsAddedToPool++;
              }
            });
          }
        });

        // Add new dares for matching categories
        Object.keys(freshContentMaps.dares).forEach(category => {
          if (shouldIncludeCategory(category)) {
            freshContentMaps.dares[category].forEach((item, id) => {
              if (!existingIds.has(id)) {
                newItems.push({
                  id: item.id,
                  text_en: item.text_en,
                  text_es: item.text_es,
                  text_dom: item.text_dom,
                  type: 'challenge',
                  category: item.category
                });
                itemsAddedToPool++;
              }
            });
          }
        });

        // If we have new items, add them to the pool and mark as updated
        if (newItems.length > 0) {
          updatedPool.push(...newItems);
          poolUpdated = true;
        }

        // Save the updated pool if changes were made
        if (poolUpdated) {
          const encryptedPool = encryptForStorage(updatedPool);
          await AsyncStorage.setItem(poolKey, encryptedPool);

          totalPoolsUpdated++;
          totalItemsUpdated += itemsUpdatedInPool;
          totalItemsAdded += itemsAddedToPool;
          totalItemsRemoved += itemsRemovedFromPool;

          console.log(`Pool ${poolKey} updated: ${itemsUpdatedInPool} updated, ${itemsAddedToPool} added, ${itemsRemovedFromPool} removed, new size: ${updatedPool.length}`);
        } else {
          console.log(`Pool ${poolKey} unchanged, size: ${pool.length}`);
        }
      } catch (error) {
        console.error(`Error processing pool ${poolKey}:`, error);
        // Continue with other pools even if one fails
      }
    }

    console.log(`Pool update complete: ${totalItemsUpdated} updated, ${totalItemsAdded} added, ${totalItemsRemoved} removed across ${totalPoolsUpdated} pools`);
    return true;
  } catch (error) {
    console.error('Error updating pool items:', error);
    return false;
  }
};

/**
 * Clear all pool-related data from AsyncStorage
 * @param categories Optional array of categories to clear. If not provided, clears all pools.
 * @returns Promise<boolean> indicating success or failure
 */
export const clearPoolStorage = async (categories?: string[]): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return true; // No-op on web
    }

    if (categories) {
      // Clear specific category pool
      const poolKey = getPoolKey(categories);
      const usedIdsKey = getUsedIdsKey(categories);

      await AsyncStorage.removeItem(poolKey);
      await AsyncStorage.removeItem(usedIdsKey);
      console.log(`Cleared pool storage data for categories: ${categories.join(', ')}`);
    } else {
      // Clear all pools by getting all keys and filtering for our prefixes
      const allKeys = await AsyncStorage.getAllKeys();
      const poolKeys = allKeys.filter(key =>
        key.startsWith(POOL_STORAGE_KEY_PREFIX) ||
        key.startsWith(USED_IDS_STORAGE_KEY_PREFIX) ||
        key === GLOBAL_USED_IDS_KEY
      );

      if (poolKeys.length > 0) {
        await AsyncStorage.multiRemove(poolKeys);
        console.log(`Cleared all pool storage data (${poolKeys.length} keys)`);
      } else {
        console.log('No pool storage data to clear');
      }
    }

    return true;
  } catch (error) {
    console.error('Error clearing pool storage:', error);
    return false;
  }
};
