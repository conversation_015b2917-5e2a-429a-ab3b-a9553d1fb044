import { useState, useEffect, useRef } from "react";
import { Platform, Alert, Linking } from 'react-native';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import * as IntentLauncher from 'expo-intent-launcher';
import * as Application from 'expo-application';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { API_URL } from './api';
import { getDeviceLanguage } from './i18nService';
import { getUniqueDeviceId } from './deviceIdService';
import * as Sentry from '@sentry/react-native';

// Storage keys
const PUSH_TOKEN_KEY = 'taptrap_push_token';
const PUSH_TOKEN_UPLOADED_KEY = 'taptrap_push_token_uploaded'; // Track if token was successfully uploaded
const PUSH_PERMISSION_DENIED_KEY = 'taptrap_push_permission_denied';
const PUSH_BANNER_DISMISSED_KEY = 'taptrap_push_banner_dismissed';
const NOTIFICATION_PREFERENCE_KEY = 'taptrap_notification_preference';
const LAST_KNOWN_PERMISSION_STATUS_KEY = 'taptrap_last_known_permission_status';

// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: false,
    shouldShowAlert: true,
    shouldSetBadge: false,
  }),
});

export interface PushNotificationState {
  expoPushToken?: Notifications.ExpoPushToken;
  notification?: Notifications.Notification;
}

// Hook for push notifications (similar to your example)
export const usePushNotifications = (): PushNotificationState => {
  const [expoPushToken, setExpoPushToken] = useState<
    Notifications.ExpoPushToken | undefined
  >();

  const [notification, setNotification] = useState<
    Notifications.Notification | undefined
  >();

  const notificationListener = useRef<Notifications.EventSubscription>();
  const responseListener = useRef<Notifications.EventSubscription>();

  async function registerForPushNotificationsAsync() {
    let token;
    if (Device.isDevice) {
      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== "granted") {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      if (finalStatus !== "granted") {
        console.log("Failed to get push token for push notification");
        return;
      }

      token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas.projectId || 'c488beec-b0b8-4ad1-b82a-4e646c9103a4',
      });
    } else {
      console.log("Must be using a physical device for Push notifications");
    }

    if (Platform.OS === "android") {
      Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
      });
    }

    return token;
  }

  useEffect(() => {
    registerForPushNotificationsAsync().then((token) => {
      setExpoPushToken(token);
    });

    notificationListener.current =
      Notifications.addNotificationReceivedListener((notification) => {
        setNotification(notification);
      });

    responseListener.current =
      Notifications.addNotificationResponseReceivedListener((response) => {
        console.log(response);
      });

    return () => {
      Notifications.removeNotificationSubscription(
        notificationListener.current!
      );

      Notifications.removeNotificationSubscription(responseListener.current!);
    };
  }, []);

  return {
    expoPushToken,
    notification,
  };
};

// Service interface for backward compatibility
interface PushNotificationService {
  requestPermissions(): Promise<boolean>;
  getExpoPushToken(): Promise<string | null>;
  uploadTokenToBackend(token: string): Promise<boolean>;
  hasPermissionBeenDenied(): Promise<boolean>;
  setPermissionDenied(): Promise<void>;
  hasBannerBeenDismissed(): Promise<boolean>;
  setBannerDismissed(): Promise<void>;
  clearBannerDismissed(): Promise<void>;
  shouldShowBanner(): Promise<boolean>;
  handleAutoTokenRegistration(): Promise<void>;
  initializeAtAppLaunch(): Promise<void>;
  handleAppStateChange(): Promise<void>;
  openNotificationSettings(): Promise<boolean>;
  showNotificationSettingsAlert(translations: {
    title: string;
    message: string;
    openSettings: string;
    cancel: string;
    error: string;
  }): Promise<void>;
  clearAllCachedStates(): Promise<void>;
  debugBannerState(): Promise<void>;
  getNotificationPreference(): Promise<boolean>;
  setNotificationPreference(enabled: boolean): Promise<void>;
  updateNotificationPreferenceOnBackend(enabled: boolean): Promise<boolean>;
  areNotificationsEnabled(): Promise<boolean>;
}

// Simplified service functions (no Firebase dependency)

/**
 * Request push notification permissions from the user
 */
async function requestPermissions(): Promise<boolean> {
  try {
    // Check if running on physical device
    if (!Device.isDevice) {
      console.log('Push notifications require a physical device');
      return false;
    }

    // Set up Android notification channel
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    // Check existing permissions
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    // Request permissions if not already granted
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Push notification permission denied');
      await setPermissionDenied();
      return false;
    }

    console.log('Push notification permission granted');
    return true;
  } catch (error) {
    console.error('Error requesting push notification permissions:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Get the Expo push token for this device
 */
async function getExpoPushToken(): Promise<string | null> {
  try {
    // Check if we already have a stored token
    const storedToken = await AsyncStorage.getItem(PUSH_TOKEN_KEY);
    if (storedToken) {
      console.log('Using stored push token');
      return storedToken;
    }

    // Get project ID from config - try multiple possible locations
    const projectId =
      Constants?.expoConfig?.extra?.eas?.projectId ??
      Constants?.easConfig?.projectId ??
      'c488beec-b0b8-4ad1-b82a-4e646c9103a4'; // Fallback to known project ID

    if (!projectId) {
      console.error('Project ID not found for push notifications');
      return null;
    }

    console.log('Using project ID for push notifications:', projectId);

    // Generate new token
    const pushTokenString = (
      await Notifications.getExpoPushTokenAsync({
        projectId,
      })
    ).data;

    // Store the token and clear uploaded flag since this is a new token
    await AsyncStorage.setItem(PUSH_TOKEN_KEY, pushTokenString);
    await AsyncStorage.removeItem(PUSH_TOKEN_UPLOADED_KEY); // Clear uploaded flag for new token
    console.log('Generated new push token:', pushTokenString);

    return pushTokenString;
  } catch (error) {
    console.error('Error getting Expo push token:', error);
    Sentry.captureException(error);
    return null;
  }
}

/**
 * Get the current language preference from AsyncStorage or device settings
 */
async function getCurrentLanguage(): Promise<string> {
  try {
    // First try to get stored language preference
    const storedLanguage = await AsyncStorage.getItem('appLanguage');
    if (storedLanguage && ['en', 'es', 'dom'].includes(storedLanguage)) {
      return storedLanguage;
    }

    // Fall back to device language
    return getDeviceLanguage();
  } catch (error) {
    console.error('Error getting current language:', error);
    Sentry.captureException(error);
    return 'en'; // Default to English
  }
}

/**
 * Upload the push token to the backend
 */
async function uploadTokenToBackend(token: string, notificationEnabled: boolean = true): Promise<boolean> {
  try {
    // Get current language preference
    const currentLanguage = await getCurrentLanguage();

    // Get unique device ID using the new device ID service
    const deviceId = await getUniqueDeviceId();

    const response = await axios.post(`${API_URL}/push-tokens`, {
      token,
      platform: Platform.OS,
      deviceId,
      language: currentLanguage,
      notificationEnabled,
    }, {
      // Content-Type header omitted to let axios set it automatically
      // This fixes Android-specific network issues
      timeout: 15000, // 15 second timeout
    });

    if (response.status === 200 || response.status === 201) {
      console.log('Push token uploaded successfully');
      return true;
    } else {
      console.error('Failed to upload push token:', response.status);
      return false;
    }
  } catch (error) {
    console.error('Error uploading push token to backend:', error);
    Sentry.captureException(error);
    if (axios.isAxiosError(error)) {
      console.error('  Status:', error.response?.status);
      console.error('  Message:', error.message);
    }
    return false;
  }
}

/**
 * Check if user has previously denied push notification permissions
 */
async function hasPermissionBeenDenied(): Promise<boolean> {
  try {
    const denied = await AsyncStorage.getItem(PUSH_PERMISSION_DENIED_KEY);
    return denied === 'true';
  } catch (error) {
    console.error('Error checking permission denied status:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Mark that user has denied push notification permissions
 */
async function setPermissionDenied(): Promise<void> {
  try {
    await AsyncStorage.setItem(PUSH_PERMISSION_DENIED_KEY, 'true');
  } catch (error) {
    console.error('Error setting permission denied status:', error);
    Sentry.captureException(error);
  }
}

/**
 * Check if banner has been dismissed within the last 8 hours
 */
async function hasBannerBeenDismissed(): Promise<boolean> {
  try {
    const dismissedTime = await AsyncStorage.getItem(PUSH_BANNER_DISMISSED_KEY);
    if (!dismissedTime) {
      return false;
    }

    const dismissedTimestamp = parseInt(dismissedTime, 10);
    const now = Date.now();
    const eightHoursInMs = 8 * 60 * 60 * 1000; // 8 hours in milliseconds

    return (now - dismissedTimestamp) < eightHoursInMs;
  } catch (error) {
    console.error('Error checking banner dismissed status:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Mark that banner has been dismissed
 */
async function setBannerDismissed(): Promise<void> {
  try {
    await AsyncStorage.setItem(PUSH_BANNER_DISMISSED_KEY, Date.now().toString());
  } catch (error) {
    console.error('Error setting banner dismissed status:', error);
    Sentry.captureException(error);
  }
}

/**
 * Clear banner dismissed status (for testing purposes)
 */
async function clearBannerDismissed(): Promise<void> {
  try {
    await AsyncStorage.removeItem(PUSH_BANNER_DISMISSED_KEY);
  } catch (error) {
    console.error('Error clearing banner dismissed status:', error);
    Sentry.captureException(error);
  }
}

/**
 * Determine if push notification banner should be shown
 */
async function shouldShowBanner(): Promise<boolean> {
  try {
    console.log('🔔 [PushBanner] Checking if banner should be shown...');
    console.log('🔔 [PushBanner] Platform:', Platform.OS);
    console.log('🔔 [PushBanner] Device.isDevice:', Device.isDevice);

    // Check if running on physical device (required for push notifications)
    if (!Device.isDevice) {
      console.log('🔔 [PushBanner] Not showing banner - not a physical device');
      return false;
    }

    // Don't show if banner was dismissed within last 8 hours
    // This applies to both first-time users and users who previously denied permissions
    const bannerDismissed = await hasBannerBeenDismissed();
    console.log('🔔 [PushBanner] Banner dismissed:', bannerDismissed);
    if (bannerDismissed) {
      console.log('🔔 [PushBanner] Not showing banner - dismissed within last 8 hours');
      return false;
    }

    // Check if we already have permission
    const { status } = await Notifications.getPermissionsAsync();
    console.log('🔔 [PushBanner] Current permission status:', status);

    if (status === 'granted') {
      console.log('🔔 [PushBanner] Permission already granted - not showing banner');
      return false;
    }

    // Show banner for users who haven't been asked yet (undetermined)
    // OR users who previously denied (so they can access settings navigation)
    if (status === 'undetermined' || status === 'denied') {
      console.log('🔔 [PushBanner] ✅ All conditions met - banner should be shown!');
      console.log('🔔 [PushBanner] Permission status allows banner:', status);
      return true;
    }

    // For any other status, don't show banner
    console.log('🔔 [PushBanner] Not showing banner - unsupported permission status:', status);
    return false;
  } catch (error) {
    console.error('🔔 [PushBanner] Error checking if banner should be shown:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Initialize push notifications at app launch
 * This ensures tokens are registered whenever permissions are granted (iOS and Android)
 */
async function initializeAtAppLaunch(): Promise<void> {
  try {
    console.log('🔔 [AppLaunch] Initializing push notifications...');
    console.log('🔔 [AppLaunch] Platform:', Platform.OS);
    console.log('🔔 [AppLaunch] Device.isDevice:', Device.isDevice);

    // Only proceed on physical devices
    if (!Device.isDevice) {
      console.log('🔔 [AppLaunch] Skipping - not a physical device');
      return;
    }

    // Set up Android notification channel first (required for Android 13+)
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    // Check current permission status
    const { status } = await Notifications.getPermissionsAsync();
    console.log('🔔 [AppLaunch] Current permission status:', status);

    // Store initial permission status for future comparison
    await storeCurrentPermissionStatus(status);

    // If permissions are granted, register token immediately
    if (status === 'granted') {
      console.log('🔔 [AppLaunch] Permissions granted - registering token');
      await handleAutoTokenRegistration();
    } else {
      console.log('🔔 [AppLaunch] Permissions not granted - will wait for user interaction');
    }
  } catch (error) {
    console.error('🔔 [AppLaunch] Error initializing push notifications:', error);
    Sentry.captureException(error);
  }
}

/**
 * Handle automatic token registration when permissions are granted
 * This function can be called multiple times safely
 * Now properly tracks whether token was successfully uploaded to backend
 */
async function handleAutoTokenRegistration(): Promise<void> {
  try {
    console.log('🔔 [AutoReg] Getting push token for auto-registration...');
    const token = await getExpoPushToken();

    if (token) {
      // Check if we already have this token uploaded to backend
      const storedToken = await AsyncStorage.getItem(PUSH_TOKEN_KEY);
      const tokenUploaded = await AsyncStorage.getItem(PUSH_TOKEN_UPLOADED_KEY);

      if (storedToken === token && tokenUploaded === 'true') {
        console.log('🔔 [AutoReg] Token already uploaded to backend, skipping redundant upload');
        console.log('🔔 [AutoReg] Preference updates will only happen when permission status actually changes');
        return;
      }

      console.log('🔔 [AutoReg] Token needs to be uploaded to backend...');
      // Always pass the current permission status when uploading token
      const { status } = await Notifications.getPermissionsAsync();
      const notificationEnabled = status === 'granted';
      const uploaded = await uploadTokenToBackend(token, notificationEnabled);
      if (uploaded) {
        console.log('🔔 [AutoReg] ✅ Auto-registration completed successfully');
        // Store the token and mark as uploaded
        await AsyncStorage.setItem(PUSH_TOKEN_KEY, token);
        await AsyncStorage.setItem(PUSH_TOKEN_UPLOADED_KEY, 'true');
        // Clear permission denied flag since we successfully registered
        await AsyncStorage.removeItem(PUSH_PERMISSION_DENIED_KEY);
      } else {
        console.log('🔔 [AutoReg] ❌ Failed to upload token to backend');
        // Clear the uploaded flag since upload failed
        await AsyncStorage.removeItem(PUSH_TOKEN_UPLOADED_KEY);
      }
    } else {
      console.log('🔔 [AutoReg] ❌ Failed to get push token');
    }
  } catch (error) {
    console.error('🔔 [AutoReg] Error in auto token registration:', error);
    Sentry.captureException(error);
  }
}

/**
 * Handle app state change to check for permission changes
 * This should be called when the app returns to the foreground
 * Now optimized to only send API calls when permission status actually changes
 */
async function handleAppStateChange(): Promise<void> {
  try {
    console.log('🔔 [AppState] App returned to foreground, checking for permission changes...');

    // Only proceed on physical devices
    if (!Device.isDevice) {
      console.log('🔔 [AppState] Skipping - not a physical device');
      return;
    }

    // Check if permission status has changed
    const permissionCheck = await hasPermissionStatusChanged();

    if (!permissionCheck.hasChanged) {
      console.log('🔔 [AppState] Permission status unchanged, skipping API call');
      return;
    }

    console.log('🔔 [AppState] Permission status changed:', {
      from: permissionCheck.lastStatus,
      to: permissionCheck.currentStatus
    });

    // Store the new permission status
    await storeCurrentPermissionStatus(permissionCheck.currentStatus);

    // Update backend with new permission status
    const notificationEnabled = permissionCheck.currentStatus === 'granted';
    console.log('🔔 [AppState] Updating backend with permission status:', notificationEnabled);

    const backendUpdateSuccess = await updateNotificationPreferenceOnBackend(notificationEnabled);
    if (backendUpdateSuccess) {
      console.log('🔔 [AppState] Backend permission status updated successfully');
    } else {
      console.error('🔔 [AppState] Failed to update backend permission status');
    }

    // If permissions are now granted, try to register token
    if (permissionCheck.currentStatus === 'granted') {
      console.log('🔔 [AppState] Permissions granted - attempting token registration');
      await handleAutoTokenRegistration();
    } else {
      console.log('🔔 [AppState] Permissions revoked - backend updated to stop sending notifications');
    }
  } catch (error) {
    console.error('🔔 [AppState] Error handling app state change:', error);
    Sentry.captureException(error);
  }
}

/**
 * Open iOS app settings using Linking.openSettings()
 */
async function openIOSSettings(): Promise<boolean> {
  try {
    console.log('🔔 [Settings] Opening iOS app settings...');
    await Linking.openSettings();
    return true;
  } catch (error) {
    console.error('🔔 [Settings] Error opening iOS settings:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Open Android app notification settings using IntentLauncher
 */
async function openAndroidNotificationSettings(): Promise<boolean> {
  try {
    console.log('🔔 [Settings] Opening Android notification settings...');

    // Get the app package name
    const packageName = Application.applicationId;
    if (!packageName) {
      console.error('🔔 [Settings] Could not get application package name');
      return false;
    }

    console.log('🔔 [Settings] Package name:', packageName);

    // Open app-specific notification settings
    await IntentLauncher.startActivityAsync(IntentLauncher.ActivityAction.APP_NOTIFICATION_SETTINGS, {
      data: `package:${packageName}`,
    });

    return true;
  } catch (error) {
    console.error('🔔 [Settings] Error opening Android notification settings:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Get the current notification preference from local storage
 */
async function getNotificationPreference(): Promise<boolean> {
  try {
    const preference = await AsyncStorage.getItem(NOTIFICATION_PREFERENCE_KEY);
    return preference === 'true';
  } catch (error) {
    console.error('Error getting notification preference:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Set the notification preference in local storage
 */
async function setNotificationPreference(enabled: boolean): Promise<void> {
  try {
    await AsyncStorage.setItem(NOTIFICATION_PREFERENCE_KEY, enabled.toString());
  } catch (error) {
    console.error('Error setting notification preference:', error);
    Sentry.captureException(error);
  }
}

/**
 * Update notification preference on the backend
 * Handles the case where device is not registered yet (404 error)
 */
async function updateNotificationPreferenceOnBackend(enabled: boolean): Promise<boolean> {
  try {
    const deviceId = await getUniqueDeviceId();

    console.log('🔔 [PreferenceUpdate] Attempting to update preference to:', enabled);
    const response = await axios.put(`${API_URL}/push-tokens/preference`, {
      deviceId,
      notificationEnabled: enabled,
    }, {
      // Content-Type header omitted to let axios set it automatically
      // This fixes Android-specific network issues
      timeout: 15000,
    });

    if (response.status === 200) {
      console.log('🔔 [PreferenceUpdate] Notification preference updated on backend successfully');
      return true;
    } else {
      console.error('🔔 [PreferenceUpdate] Failed to update notification preference on backend:', response.status);
      return false;
    }
  } catch (error) {
    // Handle the specific case where device is not registered yet (404 error)
    if (axios.isAxiosError(error) && error.response?.status === 404) {
      console.log('🔔 [PreferenceUpdate] Device not registered yet, skipping preference update');
      console.log('🔔 [PreferenceUpdate] Device will be registered with correct preference when token is uploaded');
      return true; // Return true since this is expected behavior, not an error
    }

    console.error('🔔 [PreferenceUpdate] Error updating notification preference on backend:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Check if notifications are currently enabled (both permission and preference)
 */
async function areNotificationsEnabled(): Promise<boolean> {
  try {
    // Check system permission
    const { status } = await Notifications.getPermissionsAsync();
    const hasPermission = status === 'granted';

    // Check user preference
    const userPreference = await getNotificationPreference();

    return hasPermission && userPreference;
  } catch (error) {
    console.error('Error checking notification status:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Get the last known permission status
 */
async function getLastKnownPermissionStatus(): Promise<string | null> {
  try {
    return await AsyncStorage.getItem(LAST_KNOWN_PERMISSION_STATUS_KEY);
  } catch (error) {
    console.error('Error getting last known permission status:', error);
    Sentry.captureException(error);
    return null;
  }
}

/**
 * Store the current permission status
 */
async function storeCurrentPermissionStatus(status: string): Promise<void> {
  try {
    await AsyncStorage.setItem(LAST_KNOWN_PERMISSION_STATUS_KEY, status);
    console.log('🔔 [PermissionTracking] Stored permission status:', status);
  } catch (error) {
    console.error('Error storing current permission status:', error);
    Sentry.captureException(error);
  }
}

/**
 * Check if permission status has changed since last check
 */
async function hasPermissionStatusChanged(): Promise<{ hasChanged: boolean; currentStatus: string; lastStatus: string | null }> {
  try {
    const { status: currentStatus } = await Notifications.getPermissionsAsync();
    const lastStatus = await getLastKnownPermissionStatus();

    const hasChanged = currentStatus !== lastStatus;

    console.log('🔔 [PermissionTracking] Permission status check:', {
      currentStatus,
      lastStatus,
      hasChanged
    });

    return {
      hasChanged,
      currentStatus,
      lastStatus
    };
  } catch (error) {
    console.error('Error checking permission status change:', error);
    Sentry.captureException(error);
    return {
      hasChanged: true, // Assume changed on error to ensure token is sent
      currentStatus: 'unknown',
      lastStatus: null
    };
  }
}

/**
 * Open platform-specific notification settings
 */
async function openNotificationSettings(): Promise<boolean> {
  try {
    console.log('🔔 [Settings] Opening notification settings for platform:', Platform.OS);

    if (Platform.OS === 'ios') {
      return await openIOSSettings();
    } else if (Platform.OS === 'android') {
      return await openAndroidNotificationSettings();
    } else {
      console.warn('🔔 [Settings] Unsupported platform:', Platform.OS);
      return false;
    }
  } catch (error) {
    console.error('🔔 [Settings] Error opening notification settings:', error);
    Sentry.captureException(error);
    return false;
  }
}

/**
 * Show alert and guide user to notification settings when permissions are denied
 * This function requires i18n translations to be passed in to avoid circular dependencies
 */
async function showNotificationSettingsAlert(translations: {
  title: string;
  message: string;
  openSettings: string;
  cancel: string;
  error: string;
}): Promise<void> {
  try {
    console.log('🔔 [Settings] Showing notification settings alert...');

    // Check current permission status
    const { status } = await Notifications.getPermissionsAsync();
    console.log('🔔 [Settings] Current permission status:', status);

    // Only show alert if permissions are denied
    if (status === 'granted') {
      console.log('🔔 [Settings] Permissions already granted, no need to show alert');
      return;
    }

    // Show platform-specific alert
    Alert.alert(
      translations.title,
      translations.message,
      [
        {
          text: translations.cancel,
          style: 'cancel',
          onPress: () => {
            console.log('🔔 [Settings] User cancelled settings navigation');
          },
        },
        {
          text: translations.openSettings,
          onPress: async () => {
            console.log('🔔 [Settings] User chose to open settings');
            const success = await openNotificationSettings();
            if (!success) {
              // Show error alert if settings couldn't be opened
              Alert.alert('Error', translations.error, [{ text: 'OK' }]);
            }
          },
        },
      ],
      { cancelable: true }
    );
  } catch (error) {
    console.error('🔔 [Settings] Error showing notification settings alert:', error);
    Sentry.captureException(error);
  }
}

/**
 * Clear all cached states (for testing purposes)
 */
async function clearAllCachedStates(): Promise<void> {
  try {
    console.log('🔔 [PushBanner] Clearing all cached states...');
    await AsyncStorage.removeItem(PUSH_TOKEN_KEY);
    await AsyncStorage.removeItem(PUSH_TOKEN_UPLOADED_KEY);
    await AsyncStorage.removeItem(PUSH_PERMISSION_DENIED_KEY);
    await AsyncStorage.removeItem(PUSH_BANNER_DISMISSED_KEY);
    await AsyncStorage.removeItem(NOTIFICATION_PREFERENCE_KEY);
    await AsyncStorage.removeItem(LAST_KNOWN_PERMISSION_STATUS_KEY);
    console.log('🔔 [PushBanner] All cached states cleared');
  } catch (error) {
    console.error('🔔 [PushBanner] Error clearing cached states:', error);
  }
}

/**
 * Debug current banner state (for troubleshooting)
 */
async function debugBannerState(): Promise<void> {
  try {
    console.log('🔔 [PushBanner] === DEBUG BANNER STATE ===');
    console.log('🔔 [PushBanner] Platform:', Platform.OS);
    console.log('🔔 [PushBanner] Device.isDevice:', Device.isDevice);

    const token = await AsyncStorage.getItem(PUSH_TOKEN_KEY);
    const denied = await AsyncStorage.getItem(PUSH_PERMISSION_DENIED_KEY);
    const dismissed = await AsyncStorage.getItem(PUSH_BANNER_DISMISSED_KEY);

    console.log('🔔 [PushBanner] Stored token:', token ? 'EXISTS' : 'NULL');
    console.log('🔔 [PushBanner] Permission denied:', denied);
    console.log('🔔 [PushBanner] Banner dismissed:', dismissed);

    const { status } = await Notifications.getPermissionsAsync();
    console.log('🔔 [PushBanner] Current permission status:', status);

    const shouldShow = await shouldShowBanner();
    console.log('🔔 [PushBanner] Should show banner:', shouldShow);
    console.log('🔔 [PushBanner] === END DEBUG ===');
  } catch (error) {
    console.error('🔔 [PushBanner] Error debugging banner state:', error);
  }
}

// Create service object for backward compatibility
const pushNotificationService: PushNotificationService = {
  requestPermissions,
  getExpoPushToken,
  uploadTokenToBackend,
  hasPermissionBeenDenied,
  setPermissionDenied,
  hasBannerBeenDismissed,
  setBannerDismissed,
  clearBannerDismissed,
  shouldShowBanner,
  handleAutoTokenRegistration,
  initializeAtAppLaunch,
  handleAppStateChange,
  openNotificationSettings,
  showNotificationSettingsAlert,
  clearAllCachedStates,
  debugBannerState,
  getNotificationPreference,
  setNotificationPreference,
  updateNotificationPreferenceOnBackend,
  areNotificationsEnabled,
};

// Export service instance
export { pushNotificationService };

// Export types
export type { PushNotificationService };

// Helper function to handle the complete permission flow
export const handlePushNotificationPermissionFlow = async (): Promise<void> => {
  try {
    // Request permissions
    const permissionGranted = await requestPermissions();

    if (permissionGranted) {
      // Get push token
      const token = await getExpoPushToken();

      if (token) {
        // Upload to backend
        await uploadTokenToBackend(token);
      }
    }
  } catch (error) {
    console.error('Error in push notification permission flow:', error);
  }
};

// Helper function to initialize push notifications at app launch
export const initializePushNotificationsAtLaunch = async (): Promise<void> => {
  await initializeAtAppLaunch();
};

// Helper function to handle app state changes
export const handlePushNotificationAppStateChange = async (): Promise<void> => {
  await handleAppStateChange();
};

// Helper function to open notification settings
export const openPushNotificationSettings = async (): Promise<boolean> => {
  return await openNotificationSettings();
};

// Helper function to show settings alert with translations
export const showPushNotificationSettingsAlert = async (translations: {
  title: string;
  message: string;
  openSettings: string;
  cancel: string;
  error: string;
}): Promise<void> => {
  await showNotificationSettingsAlert(translations);
};

// Helper functions for debugging (can be called from console or dev tools)
export const debugPushNotificationBanner = async (): Promise<void> => {
  await debugBannerState();
};

export const clearPushNotificationCache = async (): Promise<void> => {
  await clearAllCachedStates();
  console.log('🔔 [Debug] Push notification cache cleared. Restart the app to test banner again.');
};

export const testBannerConditions = async (): Promise<void> => {
  console.log('🔔 [Debug] Testing banner conditions...');
  await debugBannerState();
  const shouldShow = await shouldShowBanner();
  console.log('🔔 [Debug] Final result - should show banner:', shouldShow);
};

export const debugTokenUploadStatus = async (): Promise<void> => {
  console.log('🔔 [Debug] === TOKEN UPLOAD STATUS ===');
  try {
    const token = await AsyncStorage.getItem(PUSH_TOKEN_KEY);
    const uploaded = await AsyncStorage.getItem(PUSH_TOKEN_UPLOADED_KEY);
    const deviceId = await getUniqueDeviceId();
    const { status } = await Notifications.getPermissionsAsync();

    console.log('🔔 [Debug] Device ID:', deviceId);
    console.log('🔔 [Debug] Permission status:', status);
    console.log('🔔 [Debug] Token exists locally:', token ? 'YES' : 'NO');
    console.log('🔔 [Debug] Token uploaded to backend:', uploaded === 'true' ? 'YES' : 'NO');

    if (token && uploaded !== 'true') {
      console.log('🔔 [Debug] ⚠️  TOKEN EXISTS BUT NOT UPLOADED - This is the problem!');
      console.log('🔔 [Debug] 💡 Try calling handleAutoTokenRegistration() to fix this');
    }
  } catch (error) {
    console.error('🔔 [Debug] Error checking token status:', error);
  }
  console.log('🔔 [Debug] === END TOKEN STATUS ===');
};


