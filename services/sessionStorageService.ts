/**
 * Persistent Ad Storage Service
 *
 * Manages persistent storage for ad-based premium access with 15-minute expiration.
 * <PERSON><PERSON> stuck ad recovery and maintains state across app restarts.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import * as Sentry from '@sentry/react-native';

export interface AdBasedAccessState {
  hasWatchedAds: boolean;
  adsWatchedCount: number;
  questionsAnsweredSinceAds: number;
  lastAdWatchTime: number | null;
  sessionStartTime: number | null;
}

export interface FreeContentCounters {
  premiumContentRemaining: number; // Shared counter for all premium categories
  lastResetTime: number | null;
}

export interface PersistentAdWatchState {
  adIndex: number;
  watchedAt: number;
  expiresAt: number;
  sessionId: string;
}

class PersistentAdStorageService {
  // Store the required ads count (will be updated from ad settings)
  private requiredAdsCount: number = 1;

  // Store the free content limit (will be updated from ad settings)
  private freeContentLimit: number = 20;

  private adBasedAccessState: AdBasedAccessState = {
    hasWatchedAds: false,
    adsWatchedCount: 0,
    questionsAnsweredSinceAds: 0,
    lastAdWatchTime: null,
    sessionStartTime: null,
  };

  private freeContentCounters: FreeContentCounters = {
    premiumContentRemaining: 20, // Default value, will be loaded from storage or reset appropriately
    lastResetTime: null,
  };

  // Persistent ad watch tracking
  private persistentAdWatches: PersistentAdWatchState[] = [];
  private currentSessionId: string = '';

  // Constants
  private readonly PERSISTENT_AD_EXPIRATION_MS = 30 * 60 * 1000; // 30 minutes
  private readonly STORAGE_KEY_AD_STATE = 'taptrap_ad_based_access_state';
  private readonly STORAGE_KEY_CONTENT_COUNTERS = 'taptrap_free_content_counters';
  private readonly STORAGE_KEY_PERSISTENT_AD_WATCHES = 'taptrap_persistent_ad_watches';

  /**
   * Update the required ads count from ad settings
   */
  setRequiredAdsCount(count: number): void {
    this.requiredAdsCount = Math.max(1, Math.min(10, count)); // Ensure it's between 1 and 10
    console.log(`SessionStorage: Updated required ads count to ${this.requiredAdsCount}`);
  }

  /**
   * Update the free content limit from ad settings
   */
  setFreeContentLimit(limit: number): void {
    const newLimit = Math.max(1, Math.min(20, limit)); // Ensure it's between 1 and 20
    const oldLimit = this.freeContentLimit;
    this.freeContentLimit = newLimit;

    console.log(`SessionStorage: Updated free content limit from ${oldLimit} to ${this.freeContentLimit}`);

    // CRITICAL FIX: Handle the case where API settings are loaded after service initialization
    // For new users who were initialized with the wrong default, update them to the correct API value
    this.updateFreeContentForNewUsers();

    console.log('SessionStorage: Free content limit updated, existing user state preserved');
  }

  /**
   * Get the current required ads count
   */
  getRequiredAdsCount(): number {
    return this.requiredAdsCount;
  }

  /**
   * Initialize the service and load persistent data
   */
  async initialize(): Promise<void> {
    console.log('PersistentAdStorage: Service initializing...');

    // Generate a new session ID
    this.currentSessionId = this.generateSessionId();

    // Load persistent data
    await this.loadAdBasedAccessState();
    await this.loadFreeContentCounters();
    await this.loadPersistentAdWatches();

    // Clean up expired ad watches
    await this.cleanupExpiredAdWatches();

    // Check for premium access expiration
    this.checkPremiumAccessExpiration();

    // Recover valid ad watch progress
    this.recoverAdWatchProgress();

    console.log('PersistentAdStorage: Service initialized successfully');
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Load ad-based access state from AsyncStorage
   */
  private async loadAdBasedAccessState(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        return;
      }

      const stored = await AsyncStorage.getItem(this.STORAGE_KEY_AD_STATE);
      if (stored) {
        this.adBasedAccessState = JSON.parse(stored);
        console.log('PersistentAdStorage: Loaded ad-based access state from storage');
      }
    } catch (error) {
      console.error('PersistentAdStorage: Error loading ad-based access state:', error);
      Sentry.captureException(error);
    }
  }

  /**
   * Load free content counters from AsyncStorage
   */
  private async loadFreeContentCounters(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        return;
      }

      const stored = await AsyncStorage.getItem(this.STORAGE_KEY_CONTENT_COUNTERS);
      if (stored) {
        this.freeContentCounters = JSON.parse(stored);
        console.log(`PersistentAdStorage: Loaded free content counters from storage: ${this.freeContentCounters.premiumContentRemaining} remaining`);

        // CRITICAL FIX: Preserve stored user state - do NOT reset to API default
        // The stored state represents the user's actual progress and should be preserved
        // Only reset when user gains new premium access or when access expires
      } else {
        // No stored data - this is a new user, initialize with current API default
        console.log('PersistentAdStorage: No stored free content counters, initializing new user with API default');
        this.initializeFreeContentCounters();
      }
    } catch (error) {
      console.error('PersistentAdStorage: Error loading free content counters:', error);
      Sentry.captureException(error);
      // On error, initialize with API default
      this.initializeFreeContentCounters();
    }
  }

  /**
   * Initialize free content counters with API default (for new users)
   */
  private initializeFreeContentCounters(): void {
    // Use the current freeContentLimit which should be set from API settings
    // If API hasn't loaded yet, this will use the default value and be updated later
    const limitToUse = this.freeContentLimit;

    this.freeContentCounters = {
      premiumContentRemaining: limitToUse,
      lastResetTime: Date.now(),
    };
    this.saveFreeContentCounters();
    console.log(`PersistentAdStorage: Initialized free content counters with limit: ${limitToUse}`);
  }

  /**
   * Update free content counters for new users when API settings are loaded
   * This handles the case where the service was initialized before API settings were available
   */
  updateFreeContentForNewUsers(): void {
    // Only update if user has no stored progress and no premium access
    // This ensures we don't overwrite existing user state
    if (this.freeContentCounters.premiumContentRemaining === 6 && // Still using old default
        !this.hasTemporaryPremiumAccess() && // No premium access
        this.freeContentCounters.lastResetTime && // Has been initialized
        Date.now() - this.freeContentCounters.lastResetTime < 60000) { // Recently initialized (< 1 min)

      console.log(`SessionStorage: Updating new user's free content from ${this.freeContentCounters.premiumContentRemaining} to ${this.freeContentLimit}`);
      this.initializeFreeContentCounters();
    }
  }

  /**
   * Load persistent ad watches from AsyncStorage
   */
  private async loadPersistentAdWatches(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        return;
      }

      const stored = await AsyncStorage.getItem(this.STORAGE_KEY_PERSISTENT_AD_WATCHES);
      if (stored) {
        this.persistentAdWatches = JSON.parse(stored);
        console.log(`PersistentAdStorage: Loaded ${this.persistentAdWatches.length} persistent ad watches from storage`);
      }
    } catch (error) {
      console.error('PersistentAdStorage: Error loading persistent ad watches:', error);
      Sentry.captureException(error);
    }
  }

  /**
   * Clean up expired ad watches from storage
   */
  private async cleanupExpiredAdWatches(): Promise<void> {
    try {
      const now = Date.now();
      const initialCount = this.persistentAdWatches.length;
      const validWatches = this.persistentAdWatches.filter(watch => watch.expiresAt > now);

      if (validWatches.length !== initialCount) {
        const expiredCount = initialCount - validWatches.length;
        this.persistentAdWatches = validWatches;
        await this.savePersistentAdWatches();
        console.log(`PersistentAdStorage: Cleaned up ${expiredCount} expired ad watches`);
      }
    } catch (error) {
      console.error('PersistentAdStorage: Error cleaning up expired ad watches:', error);
      Sentry.captureException(error);
    }
  }

  /**
   * Check if premium access has expired based on the 15-minute rule
   */
  private checkPremiumAccessExpiration(): void {
    try {
      const now = Date.now();

      // If user has premium access, check if it should expire
      if (this.adBasedAccessState.hasWatchedAds && this.adBasedAccessState.lastAdWatchTime) {
        const timeSinceLastAd = now - this.adBasedAccessState.lastAdWatchTime;

        // If more than 15 minutes have passed since the last ad watch
        if (timeSinceLastAd > this.PERSISTENT_AD_EXPIRATION_MS) {
          console.log('PersistentAdStorage: Premium access expired after 15 minutes, resetting state');

          // Reset premium access state
          this.adBasedAccessState.hasWatchedAds = false;
          this.adBasedAccessState.adsWatchedCount = 0;
          this.adBasedAccessState.lastAdWatchTime = null;
          this.adBasedAccessState.sessionStartTime = null;
          this.adBasedAccessState.questionsAnsweredSinceAds = 0;

          // Reset free content counters to API default
          this.resetFreeContentCounters();

          // Clear all persistent ad watches since access has expired
          this.persistentAdWatches = [];
          this.savePersistentAdWatches();
          this.saveAdBasedAccessState();

          console.log('PersistentAdStorage: Premium access and all related state reset due to expiration');
        }
      }
    } catch (error) {
      console.error('PersistentAdStorage: Error checking premium access expiration:', error);
      Sentry.captureException(error);
    }
  }

  /**
   * Recover ad watch progress from valid persistent watches
   */
  private recoverAdWatchProgress(): void {
    try {
      const validWatches = this.persistentAdWatches.filter(watch => watch.expiresAt > Date.now());

      if (validWatches.length > 0) {
        // Update ads watched count based on valid persistent watches
        const recoveredCount = validWatches.length;
        this.adBasedAccessState.adsWatchedCount = Math.max(this.adBasedAccessState.adsWatchedCount, recoveredCount);

        // Update last ad watch time to the most recent valid watch
        const mostRecentWatch = validWatches.reduce((latest, watch) =>
          watch.watchedAt > latest.watchedAt ? watch : latest
        );
        this.adBasedAccessState.lastAdWatchTime = mostRecentWatch.watchedAt;

        // CRITICAL FIX: Only grant premium access if user PREVIOUSLY had it AND still has enough ads
        // Don't automatically grant access just because user has valid ad watches
        if (this.adBasedAccessState.hasWatchedAds && this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount) {
          // User previously had premium access and still qualifies - restore it
          console.log('PersistentAdStorage: Restoring existing premium access, preserving free content state');
        } else {
          // User had partial progress but not full access - preserve progress but no premium access
          this.adBasedAccessState.hasWatchedAds = false;
          console.log(`PersistentAdStorage: Recovered ${recoveredCount} ad watches but user needs ${this.requiredAdsCount} total for premium access`);
        }

        console.log(`PersistentAdStorage: Recovered ${recoveredCount} valid ad watches, total count: ${this.adBasedAccessState.adsWatchedCount}/${this.requiredAdsCount}`);
      } else {
        // No valid watches found - reset ad access state
        console.log('PersistentAdStorage: No valid ad watches found, resetting ad access state');

        // Check if user previously had premium access
        const hadPreviousAccess = this.adBasedAccessState.hasWatchedAds;

        this.adBasedAccessState.hasWatchedAds = false;
        this.adBasedAccessState.adsWatchedCount = 0;
        this.adBasedAccessState.lastAdWatchTime = null;
        this.adBasedAccessState.sessionStartTime = null;

        // CRITICAL FIX: Only reset free content if user actually had premium access that expired
        // Don't reset free content for users who never had premium access
        if (hadPreviousAccess) {
          console.log('PersistentAdStorage: User had premium access that expired, resetting free content');
          this.resetFreeContentCounters();
        } else {
          console.log('PersistentAdStorage: User never had premium access, preserving free content state');
        }
      }

      // Save the updated state
      this.saveAdBasedAccessState();
    } catch (error) {
      console.error('PersistentAdStorage: Error recovering ad watch progress:', error);
      Sentry.captureException(error);
    }
  }

  /**
   * Save ad-based access state to AsyncStorage
   */
  private async saveAdBasedAccessState(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        return;
      }

      await AsyncStorage.setItem(this.STORAGE_KEY_AD_STATE, JSON.stringify(this.adBasedAccessState));
    } catch (error) {
      console.error('PersistentAdStorage: Error saving ad-based access state:', error);
      Sentry.captureException(error);
    }
  }

  /**
   * Save free content counters to AsyncStorage
   */
  private async saveFreeContentCounters(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        return;
      }

      await AsyncStorage.setItem(this.STORAGE_KEY_CONTENT_COUNTERS, JSON.stringify(this.freeContentCounters));
    } catch (error) {
      console.error('PersistentAdStorage: Error saving free content counters:', error);
      Sentry.captureException(error);
    }
  }

  /**
   * Save persistent ad watches to AsyncStorage
   */
  private async savePersistentAdWatches(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        return;
      }

      await AsyncStorage.setItem(this.STORAGE_KEY_PERSISTENT_AD_WATCHES, JSON.stringify(this.persistentAdWatches));
    } catch (error) {
      console.error('PersistentAdStorage: Error saving persistent ad watches:', error);
      Sentry.captureException(error);
    }
  }

  /**
   * Get the current ad-based access state
   */
  getAdBasedAccessState(): AdBasedAccessState {
    return { ...this.adBasedAccessState };
  }

  /**
   * Get the current free content counters
   */
  getFreeContentCounters(): FreeContentCounters {
    return { ...this.freeContentCounters };
  }

  /**
   * Check if user has temporary premium access through ads
   */
  hasTemporaryPremiumAccess(): boolean {
    // User must have watched enough ads AND still have remaining free content
    const hasWatchedEnoughAds = this.adBasedAccessState.hasWatchedAds && this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount;
    const hasRemainingContent = this.freeContentCounters.premiumContentRemaining > 0;
    const hasAccess = hasWatchedEnoughAds && hasRemainingContent;

    console.log(`PersistentAdStorage: hasTemporaryPremiumAccess check - watchedAds: ${hasWatchedEnoughAds}, remainingContent: ${hasRemainingContent} (${this.freeContentCounters.premiumContentRemaining}), result: ${hasAccess}`);

    return hasAccess;
  }

  /**
   * Increment the count of ads watched with persistent tracking
   */
  incrementAdsWatched(): void {
    const now = Date.now();

    // Create persistent ad watch entry
    const persistentWatch: PersistentAdWatchState = {
      adIndex: this.adBasedAccessState.adsWatchedCount,
      watchedAt: now,
      expiresAt: now + this.PERSISTENT_AD_EXPIRATION_MS,
      sessionId: this.currentSessionId,
    };

    // Add to persistent watches
    this.persistentAdWatches.push(persistentWatch);
    this.savePersistentAdWatches();

    // Update regular state
    this.adBasedAccessState.adsWatchedCount += 1;
    this.adBasedAccessState.lastAdWatchTime = now;

    // Set session start time if not already set
    if (!this.adBasedAccessState.sessionStartTime) {
      this.adBasedAccessState.sessionStartTime = now;
    }

    // Grant access if user has watched all required ads
    if (this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount) {
      this.adBasedAccessState.hasWatchedAds = true;
      // Reset free content counters when gaining access
      this.resetFreeContentCounters();
      console.log(`PersistentAdStorage: User has watched all ${this.requiredAdsCount} ads, granting temporary premium access`);
    }

    // Save updated state
    this.saveAdBasedAccessState();

    console.log(`PersistentAdStorage: Ads watched count: ${this.adBasedAccessState.adsWatchedCount}/${this.requiredAdsCount} (persistent: ${this.persistentAdWatches.length})`);
  }

  /**
   * Reset free content counters to default values
   */
  private resetFreeContentCounters(): void {
    this.freeContentCounters = {
      premiumContentRemaining: this.freeContentLimit,
      lastResetTime: Date.now(),
    };
    this.saveFreeContentCounters();
    console.log(`PersistentAdStorage: Reset free content counters to ${this.freeContentLimit}`);
  }

  /**
   * Decrement free content counter for premium categories (shared counter)
   */
  decrementFreeContent(category: 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares'): void {
    // All premium categories share the same counter
    if (this.freeContentCounters.premiumContentRemaining > 0) {
      this.freeContentCounters.premiumContentRemaining--;
    }

    // CRITICAL FIX: Reset ad watch state when free content is exhausted
    if (this.freeContentCounters.premiumContentRemaining === 0) {
      console.log('PersistentAdStorage: Free content exhausted, resetting ad watch state');
      this.adBasedAccessState.hasWatchedAds = false;
      this.adBasedAccessState.adsWatchedCount = 0;
      this.adBasedAccessState.lastAdWatchTime = null;
      this.adBasedAccessState.sessionStartTime = null;
      this.adBasedAccessState.questionsAnsweredSinceAds = 0;

      // Clear persistent ad watches
      this.persistentAdWatches = [];
      this.savePersistentAdWatches();
      this.saveAdBasedAccessState();
    }

    this.saveFreeContentCounters();
    console.log(`PersistentAdStorage: Decremented premium content for ${category}, remaining: ${this.freeContentCounters.premiumContentRemaining}`);
  }

  /**
   * Get remaining free content for premium categories (shared counter)
   */
  getRemainingFreeContent(_category: 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares'): number {
    // All premium categories share the same counter
    return this.freeContentCounters.premiumContentRemaining;
  }

  /**
   * Check if user has exhausted their free content
   */
  hasFreeContentExhausted(): boolean {
    return this.freeContentCounters.premiumContentRemaining === 0;
  }

  /**
   * Reset ads watched count (used when user needs to watch ads again)
   */
  resetAdsWatchedCount(): void {
    this.adBasedAccessState.adsWatchedCount = 0;
    this.adBasedAccessState.hasWatchedAds = false;
    this.adBasedAccessState.lastAdWatchTime = null;
    this.adBasedAccessState.sessionStartTime = null;

    // Clear persistent ad watches
    this.persistentAdWatches = [];
    this.savePersistentAdWatches();
    this.saveAdBasedAccessState();

    console.log('PersistentAdStorage: Reset ads watched count and cleared persistent watches');
  }

  /**
   * Increment the count of questions/dares answered since last ad session
   */
  incrementQuestionsAnswered(): void {
    this.adBasedAccessState.questionsAnsweredSinceAds += 1;
    console.log(`SessionStorage: Questions answered since ads: ${this.adBasedAccessState.questionsAnsweredSinceAds}`);
  }

  /**
   * Reset questions answered count (used after watching ads again)
   */
  resetQuestionsAnsweredCount(): void {
    this.adBasedAccessState.questionsAnsweredSinceAds = 0;
    console.log('SessionStorage: Reset questions answered count');
  }

  /**
   * Check if user needs to watch ads again (configurable limit)
   */
  needsToWatchAdsAgain(): boolean {
    return this.adBasedAccessState.hasWatchedAds &&
           this.adBasedAccessState.questionsAnsweredSinceAds >= this.freeContentLimit;
  }

  /**
   * Complete ad watching cycle (reset questions count, keep access)
   */
  completeAdWatchingCycle(): void {
    this.adBasedAccessState.questionsAnsweredSinceAds = 0;
    this.adBasedAccessState.adsWatchedCount = this.requiredAdsCount; // Maintain full access
    this.adBasedAccessState.hasWatchedAds = true;
    this.adBasedAccessState.lastAdWatchTime = Date.now();
    console.log('SessionStorage: Completed ad watching cycle, reset questions count');
  }

  /**
   * Reset all session data (called when app starts or user loses access)
   */
  resetAllSessionData(): void {
    this.adBasedAccessState = {
      hasWatchedAds: false,
      adsWatchedCount: 0,
      questionsAnsweredSinceAds: 0,
      lastAdWatchTime: null,
      sessionStartTime: null,
    };

    // Clear persistent ad watches
    this.persistentAdWatches = [];

    // Also reset free content counters
    this.resetFreeContentCounters();

    // Save all changes
    this.saveAdBasedAccessState();
    this.savePersistentAdWatches();

    console.log('PersistentAdStorage: Reset all session data and cleared persistent watches');
  }

  /**
   * Get valid (non-expired) persistent ad watches
   */
  getValidPersistentAdWatches(): PersistentAdWatchState[] {
    const now = Date.now();
    return this.persistentAdWatches.filter(watch => watch.expiresAt > now);
  }

  /**
   * Check if there are any valid persistent ad watches
   */
  hasValidPersistentAdWatches(): boolean {
    return this.getValidPersistentAdWatches().length > 0;
  }

  /**
   * Manually cleanup expired ad watches (can be called periodically)
   */
  async cleanupExpiredAdWatchesManual(): Promise<number> {
    const initialCount = this.persistentAdWatches.length;
    await this.cleanupExpiredAdWatches();
    return initialCount - this.persistentAdWatches.length;
  }

  /**
   * Get debug information about current state
   */
  getDebugInfo(): string {
    const state = this.adBasedAccessState;
    const validWatches = this.getValidPersistentAdWatches();
    return `PersistentAdStorage Debug:
- Has watched ads: ${state.hasWatchedAds}
- Ads watched: ${state.adsWatchedCount}/${this.requiredAdsCount}
- Questions since ads: ${state.questionsAnsweredSinceAds}
- Last ad time: ${state.lastAdWatchTime ? new Date(state.lastAdWatchTime).toLocaleTimeString() : 'Never'}
- Session start: ${state.sessionStartTime ? new Date(state.sessionStartTime).toLocaleTimeString() : 'Never'}
- Has temp access: ${this.hasTemporaryPremiumAccess()}
- Needs ads again: ${this.needsToWatchAdsAgain()}
- Premium content remaining: ${this.freeContentCounters.premiumContentRemaining}
- Persistent watches: ${this.persistentAdWatches.length} total, ${validWatches.length} valid
- Session ID: ${this.currentSessionId}`;
  }
}

// Export singleton instance
export const sessionStorageService = new PersistentAdStorageService();
export default sessionStorageService;
