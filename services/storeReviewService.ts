import AsyncStorage from '@react-native-async-storage/async-storage';
import * as StoreReview from 'expo-store-review';
import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import * as Sentry from '@sentry/react-native';

// Storage keys for tracking store review state
const STORE_REVIEW_SUCCESS_KEY = 'storeReviewSuccess';
const STORE_REVIEW_LAST_ATTEMPT_KEY = 'storeReviewLastAttempt';
const STORE_REVIEW_FAILED_COUNT_KEY = 'storeReviewFailedCount';

// Constants
const RETRY_DELAY_DAYS = 5; // 5 days retry delay for failed attempts
const MILLISECONDS_PER_DAY = 24 * 60 * 60 * 1000;

export interface StoreReviewState {
  hasSucceeded: boolean;
  lastAttemptTimestamp: number | null;
  failedCount: number;
  canShowReview: boolean;
}

/**
 * Get the current store review state from AsyncStorage
 */
export const getStoreReviewState = async (): Promise<StoreReviewState> => {
  try {
    const [successStr, lastAttemptStr, failedCountStr] = await Promise.all([
      AsyncStorage.getItem(STORE_REVIEW_SUCCESS_KEY),
      AsyncStorage.getItem(STORE_REVIEW_LAST_ATTEMPT_KEY),
      AsyncStorage.getItem(STORE_REVIEW_FAILED_COUNT_KEY)
    ]);

    const hasSucceeded = successStr === 'true';
    const lastAttemptTimestamp = lastAttemptStr ? parseInt(lastAttemptStr, 10) : null;
    const failedCount = failedCountStr ? parseInt(failedCountStr, 10) : 0;

    // Calculate if we can show review based on state
    const canShowReview = calculateCanShowReview(hasSucceeded, lastAttemptTimestamp, failedCount);

    return {
      hasSucceeded,
      lastAttemptTimestamp,
      failedCount,
      canShowReview
    };
  } catch (error) {
    console.error('Error getting store review state:', error);
    Sentry.captureException(error);
    return {
      hasSucceeded: false,
      lastAttemptTimestamp: null,
      failedCount: 0,
      canShowReview: true
    };
  }
};

/**
 * Calculate if we can show the store review banner
 */
const calculateCanShowReview = (
  hasSucceeded: boolean,
  lastAttemptTimestamp: number | null,
  failedCount: number
): boolean => {
  // Never show again if user has successfully reviewed
  if (hasSucceeded) {
    return false;
  }

  // If no previous attempt, we can show
  if (!lastAttemptTimestamp) {
    return true;
  }

  // Check if enough time has passed since last failed attempt
  const now = Date.now();
  const timeSinceLastAttempt = now - lastAttemptTimestamp;
  const retryDelayMs = RETRY_DELAY_DAYS * MILLISECONDS_PER_DAY;

  return timeSinceLastAttempt >= retryDelayMs;
};

/**
 * Check if all conditions are met to show the store review banner
 * Note: Premium status is not required - both free and premium users can see the banner
 */
export const shouldShowStoreReviewBanner = async (isPremiumUnlocked: boolean): Promise<boolean> => {
  try {
    // Check network connectivity
    const networkState = await NetInfo.fetch();
    if (!networkState.isConnected) {
      console.log('No internet connection - not showing store review banner');
      return false;
    }

    // Check if platform is iOS
    if (Platform.OS !== 'ios') {
      console.log('Not iOS platform - not showing store review banner');
      return false;
    }

    // Note: Premium status is checked in TouchGameScreen for timing (2nd vs 6th question)
    // but both free and premium users can see the store review banner

    // Check store review state
    const reviewState = await getStoreReviewState();
    if (!reviewState.canShowReview) {
      console.log('Store review conditions not met - not showing banner');
      return false;
    }

    console.log(`All conditions met - can show store review banner (${isPremiumUnlocked ? 'premium' : 'free'} user)`);
    return true;
  } catch (error) {
    console.error('Error checking store review banner conditions:', error);
    Sentry.captureException(error);
    return false;
  }
};

/**
 * Handle store review banner press - request review from App Store
 */
export const handleStoreReviewRequest = async (): Promise<boolean> => {
  try {
    console.log('Requesting store review...');

    // Check if store review is available
    const isAvailable = await StoreReview.isAvailableAsync();
    if (!isAvailable) {
      console.log('Store review not available on this device');
      await recordReviewAttempt(false, 'NOT_AVAILABLE');
      return false;
    }

    // Request the review
    await StoreReview.requestReview();

    // Record successful attempt
    await recordReviewAttempt(true, 'SUCCESS');
    console.log('Store review requested successfully');
    return true;

  } catch (error) {
    console.error('Error requesting store review:', error);
    Sentry.captureException(error);

    // Check if it's the specific error we want to retry for
    const errorMessage = error instanceof Error ? error.message : String(error);
    const isRetryableError = errorMessage.includes('ERR_STORE_REVIEW_FAILED');

    await recordReviewAttempt(false, isRetryableError ? 'FAILED_RETRYABLE' : 'FAILED_PERMANENT');
    return false;
  }
};

/**
 * Record a review attempt in AsyncStorage
 */
const recordReviewAttempt = async (success: boolean, reason: string): Promise<void> => {
  try {
    const now = Date.now();

    if (success) {
      // Mark as successful - never show again
      await AsyncStorage.setItem(STORE_REVIEW_SUCCESS_KEY, 'true');
      await AsyncStorage.setItem(STORE_REVIEW_LAST_ATTEMPT_KEY, now.toString());
      console.log('Recorded successful store review attempt');
    } else {
      // Record failed attempt
      const currentFailedCount = await AsyncStorage.getItem(STORE_REVIEW_FAILED_COUNT_KEY);
      const newFailedCount = currentFailedCount ? parseInt(currentFailedCount, 10) + 1 : 1;

      await AsyncStorage.setItem(STORE_REVIEW_LAST_ATTEMPT_KEY, now.toString());
      await AsyncStorage.setItem(STORE_REVIEW_FAILED_COUNT_KEY, newFailedCount.toString());

      console.log(`Recorded failed store review attempt (${reason}). Total failed attempts: ${newFailedCount}`);
    }
  } catch (error) {
    console.error('Error recording review attempt:', error);
    Sentry.captureException(error);
  }
};

/**
 * Reset store review state (for testing purposes)
 */
export const resetStoreReviewState = async (): Promise<void> => {
  try {
    await Promise.all([
      AsyncStorage.removeItem(STORE_REVIEW_SUCCESS_KEY),
      AsyncStorage.removeItem(STORE_REVIEW_LAST_ATTEMPT_KEY),
      AsyncStorage.removeItem(STORE_REVIEW_FAILED_COUNT_KEY)
    ]);
    console.log('Store review state reset');
  } catch (error) {
    console.error('Error resetting store review state:', error);
    Sentry.captureException(error);
  }
};