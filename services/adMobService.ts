import { Platform } from 'react-native';
import { RewardedAd, AdEventType, TestIds, RewardedAdEventType } from 'react-native-google-mobile-ads';
import NetInfo from '@react-native-community/netinfo';
import Constants from 'expo-constants';
import * as Sentry from '@sentry/react-native';

// Get AdMob configuration from environment
const ADMOB_REWARDED_AD_UNIT_ID_ANDROID = Constants.expoConfig?.extra?.admobRewardedAdUnitIdAndroid || '';
const ADMOB_REWARDED_AD_UNIT_ID_IOS = Constants.expoConfig?.extra?.admobRewardedAdUnitIdIos || '';

// Use test ad unit IDs in development
const getAdUnitId = () => {
  if (__DEV__) {
    return TestIds.REWARDED;
  }

  return Platform.OS === 'ios'
    ? ADMOB_REWARDED_AD_UNIT_ID_IOS
    : ADMOB_REWARDED_AD_UNIT_ID_ANDROID;
};

export interface AdMobServiceInterface {
  isInternetAvailable(): Promise<boolean>;
  loadInterstitialAd(): Promise<RewardedAd>;
  showInterstitialAd(ad: RewardedAd): Promise<boolean>;
  preloadAds(): Promise<RewardedAd[]>;
}

class AdMobService implements AdMobServiceInterface {
  private preloadedAds: RewardedAd[] = [];
  private readonly maxPreloadedAds = 5;
  private readonly AD_TIMEOUT_MS = 20000; // 20 seconds timeout for stuck ads

  /**
   * Check if internet connection is available
   */
  async isInternetAvailable(): Promise<boolean> {
    try {
      const networkState = await NetInfo.fetch();
      return networkState.isConnected === true;
    } catch (error) {
      console.error('Error checking internet connectivity:', error);
      Sentry.captureException(error);
      return false;
    }
  }

  /**
   * Load a single rewarded ad
   */
  async loadInterstitialAd(): Promise<RewardedAd> {
    return new Promise((resolve, reject) => {
      const adUnitId = getAdUnitId();
      const rewarded = RewardedAd.createForAdRequest(adUnitId);

      const unsubscribeLoaded = rewarded.addAdEventListener(RewardedAdEventType.LOADED, () => {
        console.log('AdMob: Rewarded ad loaded successfully');
        unsubscribeLoaded();
        unsubscribeError();
        resolve(rewarded);
      });

      const unsubscribeError = rewarded.addAdEventListener(AdEventType.ERROR, (error: any) => {
        console.error('AdMob: Failed to load rewarded ad:', error);
        unsubscribeLoaded();
        unsubscribeError();
        reject(new Error(`Failed to load ad: ${error.message}`));
      });

      // Start loading the ad
      rewarded.load();
    });
  }

  /**
   * Show a rewarded ad and return whether it was successfully watched
   *
   * ENHANCED WITH DUAL-TIMEOUT SYSTEM FOR CLOSE BUTTON ISSUES:
   *
   * Problem: iOS (and potentially Android) rewarded ads sometimes have unresponsive close buttons.
   * This causes two issues:
   * 1. The ad modal stays open indefinitely (user can't continue)
   * 2. The AdEventType.CLOSED event never fires (Promise never resolves)
   *
   * Solution: Two-tier timeout system:
   *
   * TIER 1 - Ad Marking (2 seconds):
   * - Automatically marks ad as "watched" after 2 seconds
   * - Ensures user gets credit even if close button fails
   * - Applied to both iOS and Android for consistency
   *
   * TIER 2 - Promise Resolution (20 seconds):
   * - Forces Promise resolution if CLOSED event never fires
   * - Prevents app from hanging indefinitely
   * - Returns the current value of adWatched (true if Tier 1 fired)
   *
   * Why both are needed:
   * - Without Tier 1: Promise resolves with false (no credit)
   * - Without Tier 2: Promise never resolves (app hangs)
   * - Together: User gets credit AND app doesn't hang
   *
   * @param ad - The loaded RewardedAd instance
   * @returns Promise<boolean> - true if ad was watched, false otherwise
   */
  async showInterstitialAd(ad: RewardedAd): Promise<boolean> {
    return new Promise((resolve) => {
      let adWatched = false;
      let resolved = false; // Prevent multiple resolutions

      const cleanup = () => {
        unsubscribeClosed();
        unsubscribeError();
        unsubscribeRewarded();
      };

      const forceResolve = (result: boolean) => {
        if (!resolved) {
          resolved = true;
          cleanup();
          resolve(result);
        }
      };

      const unsubscribeClosed = ad.addAdEventListener(AdEventType.CLOSED, () => {
        console.log('AdMob: Rewarded ad closed normally');
        forceResolve(adWatched);
      });

      const unsubscribeError = ad.addAdEventListener(AdEventType.ERROR, (error: any) => {
        console.error('AdMob: Error showing rewarded ad:', error);
        forceResolve(false);
      });

      // For rewarded ads, we need to listen for the reward event
      const unsubscribeRewarded = ad.addAdEventListener(RewardedAdEventType.EARNED_REWARD, (reward: any) => {
        console.log('AdMob: User earned reward:', reward);
        adWatched = true;
      });

      // TIER 1 TIMEOUT (2 seconds): Auto-mark as watched
      // Purpose: Handle close button failures by giving user credit
      // Originally iOS-only, now applied to both platforms for consistency
      // This ensures adWatched = true before Tier 2 timeout fires
      // Without this: Tier 2 would resolve with false (no credit for user)
      setTimeout(() => {
        console.log('AdMob: Auto-marking ad as watched after 2 seconds (close button workaround)');
        adWatched = true;
      }, 2000);

      // TIER 2 TIMEOUT (15 seconds): Force Promise resolution
      // Purpose: Prevent app from hanging when CLOSED event never fires
      // Uses current value of adWatched (should be true if Tier 1 fired)
      // This resolves the Promise but does NOT close the ad modal
      // Without this: Promise never resolves, app hangs indefinitely
      setTimeout(() => {
        console.log(`AdMob: Force resolving after ${this.AD_TIMEOUT_MS}ms timeout - ad may be stuck`);
        console.log(`AdMob: Resolving with adWatched = ${adWatched}`);
        forceResolve(adWatched);
      }, this.AD_TIMEOUT_MS);

      // Show the ad
      ad.show();
    });
  }

  /**
   * Preload multiple ads for better user experience
   */
  async preloadAds(): Promise<RewardedAd[]> {
    const isOnline = await this.isInternetAvailable();
    if (!isOnline) {
      console.log('AdMob: No internet connection, cannot preload ads');
      return [];
    }

    const loadPromises: Promise<RewardedAd | null>[] = [];

    for (let i = 0; i < this.maxPreloadedAds; i++) {
      loadPromises.push(
        this.loadInterstitialAd().catch(error => {
          console.error(`AdMob: Failed to preload ad ${i + 1}:`, error);
          Sentry.captureException(error);
          return null;
        })
      );
    }

    const results = await Promise.all(loadPromises);
    this.preloadedAds = results.filter((ad: any) => ad !== null) as RewardedAd[];

    console.log(`AdMob: Successfully preloaded ${this.preloadedAds.length} ads`);
    return this.preloadedAds;
  }

  /**
   * Get a preloaded ad or load a new one if none available
   */
  async getAd(): Promise<RewardedAd | null> {
    const isOnline = await this.isInternetAvailable();
    if (!isOnline) {
      console.log('AdMob: No internet connection, cannot get ad');
      return null;
    }

    // Try to use a preloaded ad first
    if (this.preloadedAds.length > 0) {
      const ad = this.preloadedAds.shift();
      console.log(`AdMob: Using preloaded ad, ${this.preloadedAds.length} remaining`);

      // Preload a replacement ad in the background
      this.loadInterstitialAd()
        .then(newAd => {
          this.preloadedAds.push(newAd);
          console.log('AdMob: Replacement ad preloaded');
        })
        .catch(error => {
          console.error('AdMob: Failed to preload replacement ad:', error);
          Sentry.captureException(error);
        });

      return ad!;
    }

    // No preloaded ads available, load one now
    try {
      console.log('AdMob: No preloaded ads available, loading new ad');
      return await this.loadInterstitialAd();
    } catch (error) {
      console.error('AdMob: Failed to load ad on demand:', error);
      Sentry.captureException(error);
      return null;
    }
  }

  /**
   * Clear all preloaded ads (useful when app goes to background)
   */
  clearPreloadedAds(): void {
    this.preloadedAds = [];
    console.log('AdMob: Cleared all preloaded ads');
  }
}

// Export singleton instance
export const adMobService = new AdMobService();
export default adMobService;
