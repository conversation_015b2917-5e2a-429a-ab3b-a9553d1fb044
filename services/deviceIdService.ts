/**
 * Device ID Service
 * 
 * Generates and manages unique device identifiers using expo-secure-store and uuid.
 * This replaces the unreliable Device.osInternalBuildId approach.
 */

import * as SecureStore from 'expo-secure-store';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';

// Storage key for the unique device ID
const DEVICE_ID_KEY = 'taptrap_device_unique_id';

/**
 * Get or generate a unique device ID
 * 
 * This function will:
 * 1. Check if a unique ID already exists in SecureStore
 * 2. If it exists, return it
 * 3. If it doesn't exist, generate a new UUID and store it
 * 4. Return the unique ID
 * 
 * @returns {Promise<string>} The unique device ID
 */
export async function getUniqueDeviceId(): Promise<string> {
  try {
    // Try to get existing unique ID from SecureStore
    let uniqueId = await SecureStore.getItemAsync(DEVICE_ID_KEY);
    
    if (!uniqueId) {
      // Generate a new UUID if no ID exists
      uniqueId = uuidv4();
      
      // Store the new ID in SecureStore
      await SecureStore.setItemAsync(DEVICE_ID_KEY, uniqueId);
      
      console.log('Generated new unique device ID:', uniqueId);
    } else {
      console.log('Using existing unique device ID:', uniqueId);
    }
    
    return uniqueId;
  } catch (error) {
    console.error('Error getting/generating unique device ID:', error);
    
    // Fallback: generate a temporary UUID for this session
    // This won't persist across app restarts, but it's better than using osInternalBuildId
    const fallbackId = uuidv4();
    console.warn('Using fallback device ID (not persistent):', fallbackId);
    return fallbackId;
  }
}

/**
 * Clear the stored device ID (useful for testing or reset scenarios)
 * 
 * @returns {Promise<boolean>} True if successfully cleared, false otherwise
 */
export async function clearDeviceId(): Promise<boolean> {
  try {
    await SecureStore.deleteItemAsync(DEVICE_ID_KEY);
    console.log('Device ID cleared successfully');
    return true;
  } catch (error) {
    console.error('Error clearing device ID:', error);
    return false;
  }
}

/**
 * Check if a device ID exists in storage
 * 
 * @returns {Promise<boolean>} True if device ID exists, false otherwise
 */
export async function hasDeviceId(): Promise<boolean> {
  try {
    const uniqueId = await SecureStore.getItemAsync(DEVICE_ID_KEY);
    return uniqueId !== null;
  } catch (error) {
    console.error('Error checking device ID existence:', error);
    return false;
  }
}
