export const ENTITLEMENT_ID = 'Premium Packs';
import Purchases, { PurchasesPackage, PurchasesOffering } from 'react-native-purchases';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import Constants from 'expo-constants';
import * as Sentry from '@sentry/react-native';

// Access the RevenueCat keys from EAS environment variables
const REVENUECAT_IOS_KEY = Constants.expoConfig?.extra?.revenueCatIosKey || '';
const REVENUECAT_ANDROID_KEY = Constants.expoConfig?.extra?.revenueCatAndroidKey || '';

// Add validation to ensure the keys are available
if (!REVENUECAT_IOS_KEY || !REVENUECAT_ANDROID_KEY) {
  console.error('RevenueCat API keys not found in EAS configuration');
}

// Function to get the RevenueCat user ID
export const getUserID = async (): Promise<string> => {
  try {
    return await Purchases.getAppUserID();
  } catch (error) {
    console.error('Error getting RevenueCat user ID:', error);
    Sentry.captureException(error);
    return 'Not available';
  }
};

// SDK initialization function
export const initRevenueCat = () => {
  const apiKey = Platform.OS === 'ios'
    ? REVENUECAT_IOS_KEY
    : REVENUECAT_ANDROID_KEY;
  Purchases.configure({ apiKey });
};

// Function to fetch available offerings
export const getOfferings = async (): Promise<PurchasesOffering | null> => {
  try {
    const offerings = await Purchases.getOfferings();
    if (offerings.current) {
      return offerings.current;
    }
    return null;
  } catch (e) {
    console.error('Failed to get offerings', e);
    Sentry.captureException(e);
    return null;
  }
};

// Function to handle the purchase flow
export const unlockPremium = async (packageToPurchase: PurchasesPackage): Promise<boolean> => {
  try {
    const { customerInfo } = await Purchases.purchasePackage(packageToPurchase);
    const entitlement = customerInfo.entitlements.active[ENTITLEMENT_ID];
    return entitlement?.isActive ?? false;
  } catch (e) {
    console.warn('Purchase failed or was cancelled', e);
    return false;
  }
};

// Function to check if the user already has premium access
export const checkIfUserHasAccess = async (): Promise<boolean> => {
  try {
    // First check network connectivity
    const networkState = await NetInfo.fetch();

    // If we're offline, use the cached value from AsyncStorage
    if (!networkState.isConnected) {
      console.log('No internet connection, using cached premium status');
      const cachedStatus = await AsyncStorage.getItem('premiumUnlocked');
      return cachedStatus === 'true';
    }

    // We're online, so check with RevenueCat
    try {
      // always fetch fresh entitlements
      await Purchases.invalidateCustomerInfoCache();
      const customerInfo = await Purchases.getCustomerInfo();
      const entitlement = customerInfo.entitlements.active[ENTITLEMENT_ID];
      const isPremium = entitlement?.isActive ?? false;

      // Cache the result for offline use
      await AsyncStorage.setItem('premiumUnlocked', isPremium ? 'true' : 'false');

      return isPremium;
    } catch (e) {
      console.error('Error checking premium access with RevenueCat', e);
      Sentry.captureException(e);

      // If RevenueCat check fails, fall back to cached value
      const cachedStatus = await AsyncStorage.getItem('premiumUnlocked');
      return cachedStatus === 'true';
    }
  } catch (e) {
    console.error('Error in checkIfUserHasAccess', e);
    Sentry.captureException(e);

    // Last resort - try to read from AsyncStorage
    try {
      const cachedStatus = await AsyncStorage.getItem('premiumUnlocked');
      return cachedStatus === 'true';
    } catch {
      // If everything fails, default to not premium
      return false;
    }
  }
};