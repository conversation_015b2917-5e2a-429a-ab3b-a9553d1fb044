import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { sessionStorageService, AdBasedAccessState, FreeContentCounters } from '@/services/sessionStorageService';
import { adMobService } from '@/services/adMobService';

interface AdBasedAccessContextProps {
  // State
  adBasedAccessState: AdBasedAccessState;
  freeContentCounters: FreeContentCounters;
  hasTemporaryPremiumAccess: boolean;
  needsToWatchAdsAgain: boolean;
  isInternetAvailable: boolean;

  // Actions
  incrementAdsWatched: () => void;
  resetAdsWatchedCount: () => void;
  incrementQuestionsAnswered: () => void;
  resetQuestionsAnsweredCount: () => void;
  completeAdWatchingCycle: () => void;
  resetAllSessionData: () => void;
  checkInternetConnectivity: () => Promise<void>;

  // Free content actions
  decrementFreeContent: (category: 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares') => void;
  getRemainingFreeContent: (category: 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares') => number;
  hasFreeContentExhausted: () => boolean;

  // Debug
  getDebugInfo: () => string;
}

const AdBasedAccessContext = createContext<AdBasedAccessContextProps>({
  // Default values
  adBasedAccessState: {
    hasWatchedAds: false,
    adsWatchedCount: 0,
    questionsAnsweredSinceAds: 0,
    lastAdWatchTime: null,
    sessionStartTime: null,
  },
  freeContentCounters: {
    premiumContentRemaining: 6,
    lastResetTime: null,
  },
  hasTemporaryPremiumAccess: false,
  needsToWatchAdsAgain: false,
  isInternetAvailable: true,

  // Default functions
  incrementAdsWatched: () => {},
  resetAdsWatchedCount: () => {},
  incrementQuestionsAnswered: () => {},
  resetQuestionsAnsweredCount: () => {},
  completeAdWatchingCycle: () => {},
  resetAllSessionData: () => {},
  checkInternetConnectivity: async () => {},
  decrementFreeContent: () => {},
  getRemainingFreeContent: () => 0,
  hasFreeContentExhausted: () => false,
  getDebugInfo: () => '',
});

export const AdBasedAccessProvider = ({ children }: { children: ReactNode }) => {
  const [adBasedAccessState, setAdBasedAccessState] = useState<AdBasedAccessState>(
    sessionStorageService.getAdBasedAccessState()
  );
  const [freeContentCounters, setFreeContentCounters] = useState<FreeContentCounters>(
    sessionStorageService.getFreeContentCounters()
  );
  const [isInternetAvailable, setIsInternetAvailable] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize the service on mount
  useEffect(() => {
    const initializeService = async () => {
      try {
        await sessionStorageService.initialize();
        // Update state after initialization
        setAdBasedAccessState(sessionStorageService.getAdBasedAccessState());
        setFreeContentCounters(sessionStorageService.getFreeContentCounters());
        setIsInitialized(true);
        console.log('AdBasedAccessContext: Service initialized successfully');
      } catch (error) {
        console.error('AdBasedAccessContext: Error initializing service:', error);
        setIsInitialized(true); // Continue with default values
      }
    };

    initializeService();
  }, []);

  // Derived state (recalculated on every render to ensure updates)
  const hasTemporaryPremiumAccess = sessionStorageService.hasTemporaryPremiumAccess();
  const needsToWatchAdsAgain = sessionStorageService.needsToWatchAdsAgain();

  // Check internet connectivity
  const checkInternetConnectivity = async () => {
    const isOnline = await adMobService.isInternetAvailable();
    setIsInternetAvailable(isOnline);
  };

  // Update local state when session storage changes
  const updateState = () => {
    setAdBasedAccessState(sessionStorageService.getAdBasedAccessState());
    setFreeContentCounters(sessionStorageService.getFreeContentCounters());
  };

  // Action functions
  const incrementAdsWatched = () => {
    sessionStorageService.incrementAdsWatched();
    updateState();
  };

  const resetAdsWatchedCount = () => {
    sessionStorageService.resetAdsWatchedCount();
    updateState();
  };

  const incrementQuestionsAnswered = () => {
    sessionStorageService.incrementQuestionsAnswered();
    updateState();
  };

  const resetQuestionsAnsweredCount = () => {
    sessionStorageService.resetQuestionsAnsweredCount();
    updateState();
  };

  const completeAdWatchingCycle = () => {
    sessionStorageService.completeAdWatchingCycle();
    updateState();
  };

  const resetAllSessionData = () => {
    sessionStorageService.resetAllSessionData();
    updateState();
  };

  const getDebugInfo = () => {
    return sessionStorageService.getDebugInfo();
  };

  // Free content methods
  const decrementFreeContent = (category: 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares') => {
    sessionStorageService.decrementFreeContent(category);
    updateState();
  };

  const getRemainingFreeContent = (category: 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares') => {
    return sessionStorageService.getRemainingFreeContent(category);
  };

  const hasFreeContentExhausted = () => {
    return sessionStorageService.hasFreeContentExhausted();
  };

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        // Clear preloaded ads when app goes to background
        adMobService.clearPreloadedAds();
        console.log('AdBasedAccess: App went to background, cleared preloaded ads');
      } else if (nextAppState === 'active') {
        // Check internet connectivity when app becomes active
        checkInternetConnectivity();
        console.log('AdBasedAccess: App became active, checking connectivity');
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Initial connectivity check
    checkInternetConnectivity();

    return () => subscription.remove();
  }, []);

  // Preload ads when user has temporary access (for better UX)
  useEffect(() => {
    if (hasTemporaryPremiumAccess && isInternetAvailable) {
      // Preload ads in the background for when user needs them again
      adMobService.preloadAds().catch(error => {
        console.error('AdBasedAccess: Failed to preload ads:', error);
      });
    }
  }, [hasTemporaryPremiumAccess, isInternetAvailable]);

  const contextValue: AdBasedAccessContextProps = {
    // State
    adBasedAccessState,
    freeContentCounters,
    hasTemporaryPremiumAccess,
    needsToWatchAdsAgain,
    isInternetAvailable,

    // Actions
    incrementAdsWatched,
    resetAdsWatchedCount,
    incrementQuestionsAnswered,
    resetQuestionsAnsweredCount,
    completeAdWatchingCycle,
    resetAllSessionData,
    checkInternetConnectivity,

    // Free content actions
    decrementFreeContent,
    getRemainingFreeContent,
    hasFreeContentExhausted,

    // Debug
    getDebugInfo,
  };

  return (
    <AdBasedAccessContext.Provider value={contextValue}>
      {children}
    </AdBasedAccessContext.Provider>
  );
};

export const useAdBasedAccess = () => {
  const context = useContext(AdBasedAccessContext);
  if (!context) {
    throw new Error('useAdBasedAccess must be used within an AdBasedAccessProvider');
  }
  return context;
};

export default AdBasedAccessContext;
