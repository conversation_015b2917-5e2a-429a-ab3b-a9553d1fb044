{"name": "taptrap", "main": "node_modules/expo/AppEntry.js", "version": "1.0.15", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "run-iphone": "npx expo run:ios --device 00008110-000918462103801E", "prebuild": "npx expo prebuild && open ios/*.xcworkspace", "eas-local-android": "eas build --local --platform android --profile development", "eas-local-ios": "eas build --local --platform ios --profile development", "eas-prev": "eas build --platform ios --profile preview", "eas-dev": "eas build --platform ios --profile development", "eas-prod-ios": "eas build --platform ios --profile production", "eas-prod-android": "eas build --platform android --profile production", "testflight": "eas submit --platform ios"}, "jest": {"preset": "jest-expo"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-switch"]}}}, "dependencies": {"@expo/server": "^0.5.1", "@expo/vector-icons": "^14.0.2", "@lottiefiles/dotlottie-react": "^0.6.5", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.0.0", "@react-navigation/native": "^7.0.0", "@react-navigation/stack": "^7.2.10", "@sentry/react-native": "^6.15.0", "axios": "^1.8.1", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "expo": "~52.0.46", "expo-application": "~6.0.2", "expo-av": "~15.0.2", "expo-blur": "~14.0.3", "expo-constants": "~17.0.7", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-file-system": "~18.0.11", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-intent-launcher": "~12.0.2", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-localization": "16.0.1", "expo-notifications": "~0.29.11", "expo-router": "~4.0.21", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-store-review": "~8.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "lottie-react-native": "7.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-mobile-ads": "^14.7.0", "react-native-purchases": "^8.10.0", "react-native-purchases-ui": "^8.10.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-switch": "^1.5.1", "react-native-toast-message": "^2.2.1", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "@types/uuid": "^10.0.0", "babel-plugin-module-resolver": "^5.0.2", "eas-cli": "^16.4.1", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}