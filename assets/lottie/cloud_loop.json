{"v": "5.9.0", "fr": 29.9700012207031, "ip": 0, "op": 299.00001217852, "w": 1000, "h": 536, "nm": "NUBE COMPLETA", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Capa 1 Outlines 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [415.966, 310.977, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.459, 310.733, 0], "t": 1, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.923, 310.507, 0], "t": 2, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.343, 310.305, 0], "t": 3, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.706, 310.132, 0], "t": 4, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.999, 309.995, 0], "t": 5, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.132, 310.254, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.95, 310.555, 0], "t": 16, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.84, 310.734, 0], "t": 17, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.719, 310.931, 0], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.587, 311.144, 0], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.446, 311.371, 0], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.297, 311.612, 0], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.141, 311.865, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.98, 312.127, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.814, 312.397, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.646, 312.673, 0], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.477, 312.952, 0], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.309, 313.231, 0], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.144, 313.507, 0], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.983, 313.779, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.829, 314.042, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.684, 314.294, 0], "t": 31, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.55, 314.531, 0], "t": 32, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.43, 314.749, 0], "t": 33, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.325, 314.945, 0], "t": 34, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.172, 315.255, 0], "t": 36, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.128, 315.53, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.38, 315.818, 0], "t": 43, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.695, 315.889, 0], "t": 46, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.034, 315.772, 0], "t": 49, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.245, 315.579, 0], "t": 51, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.426, 315.295, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.501, 315.121, 0], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.565, 314.928, 0], "t": 55, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.616, 314.717, 0], "t": 56, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.654, 314.491, 0], "t": 57, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.678, 314.253, 0], "t": 58, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.686, 314.006, 0], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.68, 313.754, 0], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.658, 313.499, 0], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.621, 313.248, 0], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.568, 313.004, 0], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.5, 312.772, 0], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.417, 312.559, 0], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.321, 312.369, 0], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.099, 312.093, 0], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.762, 312.35, 0], "t": 73, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.741, 312.575, 0], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.736, 312.857, 0], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.746, 313.195, 0], "t": 76, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.77, 313.586, 0], "t": 77, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.809, 314.029, 0], "t": 78, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.861, 314.52, 0], "t": 79, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.925, 315.055, 0], "t": 80, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.001, 315.631, 0], "t": 81, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.086, 316.242, 0], "t": 82, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.18, 316.885, 0], "t": 83, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.281, 317.552, 0], "t": 84, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.388, 318.238, 0], "t": 85, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.498, 318.935, 0], "t": 86, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.61, 319.637, 0], "t": 87, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.72, 320.336, 0], "t": 88, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.829, 321.023, 0], "t": 89, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.931, 321.689, 0], "t": 90, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.026, 322.325, 0], "t": 91, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.11, 322.92, 0], "t": 92, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.18, 323.465, 0], "t": 93, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.234, 323.947, 0], "t": 94, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.268, 324.356, 0], "t": 95, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.278, 324.679, 0], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.262, 324.904, 0], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.291, 324.763, 0], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.339, 324.506, 0], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.397, 324.176, 0], "t": 103, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.463, 323.782, 0], "t": 104, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.535, 323.332, 0], "t": 105, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.611, 322.834, 0], "t": 106, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.689, 322.296, 0], "t": 107, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.767, 321.725, 0], "t": 108, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.843, 321.13, 0], "t": 109, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.916, 320.518, 0], "t": 110, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.983, 319.895, 0], "t": 111, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.045, 319.271, 0], "t": 112, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.099, 318.652, 0], "t": 113, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.144, 318.045, 0], "t": 114, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.18, 317.457, 0], "t": 115, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.205, 316.896, 0], "t": 116, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.22, 316.368, 0], "t": 117, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.223, 315.88, 0], "t": 118, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.214, 315.438, 0], "t": 119, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.193, 315.051, 0], "t": 120, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.159, 314.723, 0], "t": 121, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [418.113, 314.461, 0], "t": 122, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.985, 314.163, 0], "t": 124, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.707, 314.368, 0], "t": 127, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.547, 314.572, 0], "t": 128, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.282, 314.748, 0], "t": 129, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.928, 314.898, 0], "t": 130, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.497, 315.027, 0], "t": 131, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.002, 315.135, 0], "t": 132, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.456, 315.226, 0], "t": 133, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.871, 315.303, 0], "t": 134, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.256, 315.366, 0], "t": 135, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.622, 315.42, 0], "t": 136, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.979, 315.466, 0], "t": 137, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.336, 315.505, 0], "t": 138, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.701, 315.542, 0], "t": 139, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.082, 315.576, 0], "t": 140, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [410.486, 315.611, 0], "t": 141, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [409.919, 315.648, 0], "t": 142, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [409.388, 315.69, 0], "t": 143, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [408.897, 315.736, 0], "t": 144, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [408.451, 315.791, 0], "t": 145, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [408.054, 315.854, 0], "t": 146, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.71, 315.928, 0], "t": 147, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.42, 316.014, 0], "t": 148, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.013, 316.227, 0], "t": 150, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [406.842, 316.503, 0], "t": 152, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [406.845, 316.668, 0], "t": 153, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [406.906, 316.852, 0], "t": 154, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.023, 317.057, 0], "t": 155, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.194, 317.282, 0], "t": 156, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.415, 317.529, 0], "t": 157, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.681, 317.748, 0], "t": 158, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [407.982, 317.889, 0], "t": 159, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [408.311, 317.953, 0], "t": 160, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [408.66, 317.944, 0], "t": 161, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [409.023, 317.862, 0], "t": 162, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [409.393, 317.712, 0], "t": 163, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [409.766, 317.495, 0], "t": 164, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [410.134, 317.216, 0], "t": 165, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [410.495, 316.879, 0], "t": 166, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [410.842, 316.487, 0], "t": 167, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.174, 316.046, 0], "t": 168, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.486, 315.561, 0], "t": 169, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.776, 315.036, 0], "t": 170, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.041, 314.478, 0], "t": 171, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.279, 313.893, 0], "t": 172, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.49, 313.287, 0], "t": 173, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.673, 312.668, 0], "t": 174, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.826, 312.041, 0], "t": 175, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.951, 311.416, 0], "t": 176, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.048, 310.8, 0], "t": 177, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.118, 310.202, 0], "t": 178, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.164, 309.629, 0], "t": 179, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.186, 309.092, 0], "t": 180, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.188, 308.599, 0], "t": 181, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.173, 308.161, 0], "t": 182, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.144, 307.788, 0], "t": 183, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.106, 307.49, 0], "t": 184, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.063, 307.278, 0], "t": 185, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.969, 307.306, 0], "t": 188, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.982, 307.62, 0], "t": 189, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.019, 308.076, 0], "t": 190, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.08, 308.653, 0], "t": 191, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.161, 309.328, 0], "t": 192, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.259, 310.081, 0], "t": 193, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.372, 310.892, 0], "t": 194, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.498, 311.743, 0], "t": 195, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.634, 312.618, 0], "t": 196, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.779, 313.499, 0], "t": 197, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.929, 314.372, 0], "t": 198, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.084, 315.224, 0], "t": 199, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.24, 316.042, 0], "t": 200, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.396, 316.814, 0], "t": 201, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.549, 317.531, 0], "t": 202, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.699, 318.183, 0], "t": 203, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.843, 318.762, 0], "t": 204, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.98, 319.262, 0], "t": 205, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.108, 319.677, 0], "t": 206, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.225, 320.002, 0], "t": 207, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.329, 320.235, 0], "t": 208, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.625, 319.968, 0], "t": 213, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.629, 319.638, 0], "t": 214, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.613, 319.224, 0], "t": 215, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.576, 318.731, 0], "t": 216, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.516, 318.167, 0], "t": 217, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.426, 317.561, 0], "t": 218, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.306, 316.939, 0], "t": 219, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.163, 316.31, 0], "t": 220, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.006, 315.684, 0], "t": 221, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.84, 315.069, 0], "t": 222, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.671, 314.471, 0], "t": 223, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.505, 313.899, 0], "t": 224, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.348, 313.356, 0], "t": 225, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.203, 312.849, 0], "t": 226, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.075, 312.38, 0], "t": 227, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.968, 311.954, 0], "t": 228, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.883, 311.573, 0], "t": 229, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.824, 311.237, 0], "t": 230, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.792, 310.949, 0], "t": 231, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.789, 310.707, 0], "t": 232, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.815, 310.512, 0], "t": 233, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.958, 310.25, 0], "t": 235, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.215, 310.141, 0], "t": 237, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.576, 310.147, 0], "t": 239, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.788, 310.178, 0], "t": 240, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.018, 310.219, 0], "t": 241, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.261, 310.26, 0], "t": 242, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.514, 310.293, 0], "t": 243, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.77, 310.307, 0], "t": 244, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.024, 310.293, 0], "t": 245, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.271, 310.238, 0], "t": 246, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.504, 310.131, 0], "t": 247, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.864, 310.006, 0], "t": 249, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.083, 310.269, 0], "t": 252, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.07, 310.463, 0], "t": 253, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [417.015, 310.704, 0], "t": 254, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.919, 310.988, 0], "t": 255, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.783, 311.312, 0], "t": 256, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.61, 311.67, 0], "t": 257, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.4, 312.059, 0], "t": 258, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [416.158, 312.475, 0], "t": 259, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.885, 312.912, 0], "t": 260, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.586, 313.367, 0], "t": 261, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [415.262, 313.834, 0], "t": 262, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.92, 314.308, 0], "t": 263, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.561, 314.784, 0], "t": 264, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [414.193, 315.258, 0], "t": 265, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.818, 315.723, 0], "t": 266, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.443, 316.174, 0], "t": 267, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [413.074, 316.606, 0], "t": 268, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.716, 317.012, 0], "t": 269, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.376, 317.386, 0], "t": 270, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.061, 317.723, 0], "t": 271, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.778, 318.017, 0], "t": 272, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.533, 318.26, 0], "t": 273, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.195, 318.571, 0], "t": 275, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.228, 318.309, 0], "t": 279, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.318, 318.052, 0], "t": 280, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.421, 317.73, 0], "t": 281, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.531, 317.351, 0], "t": 282, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.642, 316.919, 0], "t": 283, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.749, 316.443, 0], "t": 284, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.847, 315.928, 0], "t": 285, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.933, 315.382, 0], "t": 286, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.003, 314.809, 0], "t": 287, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.054, 314.216, 0], "t": 288, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.083, 313.608, 0], "t": 289, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.088, 312.993, 0], "t": 290, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.068, 312.374, 0], "t": 291, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [412.021, 311.758, 0], "t": 292, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.947, 311.15, 0], "t": 293, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.845, 310.556, 0], "t": 294, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.716, 309.979, 0], "t": 295, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.56, 309.426, 0], "t": 296, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.379, 308.901, 0], "t": 297, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [411.175, 308.409, 0], "t": 298, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [410.95, 307.954, 0], "t": 299, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [194, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [262, 262, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"t": 0, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 10.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 19, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 29, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 39.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 48, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 57, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 67.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 76, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 88, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 98.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 107, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 117, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 127.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 136, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 145, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 155.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 164, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 176, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 186.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 195, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 205, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 215.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 224, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 233, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 243.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 252, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 265, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 275.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 284, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 294, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 304.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 313, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}, {"t": 322, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [-0.924, 2.853], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-1.368, -13.767], [0.962, -12.522], [3.327, -7.956], [3.972, -6.346], [4.446, -5.128], [6.37, -5.054], [8.861, -4.941], [10.13, -4.895], [11.344, -4.836], [12.465, -4.789], [14.395, -3.636], [13.897, -0.653], [7.431, 3.822], [7.866, 5.258], [8.425, 7.147], [8.985, 9.018], [9.42, 13.269], [5.938, 13.767], [2.891, 11.529], [-1.337, 9.307], [-4.446, 11.28], [-9.98, 13.269], [-11.015, 11.374], [-8.985, 2.828], [-14.457, -1.15], [-13.462, -4.631], [-9.202, -4.662], [-7.987, -4.655], [-5.005, -4.631], [-4.803, -6.466], [-3.762, -11.063], [-3.373, -12.196]], "c": true}], "h": 1}, {"t": 332.555, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.101], [0.962, -12.522], [3.088, -7.956], [4.211, -6.346], [4.446, -5.128], [6.322, -4.768], [8.861, -5.084], [10.13, -4.895], [11.105, -4.931], [12.417, -4.55], [14.443, -3.063], [13.849, -0.558], [7.813, 3.87], [7.723, 5.544], [8.186, 7.386], [8.603, 9.4], [9.181, 13.126], [5.843, 13.528], [2.891, 11.529], [-1.146, 9.593], [-4.637, 10.946], [-9.837, 13.078], [-11.54, 11.326], [-8.985, 2.828], [-15.173, -1.293], [-13.462, -4.631], [-9.775, -4.996], [-7.748, -4.512], [-5.434, -4.631], [-4.803, -6.466], [-3.523, -10.968], [-3.039, -12.148]], "c": true}], "h": 1}, {"t": 341.000013889215, "s": [{"i": [[-1.395, 0.226], [-0.993, -1.059], [-0.665, -1.68], [-0.22, -0.547], [-0.161, -0.414], [-0.654, -0.025], [-0.831, -0.038], [-0.641, -0.023], [-0.413, -0.02], [-0.381, -0.016], [-0.995, -0.995], [0.56, -1.616], [2.265, -1.342], [-0.148, -0.489], [-0.19, -0.642], [-0.19, -0.636], [0.095, -1.546], [1.932, -0.109], [1.489, 1.29], [2.366, 0.47], [1.025, -0.883], [2.411, 0], [0.04, 0.901], [0.091, 3.12], [1.861, 1.352], [-0.497, 0.498], [-1.424, -0.015], [-0.413, -0.002], [-0.994, -0.009], [-0.069, 0.624], [-0.538, 1.53], [-0.132, 0.385]], "o": [[1.337, 0.186], [1.036, 1.484], [0.213, 0.532], [0.156, 0.402], [0.635, 0.025], [0.83, 0.037], [0.628, 0.023], [0.4, 0.019], [0.37, 0.015], [0.935, 0.158], [0.062, 1.367], [-1.904, 1.838], [0.143, 0.474], [0.184, 0.623], [0.185, 0.618], [0.374, 1.517], [-1.55, 0.389], [-1.558, -0.948], [-1.862, -1.752], [-1.256, 0.512], [-1.892, 1.491], [-0.995, -0.994], [0.366, -2.976], [-1.806, -1.313], [0.498, -2.983], [1.424, -0.036], [0.401, 0.003], [0.994, 0.006], [0.066, -0.605], [0.201, -1.604], [0.128, -0.374], [0.514, -1.277]], "v": [[-0.986, -14.006], [0.962, -12.522], [2.85, -8.099], [3.686, -6.394], [4.541, -4.794], [6.322, -4.768], [8.193, -4.702], [10.13, -4.895], [11.105, -4.645], [12.656, -4.694], [14.347, -3.397], [13.372, -0.462], [8.051, 3.297], [7.627, 5.687], [7.805, 7.386], [8.269, 9.4], [8.704, 12.935], [5.843, 13.195], [3.082, 11.29], [-1.242, 9.068], [-4.494, 10.374], [-8.978, 13.03], [-11.11, 11.517], [-8.603, 2.399], [-15.363, -1.627], [-14.512, -4.583], [-10.538, -4.614], [-7.748, -4.512], [-4.862, -4.583], [-4.23, -6.466], [-3.094, -10.729], [-2.562, -12.244]], "c": true}], "h": 1}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.074509803922, 0.078431372549, 0.086274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [142.795, 57.806], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 915.000037268714, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Capa 1 Outlines 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [418.596, 311.916, 0], "to": [-0.012, 0.027, 0], "ti": [0.031, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 1, "s": [418.525, 312.077, 0], "to": [-0.031, 0.057, 0], "ti": [0.047, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.153}, "t": 2, "s": [418.408, 312.257, 0], "to": [-0.047, 0.063, 0], "ti": [0.063, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.155}, "t": 3, "s": [418.243, 312.454, 0], "to": [-0.063, 0.068, 0], "ti": [0.078, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 4, "s": [418.031, 312.667, 0], "to": [-0.078, 0.074, 0], "ti": [0.093, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 5, "s": [417.774, 312.895, 0], "to": [-0.093, 0.078, 0], "ti": [0.107, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 6, "s": [417.474, 313.136, 0], "to": [-0.107, 0.082, 0], "ti": [0.12, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 7, "s": [417.132, 313.388, 0], "to": [-0.12, 0.086, 0], "ti": [0.132, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 8, "s": [416.753, 313.649, 0], "to": [-0.132, 0.088, 0], "ti": [0.142, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 9, "s": [416.341, 313.918, 0], "to": [-0.142, 0.09, 0], "ti": [0.151, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 10, "s": [415.9, 314.191, 0], "to": [-0.151, 0.091, 0], "ti": [0.158, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 11, "s": [415.435, 314.466, 0], "to": [-0.158, 0.092, 0], "ti": [0.162, -0.091, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 12, "s": [414.953, 314.741, 0], "to": [-0.162, 0.091, 0], "ti": [0.165, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [414.461, 315.012, 0], "to": [-0.165, 0.089, 0], "ti": [0.165, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 14, "s": [413.965, 315.277, 0], "to": [-0.165, 0.087, 0], "ti": [0.161, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 15, "s": [413.473, 315.533, 0], "to": [-0.161, 0.083, 0], "ti": [0.155, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 16, "s": [412.996, 315.776, 0], "to": [-0.155, 0.078, 0], "ti": [0.146, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 17, "s": [412.541, 316.002, 0], "to": [-0.146, 0.072, 0], "ti": [0.133, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.177}, "t": 18, "s": [412.12, 316.208, 0], "to": [-0.133, 0.065, 0], "ti": [0.117, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.181}, "t": 19, "s": [411.742, 316.39, 0], "to": [-0.117, 0.056, 0], "ti": [0.096, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.189}, "t": 20, "s": [411.42, 316.545, 0], "to": [-0.096, 0.046, 0], "ti": [0.071, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.204}, "t": 21, "s": [411.166, 316.667, 0], "to": [-0.071, 0.035, 0], "ti": [0.043, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.217}, "t": 22, "s": [410.992, 316.753, 0], "to": [-0.043, 0.028, 0], "ti": [0.016, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.192}, "t": 23, "s": [410.905, 316.835, 0], "to": [-0.016, 0.029, 0], "ti": [-0.009, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.801}, "o": {"x": 0.167, "y": 0.151}, "t": 24, "s": [410.897, 316.925, 0], "to": [0.009, 0.031, 0], "ti": [-0.032, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.143}, "t": 25, "s": [410.961, 317.02, 0], "to": [0.032, 0.032, 0], "ti": [-0.051, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.148}, "t": 26, "s": [411.088, 317.116, 0], "to": [0.051, 0.032, 0], "ti": [-0.069, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 27, "s": [411.27, 317.212, 0], "to": [0.069, 0.031, 0], "ti": [-0.083, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 28, "s": [411.5, 317.304, 0], "to": [0.083, 0.03, 0], "ti": [-0.096, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 29, "s": [411.77, 317.392, 0], "to": [0.096, 0.028, 0], "ti": [-0.106, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 30, "s": [412.075, 317.472, 0], "to": [0.106, 0.025, 0], "ti": [-0.114, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 31, "s": [412.406, 317.543, 0], "to": [0.114, 0.022, 0], "ti": [-0.119, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 32, "s": [412.757, 317.604, 0], "to": [0.119, 0.018, 0], "ti": [-0.123, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 33, "s": [413.121, 317.654, 0], "to": [0.123, 0.015, 0], "ti": [-0.124, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [413.494, 317.691, 0], "to": [0.124, 0.01, 0], "ti": [-0.124, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 35, "s": [413.868, 317.716, 0], "to": [0.124, 0.006, 0], "ti": [-0.122, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 36, "s": [414.239, 317.727, 0], "to": [0.122, 0.002, 0], "ti": [-0.118, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 37, "s": [414.6, 317.725, 0], "to": [0.118, -0.003, 0], "ti": [-0.112, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 38, "s": [414.947, 317.71, 0], "to": [0.112, -0.007, 0], "ti": [-0.105, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 39, "s": [415.274, 317.681, 0], "to": [0.105, -0.012, 0], "ti": [-0.096, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 40, "s": [415.577, 317.64, 0], "to": [0.096, -0.016, 0], "ti": [-0.086, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 41, "s": [415.852, 317.587, 0], "to": [0.086, -0.02, 0], "ti": [-0.075, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 42, "s": [416.094, 317.522, 0], "to": [0.075, -0.023, 0], "ti": [-0.062, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 43, "s": [416.299, 317.449, 0], "to": [0.062, -0.026, 0], "ti": [-0.048, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.186}, "t": 44, "s": [416.464, 317.367, 0], "to": [0.048, -0.028, 0], "ti": [-0.032, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.189}, "t": 45, "s": [416.584, 317.279, 0], "to": [0.032, -0.03, 0], "ti": [-0.016, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.184}, "t": 46, "s": [416.657, 317.187, 0], "to": [0.016, -0.031, 0], "ti": [0.001, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.165}, "t": 47, "s": [416.68, 317.092, 0], "to": [-0.001, -0.031, 0], "ti": [0.019, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.149}, "t": 48, "s": [416.65, 316.998, 0], "to": [-0.019, -0.031, 0], "ti": [0.038, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.146}, "t": 49, "s": [416.563, 316.907, 0], "to": [-0.038, -0.029, 0], "ti": [0.058, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.147}, "t": 50, "s": [416.419, 316.821, 0], "to": [-0.058, -0.027, 0], "ti": [0.078, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.15}, "t": 51, "s": [416.214, 316.745, 0], "to": [-0.078, -0.023, 0], "ti": [0.091, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.163}, "t": 52, "s": [415.949, 316.681, 0], "to": [-0.091, -0.021, 0], "ti": [0.092, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 53, "s": [415.669, 316.62, 0], "to": [-0.092, -0.02, 0], "ti": [0.09, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 54, "s": [415.395, 316.558, 0], "to": [-0.09, -0.02, 0], "ti": [0.087, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 55, "s": [415.128, 316.5, 0], "to": [-0.087, -0.019, 0], "ti": [0.083, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 56, "s": [414.872, 316.446, 0], "to": [-0.083, -0.017, 0], "ti": [0.078, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 57, "s": [414.629, 316.398, 0], "to": [-0.078, -0.015, 0], "ti": [0.073, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 58, "s": [414.401, 316.359, 0], "to": [-0.073, -0.012, 0], "ti": [0.068, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 59, "s": [414.19, 316.329, 0], "to": [-0.068, -0.008, 0], "ti": [0.061, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 60, "s": [413.996, 316.309, 0], "to": [-0.061, -0.005, 0], "ti": [0.055, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 61, "s": [413.821, 316.301, 0], "to": [-0.055, -0.001, 0], "ti": [0.049, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 62, "s": [413.666, 316.304, 0], "to": [-0.049, 0.003, 0], "ti": [0.042, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.179}, "t": 63, "s": [413.53, 316.321, 0], "to": [-0.042, 0.008, 0], "ti": [0.035, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.178}, "t": 64, "s": [413.414, 316.35, 0], "to": [-0.035, 0.012, 0], "ti": [0.029, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.176}, "t": 65, "s": [413.317, 316.393, 0], "to": [-0.029, 0.016, 0], "ti": [0.023, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.171}, "t": 66, "s": [413.24, 316.448, 0], "to": [-0.023, 0.021, 0], "ti": [0.017, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.166}, "t": 67, "s": [413.181, 316.516, 0], "to": [-0.017, 0.025, 0], "ti": [0.011, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.163}, "t": 68, "s": [413.139, 316.596, 0], "to": [-0.011, 0.028, 0], "ti": [0.007, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 69, "s": [413.112, 316.686, 0], "to": [-0.007, 0.032, 0], "ti": [0.002, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 70, "s": [413.099, 316.787, 0], "to": [-0.002, 0.035, 0], "ti": [-0.001, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 71, "s": [413.098, 316.897, 0], "to": [0.001, 0.038, 0], "ti": [-0.004, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 72, "s": [413.106, 317.015, 0], "to": [0.004, 0.04, 0], "ti": [-0.005, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 73, "s": [413.12, 317.138, 0], "to": [0.005, 0.042, 0], "ti": [-0.006, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 74, "s": [413.138, 317.265, 0], "to": [0.006, 0.043, 0], "ti": [-0.005, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [413.156, 317.394, 0], "to": [0.005, 0.043, 0], "ti": [-0.003, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 76, "s": [413.17, 317.522, 0], "to": [0.003, 0.042, 0], "ti": [0, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 77, "s": [413.177, 317.648, 0], "to": [0, 0.041, 0], "ti": [0.004, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 78, "s": [413.172, 317.767, 0], "to": [-0.004, 0.038, 0], "ti": [0.011, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.17}, "t": 79, "s": [413.15, 317.878, 0], "to": [-0.011, 0.035, 0], "ti": [0.019, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.166}, "t": 80, "s": [413.107, 317.978, 0], "to": [-0.019, 0.031, 0], "ti": [0.028, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.16}, "t": 81, "s": [413.038, 318.062, 0], "to": [-0.028, 0.025, 0], "ti": [0.032, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.185}, "t": 82, "s": [412.939, 318.126, 0], "to": [-0.032, 0.016, 0], "ti": [0.026, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.204}, "t": 83, "s": [412.848, 318.159, 0], "to": [-0.026, 0.005, 0], "ti": [0.018, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.189}, "t": 84, "s": [412.782, 318.159, 0], "to": [-0.018, -0.005, 0], "ti": [0.009, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.154}, "t": 85, "s": [412.741, 318.127, 0], "to": [-0.009, -0.015, 0], "ti": [0, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.143}, "t": 86, "s": [412.727, 318.068, 0], "to": [0, -0.024, 0], "ti": [-0.01, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.145}, "t": 87, "s": [412.741, 317.983, 0], "to": [0.01, -0.032, 0], "ti": [-0.019, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.149}, "t": 88, "s": [412.784, 317.875, 0], "to": [0.019, -0.039, 0], "ti": [-0.029, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 89, "s": [412.856, 317.746, 0], "to": [0.029, -0.046, 0], "ti": [-0.038, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 90, "s": [412.957, 317.6, 0], "to": [0.038, -0.051, 0], "ti": [-0.048, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 91, "s": [413.086, 317.439, 0], "to": [0.048, -0.056, 0], "ti": [-0.057, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 92, "s": [413.244, 317.264, 0], "to": [0.057, -0.06, 0], "ti": [-0.066, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 93, "s": [413.428, 317.08, 0], "to": [0.066, -0.063, 0], "ti": [-0.074, 0.065, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 94, "s": [413.638, 316.887, 0], "to": [0.074, -0.065, 0], "ti": [-0.082, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 95, "s": [413.872, 316.69, 0], "to": [0.082, -0.066, 0], "ti": [-0.089, 0.067, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 96, "s": [414.128, 316.489, 0], "to": [0.089, -0.067, 0], "ti": [-0.095, 0.067, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 97, "s": [414.403, 316.288, 0], "to": [0.095, -0.067, 0], "ti": [-0.1, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 98, "s": [414.696, 316.088, 0], "to": [0.1, -0.066, 0], "ti": [-0.104, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 99, "s": [415.004, 315.893, 0], "to": [0.104, -0.064, 0], "ti": [-0.107, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 100, "s": [415.322, 315.705, 0], "to": [0.107, -0.061, 0], "ti": [-0.109, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [415.648, 315.525, 0], "to": [0.109, -0.058, 0], "ti": [-0.11, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 102, "s": [415.978, 315.357, 0], "to": [0.11, -0.054, 0], "ti": [-0.109, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 103, "s": [416.307, 315.202, 0], "to": [0.109, -0.049, 0], "ti": [-0.106, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 104, "s": [416.631, 315.062, 0], "to": [0.106, -0.043, 0], "ti": [-0.102, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 105, "s": [416.945, 314.941, 0], "to": [0.102, -0.037, 0], "ti": [-0.096, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 106, "s": [417.243, 314.84, 0], "to": [0.096, -0.03, 0], "ti": [-0.088, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 107, "s": [417.522, 314.761, 0], "to": [0.088, -0.022, 0], "ti": [-0.079, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.18}, "t": 108, "s": [417.774, 314.708, 0], "to": [0.079, -0.014, 0], "ti": [-0.067, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.185}, "t": 109, "s": [417.993, 314.68, 0], "to": [0.067, -0.004, 0], "ti": [-0.053, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.192}, "t": 110, "s": [418.174, 314.682, 0], "to": [0.053, 0.006, 0], "ti": [-0.036, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.195}, "t": 111, "s": [418.308, 314.715, 0], "to": [0.036, 0.016, 0], "ti": [-0.014, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.213}, "t": 112, "s": [418.389, 314.779, 0], "to": [0.014, 0.021, 0], "ti": [0.012, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.782}, "o": {"x": 0.167, "y": 0.145}, "t": 113, "s": [418.392, 314.841, 0], "to": [-0.012, 0.019, 0], "ti": [0.035, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.802}, "o": {"x": 0.167, "y": 0.135}, "t": 114, "s": [418.32, 314.894, 0], "to": [-0.035, 0.016, 0], "ti": [0.056, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.144}, "t": 115, "s": [418.18, 314.937, 0], "to": [-0.056, 0.013, 0], "ti": [0.075, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.15}, "t": 116, "s": [417.981, 314.971, 0], "to": [-0.075, 0.01, 0], "ti": [0.09, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 117, "s": [417.732, 314.999, 0], "to": [-0.09, 0.008, 0], "ti": [0.103, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 118, "s": [417.439, 315.021, 0], "to": [-0.103, 0.006, 0], "ti": [0.114, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 119, "s": [417.111, 315.037, 0], "to": [-0.114, 0.005, 0], "ti": [0.122, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 120, "s": [416.755, 315.05, 0], "to": [-0.122, 0.004, 0], "ti": [0.128, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 121, "s": [416.377, 315.059, 0], "to": [-0.128, 0.003, 0], "ti": [0.132, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 122, "s": [415.984, 315.066, 0], "to": [-0.132, 0.002, 0], "ti": [0.134, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 123, "s": [415.583, 315.072, 0], "to": [-0.134, 0.002, 0], "ti": [0.134, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [415.178, 315.078, 0], "to": [-0.134, 0.002, 0], "ti": [0.132, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 125, "s": [414.777, 315.083, 0], "to": [-0.132, 0.002, 0], "ti": [0.129, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 126, "s": [414.385, 315.09, 0], "to": [-0.129, 0.003, 0], "ti": [0.123, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 127, "s": [414.006, 315.099, 0], "to": [-0.123, 0.003, 0], "ti": [0.116, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 128, "s": [413.645, 315.109, 0], "to": [-0.116, 0.004, 0], "ti": [0.108, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 129, "s": [413.307, 315.123, 0], "to": [-0.108, 0.005, 0], "ti": [0.099, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 130, "s": [412.996, 315.141, 0], "to": [-0.099, 0.007, 0], "ti": [0.088, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 131, "s": [412.716, 315.162, 0], "to": [-0.088, 0.008, 0], "ti": [0.076, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 132, "s": [412.47, 315.189, 0], "to": [-0.076, 0.01, 0], "ti": [0.062, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 133, "s": [412.263, 315.22, 0], "to": [-0.062, 0.011, 0], "ti": [0.048, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.192}, "t": 134, "s": [412.096, 315.257, 0], "to": [-0.048, 0.013, 0], "ti": [0.034, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.201}, "t": 135, "s": [411.972, 315.301, 0], "to": [-0.034, 0.015, 0], "ti": [0.018, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.202}, "t": 136, "s": [411.894, 315.35, 0], "to": [-0.018, 0.018, 0], "ti": [0.002, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.163}, "t": 137, "s": [411.865, 315.406, 0], "to": [-0.002, 0.02, 0], "ti": [-0.015, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.795}, "o": {"x": 0.167, "y": 0.139}, "t": 138, "s": [411.885, 315.469, 0], "to": [0.015, 0.022, 0], "ti": [-0.033, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.141}, "t": 139, "s": [411.956, 315.539, 0], "to": [0.033, 0.025, 0], "ti": [-0.05, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.145}, "t": 140, "s": [412.08, 315.617, 0], "to": [0.05, 0.027, 0], "ti": [-0.068, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.15}, "t": 141, "s": [412.257, 315.702, 0], "to": [0.068, 0.029, 0], "ti": [-0.08, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.162}, "t": 142, "s": [412.486, 315.793, 0], "to": [0.08, 0.028, 0], "ti": [-0.085, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 143, "s": [412.736, 315.871, 0], "to": [0.085, 0.022, 0], "ti": [-0.086, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [412.993, 315.927, 0], "to": [0.086, 0.015, 0], "ti": [-0.086, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 145, "s": [413.253, 315.963, 0], "to": [0.086, 0.009, 0], "ti": [-0.084, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 146, "s": [413.51, 315.979, 0], "to": [0.084, 0.002, 0], "ti": [-0.081, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 147, "s": [413.759, 315.974, 0], "to": [0.081, -0.005, 0], "ti": [-0.077, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 148, "s": [413.998, 315.949, 0], "to": [0.077, -0.012, 0], "ti": [-0.072, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 149, "s": [414.223, 315.905, 0], "to": [0.072, -0.018, 0], "ti": [-0.066, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 150, "s": [414.43, 315.841, 0], "to": [0.066, -0.024, 0], "ti": [-0.059, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 151, "s": [414.617, 315.759, 0], "to": [0.059, -0.03, 0], "ti": [-0.051, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 152, "s": [414.782, 315.661, 0], "to": [0.051, -0.036, 0], "ti": [-0.043, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.17}, "t": 153, "s": [414.924, 315.545, 0], "to": [0.043, -0.041, 0], "ti": [-0.035, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.169}, "t": 154, "s": [415.041, 315.415, 0], "to": [0.035, -0.046, 0], "ti": [-0.026, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [415.132, 315.272, 0], "to": [0.026, -0.05, 0], "ti": [-0.018, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.166}, "t": 156, "s": [415.197, 315.117, 0], "to": [0.018, -0.053, 0], "ti": [-0.009, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 157, "s": [415.237, 314.951, 0], "to": [0.009, -0.057, 0], "ti": [-0.001, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 158, "s": [415.252, 314.778, 0], "to": [0.001, -0.059, 0], "ti": [0.007, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 159, "s": [415.243, 314.598, 0], "to": [-0.007, -0.061, 0], "ti": [0.014, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 160, "s": [415.211, 314.414, 0], "to": [-0.014, -0.062, 0], "ti": [0.02, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 161, "s": [415.159, 314.229, 0], "to": [-0.02, -0.062, 0], "ti": [0.026, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 162, "s": [415.089, 314.045, 0], "to": [-0.026, -0.061, 0], "ti": [0.03, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [415.004, 313.864, 0], "to": [-0.03, -0.059, 0], "ti": [0.034, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 164, "s": [414.907, 313.69, 0], "to": [-0.034, -0.056, 0], "ti": [0.036, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 165, "s": [414.801, 313.525, 0], "to": [-0.036, -0.053, 0], "ti": [0.036, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.173}, "t": 166, "s": [414.692, 313.374, 0], "to": [-0.036, -0.048, 0], "ti": [0.035, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.177}, "t": 167, "s": [414.584, 313.238, 0], "to": [-0.035, -0.042, 0], "ti": [0.032, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.183}, "t": 168, "s": [414.482, 313.122, 0], "to": [-0.032, -0.035, 0], "ti": [0.027, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.193}, "t": 169, "s": [414.392, 313.029, 0], "to": [-0.027, -0.026, 0], "ti": [0.02, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.219}, "t": 170, "s": [414.319, 312.963, 0], "to": [-0.02, -0.017, 0], "ti": [0.012, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.667}, "o": {"x": 0.167, "y": 0.272}, "t": 171, "s": [414.27, 312.928, 0], "to": [-0.012, -0.005, 0], "ti": [0.006, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.758}, "o": {"x": 0.167, "y": 0.111}, "t": 172, "s": [414.25, 312.932, 0], "to": [-0.006, 0.014, 0], "ti": [0.005, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.127}, "t": 173, "s": [414.234, 313.009, 0], "to": [-0.005, 0.038, 0], "ti": [0.006, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.143}, "t": 174, "s": [414.217, 313.159, 0], "to": [-0.006, 0.06, 0], "ti": [0.006, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.15}, "t": 175, "s": [414.2, 313.371, 0], "to": [-0.006, 0.079, 0], "ti": [0.005, -0.095, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.155}, "t": 176, "s": [414.184, 313.635, 0], "to": [-0.005, 0.095, 0], "ti": [0.005, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 177, "s": [414.168, 313.939, 0], "to": [-0.005, 0.107, 0], "ti": [0.004, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 178, "s": [414.153, 314.276, 0], "to": [-0.004, 0.116, 0], "ti": [0.004, -0.123, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 179, "s": [414.141, 314.636, 0], "to": [-0.004, 0.123, 0], "ti": [0.003, -0.126, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 180, "s": [414.131, 315.011, 0], "to": [-0.003, 0.126, 0], "ti": [0.002, -0.127, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [414.123, 315.394, 0], "to": [-0.002, 0.127, 0], "ti": [0.001, -0.126, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 182, "s": [414.119, 315.776, 0], "to": [-0.001, 0.126, 0], "ti": [0, -0.123, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 183, "s": [414.119, 316.152, 0], "to": [0, 0.123, 0], "ti": [-0.002, -0.118, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 184, "s": [414.122, 316.516, 0], "to": [0.002, 0.118, 0], "ti": [-0.003, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 185, "s": [414.129, 316.861, 0], "to": [0.003, 0.111, 0], "ti": [-0.004, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 186, "s": [414.139, 317.183, 0], "to": [0.004, 0.103, 0], "ti": [-0.005, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 187, "s": [414.153, 317.477, 0], "to": [0.005, 0.093, 0], "ti": [-0.007, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 188, "s": [414.171, 317.74, 0], "to": [0.007, 0.082, 0], "ti": [-0.008, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.183}, "t": 189, "s": [414.193, 317.967, 0], "to": [0.008, 0.069, 0], "ti": [-0.009, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.188}, "t": 190, "s": [414.217, 318.156, 0], "to": [0.009, 0.056, 0], "ti": [-0.01, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.863}, "o": {"x": 0.167, "y": 0.197}, "t": 191, "s": [414.245, 318.305, 0], "to": [0.01, 0.043, 0], "ti": [-0.011, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.213}, "t": 192, "s": [414.275, 318.412, 0], "to": [0.011, 0.028, 0], "ti": [-0.011, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.228}, "t": 193, "s": [414.308, 318.476, 0], "to": [0.011, 0.014, 0], "ti": [-0.012, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.16}, "t": 194, "s": [414.343, 318.495, 0], "to": [0.012, -0.001, 0], "ti": [-0.012, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.791}, "o": {"x": 0.167, "y": 0.131}, "t": 195, "s": [414.378, 318.471, 0], "to": [0.012, -0.016, 0], "ti": [-0.012, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.139}, "t": 196, "s": [414.415, 318.402, 0], "to": [0.012, -0.03, 0], "ti": [-0.012, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.146}, "t": 197, "s": [414.451, 318.29, 0], "to": [0.012, -0.044, 0], "ti": [-0.012, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.15}, "t": 198, "s": [414.487, 318.137, 0], "to": [0.012, -0.058, 0], "ti": [-0.011, 0.071, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 199, "s": [414.521, 317.944, 0], "to": [0.011, -0.071, 0], "ti": [-0.01, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 200, "s": [414.552, 317.713, 0], "to": [0.01, -0.082, 0], "ti": [-0.009, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.159}, "t": 201, "s": [414.58, 317.449, 0], "to": [0.009, -0.093, 0], "ti": [-0.009, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 202, "s": [414.605, 317.156, 0], "to": [0.009, -0.099, 0], "ti": [-0.01, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [414.634, 316.855, 0], "to": [0.01, -0.1, 0], "ti": [-0.012, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 204, "s": [414.668, 316.553, 0], "to": [0.012, -0.1, 0], "ti": [-0.014, 0.097, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 205, "s": [414.708, 316.257, 0], "to": [0.014, -0.097, 0], "ti": [-0.016, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 206, "s": [414.753, 315.969, 0], "to": [0.016, -0.094, 0], "ti": [-0.018, 0.089, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 207, "s": [414.803, 315.694, 0], "to": [0.018, -0.089, 0], "ti": [-0.019, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 208, "s": [414.859, 315.434, 0], "to": [0.019, -0.083, 0], "ti": [-0.021, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 209, "s": [414.92, 315.193, 0], "to": [0.021, -0.077, 0], "ti": [-0.023, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 210, "s": [414.985, 314.973, 0], "to": [0.023, -0.07, 0], "ti": [-0.024, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 211, "s": [415.055, 314.776, 0], "to": [0.024, -0.062, 0], "ti": [-0.025, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 212, "s": [415.129, 314.604, 0], "to": [0.025, -0.053, 0], "ti": [-0.026, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 213, "s": [415.206, 314.456, 0], "to": [0.026, -0.045, 0], "ti": [-0.027, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.179}, "t": 214, "s": [415.287, 314.335, 0], "to": [0.027, -0.036, 0], "ti": [-0.028, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.179}, "t": 215, "s": [415.371, 314.24, 0], "to": [0.028, -0.027, 0], "ti": [-0.029, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.177}, "t": 216, "s": [415.457, 314.171, 0], "to": [0.029, -0.019, 0], "ti": [-0.03, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.173}, "t": 217, "s": [415.546, 314.127, 0], "to": [0.03, -0.011, 0], "ti": [-0.03, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.168}, "t": 218, "s": [415.636, 314.107, 0], "to": [0.03, -0.003, 0], "ti": [-0.03, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.164}, "t": 219, "s": [415.727, 314.11, 0], "to": [0.03, 0.004, 0], "ti": [-0.031, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.162}, "t": 220, "s": [415.819, 314.133, 0], "to": [0.031, 0.011, 0], "ti": [-0.03, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 221, "s": [415.91, 314.174, 0], "to": [0.03, 0.016, 0], "ti": [-0.03, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 222, "s": [416.001, 314.231, 0], "to": [0.03, 0.021, 0], "ti": [-0.029, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 223, "s": [416.09, 314.299, 0], "to": [0.029, 0.024, 0], "ti": [-0.029, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.166}, "t": 224, "s": [416.177, 314.375, 0], "to": [0.029, 0.026, 0], "ti": [-0.028, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.169}, "t": 225, "s": [416.262, 314.455, 0], "to": [0.028, 0.026, 0], "ti": [-0.026, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.172}, "t": 226, "s": [416.342, 314.534, 0], "to": [0.026, 0.025, 0], "ti": [-0.025, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.177}, "t": 227, "s": [416.419, 314.607, 0], "to": [0.025, 0.022, 0], "ti": [-0.023, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.183}, "t": 228, "s": [416.49, 314.668, 0], "to": [0.023, 0.018, 0], "ti": [-0.021, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.189}, "t": 229, "s": [416.555, 314.712, 0], "to": [0.021, 0.011, 0], "ti": [-0.018, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.182}, "t": 230, "s": [416.614, 314.732, 0], "to": [0.018, 0.002, 0], "ti": [-0.015, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.157}, "t": 231, "s": [416.664, 314.721, 0], "to": [0.015, -0.009, 0], "ti": [-0.01, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.197}, "t": 232, "s": [416.705, 314.678, 0], "to": [0.01, -0.014, 0], "ti": [-0.003, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.216}, "t": 233, "s": [416.724, 314.639, 0], "to": [0.003, -0.011, 0], "ti": [0.004, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.789}, "o": {"x": 0.167, "y": 0.165}, "t": 234, "s": [416.722, 314.612, 0], "to": [-0.004, -0.007, 0], "ti": [0.011, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.798}, "o": {"x": 0.167, "y": 0.138}, "t": 235, "s": [416.7, 314.596, 0], "to": [-0.011, -0.004, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.142}, "t": 236, "s": [416.657, 314.591, 0], "to": [-0.017, 0, 0], "ti": [0.023, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.148}, "t": 237, "s": [416.596, 314.595, 0], "to": [-0.023, 0.003, 0], "ti": [0.029, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 238, "s": [416.517, 314.607, 0], "to": [-0.029, 0.005, 0], "ti": [0.034, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 239, "s": [416.421, 314.626, 0], "to": [-0.034, 0.008, 0], "ti": [0.039, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 240, "s": [416.311, 314.652, 0], "to": [-0.039, 0.01, 0], "ti": [0.043, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 241, "s": [416.187, 314.683, 0], "to": [-0.043, 0.011, 0], "ti": [0.047, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 242, "s": [416.05, 314.719, 0], "to": [-0.047, 0.013, 0], "ti": [0.051, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 243, "s": [415.903, 314.758, 0], "to": [-0.051, 0.014, 0], "ti": [0.053, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 244, "s": [415.746, 314.801, 0], "to": [-0.053, 0.014, 0], "ti": [0.056, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 245, "s": [415.582, 314.845, 0], "to": [-0.056, 0.015, 0], "ti": [0.058, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 246, "s": [415.412, 314.89, 0], "to": [-0.058, 0.015, 0], "ti": [0.059, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 247, "s": [415.237, 314.935, 0], "to": [-0.059, 0.015, 0], "ti": [0.059, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 248, "s": [415.06, 314.979, 0], "to": [-0.059, 0.014, 0], "ti": [0.059, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 249, "s": [414.881, 315.021, 0], "to": [-0.059, 0.014, 0], "ti": [0.059, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 250, "s": [414.704, 315.061, 0], "to": [-0.059, 0.013, 0], "ti": [0.057, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 251, "s": [414.53, 315.098, 0], "to": [-0.057, 0.011, 0], "ti": [0.055, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 252, "s": [414.361, 315.13, 0], "to": [-0.055, 0.01, 0], "ti": [0.052, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 253, "s": [414.2, 315.157, 0], "to": [-0.052, 0.008, 0], "ti": [0.049, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 254, "s": [414.047, 315.178, 0], "to": [-0.049, 0.006, 0], "ti": [0.045, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 255, "s": [413.906, 315.193, 0], "to": [-0.045, 0.004, 0], "ti": [0.04, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 256, "s": [413.779, 315.2, 0], "to": [-0.04, 0.001, 0], "ti": [0.034, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.183}, "t": 257, "s": [413.668, 315.198, 0], "to": [-0.034, -0.002, 0], "ti": [0.027, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.187}, "t": 258, "s": [413.576, 315.188, 0], "to": [-0.027, -0.005, 0], "ti": [0.02, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.191}, "t": 259, "s": [413.504, 315.167, 0], "to": [-0.02, -0.009, 0], "ti": [0.012, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.182}, "t": 260, "s": [413.455, 315.136, 0], "to": [-0.012, -0.012, 0], "ti": [0.003, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.798}, "o": {"x": 0.167, "y": 0.156}, "t": 261, "s": [413.433, 315.093, 0], "to": [-0.003, -0.016, 0], "ti": [-0.008, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.142}, "t": 262, "s": [413.44, 315.038, 0], "to": [0.008, -0.021, 0], "ti": [-0.018, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.146}, "t": 263, "s": [413.479, 314.968, 0], "to": [0.018, -0.026, 0], "ti": [-0.026, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.152}, "t": 264, "s": [413.546, 314.885, 0], "to": [0.026, -0.03, 0], "ti": [-0.032, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.157}, "t": 265, "s": [413.633, 314.791, 0], "to": [0.032, -0.033, 0], "ti": [-0.036, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.16}, "t": 266, "s": [413.736, 314.686, 0], "to": [0.036, -0.036, 0], "ti": [-0.038, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 267, "s": [413.847, 314.574, 0], "to": [0.038, -0.039, 0], "ti": [-0.038, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 268, "s": [413.963, 314.455, 0], "to": [0.038, -0.04, 0], "ti": [-0.038, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 269, "s": [414.078, 314.332, 0], "to": [0.038, -0.042, 0], "ti": [-0.035, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 270, "s": [414.188, 314.204, 0], "to": [0.035, -0.043, 0], "ti": [-0.032, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 271, "s": [414.29, 314.074, 0], "to": [0.032, -0.043, 0], "ti": [-0.027, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 272, "s": [414.379, 313.944, 0], "to": [0.027, -0.044, 0], "ti": [-0.022, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 273, "s": [414.453, 313.813, 0], "to": [0.022, -0.043, 0], "ti": [-0.015, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 274, "s": [414.51, 313.684, 0], "to": [0.015, -0.043, 0], "ti": [-0.009, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.172}, "t": 275, "s": [414.546, 313.556, 0], "to": [0.009, -0.042, 0], "ti": [-0.001, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.17}, "t": 276, "s": [414.561, 313.432, 0], "to": [0.001, -0.041, 0], "ti": [0.007, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.167}, "t": 277, "s": [414.553, 313.312, 0], "to": [-0.007, -0.039, 0], "ti": [0.015, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.164}, "t": 278, "s": [414.52, 313.197, 0], "to": [-0.015, -0.038, 0], "ti": [0.023, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.162}, "t": 279, "s": [414.464, 313.087, 0], "to": [-0.023, -0.036, 0], "ti": [0.031, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.16}, "t": 280, "s": [414.382, 312.983, 0], "to": [-0.031, -0.033, 0], "ti": [0.039, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.159}, "t": 281, "s": [414.277, 312.886, 0], "to": [-0.039, -0.031, 0], "ti": [0.047, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 282, "s": [414.148, 312.796, 0], "to": [-0.047, -0.029, 0], "ti": [0.054, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 283, "s": [413.997, 312.714, 0], "to": [-0.054, -0.026, 0], "ti": [0.06, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 284, "s": [413.825, 312.639, 0], "to": [-0.06, -0.024, 0], "ti": [0.066, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 285, "s": [413.635, 312.572, 0], "to": [-0.066, -0.021, 0], "ti": [0.071, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 286, "s": [413.429, 312.513, 0], "to": [-0.071, -0.018, 0], "ti": [0.075, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 287, "s": [413.209, 312.463, 0], "to": [-0.075, -0.015, 0], "ti": [0.078, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 288, "s": [412.979, 312.421, 0], "to": [-0.078, -0.013, 0], "ti": [0.079, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 289, "s": [412.743, 312.386, 0], "to": [-0.079, -0.01, 0], "ti": [0.079, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 290, "s": [412.505, 312.36, 0], "to": [-0.079, -0.007, 0], "ti": [0.077, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.17}, "t": 291, "s": [412.269, 312.341, 0], "to": [-0.077, -0.005, 0], "ti": [0.073, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 292, "s": [412.041, 312.328, 0], "to": [-0.073, -0.005, 0], "ti": [0.066, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 293, "s": [411.832, 312.313, 0], "to": [-0.066, -0.005, 0], "ti": [0.059, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 294, "s": [411.644, 312.297, 0], "to": [-0.059, -0.004, 0], "ti": [0.052, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.18}, "t": 295, "s": [411.478, 312.286, 0], "to": [-0.052, -0.002, 0], "ti": [0.044, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.182}, "t": 296, "s": [411.334, 312.283, 0], "to": [-0.044, 0.001, 0], "ti": [0.036, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.184}, "t": 297, "s": [411.214, 312.292, 0], "to": [-0.036, 0.005, 0], "ti": [0.028, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.182}, "t": 298, "s": [411.117, 312.316, 0], "to": [-0.028, 0.011, 0], "ti": [0.021, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.172}, "t": 299, "s": [411.043, 312.356, 0], "to": [-0.021, 0.017, 0], "ti": [0.013, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.16}, "t": 300, "s": [410.993, 312.417, 0], "to": [-0.013, 0.024, 0], "ti": [0.006, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.152}, "t": 301, "s": [410.965, 312.498, 0], "to": [-0.006, 0.031, 0], "ti": [-0.002, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.151}, "t": 302, "s": [410.959, 312.602, 0], "to": [0.002, 0.039, 0], "ti": [-0.008, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.152}, "t": 303, "s": [410.974, 312.73, 0], "to": [0.008, 0.047, 0], "ti": [-0.015, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 304, "s": [411.009, 312.882, 0], "to": [0.015, 0.055, 0], "ti": [-0.02, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 305, "s": [411.062, 313.058, 0], "to": [0.02, 0.063, 0], "ti": [-0.026, -0.071, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 306, "s": [411.132, 313.259, 0], "to": [0.026, 0.071, 0], "ti": [-0.03, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 307, "s": [411.216, 313.483, 0], "to": [0.03, 0.078, 0], "ti": [-0.034, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 308, "s": [411.313, 313.729, 0], "to": [0.034, 0.086, 0], "ti": [-0.037, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 309, "s": [411.419, 313.996, 0], "to": [0.037, 0.092, 0], "ti": [-0.039, -0.098, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 310, "s": [411.533, 314.282, 0], "to": [0.039, 0.098, 0], "ti": [-0.04, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 311, "s": [411.652, 314.585, 0], "to": [0.04, 0.103, 0], "ti": [-0.04, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 312, "s": [411.772, 314.901, 0], "to": [0.04, 0.107, 0], "ti": [-0.038, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 313, "s": [411.89, 315.229, 0], "to": [0.038, 0.11, 0], "ti": [-0.036, -0.112, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 314, "s": [412.002, 315.564, 0], "to": [0.036, 0.112, 0], "ti": [-0.032, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 315, "s": [412.104, 315.902, 0], "to": [0.032, 0.113, 0], "ti": [-0.027, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 316, "s": [412.193, 316.239, 0], "to": [0.027, 0.111, 0], "ti": [-0.02, -0.109, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 317, "s": [412.263, 316.571, 0], "to": [0.02, 0.109, 0], "ti": [-0.011, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 318, "s": [412.311, 316.891, 0], "to": [0.011, 0.104, 0], "ti": [-0.001, -0.098, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 319, "s": [412.331, 317.195, 0], "to": [0.001, 0.098, 0], "ti": [0.011, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.174}, "t": 320, "s": [412.318, 317.477, 0], "to": [-0.011, 0.089, 0], "ti": [0.023, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.174}, "t": 321, "s": [412.268, 317.73, 0], "to": [-0.023, 0.079, 0], "ti": [0.026, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 322, "s": [412.183, 317.951, 0], "to": [-0.026, 0.072, 0], "ti": [0.021, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 323, "s": [412.111, 318.161, 0], "to": [-0.021, 0.068, 0], "ti": [0.014, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 324, "s": [412.058, 318.357, 0], "to": [-0.014, 0.062, 0], "ti": [0.008, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 325, "s": [412.025, 318.535, 0], "to": [-0.008, 0.056, 0], "ti": [0.001, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.183}, "t": 326, "s": [412.012, 318.692, 0], "to": [-0.001, 0.048, 0], "ti": [-0.005, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.186}, "t": 327, "s": [412.019, 318.824, 0], "to": [0.005, 0.039, 0], "ti": [-0.012, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.186}, "t": 328, "s": [412.045, 318.928, 0], "to": [0.012, 0.03, 0], "ti": [-0.018, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.179}, "t": 329, "s": [412.089, 319.002, 0], "to": [0.018, 0.019, 0], "ti": [-0.024, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.162}, "t": 330, "s": [412.151, 319.045, 0], "to": [0.024, 0.009, 0], "ti": [-0.029, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.151}, "t": 331, "s": [412.231, 319.054, 0], "to": [0.029, -0.002, 0], "ti": [-0.034, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.149}, "t": 332, "s": [412.327, 319.03, 0], "to": [0.034, -0.014, 0], "ti": [-0.039, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.151}, "t": 333, "s": [412.438, 318.972, 0], "to": [0.039, -0.025, 0], "ti": [-0.044, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.153}, "t": 334, "s": [412.563, 318.88, 0], "to": [0.044, -0.036, 0], "ti": [-0.048, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 335, "s": [412.7, 318.755, 0], "to": [0.048, -0.047, 0], "ti": [-0.051, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 336, "s": [412.848, 318.598, 0], "to": [0.051, -0.057, 0], "ti": [-0.054, 0.067, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 337, "s": [413.005, 318.412, 0], "to": [0.054, -0.067, 0], "ti": [-0.056, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 338, "s": [413.17, 318.198, 0], "to": [0.056, -0.075, 0], "ti": [-0.057, 0.083, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 339, "s": [413.34, 317.96, 0], "to": [0.057, -0.083, 0], "ti": [-0.058, 0.089, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 340, "s": [413.513, 317.7, 0], "to": [0.058, -0.089, 0], "ti": [-0.058, 0.095, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 341, "s": [413.687, 317.423, 0], "to": [0.058, -0.095, 0], "ti": [-0.057, 0.098, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 342, "s": [413.859, 317.133, 0], "to": [0.057, -0.098, 0], "ti": [-0.055, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 343, "s": [414.027, 316.835, 0], "to": [0.055, -0.1, 0], "ti": [-0.052, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 344, "s": [414.187, 316.534, 0], "to": [0.052, -0.1, 0], "ti": [-0.048, 0.098, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 345, "s": [414.339, 316.237, 0], "to": [0.048, -0.098, 0], "ti": [-0.043, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.173}, "t": 346, "s": [414.477, 315.949, 0], "to": [0.043, -0.093, 0], "ti": [-0.037, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.176}, "t": 347, "s": [414.599, 315.678, 0], "to": [0.037, -0.086, 0], "ti": [-0.03, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.181}, "t": 348, "s": [414.701, 315.431, 0], "to": [0.03, -0.077, 0], "ti": [-0.022, 0.065, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.188}, "t": 349, "s": [414.781, 315.216, 0], "to": [0.022, -0.065, 0], "ti": [-0.013, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.203}, "t": 350, "s": [414.834, 315.041, 0], "to": [0.013, -0.05, 0], "ti": [-0.001, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.221}, "t": 351, "s": [414.856, 314.917, 0], "to": [0.001, -0.033, 0], "ti": [0.016, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.151}, "t": 352, "s": [414.84, 314.844, 0], "to": [-0.016, -0.021, 0], "ti": [0.036, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.14}, "t": 353, "s": [414.761, 314.793, 0], "to": [-0.036, -0.014, 0], "ti": [0.053, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.146}, "t": 354, "s": [414.625, 314.762, 0], "to": [-0.053, -0.007, 0], "ti": [0.068, 0, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.152}, "t": 355, "s": [414.441, 314.751, 0], "to": [-0.068, 0, 0], "ti": [0.08, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 356, "s": [414.217, 314.76, 0], "to": [-0.08, 0.006, 0], "ti": [0.09, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 357, "s": [413.961, 314.789, 0], "to": [-0.09, 0.013, 0], "ti": [0.097, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 358, "s": [413.679, 314.839, 0], "to": [-0.097, 0.02, 0], "ti": [0.102, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 359, "s": [413.379, 314.909, 0], "to": [-0.102, 0.027, 0], "ti": [0.105, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 360, "s": [413.067, 315, 0], "to": [-0.105, 0.033, 0], "ti": [0.106, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 361, "s": [412.75, 315.11, 0], "to": [-0.106, 0.04, 0], "ti": [0.105, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 362, "s": [412.432, 315.239, 0], "to": [-0.105, 0.046, 0], "ti": [0.103, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 363, "s": [412.119, 315.385, 0], "to": [-0.103, 0.052, 0], "ti": [0.099, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 364, "s": [411.816, 315.549, 0], "to": [-0.099, 0.057, 0], "ti": [0.093, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 365, "s": [411.527, 315.727, 0], "to": [-0.093, 0.062, 0], "ti": [0.087, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 366, "s": [411.256, 315.92, 0], "to": [-0.087, 0.066, 0], "ti": [0.079, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 367, "s": [411.007, 316.124, 0], "to": [-0.079, 0.07, 0], "ti": [0.07, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 368, "s": [410.783, 316.339, 0], "to": [-0.07, 0.073, 0], "ti": [0.061, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 369, "s": [410.586, 316.561, 0], "to": [-0.061, 0.075, 0], "ti": [0.05, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 370, "s": [410.42, 316.789, 0], "to": [-0.05, 0.077, 0], "ti": [0.039, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 371, "s": [410.285, 317.02, 0], "to": [-0.039, 0.077, 0], "ti": [0.028, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 372, "s": [410.184, 317.252, 0], "to": [-0.028, 0.077, 0], "ti": [0.016, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 373, "s": [410.117, 317.481, 0], "to": [-0.016, 0.075, 0], "ti": [0.005, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 374, "s": [410.085, 317.704, 0], "to": [-0.005, 0.073, 0], "ti": [-0.007, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.17}, "t": 375, "s": [410.088, 317.917, 0], "to": [0.007, 0.069, 0], "ti": [-0.019, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.169}, "t": 376, "s": [410.127, 318.118, 0], "to": [0.019, 0.064, 0], "ti": [-0.03, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.168}, "t": 377, "s": [410.2, 318.303, 0], "to": [0.03, 0.058, 0], "ti": [-0.041, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.166}, "t": 378, "s": [410.307, 318.467, 0], "to": [0.041, 0.051, 0], "ti": [-0.052, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 379, "s": [410.447, 318.607, 0], "to": [0.052, 0.042, 0], "ti": [-0.061, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.163}, "t": 380, "s": [410.617, 318.718, 0], "to": [0.061, 0.031, 0], "ti": [-0.068, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.166}, "t": 381, "s": [410.815, 318.795, 0], "to": [0.068, 0.019, 0], "ti": [-0.064, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.187}, "t": 382, "s": [411.026, 318.83, 0], "to": [0.064, 0.002, 0], "ti": [-0.05, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.178}, "t": 383, "s": [411.197, 318.807, 0], "to": [0.05, -0.017, 0], "ti": [-0.037, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.163}, "t": 384, "s": [411.325, 318.729, 0], "to": [0.037, -0.034, 0], "ti": [-0.025, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.155}, "t": 385, "s": [411.417, 318.601, 0], "to": [0.025, -0.05, 0], "ti": [-0.016, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 386, "s": [411.477, 318.429, 0], "to": [0.016, -0.064, 0], "ti": [-0.007, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 387, "s": [411.511, 318.218, 0], "to": [0.007, -0.076, 0], "ti": [-0.001, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 388, "s": [411.522, 317.972, 0], "to": [0.001, -0.087, 0], "ti": [0.004, 0.096, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 389, "s": [411.516, 317.697, 0], "to": [-0.004, -0.096, 0], "ti": [0.008, 0.103, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 390, "s": [411.496, 317.397, 0], "to": [-0.008, -0.103, 0], "ti": [0.011, 0.109, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 391, "s": [411.467, 317.078, 0], "to": [-0.011, -0.109, 0], "ti": [0.012, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 392, "s": [411.432, 316.742, 0], "to": [-0.012, -0.114, 0], "ti": [0.012, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 393, "s": [411.395, 316.396, 0], "to": [-0.012, -0.117, 0], "ti": [0.011, 0.118, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 394, "s": [411.36, 316.043, 0], "to": [-0.011, -0.118, 0], "ti": [0.009, 0.118, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 395, "s": [411.329, 315.687, 0], "to": [-0.009, -0.118, 0], "ti": [0.006, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 396, "s": [411.306, 315.332, 0], "to": [-0.006, -0.117, 0], "ti": [0.002, 0.115, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 397, "s": [411.293, 314.983, 0], "to": [-0.002, -0.115, 0], "ti": [-0.003, 0.111, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 398, "s": [411.293, 314.643, 0], "to": [0.003, -0.111, 0], "ti": [-0.008, 0.106, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 399, "s": [411.308, 314.315, 0], "to": [0.008, -0.106, 0], "ti": [-0.014, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 400, "s": [411.341, 314.004, 0], "to": [0.014, -0.101, 0], "ti": [-0.021, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 401, "s": [411.393, 313.712, 0], "to": [0.021, -0.093, 0], "ti": [-0.028, 0.085, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 402, "s": [411.467, 313.443, 0], "to": [0.028, -0.085, 0], "ti": [-0.036, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 403, "s": [411.564, 313.201, 0], "to": [0.036, -0.076, 0], "ti": [-0.045, 0.066, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.171}, "t": 404, "s": [411.685, 312.987, 0], "to": [0.045, -0.066, 0], "ti": [-0.053, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.169}, "t": 405, "s": [411.831, 312.806, 0], "to": [0.053, -0.055, 0], "ti": [-0.062, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.166}, "t": 406, "s": [412.004, 312.66, 0], "to": [0.062, -0.042, 0], "ti": [-0.071, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.163}, "t": 407, "s": [412.203, 312.552, 0], "to": [0.071, -0.029, 0], "ti": [-0.08, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.16}, "t": 408, "s": [412.431, 312.484, 0], "to": [0.08, -0.016, 0], "ti": [-0.09, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.159}, "t": 409, "s": [412.686, 312.458, 0], "to": [0.09, -0.001, 0], "ti": [-0.099, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.158}, "t": 410, "s": [412.97, 312.478, 0], "to": [0.099, 0.014, 0], "ti": [-0.107, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.161}, "t": 411, "s": [413.281, 312.545, 0], "to": [0.107, 0.028, 0], "ti": [-0.104, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 412, "s": [413.609, 312.648, 0], "to": [0.104, 0.032, 0], "ti": [-0.093, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 413, "s": [413.906, 312.737, 0], "to": [0.093, 0.027, 0], "ti": [-0.081, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.18}, "t": 414, "s": [414.167, 312.809, 0], "to": [0.081, 0.022, 0], "ti": [-0.069, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 415, "s": [414.393, 312.868, 0], "to": [0.069, 0.018, 0], "ti": [-0.058, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.185}, "t": 416, "s": [414.583, 312.915, 0], "to": [0.058, 0.014, 0], "ti": [-0.046, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.19}, "t": 417, "s": [414.739, 312.952, 0], "to": [0.046, 0.011, 0], "ti": [-0.035, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.197}, "t": 418, "s": [414.861, 312.982, 0], "to": [0.035, 0.009, 0], "ti": [-0.024, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.871}, "o": {"x": 0.167, "y": 0.21}, "t": 419, "s": [414.949, 313.007, 0], "to": [0.024, 0.008, 0], "ti": [-0.014, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.234}, "t": 420, "s": [415.005, 313.029, 0], "to": [0.014, 0.007, 0], "ti": [-0.004, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.776}, "o": {"x": 0.167, "y": 0.211}, "t": 421, "s": [415.031, 313.049, 0], "to": [0.004, 0.007, 0], "ti": [0.005, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.783}, "o": {"x": 0.167, "y": 0.133}, "t": 422, "s": [415.029, 313.069, 0], "to": [-0.005, 0.007, 0], "ti": [0.014, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.135}, "t": 423, "s": [415, 313.091, 0], "to": [-0.014, 0.008, 0], "ti": [0.021, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.144}, "t": 424, "s": [414.946, 313.116, 0], "to": [-0.021, 0.009, 0], "ti": [0.028, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.15}, "t": 425, "s": [414.871, 313.145, 0], "to": [-0.028, 0.011, 0], "ti": [0.034, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 426, "s": [414.777, 313.179, 0], "to": [-0.034, 0.013, 0], "ti": [0.039, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 427, "s": [414.667, 313.22, 0], "to": [-0.039, 0.015, 0], "ti": [0.042, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.16}, "t": 428, "s": [414.545, 313.268, 0], "to": [-0.042, 0.017, 0], "ti": [0.045, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 429, "s": [414.414, 313.325, 0], "to": [-0.045, 0.02, 0], "ti": [0.046, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 430, "s": [414.278, 313.39, 0], "to": [-0.046, 0.023, 0], "ti": [0.045, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 431, "s": [414.14, 313.465, 0], "to": [-0.045, 0.027, 0], "ti": [0.043, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 432, "s": [414.007, 313.549, 0], "to": [-0.043, 0.03, 0], "ti": [0.04, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 433, "s": [413.881, 313.644, 0], "to": [-0.04, 0.033, 0], "ti": [0.034, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 434, "s": [413.769, 313.749, 0], "to": [-0.034, 0.037, 0], "ti": [0.027, -0.04, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 435, "s": [413.675, 313.864, 0], "to": [-0.027, 0.04, 0], "ti": [0.019, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.168}, "t": 436, "s": [413.604, 313.99, 0], "to": [-0.019, 0.044, 0], "ti": [0.008, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.164}, "t": 437, "s": [413.563, 314.125, 0], "to": [-0.008, 0.047, 0], "ti": [-0.005, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.16}, "t": 438, "s": [413.557, 314.271, 0], "to": [0.005, 0.05, 0], "ti": [-0.02, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.155}, "t": 439, "s": [413.592, 314.426, 0], "to": [0.02, 0.053, 0], "ti": [-0.037, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.153}, "t": 440, "s": [413.676, 314.59, 0], "to": [0.037, 0.056, 0], "ti": [-0.056, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.152}, "t": 441, "s": [413.814, 314.762, 0], "to": [0.056, 0.058, 0], "ti": [-0.075, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.156}, "t": 442, "s": [414.012, 314.94, 0], "to": [0.075, 0.059, 0], "ti": [-0.09, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 443, "s": [414.262, 315.116, 0], "to": [0.09, 0.058, 0], "ti": [-0.102, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 444, "s": [414.554, 315.286, 0], "to": [0.102, 0.055, 0], "ti": [-0.111, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 445, "s": [414.877, 315.446, 0], "to": [0.111, 0.051, 0], "ti": [-0.117, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 446, "s": [415.221, 315.594, 0], "to": [0.117, 0.047, 0], "ti": [-0.119, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 447, "s": [415.576, 315.727, 0], "to": [0.119, 0.041, 0], "ti": [-0.119, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 448, "s": [415.936, 315.842, 0], "to": [0.119, 0.035, 0], "ti": [-0.117, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 449, "s": [416.291, 315.938, 0], "to": [0.117, 0.029, 0], "ti": [-0.112, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 450, "s": [416.635, 316.014, 0], "to": [0.112, 0.021, 0], "ti": [-0.105, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 451, "s": [416.962, 316.067, 0], "to": [0.105, 0.014, 0], "ti": [-0.097, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 452, "s": [417.266, 316.097, 0], "to": [0.097, 0.006, 0], "ti": [-0.087, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 453, "s": [417.543, 316.104, 0], "to": [0.087, -0.001, 0], "ti": [-0.076, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 454, "s": [417.788, 316.088, 0], "to": [0.076, -0.009, 0], "ti": [-0.064, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 455, "s": [417.998, 316.049, 0], "to": [0.064, -0.017, 0], "ti": [-0.051, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.181}, "t": 456, "s": [418.171, 315.988, 0], "to": [0.051, -0.024, 0], "ti": [-0.038, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.178}, "t": 457, "s": [418.304, 315.905, 0], "to": [0.038, -0.031, 0], "ti": [-0.024, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.171}, "t": 458, "s": [418.397, 315.803, 0], "to": [0.024, -0.037, 0], "ti": [-0.01, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.163}, "t": 459, "s": [418.448, 315.683, 0], "to": [0.01, -0.043, 0], "ti": [0.003, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.158}, "t": 460, "s": [418.458, 315.547, 0], "to": [-0.003, -0.048, 0], "ti": [0.016, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.156}, "t": 461, "s": [418.428, 315.397, 0], "to": [-0.016, -0.051, 0], "ti": [0.029, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 462, "s": [418.36, 315.238, 0], "to": [-0.029, -0.054, 0], "ti": [0.04, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 463, "s": [418.256, 315.071, 0], "to": [-0.04, -0.056, 0], "ti": [0.051, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 464, "s": [418.118, 314.901, 0], "to": [-0.051, -0.057, 0], "ti": [0.06, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 465, "s": [417.951, 314.731, 0], "to": [-0.06, -0.056, 0], "ti": [0.067, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 466, "s": [417.76, 314.567, 0], "to": [-0.067, -0.053, 0], "ti": [0.073, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 467, "s": [417.549, 314.412, 0], "to": [-0.073, -0.049, 0], "ti": [0.076, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 468, "s": [417.324, 314.272, 0], "to": [-0.076, -0.043, 0], "ti": [0.077, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 469, "s": [417.092, 314.153, 0], "to": [-0.077, -0.035, 0], "ti": [0.076, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.173}, "t": 470, "s": [416.861, 314.06, 0], "to": [-0.076, -0.026, 0], "ti": [0.072, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.176}, "t": 471, "s": [416.638, 313.999, 0], "to": [-0.072, -0.015, 0], "ti": [0.065, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 472, "s": [416.431, 313.972, 0], "to": [-0.065, -0.007, 0], "ti": [0.058, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 473, "s": [416.246, 313.958, 0], "to": [-0.058, -0.002, 0], "ti": [0.051, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 474, "s": [416.081, 313.957, 0], "to": [-0.051, 0.001, 0], "ti": [0.045, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 475, "s": [415.937, 313.965, 0], "to": [-0.045, 0.004, 0], "ti": [0.038, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 476, "s": [415.813, 313.981, 0], "to": [-0.038, 0.006, 0], "ti": [0.032, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.182}, "t": 477, "s": [415.708, 314.004, 0], "to": [-0.032, 0.008, 0], "ti": [0.026, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.184}, "t": 478, "s": [415.622, 314.031, 0], "to": [-0.026, 0.01, 0], "ti": [0.02, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.186}, "t": 479, "s": [415.553, 314.062, 0], "to": [-0.02, 0.011, 0], "ti": [0.015, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 480, "s": [415.502, 314.095, 0], "to": [-0.015, 0.011, 0], "ti": [0.009, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.187}, "t": 481, "s": [415.466, 314.129, 0], "to": [-0.009, 0.011, 0], "ti": [0.005, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.183}, "t": 482, "s": [415.445, 314.162, 0], "to": [-0.005, 0.011, 0], "ti": [0.001, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.175}, "t": 483, "s": [415.437, 314.195, 0], "to": [-0.001, 0.01, 0], "ti": [-0.003, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.166}, "t": 484, "s": [415.441, 314.224, 0], "to": [0.003, 0.009, 0], "ti": [-0.006, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.161}, "t": 485, "s": [415.456, 314.251, 0], "to": [0.006, 0.008, 0], "ti": [-0.009, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 486, "s": [415.48, 314.273, 0], "to": [0.009, 0.007, 0], "ti": [-0.011, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 487, "s": [415.51, 314.291, 0], "to": [0.011, 0.005, 0], "ti": [-0.012, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 488, "s": [415.546, 314.303, 0], "to": [0.012, 0.003, 0], "ti": [-0.013, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 489, "s": [415.585, 314.309, 0], "to": [0.013, 0.001, 0], "ti": [-0.013, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 490, "s": [415.625, 314.308, 0], "to": [0.013, -0.001, 0], "ti": [-0.012, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 491, "s": [415.665, 314.301, 0], "to": [0.012, -0.004, 0], "ti": [-0.011, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.17}, "t": 492, "s": [415.7, 314.286, 0], "to": [0.011, -0.006, 0], "ti": [-0.009, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.168}, "t": 493, "s": [415.73, 314.265, 0], "to": [0.009, -0.008, 0], "ti": [-0.005, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.163}, "t": 494, "s": [415.751, 314.236, 0], "to": [0.005, -0.011, 0], "ti": [-0.001, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.155}, "t": 495, "s": [415.762, 314.199, 0], "to": [0.001, -0.013, 0], "ti": [0.004, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.15}, "t": 496, "s": [415.758, 314.156, 0], "to": [-0.004, -0.016, 0], "ti": [0.01, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.148}, "t": 497, "s": [415.737, 314.105, 0], "to": [-0.01, -0.018, 0], "ti": [0.017, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.148}, "t": 498, "s": [415.697, 314.047, 0], "to": [-0.017, -0.02, 0], "ti": [0.025, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.149}, "t": 499, "s": [415.634, 313.983, 0], "to": [-0.025, -0.022, 0], "ti": [0.035, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.151}, "t": 500, "s": [415.544, 313.913, 0], "to": [-0.035, -0.024, 0], "ti": [0.044, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.152}, "t": 501, "s": [415.425, 313.838, 0], "to": [-0.044, -0.027, 0], "ti": [0.051, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.156}, "t": 502, "s": [415.278, 313.75, 0], "to": [-0.051, -0.033, 0], "ti": [0.055, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.16}, "t": 503, "s": [415.118, 313.637, 0], "to": [-0.055, -0.041, 0], "ti": [0.057, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.163}, "t": 504, "s": [414.95, 313.505, 0], "to": [-0.057, -0.046, 0], "ti": [0.057, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 505, "s": [414.778, 313.363, 0], "to": [-0.057, -0.048, 0], "ti": [0.057, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 506, "s": [414.606, 313.218, 0], "to": [-0.057, -0.048, 0], "ti": [0.055, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 507, "s": [414.438, 313.077, 0], "to": [-0.055, -0.045, 0], "ti": [0.053, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 508, "s": [414.275, 312.946, 0], "to": [-0.053, -0.041, 0], "ti": [0.049, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 509, "s": [414.122, 312.831, 0], "to": [-0.049, -0.035, 0], "ti": [0.045, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.18}, "t": 510, "s": [413.98, 312.736, 0], "to": [-0.045, -0.028, 0], "ti": [0.04, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.184}, "t": 511, "s": [413.852, 312.665, 0], "to": [-0.04, -0.019, 0], "ti": [0.035, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.186}, "t": 512, "s": [413.738, 312.623, 0], "to": [-0.035, -0.009, 0], "ti": [0.029, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.182}, "t": 513, "s": [413.641, 312.612, 0], "to": [-0.029, 0.002, 0], "ti": [0.024, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.165}, "t": 514, "s": [413.561, 312.634, 0], "to": [-0.024, 0.013, 0], "ti": [0.017, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.152}, "t": 515, "s": [413.5, 312.692, 0], "to": [-0.017, 0.025, 0], "ti": [0.011, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 516, "s": [413.457, 312.785, 0], "to": [-0.011, 0.037, 0], "ti": [0.005, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.15}, "t": 517, "s": [413.433, 312.915, 0], "to": [-0.005, 0.049, 0], "ti": [-0.001, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.152}, "t": 518, "s": [413.427, 313.08, 0], "to": [0.001, 0.061, 0], "ti": [-0.007, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 519, "s": [413.438, 313.281, 0], "to": [0.007, 0.072, 0], "ti": [-0.012, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 520, "s": [413.467, 313.515, 0], "to": [0.012, 0.083, 0], "ti": [-0.017, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 521, "s": [413.511, 313.78, 0], "to": [0.017, 0.093, 0], "ti": [-0.021, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 522, "s": [413.568, 314.074, 0], "to": [0.021, 0.102, 0], "ti": [-0.025, -0.11, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 523, "s": [413.638, 314.392, 0], "to": [0.025, 0.11, 0], "ti": [-0.028, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 524, "s": [413.717, 314.731, 0], "to": [0.028, 0.116, 0], "ti": [-0.029, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 525, "s": [413.804, 315.085, 0], "to": [0.029, 0.12, 0], "ti": [-0.03, -0.122, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 526, "s": [413.894, 315.45, 0], "to": [0.03, 0.122, 0], "ti": [-0.03, -0.122, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 527, "s": [413.985, 315.818, 0], "to": [0.03, 0.122, 0], "ti": [-0.028, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 528, "s": [414.074, 316.184, 0], "to": [0.028, 0.12, 0], "ti": [-0.025, -0.115, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 529, "s": [414.156, 316.54, 0], "to": [0.025, 0.115, 0], "ti": [-0.021, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.174}, "t": 530, "s": [414.227, 316.877, 0], "to": [0.021, 0.108, 0], "ti": [-0.017, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.172}, "t": 531, "s": [414.282, 317.187, 0], "to": [0.017, 0.1, 0], "ti": [-0.019, -0.099, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 532, "s": [414.33, 317.478, 0], "to": [0.019, 0.099, 0], "ti": [-0.025, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 533, "s": [414.398, 317.784, 0], "to": [0.025, 0.103, 0], "ti": [-0.029, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 534, "s": [414.481, 318.098, 0], "to": [0.029, 0.105, 0], "ti": [-0.032, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 535, "s": [414.574, 318.413, 0], "to": [0.032, 0.104, 0], "ti": [-0.033, -0.1, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 536, "s": [414.671, 318.719, 0], "to": [0.033, 0.1, 0], "ti": [-0.032, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 537, "s": [414.769, 319.012, 0], "to": [0.032, 0.094, 0], "ti": [-0.03, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 538, "s": [414.862, 319.285, 0], "to": [0.03, 0.087, 0], "ti": [-0.026, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.178}, "t": 539, "s": [414.947, 319.532, 0], "to": [0.026, 0.077, 0], "ti": [-0.022, -0.067, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.182}, "t": 540, "s": [415.02, 319.75, 0], "to": [0.022, 0.067, 0], "ti": [-0.017, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.189}, "t": 541, "s": [415.079, 319.934, 0], "to": [0.017, 0.055, 0], "ti": [-0.011, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.199}, "t": 542, "s": [415.12, 320.081, 0], "to": [0.011, 0.042, 0], "ti": [-0.004, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.22}, "t": 543, "s": [415.142, 320.188, 0], "to": [0.004, 0.029, 0], "ti": [0.003, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.802}, "o": {"x": 0.167, "y": 0.241}, "t": 544, "s": [415.143, 320.255, 0], "to": [-0.003, 0.015, 0], "ti": [0.011, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.763}, "o": {"x": 0.167, "y": 0.144}, "t": 545, "s": [415.122, 320.281, 0], "to": [-0.011, 0.002, 0], "ti": [0.019, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.128}, "t": 546, "s": [415.077, 320.264, 0], "to": [-0.019, -0.012, 0], "ti": [0.027, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.139}, "t": 547, "s": [415.008, 320.207, 0], "to": [-0.027, -0.026, 0], "ti": [0.035, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.146}, "t": 548, "s": [414.916, 320.109, 0], "to": [-0.035, -0.039, 0], "ti": [0.042, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.151}, "t": 549, "s": [414.8, 319.974, 0], "to": [-0.042, -0.051, 0], "ti": [0.05, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 550, "s": [414.661, 319.803, 0], "to": [-0.05, -0.062, 0], "ti": [0.057, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 551, "s": [414.501, 319.601, 0], "to": [-0.057, -0.072, 0], "ti": [0.063, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 552, "s": [414.322, 319.372, 0], "to": [-0.063, -0.08, 0], "ti": [0.068, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 553, "s": [414.125, 319.12, 0], "to": [-0.068, -0.087, 0], "ti": [0.073, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 554, "s": [413.912, 318.852, 0], "to": [-0.073, -0.091, 0], "ti": [0.076, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 555, "s": [413.687, 318.573, 0], "to": [-0.076, -0.093, 0], "ti": [0.079, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 556, "s": [413.454, 318.291, 0], "to": [-0.079, -0.093, 0], "ti": [0.08, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 557, "s": [413.216, 318.014, 0], "to": [-0.08, -0.09, 0], "ti": [0.079, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.171}, "t": 558, "s": [412.976, 317.75, 0], "to": [-0.079, -0.084, 0], "ti": [0.077, 0.075, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 559, "s": [412.741, 317.508, 0], "to": [-0.077, -0.075, 0], "ti": [0.073, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 560, "s": [412.514, 317.3, 0], "to": [-0.073, -0.062, 0], "ti": [0.066, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.182}, "t": 561, "s": [412.302, 317.135, 0], "to": [-0.066, -0.049, 0], "ti": [0.056, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.178}, "t": 562, "s": [412.116, 317.005, 0], "to": [-0.056, -0.044, 0], "ti": [0.043, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.177}, "t": 563, "s": [411.968, 316.871, 0], "to": [-0.043, -0.045, 0], "ti": [0.031, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.177}, "t": 564, "s": [411.858, 316.733, 0], "to": [-0.031, -0.046, 0], "ti": [0.02, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.175}, "t": 565, "s": [411.782, 316.594, 0], "to": [-0.02, -0.046, 0], "ti": [0.01, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.173}, "t": 566, "s": [411.739, 316.458, 0], "to": [-0.01, -0.045, 0], "ti": [0, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.171}, "t": 567, "s": [411.725, 316.326, 0], "to": [0, -0.043, 0], "ti": [-0.008, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.169}, "t": 568, "s": [411.737, 316.2, 0], "to": [0.008, -0.041, 0], "ti": [-0.016, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 569, "s": [411.775, 316.082, 0], "to": [0.016, -0.037, 0], "ti": [-0.023, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.166}, "t": 570, "s": [411.833, 315.975, 0], "to": [0.023, -0.034, 0], "ti": [-0.029, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 571, "s": [411.911, 315.879, 0], "to": [0.029, -0.03, 0], "ti": [-0.033, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 572, "s": [412.004, 315.794, 0], "to": [0.033, -0.026, 0], "ti": [-0.037, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 573, "s": [412.111, 315.723, 0], "to": [0.037, -0.021, 0], "ti": [-0.041, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 574, "s": [412.229, 315.666, 0], "to": [0.041, -0.017, 0], "ti": [-0.043, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 575, "s": [412.355, 315.623, 0], "to": [0.043, -0.012, 0], "ti": [-0.044, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 576, "s": [412.485, 315.594, 0], "to": [0.044, -0.007, 0], "ti": [-0.044, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 577, "s": [412.618, 315.578, 0], "to": [0.044, -0.003, 0], "ti": [-0.043, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 578, "s": [412.75, 315.577, 0], "to": [0.043, 0.002, 0], "ti": [-0.042, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 579, "s": [412.879, 315.588, 0], "to": [0.042, 0.006, 0], "ti": [-0.039, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 580, "s": [413.001, 315.611, 0], "to": [0.039, 0.01, 0], "ti": [-0.035, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 581, "s": [413.113, 315.645, 0], "to": [0.035, 0.013, 0], "ti": [-0.031, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 582, "s": [413.213, 315.689, 0], "to": [0.031, 0.016, 0], "ti": [-0.025, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 583, "s": [413.297, 315.74, 0], "to": [0.025, 0.018, 0], "ti": [-0.018, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.18}, "t": 584, "s": [413.363, 315.798, 0], "to": [0.018, 0.02, 0], "ti": [-0.011, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.179}, "t": 585, "s": [413.407, 315.859, 0], "to": [0.011, 0.021, 0], "ti": [-0.002, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.171}, "t": 586, "s": [413.426, 315.921, 0], "to": [0.002, 0.021, 0], "ti": [0.008, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.157}, "t": 587, "s": [413.417, 315.983, 0], "to": [-0.008, 0.02, 0], "ti": [0.019, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.149}, "t": 588, "s": [413.377, 316.04, 0], "to": [-0.019, 0.018, 0], "ti": [0.031, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.146}, "t": 589, "s": [413.303, 316.09, 0], "to": [-0.031, 0.015, 0], "ti": [0.044, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.147}, "t": 590, "s": [413.192, 316.129, 0], "to": [-0.044, 0.011, 0], "ti": [0.057, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.151}, "t": 591, "s": [413.039, 316.154, 0], "to": [-0.057, 0.009, 0], "ti": [0.066, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.156}, "t": 592, "s": [412.85, 316.182, 0], "to": [-0.066, 0.016, 0], "ti": [0.07, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 593, "s": [412.644, 316.251, 0], "to": [-0.07, 0.029, 0], "ti": [0.072, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 594, "s": [412.429, 316.358, 0], "to": [-0.072, 0.041, 0], "ti": [0.07, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 595, "s": [412.214, 316.499, 0], "to": [-0.07, 0.052, 0], "ti": [0.067, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 596, "s": [412.006, 316.669, 0], "to": [-0.067, 0.061, 0], "ti": [0.061, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 597, "s": [411.812, 316.864, 0], "to": [-0.061, 0.068, 0], "ti": [0.054, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 598, "s": [411.638, 317.08, 0], "to": [-0.054, 0.075, 0], "ti": [0.045, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 599, "s": [411.489, 317.312, 0], "to": [-0.045, 0.08, 0], "ti": [0.034, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.168}, "t": 600, "s": [411.37, 317.558, 0], "to": [-0.034, 0.083, 0], "ti": [0.022, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.168}, "t": 601, "s": [411.285, 317.813, 0], "to": [-0.022, 0.086, 0], "ti": [0.01, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 602, "s": [411.236, 318.074, 0], "to": [-0.01, 0.088, 0], "ti": [-0.003, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.166}, "t": 603, "s": [411.226, 318.338, 0], "to": [0.003, 0.088, 0], "ti": [-0.017, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 604, "s": [411.257, 318.601, 0], "to": [0.017, 0.087, 0], "ti": [-0.031, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 605, "s": [411.329, 318.862, 0], "to": [0.031, 0.086, 0], "ti": [-0.045, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.163}, "t": 606, "s": [411.443, 319.116, 0], "to": [0.045, 0.083, 0], "ti": [-0.058, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.163}, "t": 607, "s": [411.597, 319.362, 0], "to": [0.058, 0.08, 0], "ti": [-0.071, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.163}, "t": 608, "s": [411.792, 319.596, 0], "to": [0.071, 0.076, 0], "ti": [-0.083, -0.071, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.163}, "t": 609, "s": [412.024, 319.817, 0], "to": [0.083, 0.071, 0], "ti": [-0.094, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 610, "s": [412.291, 320.023, 0], "to": [0.094, 0.066, 0], "ti": [-0.104, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 611, "s": [412.59, 320.211, 0], "to": [0.104, 0.06, 0], "ti": [-0.112, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 612, "s": [412.915, 320.38, 0], "to": [0.112, 0.053, 0], "ti": [-0.119, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 613, "s": [413.264, 320.528, 0], "to": [0.119, 0.046, 0], "ti": [-0.123, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 614, "s": [413.628, 320.654, 0], "to": [0.123, 0.038, 0], "ti": [-0.126, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 615, "s": [414.003, 320.755, 0], "to": [0.126, 0.03, 0], "ti": [-0.125, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 616, "s": [414.381, 320.832, 0], "to": [0.125, 0.021, 0], "ti": [-0.122, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 617, "s": [414.755, 320.883, 0], "to": [0.122, 0.013, 0], "ti": [-0.116, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.172}, "t": 618, "s": [415.115, 320.907, 0], "to": [0.116, 0.004, 0], "ti": [-0.107, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 619, "s": [415.452, 320.904, 0], "to": [0.107, -0.006, 0], "ti": [-0.094, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 620, "s": [415.757, 320.873, 0], "to": [0.094, -0.015, 0], "ti": [-0.078, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.183}, "t": 621, "s": [416.018, 320.814, 0], "to": [0.078, -0.024, 0], "ti": [-0.059, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.183}, "t": 622, "s": [416.224, 320.726, 0], "to": [0.059, -0.034, 0], "ti": [-0.04, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.176}, "t": 623, "s": [416.373, 320.611, 0], "to": [0.04, -0.043, 0], "ti": [-0.023, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.166}, "t": 624, "s": [416.467, 320.471, 0], "to": [0.023, -0.051, 0], "ti": [-0.006, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.159}, "t": 625, "s": [416.51, 320.306, 0], "to": [0.006, -0.058, 0], "ti": [0.009, 0.065, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.157}, "t": 626, "s": [416.505, 320.12, 0], "to": [-0.009, -0.065, 0], "ti": [0.023, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.157}, "t": 627, "s": [416.457, 319.914, 0], "to": [-0.023, -0.072, 0], "ti": [0.036, 0.077, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 628, "s": [416.368, 319.69, 0], "to": [-0.036, -0.077, 0], "ti": [0.047, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 629, "s": [416.242, 319.45, 0], "to": [-0.047, -0.082, 0], "ti": [0.058, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 630, "s": [416.083, 319.196, 0], "to": [-0.058, -0.087, 0], "ti": [0.067, 0.091, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 631, "s": [415.896, 318.929, 0], "to": [-0.067, -0.091, 0], "ti": [0.075, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 632, "s": [415.682, 318.651, 0], "to": [-0.075, -0.094, 0], "ti": [0.081, 0.096, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 633, "s": [415.448, 318.365, 0], "to": [-0.081, -0.096, 0], "ti": [0.086, 0.098, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 634, "s": [415.197, 318.073, 0], "to": [-0.086, -0.098, 0], "ti": [0.09, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 635, "s": [414.932, 317.775, 0], "to": [-0.09, -0.1, 0], "ti": [0.092, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 636, "s": [414.659, 317.475, 0], "to": [-0.092, -0.1, 0], "ti": [0.093, 0.1, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 637, "s": [414.381, 317.174, 0], "to": [-0.093, -0.1, 0], "ti": [0.092, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 638, "s": [414.102, 316.875, 0], "to": [-0.092, -0.099, 0], "ti": [0.09, 0.098, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 639, "s": [413.828, 316.578, 0], "to": [-0.09, -0.098, 0], "ti": [0.086, 0.096, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 640, "s": [413.562, 316.287, 0], "to": [-0.086, -0.096, 0], "ti": [0.081, 0.093, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 641, "s": [413.31, 316.003, 0], "to": [-0.081, -0.093, 0], "ti": [0.075, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 642, "s": [413.075, 315.728, 0], "to": [-0.075, -0.09, 0], "ti": [0.066, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 643, "s": [412.862, 315.464, 0], "to": [-0.066, -0.086, 0], "ti": [0.056, 0.081, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 644, "s": [412.677, 315.213, 0], "to": [-0.056, -0.081, 0], "ti": [0.045, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 645, "s": [412.524, 314.977, 0], "to": [-0.045, -0.076, 0], "ti": [0.032, 0.07, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.18}, "t": 646, "s": [412.407, 314.759, 0], "to": [-0.032, -0.07, 0], "ti": [0.017, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.182}, "t": 647, "s": [412.333, 314.559, 0], "to": [-0.017, -0.063, 0], "ti": [0.001, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.179}, "t": 648, "s": [412.305, 314.381, 0], "to": [-0.001, -0.055, 0], "ti": [-0.018, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.169}, "t": 649, "s": [412.329, 314.227, 0], "to": [0.018, -0.047, 0], "ti": [-0.038, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.156}, "t": 650, "s": [412.411, 314.098, 0], "to": [0.038, -0.038, 0], "ti": [-0.055, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.155}, "t": 651, "s": [412.555, 313.996, 0], "to": [0.055, -0.031, 0], "ti": [-0.063, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 652, "s": [412.742, 313.912, 0], "to": [0.063, -0.028, 0], "ti": [-0.063, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 653, "s": [412.931, 313.828, 0], "to": [0.063, -0.028, 0], "ti": [-0.062, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 654, "s": [413.12, 313.747, 0], "to": [0.062, -0.027, 0], "ti": [-0.061, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 655, "s": [413.305, 313.669, 0], "to": [0.061, -0.025, 0], "ti": [-0.059, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 656, "s": [413.486, 313.598, 0], "to": [0.059, -0.023, 0], "ti": [-0.057, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 657, "s": [413.661, 313.534, 0], "to": [0.057, -0.02, 0], "ti": [-0.054, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 658, "s": [413.828, 313.478, 0], "to": [0.054, -0.017, 0], "ti": [-0.051, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 659, "s": [413.985, 313.433, 0], "to": [0.051, -0.013, 0], "ti": [-0.047, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.175}, "t": 660, "s": [414.132, 313.399, 0], "to": [0.047, -0.009, 0], "ti": [-0.043, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.176}, "t": 661, "s": [414.268, 313.376, 0], "to": [0.043, -0.005, 0], "ti": [-0.039, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.176}, "t": 662, "s": [414.392, 313.366, 0], "to": [0.039, -0.001, 0], "ti": [-0.035, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.176}, "t": 663, "s": [414.504, 313.368, 0], "to": [0.035, 0.003, 0], "ti": [-0.031, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.175}, "t": 664, "s": [414.603, 313.383, 0], "to": [0.031, 0.007, 0], "ti": [-0.027, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.173}, "t": 665, "s": [414.69, 313.411, 0], "to": [0.027, 0.011, 0], "ti": [-0.023, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.17}, "t": 666, "s": [414.764, 313.451, 0], "to": [0.023, 0.015, 0], "ti": [-0.019, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.167}, "t": 667, "s": [414.827, 313.502, 0], "to": [0.019, 0.019, 0], "ti": [-0.015, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 668, "s": [414.878, 313.565, 0], "to": [0.015, 0.023, 0], "ti": [-0.012, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.163}, "t": 669, "s": [414.918, 313.638, 0], "to": [0.012, 0.026, 0], "ti": [-0.009, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 670, "s": [414.949, 313.719, 0], "to": [0.009, 0.029, 0], "ti": [-0.006, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 671, "s": [414.972, 313.809, 0], "to": [0.006, 0.031, 0], "ti": [-0.004, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 672, "s": [414.988, 313.904, 0], "to": [0.004, 0.033, 0], "ti": [-0.003, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 673, "s": [414.999, 314.004, 0], "to": [0.003, 0.034, 0], "ti": [-0.002, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 674, "s": [415.006, 314.106, 0], "to": [0.002, 0.034, 0], "ti": [-0.002, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 675, "s": [415.013, 314.209, 0], "to": [0.002, 0.034, 0], "ti": [-0.003, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 676, "s": [415.021, 314.309, 0], "to": [0.003, 0.033, 0], "ti": [-0.005, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 677, "s": [415.032, 314.404, 0], "to": [0.005, 0.031, 0], "ti": [-0.008, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.174}, "t": 678, "s": [415.05, 314.492, 0], "to": [0.008, 0.027, 0], "ti": [-0.011, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.174}, "t": 679, "s": [415.078, 314.569, 0], "to": [0.011, 0.023, 0], "ti": [-0.016, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.17}, "t": 680, "s": [415.118, 314.633, 0], "to": [0.016, 0.018, 0], "ti": [-0.019, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 681, "s": [415.174, 314.679, 0], "to": [0.019, 0.015, 0], "ti": [-0.016, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.166}, "t": 682, "s": [415.233, 314.721, 0], "to": [0.016, 0.017, 0], "ti": [-0.01, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.159}, "t": 683, "s": [415.271, 314.783, 0], "to": [0.01, 0.023, 0], "ti": [-0.003, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 684, "s": [415.29, 314.861, 0], "to": [0.003, 0.028, 0], "ti": [0.002, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 685, "s": [415.292, 314.95, 0], "to": [-0.002, 0.031, 0], "ti": [0.008, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 686, "s": [415.276, 315.049, 0], "to": [-0.008, 0.034, 0], "ti": [0.013, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 687, "s": [415.245, 315.153, 0], "to": [-0.013, 0.035, 0], "ti": [0.017, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 688, "s": [415.2, 315.259, 0], "to": [-0.017, 0.036, 0], "ti": [0.021, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 689, "s": [415.143, 315.366, 0], "to": [-0.021, 0.035, 0], "ti": [0.025, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 690, "s": [415.074, 315.47, 0], "to": [-0.025, 0.034, 0], "ti": [0.028, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 691, "s": [414.996, 315.57, 0], "to": [-0.028, 0.032, 0], "ti": [0.03, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 692, "s": [414.909, 315.663, 0], "to": [-0.03, 0.03, 0], "ti": [0.032, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 693, "s": [414.815, 315.748, 0], "to": [-0.032, 0.027, 0], "ti": [0.034, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 694, "s": [414.715, 315.824, 0], "to": [-0.034, 0.023, 0], "ti": [0.035, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 695, "s": [414.612, 315.888, 0], "to": [-0.035, 0.019, 0], "ti": [0.036, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.17}, "t": 696, "s": [414.506, 315.94, 0], "to": [-0.036, 0.015, 0], "ti": [0.036, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 697, "s": [414.399, 315.98, 0], "to": [-0.036, 0.011, 0], "ti": [0.035, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 698, "s": [414.292, 316.006, 0], "to": [-0.035, 0.006, 0], "ti": [0.034, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 699, "s": [414.187, 316.018, 0], "to": [-0.034, 0.002, 0], "ti": [0.033, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.17}, "t": 700, "s": [414.086, 316.016, 0], "to": [-0.033, -0.003, 0], "ti": [0.031, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.17}, "t": 701, "s": [413.99, 316, 0], "to": [-0.031, -0.007, 0], "ti": [0.028, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 702, "s": [413.901, 315.971, 0], "to": [-0.028, -0.012, 0], "ti": [0.025, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.168}, "t": 703, "s": [413.82, 315.929, 0], "to": [-0.025, -0.016, 0], "ti": [0.022, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.168}, "t": 704, "s": [413.749, 315.875, 0], "to": [-0.022, -0.02, 0], "ti": [0.018, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 705, "s": [413.689, 315.81, 0], "to": [-0.018, -0.023, 0], "ti": [0.013, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.166}, "t": 706, "s": [413.643, 315.735, 0], "to": [-0.013, -0.026, 0], "ti": [0.008, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.165}, "t": 707, "s": [413.611, 315.652, 0], "to": [-0.008, -0.029, 0], "ti": [0.002, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 708, "s": [413.596, 315.562, 0], "to": [-0.002, -0.031, 0], "ti": [-0.004, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.162}, "t": 709, "s": [413.6, 315.468, 0], "to": [0.004, -0.032, 0], "ti": [-0.011, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.161}, "t": 710, "s": [413.623, 315.371, 0], "to": [0.011, -0.032, 0], "ti": [-0.018, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.174}, "t": 711, "s": [413.668, 315.274, 0], "to": [0.018, -0.029, 0], "ti": [-0.022, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.19}, "t": 712, "s": [413.729, 315.199, 0], "to": [0.022, -0.018, 0], "ti": [-0.024, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.166}, "t": 713, "s": [413.798, 315.168, 0], "to": [0.024, -0.003, 0], "ti": [-0.026, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.151}, "t": 714, "s": [413.873, 315.179, 0], "to": [0.026, 0.01, 0], "ti": [-0.027, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.149}, "t": 715, "s": [413.952, 315.228, 0], "to": [0.027, 0.022, 0], "ti": [-0.029, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 716, "s": [414.036, 315.312, 0], "to": [0.029, 0.033, 0], "ti": [-0.029, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 717, "s": [414.124, 315.426, 0], "to": [0.029, 0.043, 0], "ti": [-0.03, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 718, "s": [414.213, 315.568, 0], "to": [0.03, 0.051, 0], "ti": [-0.031, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 719, "s": [414.304, 315.733, 0], "to": [0.031, 0.059, 0], "ti": [-0.031, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 720, "s": [414.397, 315.919, 0], "to": [0.031, 0.065, 0], "ti": [-0.031, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 721, "s": [414.489, 316.123, 0], "to": [0.031, 0.07, 0], "ti": [-0.03, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 722, "s": [414.581, 316.34, 0], "to": [0.03, 0.074, 0], "ti": [-0.03, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 723, "s": [414.672, 316.568, 0], "to": [0.03, 0.077, 0], "ti": [-0.029, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 724, "s": [414.761, 316.803, 0], "to": [0.029, 0.079, 0], "ti": [-0.029, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 725, "s": [414.848, 317.042, 0], "to": [0.029, 0.08, 0], "ti": [-0.028, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 726, "s": [414.933, 317.283, 0], "to": [0.028, 0.08, 0], "ti": [-0.027, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 727, "s": [415.015, 317.521, 0], "to": [0.027, 0.079, 0], "ti": [-0.026, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 728, "s": [415.094, 317.754, 0], "to": [0.026, 0.076, 0], "ti": [-0.025, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 729, "s": [415.17, 317.979, 0], "to": [0.025, 0.073, 0], "ti": [-0.023, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 730, "s": [415.241, 318.192, 0], "to": [0.023, 0.069, 0], "ti": [-0.022, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 731, "s": [415.309, 318.392, 0], "to": [0.022, 0.064, 0], "ti": [-0.021, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 732, "s": [415.373, 318.574, 0], "to": [0.021, 0.057, 0], "ti": [-0.019, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 733, "s": [415.433, 318.737, 0], "to": [0.019, 0.05, 0], "ti": [-0.018, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.183}, "t": 734, "s": [415.489, 318.877, 0], "to": [0.018, 0.042, 0], "ti": [-0.017, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.19}, "t": 735, "s": [415.54, 318.991, 0], "to": [0.017, 0.033, 0], "ti": [-0.015, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.2}, "t": 736, "s": [415.588, 319.077, 0], "to": [0.015, 0.023, 0], "ti": [-0.014, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.21}, "t": 737, "s": [415.632, 319.132, 0], "to": [0.014, 0.013, 0], "ti": [-0.013, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.787}, "o": {"x": 0.167, "y": 0.178}, "t": 738, "s": [415.673, 319.153, 0], "to": [0.013, 0.001, 0], "ti": [-0.012, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.786}, "o": {"x": 0.167, "y": 0.137}, "t": 739, "s": [415.71, 319.138, 0], "to": [0.012, -0.011, 0], "ti": [-0.011, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.136}, "t": 740, "s": [415.743, 319.085, 0], "to": [0.011, -0.025, 0], "ti": [-0.007, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.149}, "t": 741, "s": [415.774, 318.99, 0], "to": [0.007, -0.037, 0], "ti": [0.004, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.156}, "t": 742, "s": [415.784, 318.863, 0], "to": [-0.004, -0.045, 0], "ti": [0.016, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 743, "s": [415.753, 318.722, 0], "to": [-0.016, -0.049, 0], "ti": [0.027, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 744, "s": [415.687, 318.568, 0], "to": [-0.027, -0.053, 0], "ti": [0.035, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 745, "s": [415.593, 318.403, 0], "to": [-0.035, -0.056, 0], "ti": [0.042, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 746, "s": [415.475, 318.23, 0], "to": [-0.042, -0.059, 0], "ti": [0.047, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 747, "s": [415.34, 318.049, 0], "to": [-0.047, -0.061, 0], "ti": [0.051, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 748, "s": [415.193, 317.864, 0], "to": [-0.051, -0.062, 0], "ti": [0.053, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 749, "s": [415.037, 317.676, 0], "to": [-0.053, -0.063, 0], "ti": [0.053, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 750, "s": [414.878, 317.485, 0], "to": [-0.053, -0.063, 0], "ti": [0.052, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 751, "s": [414.719, 317.295, 0], "to": [-0.052, -0.063, 0], "ti": [0.05, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 752, "s": [414.564, 317.105, 0], "to": [-0.05, -0.063, 0], "ti": [0.047, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 753, "s": [414.416, 316.918, 0], "to": [-0.047, -0.062, 0], "ti": [0.043, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 754, "s": [414.279, 316.734, 0], "to": [-0.043, -0.061, 0], "ti": [0.039, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 755, "s": [414.155, 316.555, 0], "to": [-0.039, -0.059, 0], "ti": [0.033, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 756, "s": [414.047, 316.381, 0], "to": [-0.033, -0.057, 0], "ti": [0.027, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.174}, "t": 757, "s": [413.957, 316.213, 0], "to": [-0.027, -0.055, 0], "ti": [0.02, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.174}, "t": 758, "s": [413.886, 316.052, 0], "to": [-0.02, -0.052, 0], "ti": [0.013, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.174}, "t": 759, "s": [413.836, 315.899, 0], "to": [-0.013, -0.05, 0], "ti": [0.005, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.173}, "t": 760, "s": [413.809, 315.753, 0], "to": [-0.005, -0.047, 0], "ti": [-0.002, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.171}, "t": 761, "s": [413.804, 315.616, 0], "to": [0.002, -0.044, 0], "ti": [-0.01, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.168}, "t": 762, "s": [413.823, 315.487, 0], "to": [0.01, -0.042, 0], "ti": [-0.018, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.165}, "t": 763, "s": [413.866, 315.367, 0], "to": [0.018, -0.039, 0], "ti": [-0.026, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.163}, "t": 764, "s": [413.932, 315.255, 0], "to": [0.026, -0.036, 0], "ti": [-0.034, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.161}, "t": 765, "s": [414.022, 315.152, 0], "to": [0.034, -0.033, 0], "ti": [-0.041, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 766, "s": [414.134, 315.058, 0], "to": [0.041, -0.03, 0], "ti": [-0.048, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 767, "s": [414.268, 314.971, 0], "to": [0.048, -0.028, 0], "ti": [-0.054, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 768, "s": [414.421, 314.892, 0], "to": [0.054, -0.025, 0], "ti": [-0.06, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 769, "s": [414.593, 314.821, 0], "to": [0.06, -0.023, 0], "ti": [-0.065, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 770, "s": [414.781, 314.755, 0], "to": [0.065, -0.021, 0], "ti": [-0.068, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 771, "s": [414.983, 314.696, 0], "to": [0.068, -0.022, 0], "ti": [-0.07, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 772, "s": [415.192, 314.622, 0], "to": [0.07, -0.03, 0], "ti": [-0.07, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 773, "s": [415.402, 314.518, 0], "to": [0.07, -0.039, 0], "ti": [-0.069, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 774, "s": [415.61, 314.389, 0], "to": [0.069, -0.046, 0], "ti": [-0.068, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 775, "s": [415.816, 314.241, 0], "to": [0.068, -0.051, 0], "ti": [-0.066, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 776, "s": [416.017, 314.081, 0], "to": [0.066, -0.055, 0], "ti": [-0.065, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 777, "s": [416.214, 313.913, 0], "to": [0.065, -0.056, 0], "ti": [-0.062, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 778, "s": [416.405, 313.742, 0], "to": [0.062, -0.057, 0], "ti": [-0.06, 0.056, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 779, "s": [416.589, 313.573, 0], "to": [0.06, -0.056, 0], "ti": [-0.057, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 780, "s": [416.765, 313.409, 0], "to": [0.057, -0.053, 0], "ti": [-0.055, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 781, "s": [416.933, 313.254, 0], "to": [0.055, -0.05, 0], "ti": [-0.052, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 782, "s": [417.093, 313.111, 0], "to": [0.052, -0.045, 0], "ti": [-0.049, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 783, "s": [417.244, 312.984, 0], "to": [0.049, -0.04, 0], "ti": [-0.046, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 784, "s": [417.386, 312.874, 0], "to": [0.046, -0.033, 0], "ti": [-0.043, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 785, "s": [417.519, 312.783, 0], "to": [0.043, -0.027, 0], "ti": [-0.04, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.178}, "t": 786, "s": [417.643, 312.714, 0], "to": [0.04, -0.019, 0], "ti": [-0.037, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.177}, "t": 787, "s": [417.76, 312.667, 0], "to": [0.037, -0.012, 0], "ti": [-0.035, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.175}, "t": 788, "s": [417.868, 312.643, 0], "to": [0.035, -0.004, 0], "ti": [-0.033, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.17}, "t": 789, "s": [417.969, 312.643, 0], "to": [0.033, 0.004, 0], "ti": [-0.031, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.164}, "t": 790, "s": [418.064, 312.666, 0], "to": [0.031, 0.012, 0], "ti": [-0.029, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.16}, "t": 791, "s": [418.153, 312.712, 0], "to": [0.029, 0.019, 0], "ti": [-0.028, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 792, "s": [418.238, 312.781, 0], "to": [0.028, 0.026, 0], "ti": [-0.027, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 793, "s": [418.32, 312.871, 0], "to": [0.027, 0.033, 0], "ti": [-0.027, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 794, "s": [418.4, 312.981, 0], "to": [0.027, 0.039, 0], "ti": [-0.027, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 795, "s": [418.48, 313.108, 0], "to": [0.027, 0.045, 0], "ti": [-0.028, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 796, "s": [418.561, 313.25, 0], "to": [0.028, 0.05, 0], "ti": [-0.029, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 797, "s": [418.645, 313.405, 0], "to": [0.029, 0.053, 0], "ti": [-0.031, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 798, "s": [418.735, 313.57, 0], "to": [0.031, 0.056, 0], "ti": [-0.034, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 799, "s": [418.832, 313.74, 0], "to": [0.034, 0.057, 0], "ti": [-0.037, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.165}, "t": 800, "s": [418.938, 313.912, 0], "to": [0.037, 0.057, 0], "ti": [-0.039, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.172}, "t": 801, "s": [419.057, 314.081, 0], "to": [0.039, 0.054, 0], "ti": [-0.035, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.182}, "t": 802, "s": [419.171, 314.236, 0], "to": [0.035, 0.048, 0], "ti": [-0.027, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.185}, "t": 803, "s": [419.264, 314.37, 0], "to": [0.027, 0.041, 0], "ti": [-0.019, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.19}, "t": 804, "s": [419.335, 314.484, 0], "to": [0.019, 0.035, 0], "ti": [-0.012, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.195}, "t": 805, "s": [419.381, 314.578, 0], "to": [0.012, 0.028, 0], "ti": [-0.003, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.197}, "t": 806, "s": [419.404, 314.652, 0], "to": [0.003, 0.022, 0], "ti": [0.005, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.181}, "t": 807, "s": [419.401, 314.709, 0], "to": [-0.005, 0.016, 0], "ti": [0.013, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.155}, "t": 808, "s": [419.375, 314.75, 0], "to": [-0.013, 0.011, 0], "ti": [0.021, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.146}, "t": 809, "s": [419.323, 314.774, 0], "to": [-0.021, 0.006, 0], "ti": [0.029, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.148}, "t": 810, "s": [419.248, 314.784, 0], "to": [-0.029, 0.001, 0], "ti": [0.037, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.151}, "t": 811, "s": [419.149, 314.781, 0], "to": [-0.037, -0.003, 0], "ti": [0.044, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 812, "s": [419.027, 314.766, 0], "to": [-0.044, -0.007, 0], "ti": [0.051, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 813, "s": [418.884, 314.74, 0], "to": [-0.051, -0.01, 0], "ti": [0.057, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 814, "s": [418.721, 314.705, 0], "to": [-0.057, -0.013, 0], "ti": [0.063, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 815, "s": [418.54, 314.663, 0], "to": [-0.063, -0.015, 0], "ti": [0.068, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 816, "s": [418.342, 314.616, 0], "to": [-0.068, -0.017, 0], "ti": [0.073, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 817, "s": [418.13, 314.564, 0], "to": [-0.073, -0.018, 0], "ti": [0.076, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 818, "s": [417.906, 314.509, 0], "to": [-0.076, -0.018, 0], "ti": [0.079, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 819, "s": [417.673, 314.455, 0], "to": [-0.079, -0.018, 0], "ti": [0.08, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 820, "s": [417.434, 314.402, 0], "to": [-0.08, -0.017, 0], "ti": [0.081, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 821, "s": [417.192, 314.352, 0], "to": [-0.081, -0.016, 0], "ti": [0.08, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 822, "s": [416.951, 314.308, 0], "to": [-0.08, -0.013, 0], "ti": [0.078, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 823, "s": [416.714, 314.272, 0], "to": [-0.078, -0.01, 0], "ti": [0.074, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 824, "s": [416.485, 314.245, 0], "to": [-0.074, -0.007, 0], "ti": [0.069, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 825, "s": [416.269, 314.231, 0], "to": [-0.069, -0.002, 0], "ti": [0.063, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 826, "s": [416.069, 314.232, 0], "to": [-0.063, 0.003, 0], "ti": [0.055, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 827, "s": [415.891, 314.249, 0], "to": [-0.055, 0.009, 0], "ti": [0.045, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.18}, "t": 828, "s": [415.74, 314.287, 0], "to": [-0.045, 0.016, 0], "ti": [0.034, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.177}, "t": 829, "s": [415.62, 314.346, 0], "to": [-0.034, 0.024, 0], "ti": [0.02, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.166}, "t": 830, "s": [415.538, 314.43, 0], "to": [-0.02, 0.033, 0], "ti": [0.007, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.179}, "t": 831, "s": [415.499, 314.542, 0], "to": [-0.007, 0.036, 0], "ti": [-0.002, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.208}, "t": 832, "s": [415.493, 314.646, 0], "to": [0.002, 0.029, 0], "ti": [-0.008, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.2}, "t": 833, "s": [415.508, 314.713, 0], "to": [0.008, 0.017, 0], "ti": [-0.013, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.168}, "t": 834, "s": [415.54, 314.75, 0], "to": [0.013, 0.008, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.151}, "t": 835, "s": [415.587, 314.761, 0], "to": [0.018, 0, 0], "ti": [-0.021, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.152}, "t": 836, "s": [415.647, 314.753, 0], "to": [0.021, -0.005, 0], "ti": [-0.024, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.156}, "t": 837, "s": [415.716, 314.729, 0], "to": [0.024, -0.01, 0], "ti": [-0.026, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.16}, "t": 838, "s": [415.792, 314.694, 0], "to": [0.026, -0.013, 0], "ti": [-0.028, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.163}, "t": 839, "s": [415.874, 314.653, 0], "to": [0.028, -0.014, 0], "ti": [-0.029, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 840, "s": [415.959, 314.608, 0], "to": [0.029, -0.015, 0], "ti": [-0.029, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 841, "s": [416.045, 314.565, 0], "to": [0.029, -0.014, 0], "ti": [-0.028, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 842, "s": [416.131, 314.525, 0], "to": [0.028, -0.012, 0], "ti": [-0.027, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 843, "s": [416.214, 314.493, 0], "to": [0.027, -0.009, 0], "ti": [-0.026, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.175}, "t": 844, "s": [416.294, 314.471, 0], "to": [0.026, -0.005, 0], "ti": [-0.024, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.175}, "t": 845, "s": [416.369, 314.462, 0], "to": [0.024, -0.001, 0], "ti": [-0.022, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.172}, "t": 846, "s": [416.438, 314.466, 0], "to": [0.022, 0.004, 0], "ti": [-0.019, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.165}, "t": 847, "s": [416.499, 314.488, 0], "to": [0.019, 0.01, 0], "ti": [-0.016, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.159}, "t": 848, "s": [416.552, 314.527, 0], "to": [0.016, 0.016, 0], "ti": [-0.013, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.154}, "t": 849, "s": [416.596, 314.586, 0], "to": [0.013, 0.023, 0], "ti": [-0.009, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 850, "s": [416.63, 314.664, 0], "to": [0.009, 0.03, 0], "ti": [-0.006, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.153}, "t": 851, "s": [416.653, 314.764, 0], "to": [0.006, 0.037, 0], "ti": [-0.002, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 852, "s": [416.665, 314.886, 0], "to": [0.002, 0.044, 0], "ti": [0.002, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.156}, "t": 853, "s": [416.665, 315.028, 0], "to": [-0.002, 0.051, 0], "ti": [0.006, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 854, "s": [416.654, 315.193, 0], "to": [-0.006, 0.058, 0], "ti": [0.01, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 855, "s": [416.631, 315.378, 0], "to": [-0.01, 0.065, 0], "ti": [0.013, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 856, "s": [416.597, 315.583, 0], "to": [-0.013, 0.072, 0], "ti": [0.017, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 857, "s": [416.551, 315.807, 0], "to": [-0.017, 0.078, 0], "ti": [0.021, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 858, "s": [416.494, 316.049, 0], "to": [-0.021, 0.083, 0], "ti": [0.024, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 859, "s": [416.427, 316.307, 0], "to": [-0.024, 0.088, 0], "ti": [0.027, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.163}, "t": 860, "s": [416.349, 316.579, 0], "to": [-0.027, 0.093, 0], "ti": [0.03, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.17}, "t": 861, "s": [416.262, 316.864, 0], "to": [-0.03, 0.093, 0], "ti": [0.032, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 862, "s": [416.169, 317.135, 0], "to": [-0.032, 0.085, 0], "ti": [0.033, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.176}, "t": 863, "s": [416.072, 317.376, 0], "to": [-0.033, 0.075, 0], "ti": [0.034, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.177}, "t": 864, "s": [415.971, 317.587, 0], "to": [-0.034, 0.065, 0], "ti": [0.036, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.177}, "t": 865, "s": [415.865, 317.766, 0], "to": [-0.036, 0.055, 0], "ti": [0.038, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.176}, "t": 866, "s": [415.754, 317.915, 0], "to": [-0.038, 0.045, 0], "ti": [0.04, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.175}, "t": 867, "s": [415.637, 318.035, 0], "to": [-0.04, 0.035, 0], "ti": [0.042, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.172}, "t": 868, "s": [415.514, 318.125, 0], "to": [-0.042, 0.025, 0], "ti": [0.044, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.169}, "t": 869, "s": [415.385, 318.187, 0], "to": [-0.044, 0.016, 0], "ti": [0.046, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.165}, "t": 870, "s": [415.25, 318.221, 0], "to": [-0.046, 0.007, 0], "ti": [0.048, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.163}, "t": 871, "s": [415.108, 318.231, 0], "to": [-0.048, -0.001, 0], "ti": [0.05, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.162}, "t": 872, "s": [414.959, 318.216, 0], "to": [-0.05, -0.009, 0], "ti": [0.052, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 873, "s": [414.805, 318.179, 0], "to": [-0.052, -0.016, 0], "ti": [0.054, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 874, "s": [414.645, 318.122, 0], "to": [-0.054, -0.022, 0], "ti": [0.056, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 875, "s": [414.479, 318.047, 0], "to": [-0.056, -0.027, 0], "ti": [0.058, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 876, "s": [414.307, 317.958, 0], "to": [-0.058, -0.032, 0], "ti": [0.059, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 877, "s": [414.131, 317.855, 0], "to": [-0.059, -0.036, 0], "ti": [0.061, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 878, "s": [413.951, 317.744, 0], "to": [-0.061, -0.038, 0], "ti": [0.062, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 879, "s": [413.767, 317.626, 0], "to": [-0.062, -0.04, 0], "ti": [0.063, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 880, "s": [413.581, 317.505, 0], "to": [-0.063, -0.04, 0], "ti": [0.063, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 881, "s": [413.392, 317.385, 0], "to": [-0.063, -0.039, 0], "ti": [0.063, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 882, "s": [413.203, 317.27, 0], "to": [-0.063, -0.037, 0], "ti": [0.063, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 883, "s": [413.013, 317.164, 0], "to": [-0.063, -0.033, 0], "ti": [0.063, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 884, "s": [412.825, 317.071, 0], "to": [-0.063, -0.028, 0], "ti": [0.062, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 885, "s": [412.638, 316.996, 0], "to": [-0.062, -0.021, 0], "ti": [0.06, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 886, "s": [412.455, 316.944, 0], "to": [-0.06, -0.013, 0], "ti": [0.058, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.17}, "t": 887, "s": [412.277, 316.919, 0], "to": [-0.058, -0.003, 0], "ti": [0.056, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.167}, "t": 888, "s": [412.105, 316.927, 0], "to": [-0.056, 0.009, 0], "ti": [0.053, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.163}, "t": 889, "s": [411.941, 316.973, 0], "to": [-0.053, 0.023, 0], "ti": [0.05, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.158}, "t": 890, "s": [411.786, 317.064, 0], "to": [-0.05, 0.038, 0], "ti": [0.046, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.161}, "t": 891, "s": [411.642, 317.204, 0], "to": [-0.046, 0.052, 0], "ti": [0.04, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 892, "s": [411.512, 317.377, 0], "to": [-0.04, 0.06, 0], "ti": [0.034, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 893, "s": [411.4, 317.564, 0], "to": [-0.034, 0.064, 0], "ti": [0.026, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 894, "s": [411.31, 317.758, 0], "to": [-0.026, 0.065, 0], "ti": [0.017, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 895, "s": [411.244, 317.955, 0], "to": [-0.017, 0.065, 0], "ti": [0.008, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 896, "s": [411.205, 318.149, 0], "to": [-0.008, 0.063, 0], "ti": [-0.002, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.172}, "t": 897, "s": [411.196, 318.335, 0], "to": [0.002, 0.06, 0], "ti": [-0.012, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.17}, "t": 898, "s": [411.217, 318.51, 0], "to": [0.012, 0.056, 0], "ti": [-0.023, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.168}, "t": 899, "s": [411.269, 318.671, 0], "to": [0.023, 0.05, 0], "ti": [-0.034, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.165}, "t": 900, "s": [411.354, 318.813, 0], "to": [0.034, 0.044, 0], "ti": [-0.045, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.162}, "t": 901, "s": [411.472, 318.935, 0], "to": [0.045, 0.037, 0], "ti": [-0.055, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.16}, "t": 902, "s": [411.622, 319.033, 0], "to": [0.055, 0.029, 0], "ti": [-0.066, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 903, "s": [411.805, 319.108, 0], "to": [0.066, 0.02, 0], "ti": [-0.076, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 904, "s": [412.018, 319.156, 0], "to": [0.076, 0.012, 0], "ti": [-0.085, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.159}, "t": 905, "s": [412.26, 319.179, 0], "to": [0.085, 0.003, 0], "ti": [-0.094, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 906, "s": [412.53, 319.174, 0], "to": [0.094, -0.006, 0], "ti": [-0.102, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 907, "s": [412.824, 319.143, 0], "to": [0.102, -0.015, 0], "ti": [-0.109, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 908, "s": [413.141, 319.087, 0], "to": [0.109, -0.023, 0], "ti": [-0.114, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 909, "s": [413.476, 319.005, 0], "to": [0.114, -0.031, 0], "ti": [-0.118, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 910, "s": [413.826, 318.901, 0], "to": [0.118, -0.038, 0], "ti": [-0.121, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 911, "s": [414.186, 318.776, 0], "to": [0.121, -0.045, 0], "ti": [-0.122, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 912, "s": [414.553, 318.632, 0], "to": [0.122, -0.05, 0], "ti": [-0.122, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 913, "s": [414.92, 318.473, 0], "to": [0.122, -0.055, 0], "ti": [-0.06, 0.028, 0]}, {"t": 914.000037227983, "s": [415.283, 318.303, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [194, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [262, 262, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"t": 0, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.634], [-4.9, 7.333], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 11, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 19, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.332], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 29, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 40, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 48, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 57, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 68, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 76, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 86, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 97, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 105, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 114, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 125, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 133, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 143, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 154, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 162, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 171, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 182, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 190, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 200, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 211, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 219, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 228, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 239, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 247, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 257, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 268, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 276, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}, {"t": 285, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.99, -1.952], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.588, -0.211]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.425, -0.435], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.296, 0.017]], "v": [[2.118, -16.985], [3.277, -14.439], [1.464, -8.501], [0.999, -7.475], [-1.863, -1.571], [-1.027, -1.99], [2.74, -3.84], [4.055, -4.499], [5.327, -5.116], [6.495, -5.691], [9.579, -5.549], [8.398, 2.128], [7.855, 3.298], [-0.37, 17.324], [-3.354, 16.329], [-0.867, 5.391], [-1.977, 5.935], [-3.447, 6.633], [-4.9, 7.332], [-8.826, 7.877], [-10.318, 5.888], [-7.609, -4.092], [-6.533, -6.865], [1.158, -17.052]], "c": true}], "h": 1}, {"t": 296, "s": [{"i": [[-0.345, -0.026], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.483, -0.199]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.811, -9.662], [0.287, 0.016]], "v": [[1.64, -17.414], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.703], [2.453, -3.267], [4.245, -4.117], [5.566, -4.782], [7.068, -5.453], [9.96, -4.881], [8.016, 2.08], [7.187, 3.822], [0.012, 16.56], [-3.068, 15.996], [-0.486, 5.247], [-2.31, 5.696], [-3.829, 6.49], [-5.281, 7.332], [-8.874, 7.304], [-9.937, 4.933], [-7.419, -4.092], [-6.199, -7.198], [0.694, -17.479]], "c": true}], "h": 1}, {"t": 304.000012382174, "s": [{"i": [[-0.405, -0.03], [-0.165, -1.551], [0.965, -2.039], [0.235, -0.518], [0.169, -1.475], [-0.422, 0.212], [-1.257, 0.614], [-0.664, 0.333], [-0.433, 0.21], [-0.397, 0.196], [-1.22, -0.438], [1.359, -2.82], [0.185, -0.398], [3.487, -4.121], [0.56, 0.559], [-1.618, 3.738], [0.377, -0.185], [0.5, -0.238], [0.494, -0.237], [1.532, 0.17], [0.56, 1.243], [-1.318, 3.301], [-0.355, 0.926], [-3.324, -0.165]], "o": [[0.995, 0.995], [-0.195, 2.272], [-0.23, 0.508], [-0.909, 1.991], [0.414, -0.207], [1.253, -0.622], [0.651, -0.326], [0.42, -0.204], [0.386, -0.19], [1.251, -0.405], [0.74, 3.09], [-0.179, 0.386], [-2.296, 4.861], [-2.421, 1.196], [-0.41, -4.105], [-0.366, 0.18], [-0.486, 0.231], [-0.479, 0.231], [-1.45, 0.548], [-0.932, -0.746], [0.115, -3.58], [0.366, -0.921], [3.756, -9.522], [0.327, 0.016]], "v": [[1.879, -17.032], [2.944, -14.057], [1.226, -8.501], [-0.336, -5.662], [-1.863, -1.571], [-0.693, -1.322], [2.072, -2.742], [3.816, -3.831], [5.566, -5.02], [6.973, -5.214], [10.199, -4.928], [8.302, 1.841], [6.9, 4.109], [-0.036, 16.799], [-3.068, 15.471], [-0.2, 5.152], [-2.31, 5.696], [-3.829, 6.252], [-5.615, 7.333], [-9.351, 7.065], [-10.128, 4.313], [-7.753, -4.045], [-6.58, -7.198], [0.164, -17.487]], "c": true}], "h": 1}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.074509803922, 0.078431372549, 0.086274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [241.128, 69.662], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 915.000037268714, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Capa 1 Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [412.988, 315.509, 0], "to": [-0.075, 0.07, 0], "ti": [0.147, -0.138, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 1, "s": [412.537, 315.93, 0], "to": [-0.147, 0.138, 0], "ti": [0.139, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.172}, "t": 2, "s": [412.104, 316.337, 0], "to": [-0.139, 0.132, 0], "ti": [0.128, -0.123, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 3, "s": [411.7, 316.721, 0], "to": [-0.128, 0.123, 0], "ti": [0.113, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.179}, "t": 4, "s": [411.336, 317.074, 0], "to": [-0.113, 0.111, 0], "ti": [0.093, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.185}, "t": 5, "s": [411.025, 317.387, 0], "to": [-0.093, 0.096, 0], "ti": [0.069, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.195}, "t": 6, "s": [410.778, 317.651, 0], "to": [-0.069, 0.079, 0], "ti": [0.039, -0.067, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.193}, "t": 7, "s": [410.613, 317.864, 0], "to": [-0.039, 0.067, 0], "ti": [0.007, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.181}, "t": 8, "s": [410.545, 318.055, 0], "to": [-0.007, 0.06, 0], "ti": [-0.021, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.162}, "t": 9, "s": [410.568, 318.227, 0], "to": [0.021, 0.054, 0], "ti": [-0.047, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.154}, "t": 10, "s": [410.672, 318.378, 0], "to": [0.047, 0.047, 0], "ti": [-0.069, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.153}, "t": 11, "s": [410.848, 318.507, 0], "to": [0.069, 0.039, 0], "ti": [-0.089, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.155}, "t": 12, "s": [411.087, 318.614, 0], "to": [0.089, 0.032, 0], "ti": [-0.106, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 13, "s": [411.38, 318.697, 0], "to": [0.106, 0.024, 0], "ti": [-0.12, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 14, "s": [411.72, 318.757, 0], "to": [0.12, 0.016, 0], "ti": [-0.131, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 15, "s": [412.098, 318.793, 0], "to": [0.131, 0.008, 0], "ti": [-0.14, 0, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 16, "s": [412.507, 318.806, 0], "to": [0.14, 0, 0], "ti": [-0.147, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 17, "s": [412.939, 318.795, 0], "to": [0.147, -0.007, 0], "ti": [-0.151, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 18, "s": [413.386, 318.763, 0], "to": [0.151, -0.014, 0], "ti": [-0.152, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 19, "s": [413.842, 318.71, 0], "to": [0.152, -0.021, 0], "ti": [-0.152, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [414.3, 318.638, 0], "to": [0.152, -0.027, 0], "ti": [-0.149, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 21, "s": [414.752, 318.547, 0], "to": [0.149, -0.033, 0], "ti": [-0.144, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 22, "s": [415.194, 318.441, 0], "to": [0.144, -0.038, 0], "ti": [-0.137, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 23, "s": [415.618, 318.32, 0], "to": [0.137, -0.042, 0], "ti": [-0.129, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 24, "s": [416.018, 318.188, 0], "to": [0.129, -0.046, 0], "ti": [-0.118, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 25, "s": [416.39, 318.047, 0], "to": [0.118, -0.048, 0], "ti": [-0.106, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 26, "s": [416.728, 317.9, 0], "to": [0.106, -0.049, 0], "ti": [-0.092, 0.05, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.178}, "t": 27, "s": [417.027, 317.751, 0], "to": [0.092, -0.05, 0], "ti": [-0.077, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 28, "s": [417.281, 317.602, 0], "to": [0.077, -0.049, 0], "ti": [-0.06, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 29, "s": [417.487, 317.458, 0], "to": [0.06, -0.046, 0], "ti": [-0.041, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.193}, "t": 30, "s": [417.639, 317.323, 0], "to": [0.041, -0.043, 0], "ti": [-0.022, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.2}, "t": 31, "s": [417.735, 317.201, 0], "to": [0.022, -0.038, 0], "ti": [-0.001, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.186}, "t": 32, "s": [417.769, 317.097, 0], "to": [0.001, -0.031, 0], "ti": [0.022, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.791}, "o": {"x": 0.167, "y": 0.149}, "t": 33, "s": [417.738, 317.016, 0], "to": [-0.022, -0.023, 0], "ti": [0.045, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.798}, "o": {"x": 0.167, "y": 0.139}, "t": 34, "s": [417.639, 316.962, 0], "to": [-0.045, -0.012, 0], "ti": [0.069, 0, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.142}, "t": 35, "s": [417.468, 316.941, 0], "to": [-0.069, 0, 0], "ti": [0.092, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.149}, "t": 36, "s": [417.223, 316.959, 0], "to": [-0.092, 0.012, 0], "ti": [0.106, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 37, "s": [416.914, 317.011, 0], "to": [-0.106, 0.016, 0], "ti": [0.11, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 38, "s": [416.588, 317.057, 0], "to": [-0.11, 0.013, 0], "ti": [0.111, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 39, "s": [416.256, 317.091, 0], "to": [-0.111, 0.009, 0], "ti": [0.111, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [415.922, 317.113, 0], "to": [-0.111, 0.005, 0], "ti": [0.11, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 41, "s": [415.588, 317.123, 0], "to": [-0.11, 0.001, 0], "ti": [0.109, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 42, "s": [415.259, 317.119, 0], "to": [-0.109, -0.003, 0], "ti": [0.106, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 43, "s": [414.937, 317.102, 0], "to": [-0.106, -0.008, 0], "ti": [0.102, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 44, "s": [414.625, 317.072, 0], "to": [-0.102, -0.012, 0], "ti": [0.097, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 45, "s": [414.326, 317.028, 0], "to": [-0.097, -0.017, 0], "ti": [0.092, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 46, "s": [414.04, 316.972, 0], "to": [-0.092, -0.021, 0], "ti": [0.087, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 47, "s": [413.771, 316.902, 0], "to": [-0.087, -0.025, 0], "ti": [0.081, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 48, "s": [413.519, 316.82, 0], "to": [-0.081, -0.029, 0], "ti": [0.075, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 49, "s": [413.285, 316.727, 0], "to": [-0.075, -0.033, 0], "ti": [0.068, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 50, "s": [413.071, 316.622, 0], "to": [-0.068, -0.036, 0], "ti": [0.062, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 51, "s": [412.875, 316.508, 0], "to": [-0.062, -0.04, 0], "ti": [0.056, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 52, "s": [412.698, 316.385, 0], "to": [-0.056, -0.042, 0], "ti": [0.05, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 53, "s": [412.54, 316.255, 0], "to": [-0.05, -0.045, 0], "ti": [0.044, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 54, "s": [412.4, 316.118, 0], "to": [-0.044, -0.046, 0], "ti": [0.038, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 55, "s": [412.277, 315.977, 0], "to": [-0.038, -0.048, 0], "ti": [0.034, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 56, "s": [412.17, 315.832, 0], "to": [-0.034, -0.048, 0], "ti": [0.029, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 57, "s": [412.076, 315.687, 0], "to": [-0.029, -0.048, 0], "ti": [0.026, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 58, "s": [411.994, 315.543, 0], "to": [-0.026, -0.048, 0], "ti": [0.023, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 59, "s": [411.921, 315.401, 0], "to": [-0.023, -0.046, 0], "ti": [0.022, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 60, "s": [411.855, 315.265, 0], "to": [-0.022, -0.044, 0], "ti": [0.021, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 61, "s": [411.792, 315.137, 0], "to": [-0.021, -0.041, 0], "ti": [0.022, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.173}, "t": 62, "s": [411.729, 315.019, 0], "to": [-0.022, -0.037, 0], "ti": [0.024, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.172}, "t": 63, "s": [411.663, 314.914, 0], "to": [-0.024, -0.032, 0], "ti": [0.027, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.17}, "t": 64, "s": [411.588, 314.826, 0], "to": [-0.027, -0.026, 0], "ti": [0.032, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 65, "s": [411.501, 314.756, 0], "to": [-0.032, -0.02, 0], "ti": [0.037, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.163}, "t": 66, "s": [411.396, 314.708, 0], "to": [-0.037, -0.012, 0], "ti": [0.036, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.182}, "t": 67, "s": [411.279, 314.682, 0], "to": [-0.036, -0.008, 0], "ti": [0.029, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.192}, "t": 68, "s": [411.179, 314.661, 0], "to": [-0.029, -0.006, 0], "ti": [0.021, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.875}, "o": {"x": 0.167, "y": 0.207}, "t": 69, "s": [411.103, 314.645, 0], "to": [-0.021, -0.005, 0], "ti": [0.013, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.881}, "o": {"x": 0.167, "y": 0.251}, "t": 70, "s": [411.052, 314.632, 0], "to": [-0.013, -0.004, 0], "ti": [0.003, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.654}, "o": {"x": 0.167, "y": 0.285}, "t": 71, "s": [411.027, 314.623, 0], "to": [-0.003, -0.003, 0], "ti": [-0.006, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.761}, "o": {"x": 0.167, "y": 0.111}, "t": 72, "s": [411.032, 314.616, 0], "to": [0.006, -0.002, 0], "ti": [-0.016, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.128}, "t": 73, "s": [411.065, 314.61, 0], "to": [0.016, -0.001, 0], "ti": [-0.026, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.14}, "t": 74, "s": [411.129, 314.607, 0], "to": [0.026, -0.001, 0], "ti": [-0.037, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.146}, "t": 75, "s": [411.224, 314.604, 0], "to": [0.037, -0.001, 0], "ti": [-0.047, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.151}, "t": 76, "s": [411.348, 314.602, 0], "to": [0.047, -0.001, 0], "ti": [-0.056, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 77, "s": [411.503, 314.6, 0], "to": [0.056, -0.001, 0], "ti": [-0.066, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 78, "s": [411.687, 314.598, 0], "to": [0.066, -0.001, 0], "ti": [-0.075, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 79, "s": [411.899, 314.595, 0], "to": [0.075, -0.001, 0], "ti": [-0.083, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 80, "s": [412.136, 314.591, 0], "to": [0.083, -0.001, 0], "ti": [-0.091, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 81, "s": [412.399, 314.586, 0], "to": [0.091, -0.002, 0], "ti": [-0.098, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 82, "s": [412.683, 314.58, 0], "to": [0.098, -0.002, 0], "ti": [-0.104, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 83, "s": [412.987, 314.572, 0], "to": [0.104, -0.003, 0], "ti": [-0.109, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 84, "s": [413.306, 314.562, 0], "to": [0.109, -0.004, 0], "ti": [-0.112, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 85, "s": [413.639, 314.55, 0], "to": [0.112, -0.004, 0], "ti": [-0.115, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 86, "s": [413.98, 314.535, 0], "to": [0.115, -0.005, 0], "ti": [-0.115, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [414.326, 314.519, 0], "to": [0.115, -0.006, 0], "ti": [-0.114, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 88, "s": [414.672, 314.499, 0], "to": [0.114, -0.007, 0], "ti": [-0.112, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 89, "s": [415.013, 314.478, 0], "to": [0.112, -0.008, 0], "ti": [-0.108, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 90, "s": [415.344, 314.453, 0], "to": [0.108, -0.009, 0], "ti": [-0.101, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 91, "s": [415.658, 314.426, 0], "to": [0.101, -0.009, 0], "ti": [-0.093, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 92, "s": [415.951, 314.396, 0], "to": [0.093, -0.01, 0], "ti": [-0.082, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.179}, "t": 93, "s": [416.215, 314.364, 0], "to": [0.082, -0.011, 0], "ti": [-0.069, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.185}, "t": 94, "s": [416.443, 314.329, 0], "to": [0.069, -0.012, 0], "ti": [-0.054, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.866}, "o": {"x": 0.167, "y": 0.195}, "t": 95, "s": [416.629, 314.292, 0], "to": [0.054, -0.013, 0], "ti": [-0.035, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.894}, "o": {"x": 0.167, "y": 0.222}, "t": 96, "s": [416.765, 314.252, 0], "to": [0.035, -0.013, 0], "ti": [-0.016, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.382}, "t": 97, "s": [416.842, 314.216, 0], "to": [0.016, -0.008, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.718}, "o": {"x": 0.167, "y": 0.139}, "t": 98, "s": [416.86, 314.204, 0], "to": [-0.003, 0, 0], "ti": [0.019, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.118}, "t": 99, "s": [416.826, 314.213, 0], "to": [-0.019, 0.006, 0], "ti": [0.033, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.139}, "t": 100, "s": [416.747, 314.242, 0], "to": [-0.033, 0.012, 0], "ti": [0.045, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.149}, "t": 101, "s": [416.629, 314.287, 0], "to": [-0.045, 0.017, 0], "ti": [0.054, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.154}, "t": 102, "s": [416.48, 314.345, 0], "to": [-0.054, 0.021, 0], "ti": [0.062, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.158}, "t": 103, "s": [416.304, 314.415, 0], "to": [-0.062, 0.025, 0], "ti": [0.068, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 104, "s": [416.109, 314.494, 0], "to": [-0.068, 0.027, 0], "ti": [0.072, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 105, "s": [415.898, 314.58, 0], "to": [-0.072, 0.029, 0], "ti": [0.074, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 106, "s": [415.678, 314.671, 0], "to": [-0.074, 0.031, 0], "ti": [0.075, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [415.453, 314.764, 0], "to": [-0.075, 0.031, 0], "ti": [0.074, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 108, "s": [415.229, 314.858, 0], "to": [-0.074, 0.031, 0], "ti": [0.072, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 109, "s": [415.008, 314.951, 0], "to": [-0.072, 0.03, 0], "ti": [0.069, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 110, "s": [414.796, 315.041, 0], "to": [-0.069, 0.029, 0], "ti": [0.064, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.173}, "t": 111, "s": [414.596, 315.127, 0], "to": [-0.064, 0.028, 0], "ti": [0.058, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 112, "s": [414.411, 315.207, 0], "to": [-0.058, 0.026, 0], "ti": [0.052, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.179}, "t": 113, "s": [414.245, 315.28, 0], "to": [-0.052, 0.023, 0], "ti": [0.044, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.183}, "t": 114, "s": [414.101, 315.344, 0], "to": [-0.044, 0.02, 0], "ti": [0.036, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.189}, "t": 115, "s": [413.98, 315.4, 0], "to": [-0.036, 0.017, 0], "ti": [0.027, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.201}, "t": 116, "s": [413.886, 315.444, 0], "to": [-0.027, 0.013, 0], "ti": [0.017, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.892}, "o": {"x": 0.167, "y": 0.23}, "t": 117, "s": [413.82, 315.478, 0], "to": [-0.017, 0.009, 0], "ti": [0.007, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.733}, "o": {"x": 0.167, "y": 0.359}, "t": 118, "s": [413.784, 315.499, 0], "to": [-0.007, 0.005, 0], "ti": [-0.004, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.728}, "o": {"x": 0.167, "y": 0.12}, "t": 119, "s": [413.779, 315.507, 0], "to": [0.004, 0.001, 0], "ti": [-0.015, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.786}, "o": {"x": 0.167, "y": 0.12}, "t": 120, "s": [413.806, 315.502, 0], "to": [0.015, -0.004, 0], "ti": [-0.026, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.136}, "t": 121, "s": [413.867, 315.483, 0], "to": [0.026, -0.009, 0], "ti": [-0.037, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.144}, "t": 122, "s": [413.961, 315.449, 0], "to": [0.037, -0.014, 0], "ti": [-0.048, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.149}, "t": 123, "s": [414.089, 315.401, 0], "to": [0.048, -0.018, 0], "ti": [-0.059, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.152}, "t": 124, "s": [414.251, 315.339, 0], "to": [0.059, -0.023, 0], "ti": [-0.07, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.155}, "t": 125, "s": [414.446, 315.262, 0], "to": [0.07, -0.028, 0], "ti": [-0.08, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.16}, "t": 126, "s": [414.673, 315.17, 0], "to": [0.08, -0.031, 0], "ti": [-0.083, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 127, "s": [414.924, 315.076, 0], "to": [0.083, -0.026, 0], "ti": [-0.08, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 128, "s": [415.17, 315.014, 0], "to": [0.08, -0.015, 0], "ti": [-0.076, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 129, "s": [415.405, 314.983, 0], "to": [0.076, -0.005, 0], "ti": [-0.07, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.174}, "t": 130, "s": [415.626, 314.981, 0], "to": [0.07, 0.004, 0], "ti": [-0.064, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 131, "s": [415.828, 315.004, 0], "to": [0.064, 0.012, 0], "ti": [-0.056, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.175}, "t": 132, "s": [416.008, 315.051, 0], "to": [0.056, 0.019, 0], "ti": [-0.048, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.174}, "t": 133, "s": [416.165, 315.119, 0], "to": [0.048, 0.025, 0], "ti": [-0.039, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.174}, "t": 134, "s": [416.295, 315.204, 0], "to": [0.039, 0.031, 0], "ti": [-0.029, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.172}, "t": 135, "s": [416.398, 315.304, 0], "to": [0.029, 0.036, 0], "ti": [-0.019, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.169}, "t": 136, "s": [416.471, 315.418, 0], "to": [0.019, 0.039, 0], "ti": [-0.009, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.166}, "t": 137, "s": [416.515, 315.541, 0], "to": [0.009, 0.043, 0], "ti": [0.001, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.163}, "t": 138, "s": [416.528, 315.673, 0], "to": [-0.001, 0.045, 0], "ti": [0.01, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 139, "s": [416.511, 315.81, 0], "to": [-0.01, 0.046, 0], "ti": [0.02, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 140, "s": [416.465, 315.949, 0], "to": [-0.02, 0.047, 0], "ti": [0.029, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 141, "s": [416.391, 316.09, 0], "to": [-0.029, 0.047, 0], "ti": [0.038, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 142, "s": [416.29, 316.229, 0], "to": [-0.038, 0.046, 0], "ti": [0.046, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 143, "s": [416.164, 316.364, 0], "to": [-0.046, 0.044, 0], "ti": [0.053, -0.042, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 144, "s": [416.016, 316.493, 0], "to": [-0.053, 0.042, 0], "ti": [0.058, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 145, "s": [415.849, 316.614, 0], "to": [-0.058, 0.039, 0], "ti": [0.063, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 146, "s": [415.666, 316.725, 0], "to": [-0.063, 0.035, 0], "ti": [0.066, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 147, "s": [415.471, 316.823, 0], "to": [-0.066, 0.03, 0], "ti": [0.068, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 148, "s": [415.269, 316.907, 0], "to": [-0.068, 0.025, 0], "ti": [0.068, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 149, "s": [415.064, 316.974, 0], "to": [-0.068, 0.019, 0], "ti": [0.066, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 150, "s": [414.862, 317.022, 0], "to": [-0.066, 0.013, 0], "ti": [0.062, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.174}, "t": 151, "s": [414.668, 317.05, 0], "to": [-0.062, 0.006, 0], "ti": [0.056, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 152, "s": [414.489, 317.056, 0], "to": [-0.056, -0.002, 0], "ti": [0.048, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.18}, "t": 153, "s": [414.332, 317.038, 0], "to": [-0.048, -0.01, 0], "ti": [0.037, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.18}, "t": 154, "s": [414.203, 316.993, 0], "to": [-0.037, -0.019, 0], "ti": [0.024, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.17}, "t": 155, "s": [414.11, 316.921, 0], "to": [-0.024, -0.029, 0], "ti": [0.01, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.165}, "t": 156, "s": [414.061, 316.819, 0], "to": [-0.01, -0.036, 0], "ti": [0.005, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.197}, "t": 157, "s": [414.048, 316.704, 0], "to": [-0.005, -0.033, 0], "ti": [0.007, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.204}, "t": 158, "s": [414.03, 316.621, 0], "to": [-0.007, -0.023, 0], "ti": [0.009, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.202}, "t": 159, "s": [414.004, 316.568, 0], "to": [-0.009, -0.013, 0], "ti": [0.011, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.18}, "t": 160, "s": [413.973, 316.541, 0], "to": [-0.011, -0.005, 0], "ti": [0.012, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.157}, "t": 161, "s": [413.938, 316.536, 0], "to": [-0.012, 0.001, 0], "ti": [0.013, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 162, "s": [413.9, 316.549, 0], "to": [-0.013, 0.007, 0], "ti": [0.014, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.155}, "t": 163, "s": [413.859, 316.577, 0], "to": [-0.014, 0.011, 0], "ti": [0.014, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.159}, "t": 164, "s": [413.817, 316.616, 0], "to": [-0.014, 0.014, 0], "ti": [0.014, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.162}, "t": 165, "s": [413.774, 316.664, 0], "to": [-0.014, 0.017, 0], "ti": [0.014, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.165}, "t": 166, "s": [413.732, 316.716, 0], "to": [-0.014, 0.018, 0], "ti": [0.014, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.168}, "t": 167, "s": [413.69, 316.77, 0], "to": [-0.014, 0.018, 0], "ti": [0.013, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.172}, "t": 168, "s": [413.65, 316.824, 0], "to": [-0.013, 0.017, 0], "ti": [0.012, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.175}, "t": 169, "s": [413.612, 316.875, 0], "to": [-0.012, 0.016, 0], "ti": [0.011, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.18}, "t": 170, "s": [413.577, 316.921, 0], "to": [-0.011, 0.014, 0], "ti": [0.01, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 171, "s": [413.545, 316.959, 0], "to": [-0.01, 0.011, 0], "ti": [0.009, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.195}, "t": 172, "s": [413.517, 316.988, 0], "to": [-0.009, 0.008, 0], "ti": [0.007, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.201}, "t": 173, "s": [413.492, 317.005, 0], "to": [-0.007, 0.004, 0], "ti": [0.006, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.795}, "o": {"x": 0.167, "y": 0.176}, "t": 174, "s": [413.472, 317.01, 0], "to": [-0.006, -0.001, 0], "ti": [0.005, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.79}, "o": {"x": 0.167, "y": 0.14}, "t": 175, "s": [413.456, 317, 0], "to": [-0.005, -0.006, 0], "ti": [0.003, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.138}, "t": 176, "s": [413.444, 316.975, 0], "to": [-0.003, -0.011, 0], "ti": [0.002, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.143}, "t": 177, "s": [413.437, 316.933, 0], "to": [-0.002, -0.017, 0], "ti": [0, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.147}, "t": 178, "s": [413.435, 316.874, 0], "to": [0, -0.023, 0], "ti": [-0.001, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.15}, "t": 179, "s": [413.437, 316.796, 0], "to": [0.001, -0.029, 0], "ti": [-0.003, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.153}, "t": 180, "s": [413.443, 316.699, 0], "to": [0.003, -0.035, 0], "ti": [-0.004, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.155}, "t": 181, "s": [413.454, 316.583, 0], "to": [0.004, -0.042, 0], "ti": [-0.005, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 182, "s": [413.469, 316.448, 0], "to": [0.005, -0.048, 0], "ti": [-0.007, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 183, "s": [413.487, 316.293, 0], "to": [0.007, -0.055, 0], "ti": [-0.008, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 184, "s": [413.508, 316.119, 0], "to": [0.008, -0.061, 0], "ti": [-0.008, 0.067, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 185, "s": [413.532, 315.926, 0], "to": [0.008, -0.067, 0], "ti": [-0.01, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.161}, "t": 186, "s": [413.558, 315.715, 0], "to": [0.01, -0.073, 0], "ti": [-0.012, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 187, "s": [413.589, 315.489, 0], "to": [0.012, -0.076, 0], "ti": [-0.016, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [413.632, 315.261, 0], "to": [0.016, -0.076, 0], "ti": [-0.019, 0.074, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 189, "s": [413.686, 315.036, 0], "to": [0.019, -0.074, 0], "ti": [-0.022, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 190, "s": [413.749, 314.816, 0], "to": [0.022, -0.072, 0], "ti": [-0.025, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 191, "s": [413.82, 314.606, 0], "to": [0.025, -0.068, 0], "ti": [-0.027, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 192, "s": [413.899, 314.408, 0], "to": [0.027, -0.063, 0], "ti": [-0.029, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 193, "s": [413.985, 314.225, 0], "to": [0.029, -0.058, 0], "ti": [-0.031, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 194, "s": [414.076, 314.06, 0], "to": [0.031, -0.052, 0], "ti": [-0.032, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 195, "s": [414.171, 313.913, 0], "to": [0.032, -0.045, 0], "ti": [-0.033, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.175}, "t": 196, "s": [414.27, 313.787, 0], "to": [0.033, -0.038, 0], "ti": [-0.034, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.175}, "t": 197, "s": [414.372, 313.683, 0], "to": [0.034, -0.031, 0], "ti": [-0.035, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.175}, "t": 198, "s": [414.476, 313.601, 0], "to": [0.035, -0.024, 0], "ti": [-0.035, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.174}, "t": 199, "s": [414.58, 313.541, 0], "to": [0.035, -0.016, 0], "ti": [-0.035, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.171}, "t": 200, "s": [414.684, 313.505, 0], "to": [0.035, -0.008, 0], "ti": [-0.034, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.168}, "t": 201, "s": [414.788, 313.491, 0], "to": [0.034, -0.001, 0], "ti": [-0.034, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.166}, "t": 202, "s": [414.89, 313.498, 0], "to": [0.034, 0.006, 0], "ti": [-0.033, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 203, "s": [414.991, 313.526, 0], "to": [0.033, 0.012, 0], "ti": [-0.032, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.163}, "t": 204, "s": [415.088, 313.572, 0], "to": [0.032, 0.018, 0], "ti": [-0.031, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 205, "s": [415.181, 313.635, 0], "to": [0.031, 0.023, 0], "ti": [-0.029, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 206, "s": [415.271, 313.713, 0], "to": [0.029, 0.028, 0], "ti": [-0.027, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 207, "s": [415.355, 313.802, 0], "to": [0.027, 0.031, 0], "ti": [-0.025, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 208, "s": [415.434, 313.901, 0], "to": [0.025, 0.034, 0], "ti": [-0.023, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 209, "s": [415.508, 314.004, 0], "to": [0.023, 0.035, 0], "ti": [-0.021, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 210, "s": [415.574, 314.109, 0], "to": [0.021, 0.035, 0], "ti": [-0.019, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.174}, "t": 211, "s": [415.634, 314.212, 0], "to": [0.019, 0.033, 0], "ti": [-0.016, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.18}, "t": 212, "s": [415.686, 314.307, 0], "to": [0.016, 0.03, 0], "ti": [-0.013, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.188}, "t": 213, "s": [415.73, 314.39, 0], "to": [0.013, 0.025, 0], "ti": [-0.01, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.208}, "t": 214, "s": [415.766, 314.455, 0], "to": [0.01, 0.018, 0], "ti": [-0.007, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.883}, "o": {"x": 0.167, "y": 0.271}, "t": 215, "s": [415.793, 314.497, 0], "to": [0.007, 0.009, 0], "ti": [-0.004, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.697}, "o": {"x": 0.167, "y": 0.29}, "t": 216, "s": [415.811, 314.509, 0], "to": [0.004, 0.002, 0], "ti": [0, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.745}, "o": {"x": 0.167, "y": 0.116}, "t": 217, "s": [415.818, 314.507, 0], "to": [0, 0.003, 0], "ti": [0.004, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.797}, "o": {"x": 0.167, "y": 0.124}, "t": 218, "s": [415.812, 314.529, 0], "to": [-0.004, 0.011, 0], "ti": [0.008, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.141}, "t": 219, "s": [415.794, 314.574, 0], "to": [-0.008, 0.018, 0], "ti": [0.012, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.15}, "t": 220, "s": [415.764, 314.637, 0], "to": [-0.012, 0.023, 0], "ti": [0.015, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.155}, "t": 221, "s": [415.725, 314.714, 0], "to": [-0.015, 0.027, 0], "ti": [0.018, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 222, "s": [415.676, 314.802, 0], "to": [-0.018, 0.031, 0], "ti": [0.02, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 223, "s": [415.618, 314.897, 0], "to": [-0.02, 0.032, 0], "ti": [0.023, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 224, "s": [415.553, 314.997, 0], "to": [-0.023, 0.033, 0], "ti": [0.025, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 225, "s": [415.482, 315.098, 0], "to": [-0.025, 0.033, 0], "ti": [0.026, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [415.405, 315.197, 0], "to": [-0.026, 0.033, 0], "ti": [0.028, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 227, "s": [415.323, 315.293, 0], "to": [-0.028, 0.031, 0], "ti": [0.029, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 228, "s": [415.238, 315.383, 0], "to": [-0.029, 0.029, 0], "ti": [0.03, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 229, "s": [415.15, 315.465, 0], "to": [-0.03, 0.026, 0], "ti": [0.03, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 230, "s": [415.06, 315.537, 0], "to": [-0.03, 0.022, 0], "ti": [0.03, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 231, "s": [414.969, 315.599, 0], "to": [-0.03, 0.018, 0], "ti": [0.03, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 232, "s": [414.878, 315.647, 0], "to": [-0.03, 0.014, 0], "ti": [0.03, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 233, "s": [414.788, 315.682, 0], "to": [-0.03, 0.009, 0], "ti": [0.029, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.171}, "t": 234, "s": [414.699, 315.703, 0], "to": [-0.029, 0.004, 0], "ti": [0.028, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.17}, "t": 235, "s": [414.614, 315.709, 0], "to": [-0.028, -0.001, 0], "ti": [0.027, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.168}, "t": 236, "s": [414.532, 315.699, 0], "to": [-0.027, -0.006, 0], "ti": [0.025, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.165}, "t": 237, "s": [414.455, 315.673, 0], "to": [-0.025, -0.011, 0], "ti": [0.023, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.163}, "t": 238, "s": [414.383, 315.632, 0], "to": [-0.023, -0.016, 0], "ti": [0.021, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.162}, "t": 239, "s": [414.317, 315.576, 0], "to": [-0.021, -0.021, 0], "ti": [0.018, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 240, "s": [414.259, 315.505, 0], "to": [-0.018, -0.026, 0], "ti": [0.015, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 241, "s": [414.209, 315.42, 0], "to": [-0.015, -0.03, 0], "ti": [0.012, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 242, "s": [414.168, 315.322, 0], "to": [-0.012, -0.035, 0], "ti": [0.009, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 243, "s": [414.137, 315.213, 0], "to": [-0.009, -0.038, 0], "ti": [0.005, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 244, "s": [414.116, 315.093, 0], "to": [-0.005, -0.042, 0], "ti": [0.001, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 245, "s": [414.108, 314.964, 0], "to": [-0.001, -0.044, 0], "ti": [-0.005, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.163}, "t": 246, "s": [414.111, 314.828, 0], "to": [0.005, -0.046, 0], "ti": [-0.015, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.162}, "t": 247, "s": [414.138, 314.689, 0], "to": [0.015, -0.046, 0], "ti": [-0.025, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 248, "s": [414.2, 314.553, 0], "to": [0.025, -0.045, 0], "ti": [-0.034, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 249, "s": [414.291, 314.421, 0], "to": [0.034, -0.043, 0], "ti": [-0.04, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 250, "s": [414.403, 314.294, 0], "to": [0.04, -0.041, 0], "ti": [-0.044, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 251, "s": [414.53, 314.174, 0], "to": [0.044, -0.039, 0], "ti": [-0.046, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 252, "s": [414.665, 314.062, 0], "to": [0.046, -0.036, 0], "ti": [-0.046, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 253, "s": [414.803, 313.959, 0], "to": [0.046, -0.033, 0], "ti": [-0.044, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.173}, "t": 254, "s": [414.939, 313.865, 0], "to": [0.044, -0.03, 0], "ti": [-0.041, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.176}, "t": 255, "s": [415.068, 313.78, 0], "to": [0.041, -0.026, 0], "ti": [-0.037, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.179}, "t": 256, "s": [415.185, 313.706, 0], "to": [0.037, -0.023, 0], "ti": [-0.031, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.185}, "t": 257, "s": [415.287, 313.642, 0], "to": [0.031, -0.019, 0], "ti": [-0.024, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.193}, "t": 258, "s": [415.37, 313.589, 0], "to": [0.024, -0.016, 0], "ti": [-0.017, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.872}, "o": {"x": 0.167, "y": 0.208}, "t": 259, "s": [415.432, 313.547, 0], "to": [0.017, -0.013, 0], "ti": [-0.009, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.24}, "t": 260, "s": [415.471, 313.514, 0], "to": [0.009, -0.009, 0], "ti": [0, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.748}, "o": {"x": 0.167, "y": 0.191}, "t": 261, "s": [415.484, 313.492, 0], "to": [0, -0.006, 0], "ti": [0.009, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.778}, "o": {"x": 0.167, "y": 0.124}, "t": 262, "s": [415.47, 313.478, 0], "to": [-0.009, -0.003, 0], "ti": [0.019, 0, 0]}, {"i": {"x": 0.833, "y": 0.799}, "o": {"x": 0.167, "y": 0.134}, "t": 263, "s": [415.429, 313.473, 0], "to": [-0.019, 0, 0], "ti": [0.028, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.143}, "t": 264, "s": [415.359, 313.476, 0], "to": [-0.028, 0.002, 0], "ti": [0.037, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.148}, "t": 265, "s": [415.261, 313.484, 0], "to": [-0.037, 0.004, 0], "ti": [0.046, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.152}, "t": 266, "s": [415.136, 313.499, 0], "to": [-0.046, 0.005, 0], "ti": [0.055, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 267, "s": [414.984, 313.517, 0], "to": [-0.055, 0.006, 0], "ti": [0.063, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 268, "s": [414.807, 313.537, 0], "to": [-0.063, 0.007, 0], "ti": [0.07, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 269, "s": [414.607, 313.558, 0], "to": [-0.07, 0.007, 0], "ti": [0.076, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 270, "s": [414.387, 313.577, 0], "to": [-0.076, 0.006, 0], "ti": [0.082, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 271, "s": [414.149, 313.593, 0], "to": [-0.082, 0.004, 0], "ti": [0.086, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 272, "s": [413.898, 313.604, 0], "to": [-0.086, 0.002, 0], "ti": [0.088, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 273, "s": [413.636, 313.607, 0], "to": [-0.088, -0.001, 0], "ti": [0.089, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 274, "s": [413.369, 313.599, 0], "to": [-0.089, -0.005, 0], "ti": [0.089, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 275, "s": [413.1, 313.578, 0], "to": [-0.089, -0.01, 0], "ti": [0.087, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 276, "s": [412.837, 313.541, 0], "to": [-0.087, -0.012, 0], "ti": [0.085, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 277, "s": [412.58, 313.504, 0], "to": [-0.085, -0.008, 0], "ti": [0.082, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 278, "s": [412.33, 313.494, 0], "to": [-0.082, 0.001, 0], "ti": [0.079, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 279, "s": [412.087, 313.511, 0], "to": [-0.079, 0.01, 0], "ti": [0.075, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 280, "s": [411.856, 313.553, 0], "to": [-0.075, 0.018, 0], "ti": [0.07, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 281, "s": [411.637, 313.618, 0], "to": [-0.07, 0.025, 0], "ti": [0.065, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 282, "s": [411.433, 313.705, 0], "to": [-0.065, 0.032, 0], "ti": [0.06, -0.038, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.169}, "t": 283, "s": [411.245, 313.81, 0], "to": [-0.06, 0.038, 0], "ti": [0.054, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 284, "s": [411.074, 313.934, 0], "to": [-0.054, 0.044, 0], "ti": [0.048, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.168}, "t": 285, "s": [410.922, 314.073, 0], "to": [-0.048, 0.049, 0], "ti": [0.041, -0.053, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.168}, "t": 286, "s": [410.789, 314.225, 0], "to": [-0.041, 0.053, 0], "ti": [0.035, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 287, "s": [410.675, 314.39, 0], "to": [-0.035, 0.056, 0], "ti": [0.028, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 288, "s": [410.581, 314.564, 0], "to": [-0.028, 0.059, 0], "ti": [0.022, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 289, "s": [410.506, 314.745, 0], "to": [-0.022, 0.062, 0], "ti": [0.016, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 290, "s": [410.449, 314.933, 0], "to": [-0.016, 0.063, 0], "ti": [0.01, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 291, "s": [410.41, 315.124, 0], "to": [-0.01, 0.064, 0], "ti": [0.005, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 292, "s": [410.388, 315.316, 0], "to": [-0.005, 0.064, 0], "ti": [0, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 293, "s": [410.381, 315.508, 0], "to": [0, 0.064, 0], "ti": [-0.004, -0.062, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 294, "s": [410.388, 315.698, 0], "to": [0.004, 0.062, 0], "ti": [-0.008, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 295, "s": [410.406, 315.882, 0], "to": [0.008, 0.06, 0], "ti": [-0.01, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 296, "s": [410.434, 316.06, 0], "to": [0.01, 0.058, 0], "ti": [-0.012, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 297, "s": [410.468, 316.228, 0], "to": [0.012, 0.054, 0], "ti": [-0.013, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 298, "s": [410.506, 316.385, 0], "to": [0.013, 0.05, 0], "ti": [-0.012, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 299, "s": [410.544, 316.528, 0], "to": [0.012, 0.045, 0], "ti": [-0.011, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.181}, "t": 300, "s": [410.58, 316.656, 0], "to": [0.011, 0.039, 0], "ti": [-0.008, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.187}, "t": 301, "s": [410.609, 316.765, 0], "to": [0.008, 0.033, 0], "ti": [-0.003, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.197}, "t": 302, "s": [410.627, 316.854, 0], "to": [0.003, 0.026, 0], "ti": [0.002, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.205}, "t": 303, "s": [410.63, 316.92, 0], "to": [-0.002, 0.018, 0], "ti": [0.01, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.779}, "o": {"x": 0.167, "y": 0.168}, "t": 304, "s": [410.613, 316.961, 0], "to": [-0.01, 0.009, 0], "ti": [0.019, 0, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.134}, "t": 305, "s": [410.571, 316.975, 0], "to": [-0.019, 0, 0], "ti": [0.027, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.146}, "t": 306, "s": [410.5, 316.958, 0], "to": [-0.027, -0.01, 0], "ti": [0.028, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.169}, "t": 307, "s": [410.411, 316.917, 0], "to": [-0.028, -0.016, 0], "ti": [0.024, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.167}, "t": 308, "s": [410.334, 316.861, 0], "to": [-0.024, -0.021, 0], "ti": [0.019, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 309, "s": [410.27, 316.791, 0], "to": [-0.019, -0.026, 0], "ti": [0.014, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.162}, "t": 310, "s": [410.219, 316.708, 0], "to": [-0.014, -0.03, 0], "ti": [0.009, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.161}, "t": 311, "s": [410.183, 316.611, 0], "to": [-0.009, -0.034, 0], "ti": [0.004, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.16}, "t": 312, "s": [410.163, 316.502, 0], "to": [-0.004, -0.038, 0], "ti": [-0.001, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.159}, "t": 313, "s": [410.158, 316.381, 0], "to": [0.001, -0.042, 0], "ti": [-0.006, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 314, "s": [410.169, 316.249, 0], "to": [0.006, -0.046, 0], "ti": [-0.012, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 315, "s": [410.196, 316.107, 0], "to": [0.012, -0.049, 0], "ti": [-0.017, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 316, "s": [410.24, 315.954, 0], "to": [0.017, -0.052, 0], "ti": [-0.022, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 317, "s": [410.298, 315.793, 0], "to": [0.022, -0.055, 0], "ti": [-0.027, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 318, "s": [410.373, 315.624, 0], "to": [0.027, -0.057, 0], "ti": [-0.032, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 319, "s": [410.461, 315.449, 0], "to": [0.032, -0.06, 0], "ti": [-0.036, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 320, "s": [410.563, 315.267, 0], "to": [0.036, -0.061, 0], "ti": [-0.04, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 321, "s": [410.678, 315.08, 0], "to": [0.04, -0.063, 0], "ti": [-0.043, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 322, "s": [410.803, 314.89, 0], "to": [0.043, -0.064, 0], "ti": [-0.046, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 323, "s": [410.939, 314.698, 0], "to": [0.046, -0.064, 0], "ti": [-0.049, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 324, "s": [411.082, 314.505, 0], "to": [0.049, -0.064, 0], "ti": [-0.05, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 325, "s": [411.231, 314.312, 0], "to": [0.05, -0.064, 0], "ti": [-0.051, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 326, "s": [411.384, 314.12, 0], "to": [0.051, -0.063, 0], "ti": [-0.052, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 327, "s": [411.539, 313.932, 0], "to": [0.052, -0.062, 0], "ti": [-0.051, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 328, "s": [411.694, 313.749, 0], "to": [0.051, -0.06, 0], "ti": [-0.049, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 329, "s": [411.844, 313.572, 0], "to": [0.049, -0.058, 0], "ti": [-0.047, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 330, "s": [411.989, 313.403, 0], "to": [0.047, -0.055, 0], "ti": [-0.043, 0.051, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 331, "s": [412.124, 313.243, 0], "to": [0.043, -0.051, 0], "ti": [-0.038, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 332, "s": [412.246, 313.095, 0], "to": [0.038, -0.047, 0], "ti": [-0.032, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.18}, "t": 333, "s": [412.353, 312.961, 0], "to": [0.032, -0.042, 0], "ti": [-0.025, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.186}, "t": 334, "s": [412.44, 312.842, 0], "to": [0.025, -0.037, 0], "ti": [-0.016, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.194}, "t": 335, "s": [412.503, 312.74, 0], "to": [0.016, -0.031, 0], "ti": [-0.005, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.211}, "t": 336, "s": [412.538, 312.657, 0], "to": [0.005, -0.023, 0], "ti": [0.011, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.158}, "t": 337, "s": [412.531, 312.6, 0], "to": [-0.011, -0.013, 0], "ti": [0.028, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.798}, "o": {"x": 0.167, "y": 0.135}, "t": 338, "s": [412.471, 312.578, 0], "to": [-0.028, -0.002, 0], "ti": [0.041, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.81}, "o": {"x": 0.167, "y": 0.142}, "t": 339, "s": [412.366, 312.591, 0], "to": [-0.041, 0.011, 0], "ti": [0.053, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.148}, "t": 340, "s": [412.224, 312.641, 0], "to": [-0.053, 0.023, 0], "ti": [0.062, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 341, "s": [412.051, 312.728, 0], "to": [-0.062, 0.035, 0], "ti": [0.068, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 342, "s": [411.855, 312.853, 0], "to": [-0.068, 0.048, 0], "ti": [0.073, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 343, "s": [411.641, 313.016, 0], "to": [-0.073, 0.06, 0], "ti": [0.076, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 344, "s": [411.418, 313.215, 0], "to": [-0.076, 0.073, 0], "ti": [0.076, -0.085, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 345, "s": [411.188, 313.451, 0], "to": [-0.076, 0.085, 0], "ti": [0.076, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 346, "s": [410.959, 313.722, 0], "to": [-0.076, 0.096, 0], "ti": [0.073, -0.107, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 347, "s": [410.735, 314.027, 0], "to": [-0.073, 0.107, 0], "ti": [0.069, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 348, "s": [410.52, 314.364, 0], "to": [-0.069, 0.117, 0], "ti": [0.064, -0.127, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 349, "s": [410.318, 314.73, 0], "to": [-0.064, 0.127, 0], "ti": [0.058, -0.135, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 350, "s": [410.133, 315.123, 0], "to": [-0.058, 0.135, 0], "ti": [0.051, -0.142, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 351, "s": [409.968, 315.54, 0], "to": [-0.051, 0.142, 0], "ti": [0.043, -0.149, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 352, "s": [409.826, 315.977, 0], "to": [-0.043, 0.149, 0], "ti": [0.034, -0.154, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 353, "s": [409.709, 316.432, 0], "to": [-0.034, 0.154, 0], "ti": [0.025, -0.157, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 354, "s": [409.619, 316.899, 0], "to": [-0.025, 0.157, 0], "ti": [0.015, -0.159, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 355, "s": [409.558, 317.375, 0], "to": [-0.015, 0.159, 0], "ti": [0.005, -0.16, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 356, "s": [409.526, 317.854, 0], "to": [-0.005, 0.16, 0], "ti": [-0.005, -0.158, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 357, "s": [409.525, 318.332, 0], "to": [0.005, 0.158, 0], "ti": [-0.015, -0.155, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 358, "s": [409.555, 318.802, 0], "to": [0.015, 0.155, 0], "ti": [-0.025, -0.149, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 359, "s": [409.615, 319.26, 0], "to": [0.025, 0.149, 0], "ti": [-0.035, -0.142, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 360, "s": [409.706, 319.699, 0], "to": [0.035, 0.142, 0], "ti": [-0.045, -0.132, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 361, "s": [409.826, 320.112, 0], "to": [0.045, 0.132, 0], "ti": [-0.054, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 362, "s": [409.974, 320.492, 0], "to": [0.054, 0.12, 0], "ti": [-0.062, -0.105, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 363, "s": [410.148, 320.832, 0], "to": [0.062, 0.105, 0], "ti": [-0.07, -0.088, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 364, "s": [410.346, 321.124, 0], "to": [0.07, 0.088, 0], "ti": [-0.076, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 365, "s": [410.566, 321.361, 0], "to": [0.076, 0.068, 0], "ti": [-0.079, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.179}, "t": 366, "s": [410.804, 321.533, 0], "to": [0.079, 0.046, 0], "ti": [-0.073, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.185}, "t": 367, "s": [411.037, 321.64, 0], "to": [0.073, 0.026, 0], "ti": [-0.064, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.181}, "t": 368, "s": [411.242, 321.688, 0], "to": [0.064, 0.007, 0], "ti": [-0.056, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.172}, "t": 369, "s": [411.422, 321.68, 0], "to": [0.056, -0.011, 0], "ti": [-0.049, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.163}, "t": 370, "s": [411.579, 321.621, 0], "to": [0.049, -0.028, 0], "ti": [-0.043, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.158}, "t": 371, "s": [411.717, 321.512, 0], "to": [0.043, -0.044, 0], "ti": [-0.038, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.156}, "t": 372, "s": [411.839, 321.358, 0], "to": [0.038, -0.058, 0], "ti": [-0.034, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 373, "s": [411.948, 321.162, 0], "to": [0.034, -0.072, 0], "ti": [-0.031, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 374, "s": [412.046, 320.928, 0], "to": [0.031, -0.084, 0], "ti": [-0.029, 0.095, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 375, "s": [412.136, 320.658, 0], "to": [0.029, -0.095, 0], "ti": [-0.027, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 376, "s": [412.22, 320.357, 0], "to": [0.027, -0.105, 0], "ti": [-0.027, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 377, "s": [412.301, 320.03, 0], "to": [0.027, -0.113, 0], "ti": [-0.027, 0.12, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 378, "s": [412.38, 319.678, 0], "to": [0.027, -0.12, 0], "ti": [-0.027, 0.126, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 379, "s": [412.461, 319.308, 0], "to": [0.027, -0.126, 0], "ti": [-0.029, 0.13, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 380, "s": [412.545, 318.923, 0], "to": [0.029, -0.13, 0], "ti": [-0.031, 0.133, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 381, "s": [412.633, 318.528, 0], "to": [0.031, -0.133, 0], "ti": [-0.033, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 382, "s": [412.728, 318.126, 0], "to": [0.033, -0.134, 0], "ti": [-0.036, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 383, "s": [412.832, 317.722, 0], "to": [0.036, -0.134, 0], "ti": [-0.04, 0.132, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 384, "s": [412.945, 317.322, 0], "to": [0.04, -0.132, 0], "ti": [-0.043, 0.129, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 385, "s": [413.069, 316.929, 0], "to": [0.043, -0.129, 0], "ti": [-0.048, 0.124, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 386, "s": [413.206, 316.549, 0], "to": [0.048, -0.124, 0], "ti": [-0.052, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 387, "s": [413.356, 316.187, 0], "to": [0.052, -0.117, 0], "ti": [-0.057, 0.109, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 388, "s": [413.52, 315.847, 0], "to": [0.057, -0.109, 0], "ti": [-0.063, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 389, "s": [413.701, 315.535, 0], "to": [0.063, -0.099, 0], "ti": [-0.068, 0.087, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 390, "s": [413.897, 315.255, 0], "to": [0.068, -0.087, 0], "ti": [-0.074, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.172}, "t": 391, "s": [414.111, 315.015, 0], "to": [0.074, -0.073, 0], "ti": [-0.08, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.171}, "t": 392, "s": [414.341, 314.818, 0], "to": [0.08, -0.057, 0], "ti": [-0.086, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.169}, "t": 393, "s": [414.59, 314.671, 0], "to": [0.086, -0.04, 0], "ti": [-0.092, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.165}, "t": 394, "s": [414.858, 314.579, 0], "to": [0.092, -0.021, 0], "ti": [-0.098, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.162}, "t": 395, "s": [415.143, 314.548, 0], "to": [0.098, 0.001, 0], "ti": [-0.098, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.17}, "t": 396, "s": [415.447, 314.583, 0], "to": [0.098, 0.018, 0], "ti": [-0.085, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.188}, "t": 397, "s": [415.732, 314.655, 0], "to": [0.085, 0.023, 0], "ti": [-0.064, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.194}, "t": 398, "s": [415.955, 314.723, 0], "to": [0.064, 0.022, 0], "ti": [-0.045, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.203}, "t": 399, "s": [416.118, 314.787, 0], "to": [0.045, 0.021, 0], "ti": [-0.027, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.215}, "t": 400, "s": [416.225, 314.848, 0], "to": [0.027, 0.02, 0], "ti": [-0.009, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.202}, "t": 401, "s": [416.278, 314.904, 0], "to": [0.009, 0.018, 0], "ti": [0.008, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.15}, "t": 402, "s": [416.279, 314.957, 0], "to": [-0.008, 0.017, 0], "ti": [0.023, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.139}, "t": 403, "s": [416.232, 315.007, 0], "to": [-0.023, 0.016, 0], "ti": [0.037, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.145}, "t": 404, "s": [416.141, 315.052, 0], "to": [-0.037, 0.015, 0], "ti": [0.051, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.15}, "t": 405, "s": [416.007, 315.095, 0], "to": [-0.051, 0.014, 0], "ti": [0.063, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 406, "s": [415.836, 315.134, 0], "to": [-0.063, 0.012, 0], "ti": [0.073, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.156}, "t": 407, "s": [415.631, 315.17, 0], "to": [-0.073, 0.012, 0], "ti": [0.083, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 408, "s": [415.397, 315.203, 0], "to": [-0.083, 0.011, 0], "ti": [0.09, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 409, "s": [415.136, 315.233, 0], "to": [-0.09, 0.01, 0], "ti": [0.097, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 410, "s": [414.855, 315.261, 0], "to": [-0.097, 0.009, 0], "ti": [0.101, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 411, "s": [414.557, 315.287, 0], "to": [-0.101, 0.008, 0], "ti": [0.104, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 412, "s": [414.248, 315.312, 0], "to": [-0.104, 0.008, 0], "ti": [0.105, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 413, "s": [413.932, 315.335, 0], "to": [-0.105, 0.008, 0], "ti": [0.105, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 414, "s": [413.615, 315.357, 0], "to": [-0.105, 0.007, 0], "ti": [0.102, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.169}, "t": 415, "s": [413.303, 315.379, 0], "to": [-0.102, 0.007, 0], "ti": [0.098, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 416, "s": [413.001, 315.4, 0], "to": [-0.098, 0.007, 0], "ti": [0.092, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 417, "s": [412.715, 315.422, 0], "to": [-0.092, 0.008, 0], "ti": [0.083, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.177}, "t": 418, "s": [412.451, 315.445, 0], "to": [-0.083, 0.008, 0], "ti": [0.073, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.181}, "t": 419, "s": [412.215, 315.47, 0], "to": [-0.073, 0.009, 0], "ti": [0.06, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.188}, "t": 420, "s": [412.014, 315.497, 0], "to": [-0.06, 0.009, 0], "ti": [0.045, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.2}, "t": 421, "s": [411.856, 315.526, 0], "to": [-0.045, 0.01, 0], "ti": [0.027, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.859}, "o": {"x": 0.167, "y": 0.228}, "t": 422, "s": [411.746, 315.559, 0], "to": [-0.027, 0.012, 0], "ti": [0.008, -0.013, 0]}, {"i": {"x": 0.833, "y": 0.75}, "o": {"x": 0.167, "y": 0.202}, "t": 423, "s": [411.691, 315.595, 0], "to": [-0.008, 0.013, 0], "ti": [-0.015, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.767}, "o": {"x": 0.167, "y": 0.125}, "t": 424, "s": [411.7, 315.637, 0], "to": [0.015, 0.015, 0], "ti": [-0.04, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.792}, "o": {"x": 0.167, "y": 0.13}, "t": 425, "s": [411.78, 315.684, 0], "to": [0.04, 0.017, 0], "ti": [-0.067, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.139}, "t": 426, "s": [411.938, 315.737, 0], "to": [0.067, 0.02, 0], "ti": [-0.093, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.147}, "t": 427, "s": [412.179, 315.803, 0], "to": [0.093, 0.025, 0], "ti": [-0.115, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.154}, "t": 428, "s": [412.493, 315.886, 0], "to": [0.115, 0.03, 0], "ti": [-0.132, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.158}, "t": 429, "s": [412.867, 315.981, 0], "to": [0.132, 0.033, 0], "ti": [-0.146, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 430, "s": [413.288, 316.083, 0], "to": [0.146, 0.035, 0], "ti": [-0.156, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 431, "s": [413.743, 316.189, 0], "to": [0.156, 0.035, 0], "ti": [-0.162, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 432, "s": [414.221, 316.295, 0], "to": [0.162, 0.035, 0], "ti": [-0.164, -0.033, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 433, "s": [414.713, 316.398, 0], "to": [0.164, 0.033, 0], "ti": [-0.164, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 434, "s": [415.208, 316.496, 0], "to": [0.164, 0.031, 0], "ti": [-0.161, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 435, "s": [415.697, 316.584, 0], "to": [0.161, 0.028, 0], "ti": [-0.155, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 436, "s": [416.173, 316.662, 0], "to": [0.155, 0.024, 0], "ti": [-0.147, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 437, "s": [416.628, 316.727, 0], "to": [0.147, 0.019, 0], "ti": [-0.137, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 438, "s": [417.056, 316.777, 0], "to": [0.137, 0.014, 0], "ti": [-0.125, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 439, "s": [417.451, 316.811, 0], "to": [0.125, 0.009, 0], "ti": [-0.112, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 440, "s": [417.808, 316.828, 0], "to": [0.112, 0.003, 0], "ti": [-0.098, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.18}, "t": 441, "s": [418.123, 316.828, 0], "to": [0.098, -0.003, 0], "ti": [-0.082, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.183}, "t": 442, "s": [418.394, 316.81, 0], "to": [0.082, -0.009, 0], "ti": [-0.066, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.187}, "t": 443, "s": [418.617, 316.774, 0], "to": [0.066, -0.015, 0], "ti": [-0.05, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.189}, "t": 444, "s": [418.791, 316.72, 0], "to": [0.05, -0.021, 0], "ti": [-0.033, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.187}, "t": 445, "s": [418.915, 316.649, 0], "to": [0.033, -0.026, 0], "ti": [-0.017, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.174}, "t": 446, "s": [418.99, 316.562, 0], "to": [0.017, -0.032, 0], "ti": [-0.001, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.158}, "t": 447, "s": [419.015, 316.459, 0], "to": [0.001, -0.036, 0], "ti": [0.015, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 448, "s": [418.993, 316.343, 0], "to": [-0.015, -0.041, 0], "ti": [0.029, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.152}, "t": 449, "s": [418.926, 316.216, 0], "to": [-0.029, -0.044, 0], "ti": [0.043, 0.047, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 450, "s": [418.817, 316.079, 0], "to": [-0.043, -0.047, 0], "ti": [0.055, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 451, "s": [418.669, 315.935, 0], "to": [-0.055, -0.049, 0], "ti": [0.065, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 452, "s": [418.489, 315.787, 0], "to": [-0.065, -0.049, 0], "ti": [0.073, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 453, "s": [418.281, 315.638, 0], "to": [-0.073, -0.049, 0], "ti": [0.079, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 454, "s": [418.052, 315.492, 0], "to": [-0.079, -0.048, 0], "ti": [0.082, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.167}, "t": 455, "s": [417.809, 315.352, 0], "to": [-0.082, -0.045, 0], "ti": [0.08, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.173}, "t": 456, "s": [417.559, 315.223, 0], "to": [-0.08, -0.042, 0], "ti": [0.072, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 457, "s": [417.328, 315.103, 0], "to": [-0.072, -0.038, 0], "ti": [0.061, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.181}, "t": 458, "s": [417.129, 314.992, 0], "to": [-0.061, -0.035, 0], "ti": [0.05, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.185}, "t": 459, "s": [416.964, 314.893, 0], "to": [-0.05, -0.031, 0], "ti": [0.039, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.189}, "t": 460, "s": [416.83, 314.806, 0], "to": [-0.039, -0.026, 0], "ti": [0.029, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.198}, "t": 461, "s": [416.728, 314.734, 0], "to": [-0.029, -0.021, 0], "ti": [0.019, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.215}, "t": 462, "s": [416.656, 314.679, 0], "to": [-0.019, -0.016, 0], "ti": [0.01, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.272}, "t": 463, "s": [416.614, 314.64, 0], "to": [-0.01, -0.01, 0], "ti": [0.001, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.677}, "o": {"x": 0.167, "y": 0.236}, "t": 464, "s": [416.598, 314.62, 0], "to": [-0.001, -0.004, 0], "ti": [-0.008, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.772}, "o": {"x": 0.167, "y": 0.111}, "t": 465, "s": [416.609, 314.619, 0], "to": [0.008, 0.003, 0], "ti": [-0.015, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.131}, "t": 466, "s": [416.644, 314.637, 0], "to": [0.015, 0.009, 0], "ti": [-0.023, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.143}, "t": 467, "s": [416.702, 314.675, 0], "to": [0.023, 0.016, 0], "ti": [-0.029, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.149}, "t": 468, "s": [416.78, 314.731, 0], "to": [0.029, 0.022, 0], "ti": [-0.034, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.153}, "t": 469, "s": [416.875, 314.807, 0], "to": [0.034, 0.028, 0], "ti": [-0.039, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.156}, "t": 470, "s": [416.986, 314.9, 0], "to": [0.039, 0.034, 0], "ti": [-0.043, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 471, "s": [417.11, 315.009, 0], "to": [0.043, 0.039, 0], "ti": [-0.046, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 472, "s": [417.244, 315.135, 0], "to": [0.046, 0.044, 0], "ti": [-0.048, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 473, "s": [417.385, 315.274, 0], "to": [0.048, 0.048, 0], "ti": [-0.048, -0.052, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 474, "s": [417.529, 315.425, 0], "to": [0.048, 0.052, 0], "ti": [-0.048, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 475, "s": [417.674, 315.586, 0], "to": [0.048, 0.055, 0], "ti": [-0.046, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 476, "s": [417.815, 315.754, 0], "to": [0.046, 0.057, 0], "ti": [-0.043, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 477, "s": [417.95, 315.927, 0], "to": [0.043, 0.058, 0], "ti": [-0.039, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 478, "s": [418.074, 316.102, 0], "to": [0.039, 0.058, 0], "ti": [-0.033, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 479, "s": [418.183, 316.275, 0], "to": [0.033, 0.057, 0], "ti": [-0.026, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 480, "s": [418.272, 316.444, 0], "to": [0.026, 0.055, 0], "ti": [-0.017, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 481, "s": [418.338, 316.603, 0], "to": [0.017, 0.051, 0], "ti": [-0.007, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.181}, "t": 482, "s": [418.376, 316.749, 0], "to": [0.007, 0.046, 0], "ti": [0.005, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.179}, "t": 483, "s": [418.38, 316.878, 0], "to": [-0.005, 0.039, 0], "ti": [0.018, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.167}, "t": 484, "s": [418.346, 316.985, 0], "to": [-0.018, 0.031, 0], "ti": [0.034, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.152}, "t": 485, "s": [418.269, 317.065, 0], "to": [-0.034, 0.021, 0], "ti": [0.051, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.146}, "t": 486, "s": [418.143, 317.112, 0], "to": [-0.051, 0.012, 0], "ti": [0.068, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.149}, "t": 487, "s": [417.964, 317.137, 0], "to": [-0.068, 0.006, 0], "ti": [0.083, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.154}, "t": 488, "s": [417.735, 317.15, 0], "to": [-0.083, 0.002, 0], "ti": [0.096, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.157}, "t": 489, "s": [417.466, 317.152, 0], "to": [-0.096, -0.001, 0], "ti": [0.106, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 490, "s": [417.162, 317.145, 0], "to": [-0.106, -0.003, 0], "ti": [0.114, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 491, "s": [416.83, 317.132, 0], "to": [-0.114, -0.005, 0], "ti": [0.12, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 492, "s": [416.478, 317.113, 0], "to": [-0.12, -0.007, 0], "ti": [0.124, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 493, "s": [416.11, 317.089, 0], "to": [-0.124, -0.008, 0], "ti": [0.126, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 494, "s": [415.733, 317.063, 0], "to": [-0.126, -0.009, 0], "ti": [0.127, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 495, "s": [415.352, 317.035, 0], "to": [-0.127, -0.009, 0], "ti": [0.126, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 496, "s": [414.971, 317.007, 0], "to": [-0.126, -0.009, 0], "ti": [0.124, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 497, "s": [414.596, 316.979, 0], "to": [-0.124, -0.009, 0], "ti": [0.12, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 498, "s": [414.23, 316.953, 0], "to": [-0.12, -0.008, 0], "ti": [0.115, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 499, "s": [413.877, 316.929, 0], "to": [-0.115, -0.007, 0], "ti": [0.109, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 500, "s": [413.541, 316.909, 0], "to": [-0.109, -0.006, 0], "ti": [0.102, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 501, "s": [413.223, 316.893, 0], "to": [-0.102, -0.005, 0], "ti": [0.095, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 502, "s": [412.927, 316.881, 0], "to": [-0.095, -0.003, 0], "ti": [0.086, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 503, "s": [412.656, 316.875, 0], "to": [-0.086, -0.001, 0], "ti": [0.077, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 504, "s": [412.41, 316.875, 0], "to": [-0.077, 0.001, 0], "ti": [0.068, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.179}, "t": 505, "s": [412.191, 316.881, 0], "to": [-0.068, 0.003, 0], "ti": [0.059, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.181}, "t": 506, "s": [412.001, 316.893, 0], "to": [-0.059, 0.005, 0], "ti": [0.049, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.184}, "t": 507, "s": [411.839, 316.913, 0], "to": [-0.049, 0.008, 0], "ti": [0.039, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.187}, "t": 508, "s": [411.706, 316.939, 0], "to": [-0.039, 0.01, 0], "ti": [0.03, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.189}, "t": 509, "s": [411.602, 316.972, 0], "to": [-0.03, 0.012, 0], "ti": [0.021, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.189}, "t": 510, "s": [411.526, 317.012, 0], "to": [-0.021, 0.015, 0], "ti": [0.012, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.179}, "t": 511, "s": [411.478, 317.059, 0], "to": [-0.012, 0.017, 0], "ti": [0.003, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.164}, "t": 512, "s": [411.456, 317.113, 0], "to": [-0.003, 0.019, 0], "ti": [-0.004, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.155}, "t": 513, "s": [411.458, 317.173, 0], "to": [0.004, 0.021, 0], "ti": [-0.011, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.153}, "t": 514, "s": [411.482, 317.24, 0], "to": [0.011, 0.023, 0], "ti": [-0.018, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.805}, "o": {"x": 0.167, "y": 0.155}, "t": 515, "s": [411.527, 317.312, 0], "to": [0.018, 0.025, 0], "ti": [-0.028, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.146}, "t": 516, "s": [411.589, 317.389, 0], "to": [0.028, 0.027, 0], "ti": [-0.044, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.145}, "t": 517, "s": [411.692, 317.471, 0], "to": [0.044, 0.028, 0], "ti": [-0.06, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.151}, "t": 518, "s": [411.85, 317.557, 0], "to": [0.06, 0.028, 0], "ti": [-0.074, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.156}, "t": 519, "s": [412.054, 317.641, 0], "to": [0.074, 0.027, 0], "ti": [-0.085, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 520, "s": [412.294, 317.721, 0], "to": [0.085, 0.026, 0], "ti": [-0.094, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 521, "s": [412.564, 317.794, 0], "to": [0.094, 0.023, 0], "ti": [-0.1, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 522, "s": [412.856, 317.857, 0], "to": [0.1, 0.019, 0], "ti": [-0.104, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 523, "s": [413.163, 317.909, 0], "to": [0.104, 0.015, 0], "ti": [-0.106, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 524, "s": [413.478, 317.946, 0], "to": [0.106, 0.01, 0], "ti": [-0.106, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 525, "s": [413.796, 317.968, 0], "to": [0.106, 0.005, 0], "ti": [-0.104, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 526, "s": [414.111, 317.973, 0], "to": [0.104, -0.001, 0], "ti": [-0.1, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 527, "s": [414.418, 317.961, 0], "to": [0.1, -0.007, 0], "ti": [-0.095, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 528, "s": [414.713, 317.929, 0], "to": [0.095, -0.014, 0], "ti": [-0.089, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 529, "s": [414.99, 317.879, 0], "to": [0.089, -0.02, 0], "ti": [-0.082, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 530, "s": [415.247, 317.81, 0], "to": [0.082, -0.026, 0], "ti": [-0.073, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.173}, "t": 531, "s": [415.481, 317.723, 0], "to": [0.073, -0.032, 0], "ti": [-0.064, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.173}, "t": 532, "s": [415.688, 317.617, 0], "to": [0.064, -0.038, 0], "ti": [-0.054, 0.043, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 533, "s": [415.867, 317.495, 0], "to": [0.054, -0.043, 0], "ti": [-0.044, 0.048, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.171}, "t": 534, "s": [416.015, 317.356, 0], "to": [0.044, -0.048, 0], "ti": [-0.033, 0.053, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.17}, "t": 535, "s": [416.131, 317.204, 0], "to": [0.033, -0.053, 0], "ti": [-0.022, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.168}, "t": 536, "s": [416.215, 317.039, 0], "to": [0.022, -0.057, 0], "ti": [-0.011, 0.06, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.166}, "t": 537, "s": [416.266, 316.864, 0], "to": [0.011, -0.06, 0], "ti": [0, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.165}, "t": 538, "s": [416.284, 316.681, 0], "to": [0, -0.062, 0], "ti": [0.01, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 539, "s": [416.269, 316.494, 0], "to": [-0.01, -0.063, 0], "ti": [0.02, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.164}, "t": 540, "s": [416.223, 316.306, 0], "to": [-0.02, -0.062, 0], "ti": [0.03, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 541, "s": [416.146, 316.119, 0], "to": [-0.03, -0.061, 0], "ti": [0.039, 0.058, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 542, "s": [416.042, 315.939, 0], "to": [-0.039, -0.058, 0], "ti": [0.047, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 543, "s": [415.912, 315.769, 0], "to": [-0.047, -0.054, 0], "ti": [0.054, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 544, "s": [415.759, 315.613, 0], "to": [-0.054, -0.049, 0], "ti": [0.06, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.167}, "t": 545, "s": [415.586, 315.476, 0], "to": [-0.06, -0.041, 0], "ti": [0.059, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 546, "s": [415.398, 315.364, 0], "to": [-0.059, -0.039, 0], "ti": [0.049, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.171}, "t": 547, "s": [415.231, 315.241, 0], "to": [-0.049, -0.045, 0], "ti": [0.037, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.168}, "t": 548, "s": [415.102, 315.091, 0], "to": [-0.037, -0.054, 0], "ti": [0.025, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 549, "s": [415.01, 314.92, 0], "to": [-0.025, -0.059, 0], "ti": [0.015, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 550, "s": [414.95, 314.736, 0], "to": [-0.015, -0.063, 0], "ti": [0.006, 0.064, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 551, "s": [414.92, 314.545, 0], "to": [-0.006, -0.064, 0], "ti": [-0.003, 0.063, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 552, "s": [414.917, 314.353, 0], "to": [0.003, -0.063, 0], "ti": [-0.01, 0.061, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 553, "s": [414.937, 314.166, 0], "to": [0.01, -0.061, 0], "ti": [-0.017, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 554, "s": [414.979, 313.988, 0], "to": [0.017, -0.057, 0], "ti": [-0.022, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 555, "s": [415.038, 313.823, 0], "to": [0.022, -0.052, 0], "ti": [-0.027, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 556, "s": [415.112, 313.677, 0], "to": [0.027, -0.045, 0], "ti": [-0.03, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 557, "s": [415.198, 313.551, 0], "to": [0.03, -0.038, 0], "ti": [-0.033, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.175}, "t": 558, "s": [415.293, 313.449, 0], "to": [0.033, -0.03, 0], "ti": [-0.034, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.175}, "t": 559, "s": [415.395, 313.373, 0], "to": [0.034, -0.021, 0], "ti": [-0.035, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.173}, "t": 560, "s": [415.5, 313.324, 0], "to": [0.035, -0.011, 0], "ti": [-0.035, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.169}, "t": 561, "s": [415.606, 313.304, 0], "to": [0.035, -0.002, 0], "ti": [-0.034, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.165}, "t": 562, "s": [415.71, 313.314, 0], "to": [0.034, 0.008, 0], "ti": [-0.032, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.161}, "t": 563, "s": [415.809, 313.354, 0], "to": [0.032, 0.018, 0], "ti": [-0.029, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.159}, "t": 564, "s": [415.901, 313.423, 0], "to": [0.029, 0.028, 0], "ti": [-0.025, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 565, "s": [415.982, 313.52, 0], "to": [0.025, 0.037, 0], "ti": [-0.02, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.158}, "t": 566, "s": [416.051, 313.645, 0], "to": [0.02, 0.046, 0], "ti": [-0.015, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 567, "s": [416.105, 313.794, 0], "to": [0.015, 0.054, 0], "ti": [-0.009, -0.061, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 568, "s": [416.141, 313.967, 0], "to": [0.009, 0.061, 0], "ti": [-0.001, -0.067, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 569, "s": [416.157, 314.158, 0], "to": [0.001, 0.067, 0], "ti": [0.007, -0.071, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 570, "s": [416.149, 314.366, 0], "to": [-0.007, 0.071, 0], "ti": [0.016, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 571, "s": [416.116, 314.586, 0], "to": [-0.016, 0.075, 0], "ti": [0.025, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 572, "s": [416.056, 314.813, 0], "to": [-0.025, 0.076, 0], "ti": [0.036, -0.076, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 573, "s": [415.965, 315.043, 0], "to": [-0.036, 0.076, 0], "ti": [0.047, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.163}, "t": 574, "s": [415.841, 315.269, 0], "to": [-0.047, 0.074, 0], "ti": [0.059, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.163}, "t": 575, "s": [415.682, 315.486, 0], "to": [-0.059, 0.07, 0], "ti": [0.073, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.156}, "t": 576, "s": [415.486, 315.687, 0], "to": [-0.073, 0.068, 0], "ti": [0.089, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.156}, "t": 577, "s": [415.241, 315.895, 0], "to": [-0.089, 0.073, 0], "ti": [0.102, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 578, "s": [414.953, 316.123, 0], "to": [-0.102, 0.079, 0], "ti": [0.11, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 579, "s": [414.632, 316.366, 0], "to": [-0.11, 0.083, 0], "ti": [0.116, -0.086, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 580, "s": [414.29, 316.622, 0], "to": [-0.116, 0.086, 0], "ti": [0.118, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 581, "s": [413.938, 316.885, 0], "to": [-0.118, 0.089, 0], "ti": [0.117, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 582, "s": [413.583, 317.155, 0], "to": [-0.117, 0.09, 0], "ti": [0.113, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 583, "s": [413.236, 317.426, 0], "to": [-0.113, 0.09, 0], "ti": [0.107, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 584, "s": [412.903, 317.697, 0], "to": [-0.107, 0.09, 0], "ti": [0.099, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 585, "s": [412.591, 317.965, 0], "to": [-0.099, 0.089, 0], "ti": [0.089, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 586, "s": [412.307, 318.228, 0], "to": [-0.089, 0.087, 0], "ti": [0.078, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 587, "s": [412.056, 318.485, 0], "to": [-0.078, 0.084, 0], "ti": [0.065, -0.081, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 588, "s": [411.842, 318.732, 0], "to": [-0.065, 0.081, 0], "ti": [0.05, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 589, "s": [411.669, 318.969, 0], "to": [-0.05, 0.077, 0], "ti": [0.036, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.178}, "t": 590, "s": [411.539, 319.194, 0], "to": [-0.036, 0.073, 0], "ti": [0.02, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.177}, "t": 591, "s": [411.455, 319.407, 0], "to": [-0.02, 0.069, 0], "ti": [0.005, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.174}, "t": 592, "s": [411.417, 319.607, 0], "to": [-0.005, 0.064, 0], "ti": [-0.011, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.169}, "t": 593, "s": [411.426, 319.792, 0], "to": [0.011, 0.059, 0], "ti": [-0.026, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.164}, "t": 594, "s": [411.481, 319.963, 0], "to": [0.026, 0.055, 0], "ti": [-0.04, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.16}, "t": 595, "s": [411.581, 320.12, 0], "to": [0.04, 0.05, 0], "ti": [-0.054, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.159}, "t": 596, "s": [411.724, 320.262, 0], "to": [0.054, 0.045, 0], "ti": [-0.067, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 597, "s": [411.906, 320.39, 0], "to": [0.067, 0.041, 0], "ti": [-0.078, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 598, "s": [412.124, 320.505, 0], "to": [0.078, 0.036, 0], "ti": [-0.087, -0.032, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 599, "s": [412.372, 320.608, 0], "to": [0.087, 0.032, 0], "ti": [-0.095, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 600, "s": [412.646, 320.7, 0], "to": [0.095, 0.029, 0], "ti": [-0.1, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 601, "s": [412.939, 320.781, 0], "to": [0.1, 0.026, 0], "ti": [-0.102, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.166}, "t": 602, "s": [413.245, 320.854, 0], "to": [0.102, 0.023, 0], "ti": [-0.102, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 603, "s": [413.553, 320.92, 0], "to": [0.102, 0.021, 0], "ti": [-0.099, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.171}, "t": 604, "s": [413.857, 320.982, 0], "to": [0.099, 0.02, 0], "ti": [-0.092, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.174}, "t": 605, "s": [414.147, 321.042, 0], "to": [0.092, 0.02, 0], "ti": [-0.08, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 606, "s": [414.41, 321.101, 0], "to": [0.08, 0.014, 0], "ti": [-0.064, 0, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.192}, "t": 607, "s": [414.628, 321.126, 0], "to": [0.064, 0, 0], "ti": [-0.047, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.186}, "t": 608, "s": [414.792, 321.102, 0], "to": [0.047, -0.016, 0], "ti": [-0.03, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.168}, "t": 609, "s": [414.907, 321.032, 0], "to": [0.03, -0.031, 0], "ti": [-0.015, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.154}, "t": 610, "s": [414.974, 320.918, 0], "to": [0.015, -0.045, 0], "ti": [0, 0.057, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.151}, "t": 611, "s": [414.996, 320.764, 0], "to": [0, -0.057, 0], "ti": [0.013, 0.069, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.152}, "t": 612, "s": [414.976, 320.573, 0], "to": [-0.013, -0.069, 0], "ti": [0.026, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 613, "s": [414.917, 320.348, 0], "to": [-0.026, -0.08, 0], "ti": [0.037, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 614, "s": [414.821, 320.093, 0], "to": [-0.037, -0.09, 0], "ti": [0.048, 0.098, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 615, "s": [414.694, 319.81, 0], "to": [-0.048, -0.098, 0], "ti": [0.057, 0.106, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 616, "s": [414.536, 319.503, 0], "to": [-0.057, -0.106, 0], "ti": [0.065, 0.112, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 617, "s": [414.353, 319.175, 0], "to": [-0.065, -0.112, 0], "ti": [0.072, 0.117, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 618, "s": [414.147, 318.831, 0], "to": [-0.072, -0.117, 0], "ti": [0.077, 0.121, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 619, "s": [413.923, 318.473, 0], "to": [-0.077, -0.121, 0], "ti": [0.081, 0.124, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 620, "s": [413.684, 318.105, 0], "to": [-0.081, -0.124, 0], "ti": [0.084, 0.125, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 621, "s": [413.434, 317.73, 0], "to": [-0.084, -0.125, 0], "ti": [0.086, 0.125, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 622, "s": [413.178, 317.354, 0], "to": [-0.086, -0.125, 0], "ti": [0.086, 0.124, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 623, "s": [412.919, 316.978, 0], "to": [-0.086, -0.124, 0], "ti": [0.084, 0.122, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 624, "s": [412.663, 316.607, 0], "to": [-0.084, -0.122, 0], "ti": [0.081, 0.118, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 625, "s": [412.413, 316.246, 0], "to": [-0.081, -0.118, 0], "ti": [0.077, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 626, "s": [412.174, 315.897, 0], "to": [-0.077, -0.113, 0], "ti": [0.071, 0.107, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 627, "s": [411.952, 315.565, 0], "to": [-0.071, -0.107, 0], "ti": [0.063, 0.099, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 628, "s": [411.751, 315.255, 0], "to": [-0.063, -0.099, 0], "ti": [0.053, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.178}, "t": 629, "s": [411.576, 314.969, 0], "to": [-0.053, -0.09, 0], "ti": [0.042, 0.08, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.182}, "t": 630, "s": [411.432, 314.713, 0], "to": [-0.042, -0.08, 0], "ti": [0.029, 0.068, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.188}, "t": 631, "s": [411.325, 314.49, 0], "to": [-0.029, -0.068, 0], "ti": [0.014, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.197}, "t": 632, "s": [411.26, 314.305, 0], "to": [-0.014, -0.055, 0], "ti": [-0.003, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.198}, "t": 633, "s": [411.242, 314.162, 0], "to": [0.003, -0.04, 0], "ti": [-0.022, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.793}, "o": {"x": 0.167, "y": 0.164}, "t": 634, "s": [411.278, 314.066, 0], "to": [0.022, -0.024, 0], "ti": [-0.042, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.139}, "t": 635, "s": [411.374, 314.021, 0], "to": [0.042, -0.006, 0], "ti": [-0.058, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.153}, "t": 636, "s": [411.533, 314.031, 0], "to": [0.058, 0.008, 0], "ti": [-0.064, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 637, "s": [411.722, 314.07, 0], "to": [0.064, 0.016, 0], "ti": [-0.067, -0.02, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 638, "s": [411.919, 314.124, 0], "to": [0.067, 0.02, 0], "ti": [-0.068, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 639, "s": [412.122, 314.19, 0], "to": [0.068, 0.023, 0], "ti": [-0.069, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 640, "s": [412.329, 314.264, 0], "to": [0.069, 0.026, 0], "ti": [-0.069, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 641, "s": [412.537, 314.344, 0], "to": [0.069, 0.027, 0], "ti": [-0.068, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 642, "s": [412.744, 314.429, 0], "to": [0.068, 0.028, 0], "ti": [-0.067, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 643, "s": [412.947, 314.515, 0], "to": [0.067, 0.029, 0], "ti": [-0.065, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 644, "s": [413.145, 314.601, 0], "to": [0.065, 0.028, 0], "ti": [-0.063, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 645, "s": [413.337, 314.684, 0], "to": [0.063, 0.027, 0], "ti": [-0.06, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 646, "s": [413.521, 314.764, 0], "to": [0.06, 0.026, 0], "ti": [-0.057, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 647, "s": [413.695, 314.839, 0], "to": [0.057, 0.024, 0], "ti": [-0.053, -0.021, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 648, "s": [413.86, 314.907, 0], "to": [0.053, 0.021, 0], "ti": [-0.049, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 649, "s": [414.013, 314.967, 0], "to": [0.049, 0.019, 0], "ti": [-0.045, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 650, "s": [414.156, 315.018, 0], "to": [0.045, 0.016, 0], "ti": [-0.042, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 651, "s": [414.286, 315.06, 0], "to": [0.042, 0.012, 0], "ti": [-0.038, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 652, "s": [414.405, 315.092, 0], "to": [0.038, 0.009, 0], "ti": [-0.034, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.178}, "t": 653, "s": [414.512, 315.114, 0], "to": [0.034, 0.005, 0], "ti": [-0.03, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.178}, "t": 654, "s": [414.607, 315.125, 0], "to": [0.03, 0.002, 0], "ti": [-0.027, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.177}, "t": 655, "s": [414.692, 315.125, 0], "to": [0.027, -0.002, 0], "ti": [-0.023, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.175}, "t": 656, "s": [414.767, 315.114, 0], "to": [0.023, -0.005, 0], "ti": [-0.02, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.171}, "t": 657, "s": [414.832, 315.093, 0], "to": [0.02, -0.009, 0], "ti": [-0.018, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.167}, "t": 658, "s": [414.89, 315.062, 0], "to": [0.018, -0.012, 0], "ti": [-0.016, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 659, "s": [414.94, 315.021, 0], "to": [0.016, -0.015, 0], "ti": [-0.015, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.162}, "t": 660, "s": [414.986, 314.972, 0], "to": [0.015, -0.018, 0], "ti": [-0.014, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.162}, "t": 661, "s": [415.028, 314.916, 0], "to": [0.014, -0.02, 0], "ti": [-0.014, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.162}, "t": 662, "s": [415.069, 314.853, 0], "to": [0.014, -0.022, 0], "ti": [-0.014, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 663, "s": [415.111, 314.786, 0], "to": [0.014, -0.023, 0], "ti": [-0.016, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 664, "s": [415.156, 314.715, 0], "to": [0.016, -0.024, 0], "ti": [-0.018, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.162}, "t": 665, "s": [415.207, 314.642, 0], "to": [0.018, -0.024, 0], "ti": [-0.016, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.167}, "t": 666, "s": [415.265, 314.569, 0], "to": [0.016, -0.026, 0], "ti": [-0.009, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.162}, "t": 667, "s": [415.305, 314.485, 0], "to": [0.009, -0.03, 0], "ti": [0, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.159}, "t": 668, "s": [415.318, 314.388, 0], "to": [0, -0.034, 0], "ti": [0.008, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 669, "s": [415.305, 314.28, 0], "to": [-0.008, -0.037, 0], "ti": [0.015, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 670, "s": [415.269, 314.167, 0], "to": [-0.015, -0.038, 0], "ti": [0.022, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 671, "s": [415.213, 314.05, 0], "to": [-0.022, -0.039, 0], "ti": [0.027, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 672, "s": [415.139, 313.934, 0], "to": [-0.027, -0.039, 0], "ti": [0.032, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 673, "s": [415.05, 313.819, 0], "to": [-0.032, -0.037, 0], "ti": [0.036, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 674, "s": [414.947, 313.709, 0], "to": [-0.036, -0.035, 0], "ti": [0.039, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 675, "s": [414.834, 313.607, 0], "to": [-0.039, -0.033, 0], "ti": [0.042, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 676, "s": [414.712, 313.513, 0], "to": [-0.042, -0.03, 0], "ti": [0.043, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 677, "s": [414.584, 313.429, 0], "to": [-0.043, -0.026, 0], "ti": [0.045, 0.022, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 678, "s": [414.452, 313.357, 0], "to": [-0.045, -0.022, 0], "ti": [0.045, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 679, "s": [414.317, 313.297, 0], "to": [-0.045, -0.018, 0], "ti": [0.045, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 680, "s": [414.182, 313.251, 0], "to": [-0.045, -0.013, 0], "ti": [0.044, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 681, "s": [414.049, 313.219, 0], "to": [-0.044, -0.008, 0], "ti": [0.042, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 682, "s": [413.919, 313.202, 0], "to": [-0.042, -0.003, 0], "ti": [0.04, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 683, "s": [413.795, 313.199, 0], "to": [-0.04, 0.001, 0], "ti": [0.038, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 684, "s": [413.678, 313.21, 0], "to": [-0.038, 0.006, 0], "ti": [0.034, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 685, "s": [413.569, 313.236, 0], "to": [-0.034, 0.011, 0], "ti": [0.031, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 686, "s": [413.472, 313.274, 0], "to": [-0.031, 0.015, 0], "ti": [0.026, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 687, "s": [413.386, 313.324, 0], "to": [-0.026, 0.019, 0], "ti": [0.021, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.17}, "t": 688, "s": [413.315, 313.386, 0], "to": [-0.021, 0.022, 0], "ti": [0.016, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.169}, "t": 689, "s": [413.258, 313.457, 0], "to": [-0.016, 0.025, 0], "ti": [0.01, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.168}, "t": 690, "s": [413.218, 313.535, 0], "to": [-0.01, 0.027, 0], "ti": [0.004, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.166}, "t": 691, "s": [413.197, 313.619, 0], "to": [-0.004, 0.029, 0], "ti": [-0.003, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 692, "s": [413.194, 313.707, 0], "to": [0.003, 0.029, 0], "ti": [-0.01, -0.029, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.162}, "t": 693, "s": [413.213, 313.795, 0], "to": [0.01, 0.029, 0], "ti": [-0.017, -0.028, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.16}, "t": 694, "s": [413.253, 313.882, 0], "to": [0.017, 0.028, 0], "ti": [-0.025, -0.026, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.158}, "t": 695, "s": [413.317, 313.964, 0], "to": [0.025, 0.026, 0], "ti": [-0.034, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.143}, "t": 696, "s": [413.405, 314.04, 0], "to": [0.034, 0.031, 0], "ti": [-0.044, -0.045, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.146}, "t": 697, "s": [413.523, 314.152, 0], "to": [0.044, 0.045, 0], "ti": [-0.054, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.151}, "t": 698, "s": [413.671, 314.311, 0], "to": [0.054, 0.06, 0], "ti": [-0.062, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 699, "s": [413.846, 314.511, 0], "to": [0.062, 0.073, 0], "ti": [-0.069, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 700, "s": [414.043, 314.748, 0], "to": [0.069, 0.084, 0], "ti": [-0.075, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 701, "s": [414.259, 315.016, 0], "to": [0.075, 0.094, 0], "ti": [-0.08, -0.102, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 702, "s": [414.491, 315.311, 0], "to": [0.08, 0.102, 0], "ti": [-0.083, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 703, "s": [414.737, 315.627, 0], "to": [0.083, 0.108, 0], "ti": [-0.086, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 704, "s": [414.992, 315.96, 0], "to": [0.086, 0.113, 0], "ti": [-0.088, -0.117, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 705, "s": [415.255, 316.307, 0], "to": [0.088, 0.117, 0], "ti": [-0.089, -0.119, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 706, "s": [415.522, 316.662, 0], "to": [0.089, 0.119, 0], "ti": [-0.09, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 707, "s": [415.791, 317.022, 0], "to": [0.09, 0.12, 0], "ti": [-0.089, -0.12, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 708, "s": [416.06, 317.383, 0], "to": [0.089, 0.12, 0], "ti": [-0.088, -0.119, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 709, "s": [416.327, 317.741, 0], "to": [0.088, 0.119, 0], "ti": [-0.086, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 710, "s": [416.589, 318.094, 0], "to": [0.086, 0.116, 0], "ti": [-0.084, -0.113, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 711, "s": [416.844, 318.438, 0], "to": [0.084, 0.113, 0], "ti": [-0.081, -0.108, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 712, "s": [417.091, 318.77, 0], "to": [0.081, 0.108, 0], "ti": [-0.077, -0.103, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 713, "s": [417.328, 319.088, 0], "to": [0.077, 0.103, 0], "ti": [-0.073, -0.097, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 714, "s": [417.554, 319.389, 0], "to": [0.073, 0.097, 0], "ti": [-0.068, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 715, "s": [417.766, 319.67, 0], "to": [0.068, 0.09, 0], "ti": [-0.064, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 716, "s": [417.965, 319.929, 0], "to": [0.064, 0.083, 0], "ti": [-0.058, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 717, "s": [418.148, 320.165, 0], "to": [0.058, 0.074, 0], "ti": [-0.053, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 718, "s": [418.315, 320.376, 0], "to": [0.053, 0.066, 0], "ti": [-0.047, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.18}, "t": 719, "s": [418.464, 320.559, 0], "to": [0.047, 0.057, 0], "ti": [-0.041, -0.047, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.183}, "t": 720, "s": [418.596, 320.715, 0], "to": [0.041, 0.047, 0], "ti": [-0.035, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.188}, "t": 721, "s": [418.71, 320.841, 0], "to": [0.035, 0.037, 0], "ti": [-0.028, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.196}, "t": 722, "s": [418.805, 320.936, 0], "to": [0.028, 0.027, 0], "ti": [-0.022, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.21}, "t": 723, "s": [418.881, 321.001, 0], "to": [0.022, 0.016, 0], "ti": [-0.016, -0.006, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.223}, "t": 724, "s": [418.937, 321.034, 0], "to": [0.016, 0.006, 0], "ti": [-0.009, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.712}, "o": {"x": 0.167, "y": 0.163}, "t": 725, "s": [418.975, 321.034, 0], "to": [0.009, -0.006, 0], "ti": [0.004, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.765}, "o": {"x": 0.167, "y": 0.117}, "t": 726, "s": [418.991, 321, 0], "to": [-0.004, -0.021, 0], "ti": [0.023, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.8}, "o": {"x": 0.167, "y": 0.129}, "t": 727, "s": [418.952, 320.909, 0], "to": [-0.023, -0.04, 0], "ti": [0.042, 0.059, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.143}, "t": 728, "s": [418.852, 320.758, 0], "to": [-0.042, -0.059, 0], "ti": [0.058, 0.076, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.15}, "t": 729, "s": [418.7, 320.554, 0], "to": [-0.058, -0.076, 0], "ti": [0.071, 0.09, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 730, "s": [418.504, 320.304, 0], "to": [-0.071, -0.09, 0], "ti": [0.082, 0.103, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 731, "s": [418.273, 320.012, 0], "to": [-0.082, -0.103, 0], "ti": [0.091, 0.113, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 732, "s": [418.012, 319.687, 0], "to": [-0.091, -0.113, 0], "ti": [0.097, 0.122, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 733, "s": [417.729, 319.333, 0], "to": [-0.097, -0.122, 0], "ti": [0.101, 0.129, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 734, "s": [417.431, 318.955, 0], "to": [-0.101, -0.129, 0], "ti": [0.103, 0.134, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 735, "s": [417.124, 318.561, 0], "to": [-0.103, -0.134, 0], "ti": [0.103, 0.137, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 736, "s": [416.813, 318.153, 0], "to": [-0.103, -0.137, 0], "ti": [0.102, 0.139, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 737, "s": [416.503, 317.738, 0], "to": [-0.102, -0.139, 0], "ti": [0.099, 0.139, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 738, "s": [416.201, 317.321, 0], "to": [-0.099, -0.139, 0], "ti": [0.094, 0.138, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 739, "s": [415.91, 316.905, 0], "to": [-0.094, -0.138, 0], "ti": [0.089, 0.135, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 740, "s": [415.634, 316.495, 0], "to": [-0.089, -0.135, 0], "ti": [0.082, 0.131, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 741, "s": [415.378, 316.094, 0], "to": [-0.082, -0.131, 0], "ti": [0.074, 0.126, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 742, "s": [415.144, 315.708, 0], "to": [-0.074, -0.126, 0], "ti": [0.065, 0.12, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 743, "s": [414.936, 315.339, 0], "to": [-0.065, -0.12, 0], "ti": [0.055, 0.112, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 744, "s": [414.757, 314.99, 0], "to": [-0.055, -0.112, 0], "ti": [0.044, 0.104, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 745, "s": [414.608, 314.665, 0], "to": [-0.044, -0.104, 0], "ti": [0.033, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 746, "s": [414.492, 314.367, 0], "to": [-0.033, -0.094, 0], "ti": [0.022, 0.084, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.18}, "t": 747, "s": [414.41, 314.098, 0], "to": [-0.022, -0.084, 0], "ti": [0.01, 0.073, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 748, "s": [414.363, 313.861, 0], "to": [-0.01, -0.073, 0], "ti": [-0.002, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.184}, "t": 749, "s": [414.352, 313.659, 0], "to": [0.002, -0.062, 0], "ti": [-0.014, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.182}, "t": 750, "s": [414.377, 313.492, 0], "to": [0.014, -0.049, 0], "ti": [-0.026, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.173}, "t": 751, "s": [414.438, 313.363, 0], "to": [0.026, -0.036, 0], "ti": [-0.038, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.162}, "t": 752, "s": [414.535, 313.274, 0], "to": [0.038, -0.023, 0], "ti": [-0.049, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.154}, "t": 753, "s": [414.666, 313.225, 0], "to": [0.049, -0.009, 0], "ti": [-0.06, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.152}, "t": 754, "s": [414.831, 313.218, 0], "to": [0.06, 0.005, 0], "ti": [-0.07, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.153}, "t": 755, "s": [415.029, 313.254, 0], "to": [0.07, 0.019, 0], "ti": [-0.077, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.163}, "t": 756, "s": [415.254, 313.331, 0], "to": [0.077, 0.027, 0], "ti": [-0.078, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 757, "s": [415.488, 313.415, 0], "to": [0.078, 0.027, 0], "ti": [-0.077, -0.025, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 758, "s": [415.722, 313.493, 0], "to": [0.077, 0.025, 0], "ti": [-0.076, -0.022, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 759, "s": [415.953, 313.563, 0], "to": [0.076, 0.022, 0], "ti": [-0.075, -0.018, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 760, "s": [416.18, 313.622, 0], "to": [0.075, 0.018, 0], "ti": [-0.072, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 761, "s": [416.4, 313.67, 0], "to": [0.072, 0.014, 0], "ti": [-0.069, -0.009, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 762, "s": [416.613, 313.705, 0], "to": [0.069, 0.009, 0], "ti": [-0.066, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 763, "s": [416.816, 313.726, 0], "to": [0.066, 0.004, 0], "ti": [-0.062, 0.001, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 764, "s": [417.009, 313.731, 0], "to": [0.062, -0.001, 0], "ti": [-0.058, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.172}, "t": 765, "s": [417.191, 313.72, 0], "to": [0.058, -0.006, 0], "ti": [-0.054, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 766, "s": [417.36, 313.693, 0], "to": [0.054, -0.012, 0], "ti": [-0.05, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.17}, "t": 767, "s": [417.516, 313.649, 0], "to": [0.05, -0.017, 0], "ti": [-0.046, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.169}, "t": 768, "s": [417.659, 313.589, 0], "to": [0.046, -0.023, 0], "ti": [-0.041, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.168}, "t": 769, "s": [417.79, 313.513, 0], "to": [0.041, -0.028, 0], "ti": [-0.037, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.166}, "t": 770, "s": [417.907, 313.422, 0], "to": [0.037, -0.033, 0], "ti": [-0.033, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 771, "s": [418.011, 313.315, 0], "to": [0.033, -0.038, 0], "ti": [-0.029, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.165}, "t": 772, "s": [418.104, 313.196, 0], "to": [0.029, -0.042, 0], "ti": [-0.025, 0.046, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 773, "s": [418.185, 313.063, 0], "to": [0.025, -0.046, 0], "ti": [-0.022, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 774, "s": [418.256, 312.92, 0], "to": [0.022, -0.049, 0], "ti": [-0.019, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.165}, "t": 775, "s": [418.317, 312.769, 0], "to": [0.019, -0.052, 0], "ti": [-0.017, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 776, "s": [418.372, 312.61, 0], "to": [0.017, -0.054, 0], "ti": [-0.015, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 777, "s": [418.42, 312.447, 0], "to": [0.015, -0.055, 0], "ti": [-0.014, 0.055, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 778, "s": [418.464, 312.282, 0], "to": [0.014, -0.055, 0], "ti": [-0.014, 0.054, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 779, "s": [418.506, 312.118, 0], "to": [0.014, -0.054, 0], "ti": [-0.015, 0.052, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 780, "s": [418.549, 311.958, 0], "to": [0.015, -0.052, 0], "ti": [-0.016, 0.049, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 781, "s": [418.594, 311.805, 0], "to": [0.016, -0.049, 0], "ti": [-0.019, 0.045, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 782, "s": [418.645, 311.663, 0], "to": [0.019, -0.045, 0], "ti": [-0.022, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.173}, "t": 783, "s": [418.705, 311.536, 0], "to": [0.022, -0.039, 0], "ti": [-0.026, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.172}, "t": 784, "s": [418.777, 311.428, 0], "to": [0.026, -0.032, 0], "ti": [-0.032, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.168}, "t": 785, "s": [418.864, 311.343, 0], "to": [0.032, -0.024, 0], "ti": [-0.031, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.171}, "t": 786, "s": [418.967, 311.282, 0], "to": [0.031, -0.023, 0], "ti": [-0.024, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.169}, "t": 787, "s": [419.052, 311.207, 0], "to": [0.024, -0.028, 0], "ti": [-0.014, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.166}, "t": 788, "s": [419.109, 311.113, 0], "to": [0.014, -0.034, 0], "ti": [-0.005, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.164}, "t": 789, "s": [419.137, 311.005, 0], "to": [0.005, -0.037, 0], "ti": [0.005, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.163}, "t": 790, "s": [419.137, 310.889, 0], "to": [-0.005, -0.039, 0], "ti": [0.014, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.162}, "t": 791, "s": [419.107, 310.771, 0], "to": [-0.014, -0.039, 0], "ti": [0.024, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 792, "s": [419.05, 310.656, 0], "to": [-0.024, -0.037, 0], "ti": [0.033, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 793, "s": [418.964, 310.546, 0], "to": [-0.033, -0.035, 0], "ti": [0.042, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 794, "s": [418.851, 310.448, 0], "to": [-0.042, -0.031, 0], "ti": [0.05, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 795, "s": [418.712, 310.363, 0], "to": [-0.05, -0.026, 0], "ti": [0.059, 0.019, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 796, "s": [418.548, 310.295, 0], "to": [-0.059, -0.019, 0], "ti": [0.066, 0.013, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 797, "s": [418.361, 310.246, 0], "to": [-0.066, -0.013, 0], "ti": [0.073, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 798, "s": [418.152, 310.218, 0], "to": [-0.073, -0.005, 0], "ti": [0.079, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.161}, "t": 799, "s": [417.923, 310.214, 0], "to": [-0.079, 0.003, 0], "ti": [0.085, -0.011, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 800, "s": [417.676, 310.233, 0], "to": [-0.085, 0.011, 0], "ti": [0.089, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 801, "s": [417.415, 310.278, 0], "to": [-0.089, 0.019, 0], "ti": [0.093, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 802, "s": [417.14, 310.348, 0], "to": [-0.093, 0.027, 0], "ti": [0.096, -0.036, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 803, "s": [416.857, 310.442, 0], "to": [-0.096, 0.036, 0], "ti": [0.097, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 804, "s": [416.567, 310.561, 0], "to": [-0.097, 0.044, 0], "ti": [0.098, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 805, "s": [416.274, 310.704, 0], "to": [-0.098, 0.051, 0], "ti": [0.097, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 806, "s": [415.981, 310.868, 0], "to": [-0.097, 0.058, 0], "ti": [0.094, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 807, "s": [415.694, 311.051, 0], "to": [-0.094, 0.064, 0], "ti": [0.091, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 808, "s": [415.414, 311.253, 0], "to": [-0.091, 0.07, 0], "ti": [0.086, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 809, "s": [415.149, 311.469, 0], "to": [-0.086, 0.074, 0], "ti": [0.079, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 810, "s": [414.9, 311.696, 0], "to": [-0.079, 0.077, 0], "ti": [0.071, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 811, "s": [414.675, 311.93, 0], "to": [-0.071, 0.079, 0], "ti": [0.061, -0.079, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 812, "s": [414.476, 312.168, 0], "to": [-0.061, 0.079, 0], "ti": [0.049, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.175}, "t": 813, "s": [414.311, 312.404, 0], "to": [-0.049, 0.078, 0], "ti": [0.035, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 814, "s": [414.185, 312.634, 0], "to": [-0.035, 0.075, 0], "ti": [0.019, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.177}, "t": 815, "s": [414.103, 312.852, 0], "to": [-0.019, 0.07, 0], "ti": [0.006, -0.071, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.161}, "t": 816, "s": [414.069, 313.056, 0], "to": [-0.006, 0.071, 0], "ti": [-0.002, -0.078, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 817, "s": [414.064, 313.279, 0], "to": [0.002, 0.078, 0], "ti": [-0.009, -0.084, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 818, "s": [414.082, 313.524, 0], "to": [0.009, 0.084, 0], "ti": [-0.015, -0.089, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 819, "s": [414.118, 313.786, 0], "to": [0.015, 0.089, 0], "ti": [-0.02, -0.092, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 820, "s": [414.172, 314.059, 0], "to": [0.02, 0.092, 0], "ti": [-0.024, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 821, "s": [414.239, 314.339, 0], "to": [0.024, 0.094, 0], "ti": [-0.028, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 822, "s": [414.319, 314.622, 0], "to": [0.028, 0.094, 0], "ti": [-0.031, -0.093, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 823, "s": [414.407, 314.903, 0], "to": [0.031, 0.093, 0], "ti": [-0.033, -0.09, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.169}, "t": 824, "s": [414.503, 315.178, 0], "to": [0.033, 0.09, 0], "ti": [-0.034, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 825, "s": [414.604, 315.445, 0], "to": [0.034, 0.087, 0], "ti": [-0.035, -0.083, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.171}, "t": 826, "s": [414.709, 315.699, 0], "to": [0.035, 0.083, 0], "ti": [-0.035, -0.077, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 827, "s": [414.814, 315.94, 0], "to": [0.035, 0.077, 0], "ti": [-0.035, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 828, "s": [414.919, 316.164, 0], "to": [0.035, 0.072, 0], "ti": [-0.034, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.174}, "t": 829, "s": [415.022, 316.369, 0], "to": [0.034, 0.065, 0], "ti": [-0.032, -0.058, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 830, "s": [415.122, 316.555, 0], "to": [0.032, 0.058, 0], "ti": [-0.03, -0.051, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 831, "s": [415.216, 316.72, 0], "to": [0.03, 0.051, 0], "ti": [-0.028, -0.044, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.18}, "t": 832, "s": [415.304, 316.864, 0], "to": [0.028, 0.044, 0], "ti": [-0.025, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.182}, "t": 833, "s": [415.384, 316.986, 0], "to": [0.025, 0.037, 0], "ti": [-0.022, -0.03, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.186}, "t": 834, "s": [415.455, 317.086, 0], "to": [0.022, 0.03, 0], "ti": [-0.019, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.191}, "t": 835, "s": [415.517, 317.165, 0], "to": [0.019, 0.023, 0], "ti": [-0.015, -0.016, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.2}, "t": 836, "s": [415.568, 317.223, 0], "to": [0.015, 0.016, 0], "ti": [-0.011, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.217}, "t": 837, "s": [415.607, 317.262, 0], "to": [0.011, 0.01, 0], "ti": [-0.007, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.871}, "o": {"x": 0.167, "y": 0.27}, "t": 838, "s": [415.633, 317.284, 0], "to": [0.007, 0.005, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.692}, "o": {"x": 0.167, "y": 0.241}, "t": 839, "s": [415.647, 317.29, 0], "to": [0.002, 0, 0], "ti": [0.002, 0.004, 0]}, {"i": {"x": 0.833, "y": 0.777}, "o": {"x": 0.167, "y": 0.115}, "t": 840, "s": [415.648, 317.283, 0], "to": [-0.002, -0.004, 0], "ti": [0.007, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.133}, "t": 841, "s": [415.635, 317.265, 0], "to": [-0.007, -0.007, 0], "ti": [0.011, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.144}, "t": 842, "s": [415.608, 317.241, 0], "to": [-0.011, -0.009, 0], "ti": [0.016, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.15}, "t": 843, "s": [415.566, 317.212, 0], "to": [-0.016, -0.01, 0], "ti": [0.021, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.822}, "o": {"x": 0.167, "y": 0.154}, "t": 844, "s": [415.51, 317.183, 0], "to": [-0.021, -0.009, 0], "ti": [0.025, 0.007, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.157}, "t": 845, "s": [415.44, 317.159, 0], "to": [-0.025, -0.007, 0], "ti": [0.027, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.163}, "t": 846, "s": [415.358, 317.139, 0], "to": [-0.027, -0.01, 0], "ti": [0.025, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.16}, "t": 847, "s": [415.28, 317.099, 0], "to": [-0.025, -0.017, 0], "ti": [0.023, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 848, "s": [415.208, 317.036, 0], "to": [-0.023, -0.024, 0], "ti": [0.022, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 849, "s": [415.14, 316.956, 0], "to": [-0.022, -0.028, 0], "ti": [0.021, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 850, "s": [415.075, 316.865, 0], "to": [-0.021, -0.031, 0], "ti": [0.021, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.165}, "t": 851, "s": [415.012, 316.767, 0], "to": [-0.021, -0.033, 0], "ti": [0.022, 0.033, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 852, "s": [414.947, 316.668, 0], "to": [-0.022, -0.033, 0], "ti": [0.022, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 853, "s": [414.881, 316.571, 0], "to": [-0.022, -0.031, 0], "ti": [0.024, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.17}, "t": 854, "s": [414.813, 316.48, 0], "to": [-0.024, -0.029, 0], "ti": [0.025, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.171}, "t": 855, "s": [414.74, 316.399, 0], "to": [-0.025, -0.025, 0], "ti": [0.027, 0.02, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.171}, "t": 856, "s": [414.662, 316.33, 0], "to": [-0.027, -0.02, 0], "ti": [0.029, 0.015, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.169}, "t": 857, "s": [414.579, 316.276, 0], "to": [-0.029, -0.015, 0], "ti": [0.031, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.166}, "t": 858, "s": [414.489, 316.24, 0], "to": [-0.031, -0.009, 0], "ti": [0.033, 0.002, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.162}, "t": 859, "s": [414.393, 316.222, 0], "to": [-0.033, -0.002, 0], "ti": [0.036, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.159}, "t": 860, "s": [414.289, 316.225, 0], "to": [-0.036, 0.005, 0], "ti": [0.038, -0.012, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.158}, "t": 861, "s": [414.178, 316.25, 0], "to": [-0.038, 0.012, 0], "ti": [0.041, -0.019, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.157}, "t": 862, "s": [414.06, 316.296, 0], "to": [-0.041, 0.019, 0], "ti": [0.043, -0.027, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 863, "s": [413.934, 316.366, 0], "to": [-0.043, 0.027, 0], "ti": [0.046, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.158}, "t": 864, "s": [413.801, 316.457, 0], "to": [-0.046, 0.034, 0], "ti": [0.048, -0.041, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 865, "s": [413.66, 316.57, 0], "to": [-0.048, 0.041, 0], "ti": [0.05, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 866, "s": [413.513, 316.704, 0], "to": [-0.05, 0.048, 0], "ti": [0.052, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 867, "s": [413.36, 316.858, 0], "to": [-0.052, 0.054, 0], "ti": [0.054, -0.06, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 868, "s": [413.202, 317.029, 0], "to": [-0.054, 0.06, 0], "ti": [0.055, -0.065, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 869, "s": [413.039, 317.216, 0], "to": [-0.055, 0.065, 0], "ti": [0.056, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 870, "s": [412.872, 317.416, 0], "to": [-0.056, 0.068, 0], "ti": [0.057, -0.071, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 871, "s": [412.703, 317.627, 0], "to": [-0.057, 0.071, 0], "ti": [0.057, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 872, "s": [412.533, 317.844, 0], "to": [-0.057, 0.073, 0], "ti": [0.057, -0.074, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 873, "s": [412.362, 318.065, 0], "to": [-0.057, 0.074, 0], "ti": [0.056, -0.073, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 874, "s": [412.193, 318.285, 0], "to": [-0.056, 0.073, 0], "ti": [0.054, -0.07, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 875, "s": [412.028, 318.5, 0], "to": [-0.054, 0.07, 0], "ti": [0.05, -0.068, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 876, "s": [411.868, 318.706, 0], "to": [-0.05, 0.068, 0], "ti": [0.044, -0.066, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 877, "s": [411.726, 318.907, 0], "to": [-0.044, 0.066, 0], "ti": [0.035, -0.063, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 878, "s": [411.607, 319.1, 0], "to": [-0.035, 0.063, 0], "ti": [0.026, -0.059, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 879, "s": [411.515, 319.283, 0], "to": [-0.026, 0.059, 0], "ti": [0.015, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.179}, "t": 880, "s": [411.453, 319.454, 0], "to": [-0.015, 0.054, 0], "ti": [0.004, -0.049, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.178}, "t": 881, "s": [411.424, 319.61, 0], "to": [-0.004, 0.049, 0], "ti": [-0.008, -0.043, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.173}, "t": 882, "s": [411.431, 319.749, 0], "to": [0.008, 0.043, 0], "ti": [-0.021, -0.037, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.166}, "t": 883, "s": [411.474, 319.871, 0], "to": [0.021, 0.037, 0], "ti": [-0.034, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.158}, "t": 884, "s": [411.555, 319.973, 0], "to": [0.034, 0.031, 0], "ti": [-0.047, -0.024, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.155}, "t": 885, "s": [411.675, 320.055, 0], "to": [0.047, 0.024, 0], "ti": [-0.059, -0.017, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.154}, "t": 886, "s": [411.834, 320.116, 0], "to": [0.059, 0.017, 0], "ti": [-0.072, -0.01, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.155}, "t": 887, "s": [412.032, 320.157, 0], "to": [0.072, 0.01, 0], "ti": [-0.084, -0.003, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.156}, "t": 888, "s": [412.267, 320.177, 0], "to": [0.084, 0.003, 0], "ti": [-0.096, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.157}, "t": 889, "s": [412.537, 320.176, 0], "to": [0.096, -0.003, 0], "ti": [-0.107, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.159}, "t": 890, "s": [412.842, 320.156, 0], "to": [0.107, -0.01, 0], "ti": [-0.117, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.16}, "t": 891, "s": [413.178, 320.117, 0], "to": [0.117, -0.016, 0], "ti": [-0.126, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 892, "s": [413.542, 320.061, 0], "to": [0.126, -0.021, 0], "ti": [-0.133, 0.026, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 893, "s": [413.931, 319.989, 0], "to": [0.133, -0.026, 0], "ti": [-0.139, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 894, "s": [414.341, 319.903, 0], "to": [0.139, -0.03, 0], "ti": [-0.144, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 895, "s": [414.766, 319.807, 0], "to": [0.144, -0.034, 0], "ti": [-0.146, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 896, "s": [415.202, 319.702, 0], "to": [0.146, -0.036, 0], "ti": [-0.147, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 897, "s": [415.643, 319.591, 0], "to": [0.147, -0.037, 0], "ti": [-0.145, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 898, "s": [416.083, 319.478, 0], "to": [0.145, -0.038, 0], "ti": [-0.141, 0.036, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 899, "s": [416.514, 319.366, 0], "to": [0.141, -0.036, 0], "ti": [-0.135, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 900, "s": [416.93, 319.259, 0], "to": [0.135, -0.034, 0], "ti": [-0.125, 0.03, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.174}, "t": 901, "s": [417.322, 319.162, 0], "to": [0.125, -0.03, 0], "ti": [-0.113, 0.025, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.178}, "t": 902, "s": [417.682, 319.079, 0], "to": [0.113, -0.025, 0], "ti": [-0.098, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.183}, "t": 903, "s": [418.002, 319.014, 0], "to": [0.098, -0.017, 0], "ti": [-0.08, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.192}, "t": 904, "s": [418.272, 318.974, 0], "to": [0.08, -0.008, 0], "ti": [-0.059, -0.001, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.202}, "t": 905, "s": [418.481, 318.963, 0], "to": [0.059, 0.001, 0], "ti": [-0.045, -0.004, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.182}, "t": 906, "s": [418.627, 318.981, 0], "to": [0.045, 0.004, 0], "ti": [-0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.181}, "t": 907, "s": [418.752, 318.989, 0], "to": [0.038, 0, 0], "ti": [-0.032, 0.005, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 908, "s": [418.858, 318.981, 0], "to": [0.032, -0.005, 0], "ti": [-0.024, 0.009, 0]}, {"i": {"x": 0.833, "y": 0.855}, "o": {"x": 0.167, "y": 0.193}, "t": 909, "s": [418.942, 318.961, 0], "to": [0.024, -0.009, 0], "ti": [-0.014, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.195}, "t": 910, "s": [418.999, 318.93, 0], "to": [0.014, -0.012, 0], "ti": [-0.004, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.803}, "o": {"x": 0.167, "y": 0.171}, "t": 911, "s": [419.027, 318.89, 0], "to": [0.004, -0.014, 0], "ti": [0.007, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.797}, "o": {"x": 0.167, "y": 0.145}, "t": 912, "s": [419.023, 318.845, 0], "to": [-0.007, -0.016, 0], "ti": [0.019, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.141}, "t": 913, "s": [418.984, 318.795, 0], "to": [-0.019, -0.017, 0], "ti": [0.012, 0.009, 0]}, {"t": 914.000037227983, "s": [418.91, 318.743, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [194, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [262, 262, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"t": 0, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 11, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 19, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.646, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 29, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 40, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 48, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 57, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 68, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 76, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 86, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 97, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 105, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 114, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 125, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 133, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 143, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 154, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 162, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 171, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 182, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 190, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 200, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 211, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 219, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 228, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 239, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 247, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 257, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 268, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 276, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}, {"t": 285, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.685, 4.845], [0.701, 8.326], [-5.766, 4.845], [-9.415, -4.523], [-7.258, -8.083], [-3.932, -6.745], [-2.284, -2.613], [0.203, -3.111], [1.54, -5.316], [5.178, -8.704], [7.665, -7.586]], "c": true}], "h": 1}, {"t": 296, "s": [{"i": [[2.351, -3.861], [1.129, -1.129], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-1.284, 0.419], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.465, 0.159], [0.759, 1.335], [1.203, -0.079], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[3.971, 5.036], [0.701, 7.849], [-5.384, 4.655], [-9.224, -4.523], [-7.449, -7.892], [-3.932, -7.175], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.273, -9.133], [7.999, -7.586]], "c": true}], "h": 1}, {"t": 304.000012382174, "s": [{"i": [[2.351, -3.861], [1.48, -0.368], [1.933, 1.748], [-0.064, 3.814], [-1.13, 1.095], [-1.059, -1.059], [-0.434, -1.47], [-0.647, 0.096], [-0.539, 1.222], [-2.052, 0.171], [-0.995, -0.994]], "o": [[-0.933, 1.292], [-2.696, -0.169], [-2.494, -2.85], [0.247, -1.585], [1.376, -0.276], [0.759, 1.335], [0.6, 0.409], [0.798, -0.984], [1.586, -3.216], [1.492, 0.124], [0.8, 4.637]], "v": [[4.114, 4.75], [0.319, 7.802], [-5.05, 4.559], [-9.224, -4.523], [-7.449, -7.892], [-4.266, -7.318], [-2.284, -3.09], [0.108, -2.92], [1.874, -5.412], [5.798, -9.228], [7.856, -7.347]], "c": true}], "h": 1}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"t": 0, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 11, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 19, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 29, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 40, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 48, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 58, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 69, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 77, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 87, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 98, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 106, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 116, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 127, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 135, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 145, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 156, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 164, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 173, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 184, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 192, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 202, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 213, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 221, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 231, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 242, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 250, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 260, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 271, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 279, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 291, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 302, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 310, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}, {"t": 320, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.474, -8.198], [8.544, -12.959], [2.451, -12.943], [-1.071, -10.521], [-4.628, -12.558], [-10.691, -11.368], [-13.856, -4.974], [-14.051, -3.021], [-13.228, 1.277], [-4.983, 11.388], [2.575, 12.44], [7.856, 6.52]], "c": true}], "h": 1}, {"t": 331, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-3.857, -3.572], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[12.333, -8.102], [8.544, -12.959], [2.117, -13.086], [-0.833, -9.376], [-4.628, -12.701], [-11.025, -10.986], [-14.238, -4.926], [-13.812, -2.162], [-13.514, 1.277], [-5.174, 10.767], [2.67, 12.344], [8.19, 6.616]], "c": true}], "h": 1}, {"t": 339.000013807753, "s": [{"i": [[1.324, 5.789], [1.933, 1.202], [2.274, -1.461], [1.603, -0.178], [1.009, 0.64], [1.789, -1.056], [-0.039, -3.211], [-0.014, -0.345], [-0.383, -1.381], [-4.382, -3], [-2.557, 0.533], [-1.524, 2.347]], "o": [[-1.042, -2.002], [-2.591, -0.722], [-1.275, 0.942], [-0.977, -0.688], [-2.004, -0.413], [-2.141, 2.26], [0.015, 0.356], [0.018, 1.314], [0.971, 5.191], [2.193, 1.397], [2.341, -1.56], [2.857, -4.848]], "v": [[11.808, -8.15], [8.735, -12.625], [2.26, -12.323], [-0.833, -9.089], [-5.01, -12.796], [-11.837, -10.986], [-14.524, -4.926], [-14.433, -2.162], [-13.753, 1.516], [-5.556, 10.863], [3.624, 11.724], [9.478, 5.805]], "c": true}], "h": 1}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.074509803922, 0.078431372549, 0.086274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [312.684, 54.793], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 915.000037268714, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "CARA 2", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [81.558, 161.375, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [81.558, 161.375, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 32, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33.2, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 33.8, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 35, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 40, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 41.2, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 41.8, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 43, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 77, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78.2, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78.8, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 80, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 122, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 123.2, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 123.8, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 125, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 130, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 131.2, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 131.8, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 133, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 177, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 178.2, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 178.8, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 180, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 241, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 242.2, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 242.8, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 244, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 249, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 250.2, "s": [100, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 250.8, "s": [100, 0, 100]}, {"t": 252.00001026417, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [48.5, 48.5], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.074509803922, 0.078431372549, 0.086274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.047058827269, 0.039215686275, 0.062745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [14.438, 154.719], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [145.403, 188.662], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 3", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [48.5, 48.5], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.074509803922, 0.078431372549, 0.086274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.047058827269, 0.039215686275, 0.062745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [159.946, 144.845], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [145.403, 188.662], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 299.00001217852, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "SONRRISA", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"k": [{"s": [12.994], "t": 14, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.984], "t": 15, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.969], "t": 16, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.944], "t": 17, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.903], "t": 18, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.838], "t": 19, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.732], "t": 20, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.563], "t": 21, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.288], "t": 22, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [11.846], "t": 23, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [11.133], "t": 24, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [9.983], "t": 25, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [8.128], "t": 26, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [5.136], "t": 27, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.864], "t": 28, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-2.128], "t": 29, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-3.983], "t": 30, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-5.133], "t": 31, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-5.846], "t": 32, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.288], "t": 33, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.563], "t": 34, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.732], "t": 35, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.838], "t": 36, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.903], "t": 37, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.944], "t": 38, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.969], "t": 39, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.984], "t": 40, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.994], "t": 41, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-7], "t": 83, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.993], "t": 84, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.982], "t": 85, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.965], "t": 86, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.94], "t": 87, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.902], "t": 88, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.846], "t": 89, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.76], "t": 90, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.632], "t": 91, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.44], "t": 92, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.151], "t": 93, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-5.716], "t": 94, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-5.062], "t": 95, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-4.078], "t": 96, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-2.6], "t": 97, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-0.378], "t": 98, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [2.963], "t": 99, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [7.985], "t": 100, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [13.037], "t": 101, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.378], "t": 102, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [18.6], "t": 103, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [20.078], "t": 104, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [21.062], "t": 105, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [21.716], "t": 106, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.151], "t": 107, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.44], "t": 108, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.632], "t": 109, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.76], "t": 110, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.846], "t": 111, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.902], "t": 112, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.94], "t": 113, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.965], "t": 114, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.982], "t": 115, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.961], "t": 149, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.932], "t": 150, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.887], "t": 151, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.818], "t": 152, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.711], "t": 153, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.546], "t": 154, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.292], "t": 155, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [21.9], "t": 156, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [21.295], "t": 157, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [20.363], "t": 158, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [18.925], "t": 159, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [16.708], "t": 160, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [13.288], "t": 161, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [8.015], "t": 162, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [2.712], "t": 163, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-0.708], "t": 164, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-2.925], "t": 165, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-4.363], "t": 166, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-5.295], "t": 167, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-5.9], "t": 168, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.292], "t": 169, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.546], "t": 170, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.711], "t": 171, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.818], "t": 172, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.887], "t": 173, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.932], "t": 174, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.961], "t": 175, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.98], "t": 176, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.992], "t": 177, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-7], "t": 178, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.993], "t": 199, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.982], "t": 200, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.964], "t": 201, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.934], "t": 202, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.883], "t": 203, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.797], "t": 204, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.654], "t": 205, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.416], "t": 206, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-6.018], "t": 207, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-5.352], "t": 208, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-4.239], "t": 209, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [-2.38], "t": 210, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0.726], "t": 211, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [5.274], "t": 212, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [8.38], "t": 213, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [10.239], "t": 214, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [11.352], "t": 215, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.018], "t": 216, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.416], "t": 217, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.654], "t": 218, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.797], "t": 219, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.883], "t": 220, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.934], "t": 221, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.964], "t": 222, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.982], "t": 223, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.993], "t": 224, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "p": {"k": [{"s": [99.076, 111.153, 0], "t": 17, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.082, 111.077, 0], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.092, 110.953, 0], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.109, 110.754, 0], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.135, 110.432, 0], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.177, 109.914, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.245, 109.077, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.355, 107.728, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.532, 105.552, 0], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.818, 102.042, 0], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [100.279, 96.381, 0], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [100.936, 88.299, 0], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.397, 82.638, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.683, 79.128, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.86, 76.952, 0], "t": 31, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.97, 75.603, 0], "t": 32, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.038, 74.766, 0], "t": 33, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.08, 74.248, 0], "t": 34, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.106, 73.926, 0], "t": 35, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.123, 73.727, 0], "t": 36, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.133, 73.603, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.139, 73.527, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.143, 73.479, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.062, 73.437, 0], "t": 87, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [102.008, 73.447, 0], "t": 88, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.928, 73.463, 0], "t": 89, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.807, 73.487, 0], "t": 90, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.624, 73.522, 0], "t": 91, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [101.351, 73.576, 0], "t": 92, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [100.939, 73.657, 0], "t": 93, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [100.32, 73.778, 0], "t": 94, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.39, 73.96, 0], "t": 95, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [97.991, 74.234, 0], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [95.888, 74.646, 0], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [92.727, 75.265, 0], "t": 98, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [87.974, 76.196, 0], "t": 99, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [80.828, 77.596, 0], "t": 100, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [73.641, 79.004, 0], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.888, 79.935, 0], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [65.727, 80.554, 0], "t": 103, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [63.624, 80.966, 0], "t": 104, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.225, 81.24, 0], "t": 105, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.295, 81.422, 0], "t": 106, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [60.676, 81.543, 0], "t": 107, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [60.264, 81.624, 0], "t": 108, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.991, 81.678, 0], "t": 109, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.808, 81.713, 0], "t": 110, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.687, 81.737, 0], "t": 111, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.607, 81.753, 0], "t": 112, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.553, 81.763, 0], "t": 113, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.494, 81.775, 0], "t": 115, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 81.837, 0], "t": 149, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 81.88, 0], "t": 150, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 81.946, 0], "t": 151, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 82.047, 0], "t": 152, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 82.203, 0], "t": 153, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 82.444, 0], "t": 154, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 82.816, 0], "t": 155, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 83.39, 0], "t": 156, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 84.274, 0], "t": 157, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 85.638, 0], "t": 158, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 87.741, 0], "t": 159, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 90.985, 0], "t": 160, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 95.988, 0], "t": 161, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 103.704, 0], "t": 162, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 111.462, 0], "t": 163, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 116.465, 0], "t": 164, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 119.709, 0], "t": 165, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 121.812, 0], "t": 166, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 123.176, 0], "t": 167, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 124.06, 0], "t": 168, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 124.634, 0], "t": 169, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 125.006, 0], "t": 170, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 125.247, 0], "t": 171, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 125.403, 0], "t": 172, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 125.504, 0], "t": 173, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.468, 125.613, 0], "t": 175, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.502, 125.657, 0], "t": 200, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.538, 125.644, 0], "t": 201, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.599, 125.622, 0], "t": 202, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.7, 125.585, 0], "t": 203, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.869, 125.524, 0], "t": 204, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [60.152, 125.421, 0], "t": 205, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [60.624, 125.249, 0], "t": 206, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.413, 124.962, 0], "t": 207, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.731, 124.483, 0], "t": 208, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [64.933, 123.681, 0], "t": 209, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.614, 122.342, 0], "t": 210, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74.765, 120.103, 0], "t": 211, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [83.77, 116.827, 0], "t": 212, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [89.921, 114.588, 0], "t": 213, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [93.602, 113.249, 0], "t": 214, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [95.804, 112.447, 0], "t": 215, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [97.122, 111.968, 0], "t": 216, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [97.911, 111.681, 0], "t": 217, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [98.383, 111.509, 0], "t": 218, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [98.666, 111.406, 0], "t": 219, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [98.835, 111.345, 0], "t": 220, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [98.936, 111.308, 0], "t": 221, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [99.033, 111.273, 0], "t": 223, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [52.25, 232, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [24.75, 24.75, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"k": [{"s": [{"i": [[0, 0], [-86.116, -17.611], [-29.009, 4.38], [-17.034, 10.8], [0, 0]], "o": [[0, 0], [22.323, 4.565], [29.695, -4.483], [40.333, -25.571], [0, 0]], "v": [[-69.803, 271.333], [47.39, 349.982], [115.692, 352.68], [190.481, 321.121], [261.104, 223.879]], "c": false}], "t": 94, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-85.917, -17.569], [-28.961, 4.386], [-16.996, 10.776], [0, 0]], "o": [[0, 0], [22.298, 4.553], [29.627, -4.474], [40.244, -25.516], [0, 0]], "v": [[-69.5, 271.395], [47.416, 349.86], [115.558, 352.552], [190.173, 321.066], [260.629, 224.053]], "c": false}], "t": 95, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-84.827, -17.343], [-28.698, 4.422], [-16.787, 10.646], [0, 0]], "o": [[0, 0], [22.162, 4.487], [29.257, -4.425], [39.757, -25.212], [0, 0]], "v": [[-67.845, 271.735], [47.559, 349.192], [114.822, 351.853], [188.487, 320.767], [258.029, 225.001]], "c": false}], "t": 96, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-78.635, -16.057], [-27.204, 4.624], [-15.601, 9.905], [0, 0]], "o": [[0, 0], [21.387, 4.112], [27.15, -4.146], [36.99, -23.484], [0, 0]], "v": [[-58.435, 273.666], [48.367, 345.401], [110.64, 347.883], [178.907, 319.067], [243.257, 230.393]], "c": false}], "t": 97, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-43.555, -8.77], [-18.739, 5.77], [-8.881, 5.711], [0, 0]], "o": [[0, 0], [16.996, 1.988], [15.218, -2.564], [21.315, -13.699], [0, 0]], "v": [[-5.13, 284.603], [52.946, 323.924], [86.946, 325.389], [124.634, 309.435], [159.566, 260.935]], "c": false}], "t": 98, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-8.554, -1.5], [-10.294, 6.914], [-2.175, 1.527], [0, 0]], "o": [[0, 0], [12.615, -0.131], [3.313, -0.985], [5.676, -3.935], [0, 0]], "v": [[48.055, 295.515], [57.515, 302.496], [63.306, 302.946], [70.485, 299.826], [76.066, 291.409]], "c": false}], "t": 99, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-2.362, -0.214], [-8.8, 7.116], [-0.989, 0.786], [0, 0]], "o": [[0, 0], [11.84, -0.505], [1.207, -0.706], [2.91, -2.208], [0, 0]], "v": [[57.464, 297.446], [58.323, 298.704], [59.124, 298.976], [60.905, 298.126], [61.293, 296.8]], "c": false}], "t": 100, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-1.273, 0.012], [-8.537, 7.152], [-0.78, 0.656], [0, 0]], "o": [[0, 0], [11.704, -0.571], [0.836, -0.657], [2.423, -1.904], [0, 0]], "v": [[59.12, 297.785], [58.465, 298.037], [58.388, 298.277], [59.219, 297.827], [58.693, 297.749]], "c": false}], "t": 101, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-1.073, 0.054], [-8.489, 7.158], [-0.742, 0.632], [0, 0]], "o": [[0, 0], [11.679, -0.583], [0.768, -0.648], [2.334, -1.848], [0, 0]], "v": [[59.423, 297.848], [58.491, 297.915], [58.253, 298.149], [58.911, 297.772], [58.218, 297.922]], "c": false}], "t": 102, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-1.073, 0.054], [-8.489, 7.158], [-0.742, 0.632], [0, 0]], "o": [[0, 0], [11.679, -0.583], [0.768, -0.648], [2.334, -1.848], [0, 0]], "v": [[59.423, 297.848], [58.491, 297.915], [58.253, 298.149], [58.911, 297.772], [58.218, 297.922]], "c": false}], "t": 206, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-1.331, 0], [-8.551, 7.15], [-0.792, 0.663], [0, 0]], "o": [[0, 0], [11.711, -0.568], [0.856, -0.66], [2.449, -1.92], [0, 0]], "v": [[59.03, 297.767], [58.458, 298.073], [58.428, 298.315], [59.31, 297.843], [58.834, 297.698]], "c": false}], "t": 207, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-3.215, -0.391], [-9.006, 7.088], [-1.152, 0.888], [0, 0]], "o": [[0, 0], [11.947, -0.454], [1.497, -0.745], [3.29, -2.445], [0, 0]], "v": [[56.169, 297.18], [58.212, 299.226], [59.7, 299.522], [62.224, 298.36], [63.326, 296.058]], "c": false}], "t": 208, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-16.821, -3.217], [-12.289, 6.644], [-3.759, 2.515], [0, 0]], "o": [[0, 0], [13.65, 0.37], [6.125, -1.358], [9.37, -6.241], [0, 0]], "v": [[35.493, 292.938], [56.436, 307.557], [68.89, 308.247], [83.275, 302.096], [95.788, 284.211]], "c": false}], "t": 209, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-70.368, -14.34], [-25.209, 4.894], [-14.017, 8.917], [0, 0]], "o": [[0, 0], [20.352, 3.612], [24.339, -3.773], [33.296, -21.178], [0, 0]], "v": [[-45.873, 276.243], [49.446, 340.34], [105.056, 342.582], [166.117, 316.797], [223.534, 237.59]], "c": false}], "t": 210, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-83.975, -17.166], [-28.492, 4.45], [-16.624, 10.544], [0, 0]], "o": [[0, 0], [22.055, 4.435], [28.967, -4.387], [39.376, -24.974], [0, 0]], "v": [[-66.55, 272.001], [47.67, 348.671], [114.246, 351.307], [187.168, 320.533], [255.996, 225.743]], "c": false}], "t": 211, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-85.858, -17.557], [-28.947, 4.388], [-16.985, 10.769], [0, 0]], "o": [[0, 0], [22.291, 4.549], [29.607, -4.472], [40.217, -25.499], [0, 0]], "v": [[-69.411, 271.414], [47.424, 349.824], [115.518, 352.514], [190.082, 321.05], [260.488, 224.104]], "c": false}], "t": 212, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [{"i": [[0, 0], [-86.116, -17.611], [-29.009, 4.38], [-17.034, 10.8], [0, 0]], "o": [[0, 0], [22.323, 4.565], [29.695, -4.483], [40.333, -25.571], [0, 0]], "v": [[-69.803, 271.333], [47.39, 349.982], [115.692, 352.68], [190.481, 321.121], [261.104, 223.879]], "c": false}], "t": 213, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}]}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.074509803922, 0.078431372549, 0.086274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"k": [{"s": [47], "t": 94, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [47.075], "t": 95, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [47.485], "t": 96, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [49.815], "t": 97, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [63.015], "t": 98, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [76.185], "t": 99, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [78.515], "t": 100, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [78.925], "t": 101, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [79], "t": 206, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [78.903], "t": 207, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [78.194], "t": 208, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [73.074], "t": 209, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [52.926], "t": 210, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [47.806], "t": 211, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [47.097], "t": 212, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [47], "t": 213, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 299.00001217852, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Capa 1 Outlines 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [500, 274, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [194, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [259, 259, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.523, 0.569], [-8.014, -7], [-2.413, -4.521], [-1.341, 0.903], [-3.497, -0.299], [-2.328, -1.671], [-0.136, 0.451], [-7.4, 3.498], [-4.333, -0.014], [-0.499, 0.008], [-5.819, -5.768], [-0.374, -4.739], [-0.55, 0.328], [-3.228, -0.28], [-1.743, -1.162], [-0.18, 0.644], [-6.228, 3.606], [-8.985, -2.977], [-5.11, -7.689], [-0.52, 0.416], [-3.288, -0.472], [-1.599, -1.031], [-0.136, 0.451], [-7.641, 3.768], [-9.29, -3.224], [-3.832, -3.524], [-0.431, -0.383], [-2.194, -3.908], [-1.043, 1.285], [-3.873, -0.078], [-1.517, -1.205], [-0.908, 1.543], [-7.388, 3.308], [-0.48, 0.225], [-7.418, -1.622], [-0.463, -0.098], [-2.634, -1.288], [-0.321, -0.155], [-2.96, -2.152], [0, 0], [0, 0], [0, 0], [-2.868, 1.781], [-0.305, 0.189]], "o": [[10.694, -0.518], [3.697, 3.528], [1.499, -0.566], [3.001, -1.979], [2.775, 0.792], [0.132, -0.437], [2.86, -7.977], [4.044, -1.63], [0.483, -0.008], [8.43, -0.006], [3.024, 3.536], [0.534, -0.318], [2.82, -1.68], [2.022, 0.524], [0.175, -0.626], [2.339, -6.928], [8.674, -4.292], [8.853, 3.193], [0.504, -0.404], [2.782, -1.978], [1.791, 0.648], [0.132, -0.437], [2.996, -8.358], [9.085, -4.277], [4.838, 1.893], [0.418, 0.372], [3.293, 3.088], [1.569, -0.393], [2.882, -2.75], [1.949, 0.259], [1.763, -0.587], [4.473, -6.943], [0.466, -0.217], [7.215, -2.806], [0.449, 0.095], [2.876, 0.64], [0.312, 0.151], [3.151, 1.6], [0, 0], [0, 0], [0, 0], [6.179, -5.615], [0.296, -0.184], [5.602, -3.442]], "v": [[-166.818, -50.018], [-137.537, -39.65], [-128.339, -28.025], [-124.328, -30.045], [-114.41, -31.506], [-106.949, -27.528], [-106.549, -28.853], [-89.663, -45.117], [-77.382, -47.075], [-75.916, -47.099], [-55.215, -36.975], [-48.749, -24.047], [-47.132, -25.011], [-38.303, -26.534], [-32.831, -24.047], [-32.302, -25.943], [-18.652, -41.584], [8.526, -42.359], [28.354, -25.539], [29.883, -26.763], [38.8, -28.025], [43.774, -25.539], [44.175, -26.864], [62.133, -44.077], [90.036, -44.434], [102.969, -36.478], [104.236, -35.35], [111.923, -25.042], [114.535, -26.72], [123.955, -29.703], [128.338, -27.528], [131.105, -29.983], [149.728, -44.434], [151.139, -45.094], [175.097, -45.428], [176.458, -45.14], [184.549, -42.103], [185.495, -41.647], [194, -35.981], [194, 50.536], [-194, 50.536], [-194, -36.478], [-184.953, -43.874], [-184.057, -44.431]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.074509803922, 0.078431372549, 0.086274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [194, 157.275], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 915.000037268714, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Capa 1 Outlines 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [501, 268, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [194, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [259, 259, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.728, -2.012], [0, 0], [2.921, -2.523], [0, 0], [1.508, -1.862], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [7.725, -2.266], [0, 0], [3.936, -4.392], [0, 0], [0.932, 1.3], [0.049, 0.068], [4.936, 2.637], [1.492, 0.994], [0, 0], [1.136, 0.318], [0, 0], [8.997, -4.865], [4.416, -11.694], [0, 0], [0.643, 0.845], [8.052, 2.723], [0, 0], [7.088, -3.18], [3.296, -9.689], [1.735, 0.901], [9.015, -2.403], [0, 0], [6.667, 5.547], [5.461, 1.329], [0, 0], [7.86, -5.725], [1.065, -7.026], [1.054, 1.243], [3.036, 1.343], [0.215, 0.095], [0, 0], [4.758, -1.767], [1.88, -1.334], [0, 0], [0, 0], [4.086, 5.528], [0, 0], [8.989, 1.665], [2.421, 0]], "o": [[0, 0], [-3.517, 1.624], [0, 0], [-1.852, 1.518], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.254, -3.083], [0, 0], [-5.666, 1.716], [0, 0], [-1.661, -0.554], [-0.046, -0.064], [-3.416, -4.48], [-1.511, -0.81], [0, 0], [-1.121, -0.369], [0, 0], [-9.912, -2.524], [-10.658, 6.724], [0, 0], [-0.689, -0.809], [-5.402, -6.704], [0, 0], [-7.482, -2.572], [-9.269, 4.716], [-1.835, -0.685], [-8.121, -4.191], [0, 0], [-1.081, -8.406], [-4.523, -3.31], [0, 0], [-9.532, -1.468], [-5.479, 4.186], [-1.501, -0.66], [-2.353, -2.429], [-0.215, -0.095], [0, 0], [-4.783, -2.012], [-2.116, 0.935], [0, 0], [0, 0], [-0.89, -6.781], [0, 0], [-5.556, -7.251], [-2.492, -0.27], [-4.928, 0]], "v": [[-176.093, -83.988], [-177.728, -83.298], [-187.036, -77.026], [-188.013, -76.231], [-192.924, -71.218], [-193.502, -70.562], [-194, -70.562], [-194, 86.706], [194, 86.706], [194, -61.115], [188.528, -64.098], [166.144, -66.584], [164.698, -66.18], [151.221, -57.137], [150.723, -56.142], [147.819, -58.43], [147.676, -58.629], [135.819, -68.758], [132.816, -70.562], [132.815, -70.562], [129.427, -71.588], [127.614, -72.102], [97.962, -68.499], [76.108, -40.231], [75.113, -40.231], [73.123, -42.718], [52.231, -56.142], [50.995, -56.625], [27.388, -54.989], [9.451, -33.271], [4.169, -35.692], [-20.892, -38.74], [-21.079, -40.419], [-32.11, -62.212], [-46.759, -69.071], [-47.884, -69.358], [-75.113, -63.601], [-86.554, -46.696], [-89.974, -49.275], [-98.344, -54.365], [-98.99, -54.651], [-100.121, -55.201], [-116.027, -55.024], [-121.872, -51.668], [-123.364, -51.668], [-123.494, -52.82], [-130.329, -71.06], [-131.337, -72.454], [-154.275, -86.265], [-161.642, -86.706]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.863, 0.188, 0.937, 0.5, 0.81, 0.143, 0.959, 1, 0.757, 0.098, 0.98], "ix": 9}}, "s": {"a": 0, "k": [-4.633, -7.336], "ix": 5}, "e": {"a": 0, "k": [4.247, 52.51], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [194, 87.44], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 915.000037268714, "st": 0, "bm": 0}], "markers": []}