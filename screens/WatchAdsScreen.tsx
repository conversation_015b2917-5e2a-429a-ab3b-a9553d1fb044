import React, { useState, useEffect } from 'react';
import { Platform, ScrollView, StyleSheet, Text, View, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { typography } from '@/components/ThemedText';
import { StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ArrowRightIcon from '@/assets/redesign/icon_control_chevronLeft.svg';
import Animated, { FadeInDown, Easing } from 'react-native-reanimated';
import { useLanguage } from '@/services/i18nService';
import { useSound } from '@/components/game/playSound';
import * as Haptics from 'expo-haptics';
import { useAppSettings } from '@/context/AppSettingsContext';
import { useAdBasedAccess } from '@/context/AdBasedAccessContext';
import { useAdSettings } from '@/context/AdSettingsContext';
import { adMobService } from '@/services/adMobService';
import AdsCounter from '@/components/ads/AdsCounter';

// Libraries
import { LinearGradient } from 'expo-linear-gradient';

// SVGs
import PlayGreenIcon from '@/assets/ads/icon_play_green.svg';
import CheckMarkDarkIcon from '@/assets/ads/icon_check_dark.svg';
import PlayGreyIcon from '@/assets/ads/icon_play_grey.svg';
import InfoGreenIcon from '@/assets/ads/icon_info_green.svg';
import CompletedIcon from '@/assets/ads/icon_completed.svg'

// Component
export default function WatchAdsScreen() {
  const navigation = useNavigation();
  const { t } = useLanguage();
  const { playSound } = useSound();
  const { isHapticsOn } = useAppSettings();
  const {
    adBasedAccessState,
    incrementAdsWatched,
    isInternetAvailable,
    checkInternetConnectivity
  } = useAdBasedAccess();
  const { requiredAdsCount } = useAdSettings();

  const [watchedAds, setWatchedAds] = useState<boolean[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [adIndexLoading, setAdIndexLoading] = useState<number | null>(null);
  const [showSuccessCallout, setShowSuccessCallout] = useState(false);

  const getFadeInAnimation = (delay: number) =>
    FadeInDown.duration(400).delay(delay).easing(Easing.bounce);

  // Initialize and sync with global state
  useEffect(() => {
    checkInternetConnectivity();

    // Always start fresh and sync with global state
    const currentWatchedCount = adBasedAccessState.adsWatchedCount;
    const updatedWatchedState = new Array(requiredAdsCount).fill(false);
    for (let i = 0; i < currentWatchedCount && i < requiredAdsCount; i++) {
      updatedWatchedState[i] = true;
    }

    setWatchedAds(updatedWatchedState);
    setShowSuccessCallout(currentWatchedCount >= requiredAdsCount);
  }, [requiredAdsCount]);

  // Sync whenever global state changes
  useEffect(() => {
    const currentWatchedCount = adBasedAccessState.adsWatchedCount;

    const updatedWatchedState = new Array(requiredAdsCount).fill(false);
    for (let i = 0; i < currentWatchedCount && i < requiredAdsCount; i++) {
      updatedWatchedState[i] = true;
    }

    setWatchedAds(updatedWatchedState);
    setShowSuccessCallout(currentWatchedCount >= requiredAdsCount);
  }, [adBasedAccessState.adsWatchedCount, requiredAdsCount]);

  const handleWatchAd = async (adIndex: number) => {
    if (!isInternetAvailable) {
      Alert.alert(
        'No Internet',
        'Please check your internet connection and try again.',
        [{ text: 'OK' }]
      );
      return;
    }

    if (watchedAds[adIndex] || isLoading) {
      return; // Ad already watched or currently loading
    }

    setAdIndexLoading(adIndex);
    setIsLoading(true);

    try {
      // Get an ad from the service
      const ad = await adMobService.getAd();

      if (!ad) {
        Alert.alert(
          'Ad Load Error',
          'Failed to load ad. Please try again.',
          [{ text: 'OK' }]
        );
        setIsLoading(false);
        setAdIndexLoading(null);
        return;
      }

      // Show the ad
      const adWatched = await adMobService.showInterstitialAd(ad);

      if (adWatched) {
        // Mark this ad as watched
        const newWatchedAds = [...watchedAds];
        newWatchedAds[adIndex] = true;
        setWatchedAds(newWatchedAds);

        // Update the context
        incrementAdsWatched();

        // Play success sound and haptic feedback | COMMENTED OUT: To avoid users to know when leaving the app
        // if (isHapticsOn) {
        //   Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // }
        // playSound('successEffect');

        // Check if all ads are watched
        const totalWatched = newWatchedAds.filter(watched => watched).length;
        if (totalWatched >= requiredAdsCount) {
          setShowSuccessCallout(true);
          // User can manually navigate back when ready
        }
      }
    } catch (error) {
      console.error('WatchAdsScreen: Error showing ad:', error);
      Alert.alert(
        'Ad Error',
        'Something went wrong while showing the ad. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
      setAdIndexLoading(null);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="light-content" // Makes icons and text white
      />

      <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={async () => {
            await playSound('tapEffect1');
            if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            navigation.goBack();
          }}
        >
          <ArrowRightIcon width={48} height={48} stroke="white" />
        </TouchableOpacity>

        <View style={styles.container}>
          <Animated.Text
            style={[typography.heading2, styles.title]}
            entering={FadeInDown.duration(400).delay(100).easing(Easing.bounce)}
          >
            {t('watchAdsTitle')}
          </Animated.Text>

          {/* Callout */}
          {showSuccessCallout ? (
            <Animated.View
              style={[styles.callout, styles.calloutUnlocked]}
              entering={getFadeInAnimation(300)}
            >
              <LinearGradient colors={['#42E0FF', '#42FFB7']} style={styles.calloutUnlockedBackground}>
                <CheckMarkDarkIcon width={32} height={32}/>
                <Text style={[typography.subtitle2, styles.calloutText, styles.calloutTextUnlocked]}>
                  {t('watchAdsCalloutUnlocked')}
                </Text>
              </LinearGradient>
            </Animated.View>
          ) : (
            <Animated.View
              style={styles.callout}
              entering={getFadeInAnimation(300)}
            >
              <InfoGreenIcon width={32} height={32}/>
              <Text style={[typography.subtitle2, styles.calloutText]}>
                {t('watchAdsCallout')}
              </Text>
            </Animated.View>
          )}

          {/* Content */}
          <View style={styles.content}>
            <Animated.View entering={getFadeInAnimation(450)}>
              <AdsCounter
                watchedCount={watchedAds.filter(watched => watched).length}
                totalCount={requiredAdsCount}
                style={styles.adsCounter}
              />
            </Animated.View>

            {watchedAds.map((isWatched, index) => (
              <Animated.View key={index} entering={getFadeInAnimation(500 + index * 150)}>
                <TouchableOpacity
                  style={[
                    styles.adItem,
                    isWatched && styles.adItemWatched,
                    isLoading && styles.adItemDisabled
                  ]}
                  onPress={() => handleWatchAd(index)}
                  disabled={isWatched || isLoading || !isInternetAvailable}
                >
                  {isWatched ? (
                    <CompletedIcon width={32} height={32}/>
                  ) : (
                    isLoading && adIndexLoading === index ? (
                      <ActivityIndicator size="small" color="#5E5E69" style={{ width: 32, height: 32 }} />
                    ) : (
                      <PlayGreyIcon width={32} height={32}/>
                    )
                  )}
                  <Text style={[
                    typography.subtitle1,
                    styles.adText,
                    isWatched && styles.adTextWatched
                  ]}>
                    {t('watchAdsAdText')} #{index + 1}
                  </Text>
                  <View style={[
                    styles.adState,
                    isWatched ? styles.adStateWatched : styles.adStatePending
                  ]}>
                    <Text style={[
                      typography.subtitle3,
                      styles.adStateText,
                      isWatched ? styles.adStateTextWatched : styles.adStateTextPending
                    ]}>
                      {isWatched ? t('watchAdsWatched') : t('watchAdsPending')}
                    </Text>
                  </View>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    position: "relative",
    flex: 1,
    backgroundColor: '#131416',
  },
  scrollContainer: {
    flexGrow: 1,
  },
  backButton: {
    zIndex: 999,
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 8,
    left: 8,
    padding: 16,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 72,
    paddingHorizontal: 28,
    paddingBottom: 28,
  },
  title: {
    marginBottom: 32,
    textAlign: 'center',
    maxWidth: 280,
    color: "#F3EBDC",
  },
  callout: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
    backgroundColor: '#232325',
    borderRadius: 32,
    padding: 24,
    paddingVertical: 20,
    paddingHorizontal: 24,
    marginBottom: 32,
    width: '100%',
    maxWidth: 340,
  },
  calloutText: {
    top: 4,
    flex: 1,
    color: '#F3EBDC',
    fontSize: 32,
    lineHeight: 34,
    letterSpacing: 1.2,
  },
  calloutUnlocked: {
    padding: 0,
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  calloutTextUnlocked: {
    color: '#131416',
  },
  calloutUnlockedBackground: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
    backgroundColor: '#232325',
    borderRadius: 32,
    padding: 24,
    paddingVertical: 20,
    paddingHorizontal: 24,
  },
  content: {
    width: '100%',
  },
  adsCounter: {
    color: '#5E5E69',
    fontSize: 20,
    lineHeight: 32,
    letterSpacing: 0.75,
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  adItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#232325',
    borderRadius: 28,
    padding: 24,
    marginBottom: 20,
    width: '100%',
  },
  adText: {
    top: 2,
    flex: 1,
    marginLeft: 12,
    color: '#5E5E69',
    textTransform: 'uppercase',
    fontSize: 26,
    lineHeight: 32,
    letterSpacing: 1.2,
  },
  adTextDisabled: {
    color: '#5E5E69',
  },
  adItemWatched: {
    backgroundColor: '#1D2D2A',
  },
  adItemDisabled: {
    opacity: 0.6,
  },
  adTextWatched: {
    color: '#43FDBC',
  },
  adState: {
    height: 28,
    paddingHorizontal: 12,
    borderRadius: 32,
  },
  adStateText: {
    textTransform: 'uppercase',
    fontSize: 16,
    lineHeight: 20,
    letterSpacing: 0.75,
  },
  adStatePending: {
    borderWidth: 3,
    borderStyle: 'solid',
    borderColor: '#3A3A41',
  },
  adStateWatched: {
    backgroundColor: '#43FDBC',
  },
  adStateTextPending: {
    top: 2,
    color: '#5E5E69',
  },
  adStateTextWatched: {
    top: 5,
    color: '#131416',
  },
  successCallout: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#43FDBC',
    borderRadius: 28,
    padding: 20,
    marginTop: 20,
    gap: 12,
  },
  successText: {
    color: '#131416',
    fontSize: 20,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
});