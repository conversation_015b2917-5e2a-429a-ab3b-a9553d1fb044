import React, { useState, useEffect, useRef } from 'react';
import { Platform, ScrollView, StyleSheet, Text, View, TouchableOpacity, Modal, Animated as RNAnimated } from 'react-native';
import { typography } from '@/components/ThemedText';
import { useGameContext } from '@/components/game/GameContext';
import { StatusBar } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import ArrowRightIcon from '@/assets/redesign/icon_control_chevronLeft.svg';
import IconCasual from '@/assets/redesign/icon_casual.svg';
import IconMild from '@/assets/redesign/icon_mild.svg';
import IconNoLimits from '@/assets/redesign/icon_no_limits.svg';
import IconSpicy from '@/assets/redesign/icon_spicy.svg';
import IconCouple from '@/assets/redesign/icon_couple.svg';
import IconLocked from '@/assets/redesign/icon_locked.svg';
import Animated, { FadeInDown, FadeOut, Easing, useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { useLanguage } from '@/services/i18nService';
import { useSound } from '@/components/game/playSound';
import * as Haptics from 'expo-haptics';
import { useAppSettings } from '@/context/AppSettingsContext';
import { useAdBasedAccess } from '@/context/AdBasedAccessContext';
import { LinearGradient } from 'expo-linear-gradient';
import PlayIcon from '@/assets/redesign/icon_control_play.svg';
import SelectedIcon from '@/assets/redesign/icon_selected.svg';
import RevenueCatUI from 'react-native-purchases-ui';

// Unlock
import UnlockDialog from '@/components/ads/UnlockDialog';

export default function SelectCategoriesScreen() {
  const navigation = useNavigation();
  const { setSelectedQuestionCategory, resetGame, setSelectedCategories: setGlobalCategories } = useGameContext();
  const { t } = useLanguage();
  const { playSound } = useSound();
  const { isHapticsOn, isPremiumUnlocked, unlockPremium } = useAppSettings();
  const { hasTemporaryPremiumAccess, getRemainingFreeContent } = useAdBasedAccess();
  const [selectedCategories, setSelectedCategories] = useState<('casual_questions' | 'mild_questions' | 'spicy_questions' | 'no_limits_questions' | 'couple_questions')[]>([]);
  const [showPaywall, setShowPaywall] = useState(false);
  const [showUnlockDialog, setShowUnlockDialog] = useState(false);
  const fadeAnim = useRef(new RNAnimated.Value(0)).current;

  // Check if user has any form of premium access (subscription or ad-based)
  const hasAnyPremiumAccess = isPremiumUnlocked || hasTemporaryPremiumAccess;

  const toggleCategory = async (category: 'casual_questions' | 'mild_questions' | 'spicy_questions' | 'no_limits_questions' | 'couple_questions') => {
    await playSound('tapEffect2');
    if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const isPremium = category === 'no_limits_questions' || category === 'spicy_questions' || category === 'couple_questions';
    if (isPremium && !hasAnyPremiumAccess) {
      console.log(`Opening unlock dialog for premium category: ${category}`);
      setShowUnlockDialog(true);
      return;
    }

    setSelectedCategories(prev =>
      prev.includes(category) ? prev.filter(c => c !== category) : [...prev, category]
    );
  };

  const getFadeInAnimation = (delay: number) =>
      FadeInDown.duration(400).delay(delay).easing(Easing.bounce);

  const categoryBottomSheetTranslateY = useSharedValue(200);

  const categoryBottomSheetStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: categoryBottomSheetTranslateY.value }],
  }));

  useEffect(() => {
    categoryBottomSheetTranslateY.value = withTiming(selectedCategories.length > 0 ? 0 : 200, {
      duration: 300,
      easing: Easing.out(Easing.exp),
    });
  }, [selectedCategories]);

  useEffect(() => {
    if (showPaywall) {
      fadeAnim.setValue(0);
      setTimeout(() => {
        RNAnimated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }, 300); // delay before fade-in starts
    }
  }, [showPaywall]);

  // Handle screen focus to hide unlock dialog when returning from WatchAdsScreen
  useFocusEffect(
    React.useCallback(() => {
      // Check if user has completed ad watching and has temporary premium access
      if (hasTemporaryPremiumAccess && showUnlockDialog) {
        console.log('SelectCategoriesScreen: User returned with temporary premium access, hiding unlock dialog');
        setShowUnlockDialog(false);
      }

      // Clean up premium categories if user no longer has premium access
      if (!hasAnyPremiumAccess) {
        const premiumCategories = ['spicy_questions', 'no_limits_questions', 'couple_questions'];
        const hasSelectedPremiumCategories = selectedCategories.some(cat => premiumCategories.includes(cat));

        if (hasSelectedPremiumCategories) {
          console.log('SelectCategoriesScreen: User lost premium access, removing premium categories from selection');
          setSelectedCategories(prev =>
            prev.filter(cat => !premiumCategories.includes(cat))
          );
        }
      }
    }, [hasTemporaryPremiumAccess, showUnlockDialog, hasAnyPremiumAccess, selectedCategories])
  );

  return (
    <View style={styles.mainContainer}>

      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="light-content" // Makes icons and text white
      />

      <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={async () => {
            await playSound('tapEffect1');
            if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            resetGame();
            navigation.goBack();
          }}
        >
          <ArrowRightIcon width={48} height={48} stroke="white" />
        </TouchableOpacity>

        <View style={[styles.container, selectedCategories.length > 0 && styles.containerSelected]}>
          <Animated.Text
            style={[typography.heading2, styles.title]}
            entering={FadeInDown.duration(400).delay(100).easing(Easing.bounce)}
            exiting={FadeOut}
            >
            {t('pickTheCategory')}
          </Animated.Text>

          {/* LIST OF CATEGORIES */}
          <View style={styles.buttonStack}>
            <Animated.View
                entering={getFadeInAnimation(300)}
              >
              <TouchableOpacity
                onPress={() => toggleCategory('casual_questions')}
                style={[
                  styles.categoryButton,
                  selectedCategories.includes('casual_questions') && styles.categoryButtonSelected
                ]}
              >
                {selectedCategories.includes('casual_questions') ? (
                  <SelectedIcon style={styles.categoryIconSelected} />
                ) : (
                  <IconCasual style={styles.categoryIcon} />
                )}
                <Text style={[
                  typography.heading3,
                  styles.categoryText,
                  selectedCategories.includes('casual_questions') && styles.categoryTextSelected
                ]}>{t('casual')}</Text>
              </TouchableOpacity>
            </Animated.View>

            <Animated.View
              entering={getFadeInAnimation(450)}
            >
              <TouchableOpacity
                onPress={() => toggleCategory('mild_questions')}
                style={[
                  styles.categoryButton,
                  selectedCategories.includes('mild_questions') && styles.categoryButtonSelected
                ]}
              >
                {selectedCategories.includes('mild_questions') ? (
                  <SelectedIcon style={styles.categoryIconSelected} />
                ) : (
                  <IconMild style={styles.categoryIcon} />
                )}
                <Text style={[
                  typography.heading3,
                  styles.categoryText,
                  selectedCategories.includes('mild_questions') && styles.categoryTextSelected
                ]}>{t('friends')}</Text>
              </TouchableOpacity>
            </Animated.View>

            <Animated.View
              entering={getFadeInAnimation(550)}
            >
              <TouchableOpacity
                onPress={() => toggleCategory('couple_questions')}
                style={[
                  styles.categoryButton,
                  selectedCategories.includes('couple_questions') && styles.categoryButtonSelected
                ]}
              >
                {selectedCategories.includes('couple_questions') ? (
                  <SelectedIcon style={styles.categoryIconSelected} />
                ) : !hasAnyPremiumAccess ? (
                  <IconLocked style={styles.categoryIcon} />
                ) : (
                  <IconCouple style={styles.categoryIcon} />
                )}
                <Text style={[
                  typography.heading3,
                  styles.categoryText,
                  selectedCategories.includes('couple_questions') && styles.categoryTextSelected
                ]}>{t('couple')}</Text>
                {hasTemporaryPremiumAccess && (
                  <Text style={[typography.subtitle2, styles.catrgoryFreeAmount]}>
                    {getRemainingFreeContent('couple_questions')} {t('free')}
                  </Text>
                )}
              </TouchableOpacity>
            </Animated.View>

            <Animated.View
              entering={getFadeInAnimation(650)}
            >
              <TouchableOpacity
                onPress={() => toggleCategory('spicy_questions')}
                style={[
                  styles.categoryButton,
                  selectedCategories.includes('spicy_questions') && styles.categoryButtonSelected
                ]}
              >
                {selectedCategories.includes('spicy_questions') ? (
                  <SelectedIcon style={styles.categoryIconSelected} />
                ) : !hasAnyPremiumAccess ? (
                  <IconLocked style={styles.categoryIcon} />
                ) : (
                  <IconSpicy style={styles.categoryIcon} />
                )}
                <Text style={[
                  typography.heading3,
                  styles.categoryText,
                  selectedCategories.includes('spicy_questions') && styles.categoryTextSelected
                ]}>{t('partners')}</Text>
                {hasTemporaryPremiumAccess && (
                  <Text style={[typography.subtitle2, styles.catrgoryFreeAmount]}>
                    {getRemainingFreeContent('spicy_questions')} {t('free')}
                  </Text>
                )}
              </TouchableOpacity>
            </Animated.View>

            <Animated.View
              entering={getFadeInAnimation(750)}
            >
              <TouchableOpacity
                onPress={() => toggleCategory('no_limits_questions')}
                style={[
                  styles.categoryButton,
                  selectedCategories.includes('no_limits_questions') && styles.categoryButtonSelected
                ]}
              >
                {selectedCategories.includes('no_limits_questions') ? (
                  <SelectedIcon style={styles.categoryIconSelected} />
                ) : !hasAnyPremiumAccess ? (
                  <IconLocked style={styles.categoryIcon} />
                ) : (
                  <IconNoLimits style={styles.categoryIcon} />
                )}
                <Text style={[
                  typography.heading3,
                  styles.categoryText,
                  selectedCategories.includes('no_limits_questions') && styles.categoryTextSelected
                ]}>{t('noLimits')}</Text>
                {hasTemporaryPremiumAccess && (
                  <Text style={[typography.subtitle2, styles.catrgoryFreeAmount]}>
                    {getRemainingFreeContent('no_limits_questions')} {t('free')}
                  </Text>
                )}
              </TouchableOpacity>
            </Animated.View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Sheet for selection */}
      <View style={StyleSheet.absoluteFill} pointerEvents="box-none">
        <View style={{ flex: 1 }} pointerEvents="none" />
        <Animated.View style={[styles.bottomSheetAnimated, categoryBottomSheetStyle]}>
          <TouchableOpacity
            onPress={() => {
                setSelectedQuestionCategory(selectedCategories[0]);
                setGlobalCategories(selectedCategories); // Store all selected categories in context
                navigation.navigate('TouchGame' as never);
            }}
          >
            <LinearGradient
              colors={['#42E0FF', '#42FFB7']}
              style={styles.bottomSheet}
              pointerEvents="auto"
              >
              <Text style={[typography.heading3, styles.bottomSheetText]}>{selectedCategories.length} {t('selectedOption')}</Text>
              <PlayIcon width={64} height={64}/>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Show paywall modal for iOS only - Android uses presentPaywall */}
      {showPaywall && Platform.OS === 'ios' && (
        <Modal
          transparent
          visible
          animationType="none"
          onRequestClose={() => {
            console.log('Modal closed by back button');
            setShowPaywall(false);
          }}
        >
          <RNAnimated.View style={{ flex: 1, opacity: fadeAnim }}>
            <RevenueCatUI.Paywall
              options={{
                fontFamily: "Melindya",
                displayCloseButton: true,
              }}
              onDismiss={() => {
                console.log('Paywall dismissed');
                setShowPaywall(false);
              }}
              onPurchaseCompleted={() => {
                console.log('Purchase completed, updating premium status');
                unlockPremium();
                setShowPaywall(false);
              }}
              onPurchaseError={(error) => {
                console.error('Purchase error:', error);
                setShowPaywall(false);
              }}
            />
          </RNAnimated.View>
        </Modal>
      )}

      {/* UnlockDialog */}
      {showUnlockDialog && (
        <UnlockDialog
          isSequential={false}
          onClose={() => setShowUnlockDialog(false)}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    position: "relative",
    flex: 1,
    backgroundColor: '#131416',
  },
  scrollContainer: {
    flexGrow: 1,
  },
  backButton: {
    zIndex: 999,
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 8,
    left: 8,
    padding: 16,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 72,
    paddingHorizontal: 28,
    paddingBottom: 28,
  },
  containerSelected: {
    paddingBottom: 144,
  },
  title: {
    marginBottom: 48,
    textAlign: 'center',
    maxWidth: 280,
    color: "#F3EBDC",
  },
  buttonStack: {
    width: '100%',
    maxWidth: 340,
    paddingHorizontal: 28,
    gap: 42,
    marginBottom: 40,
  },
  categoryButton: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F3EBDC',
    borderRadius: 28,
    height: 96,
  },
  categoryText: {
    top: 4,
    color: '#131416',
    fontSize: 41,
    lineHeight: 48,
  },
  categoryIcon: {
    position: 'absolute',
    top: -32,
    left: -38,
    width: 100,
    height: 100,
  },
  categoryButtonSelected: {
    borderColor: '#43F5CC',
    borderWidth: 8,
    borderStyle: 'solid',
    backgroundColor: 'transparent',
  },
  categoryTextSelected: {
    color: '#43F5CC',
  },
  categoryIconSelected: {
    position: 'absolute',
    top: -44,
    left: -48,
    width: 100,
    height: 100,
  },
  catrgoryFreeAmount: {
    fontSize: 24,
    lineHeight: 28,
    letterSpacing: 1.5,
    color: '#6D6F75',
    textTransform: 'uppercase',
  },
  bottomSheetContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0
  },
  bottomSheet: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    paddingTop: 20,
    paddingLeft: 28,
    paddingRight: 28,
    paddingBottom: 28,
    minHeight: 140,
  },
  bottomSheetText: {
    top: 4,
    color: '#131416',
    fontSize: 41,
    lineHeight: 48,
  },
  bottomSheetAnimated: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  androidPaywallContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
});