import React, { useState, useEffect } from 'react';
import { Platform, StyleSheet, Text, View, TouchableOpacity, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { typography } from '@/components/ThemedText';
import { StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSound } from '@/components/game/playSound';
import * as Haptics from 'expo-haptics';
import { useAppSettings } from '@/context/AppSettingsContext';
import WhatsNewLog from '@/components/whatsnew/WhatsNewLog';
import { fetchAndUpdateWhatsNewLogs, markWhatsNewAsVisited, getCachedWhatsNewLogs, WhatsNewLog as WhatsNewLogType } from '@/services/whatsNewService';
import NetInfo from '@react-native-community/netinfo';
import { useLanguage } from '@/services/i18nService';
import * as Sentry from '@sentry/react-native';

// SVGs
import ArrowRightIcon from '@/assets/redesign/icon_control_chevronLeft.svg';
import GiftIncon from '@/assets/whatsnew/icon_gift.svg';

// Component
export default function WhatsNewScreen() {
  const navigation = useNavigation();
  const { playSound } = useSound();
  const { isHapticsOn } = useAppSettings();
  const { t } = useLanguage();

  const [logs, setLogs] = useState<WhatsNewLogType[]>([]);
  const [loading, setLoading] = useState(true);

  // Check internet connectivity and show alert if offline
  const checkInternetAndShowAlert = async () => {
    const networkState = await NetInfo.fetch();
    if (!networkState.isConnected) {
      Alert.alert(
        t('whatsNewNoInternetTitle'),
        t('whatsNewNoInternetMessage'),
        [{ text: t('ok') }]
      );
      return false;
    }
    return true;
  };

  // Load What's New logs
  const loadLogs = async () => {
    try {
      setLoading(true);
      console.log('📱 WhatsNewScreen: Starting to load logs...');

      // First, try to get cached logs for immediate display
      const cachedLogs = await getCachedWhatsNewLogs();
      console.log('📱 WhatsNewScreen: Found cached logs:', cachedLogs.length);
      if (cachedLogs.length > 0) {
        setLogs(cachedLogs);
      }

      // Check internet connectivity
      const hasInternet = await checkInternetAndShowAlert();
      if (!hasInternet) {
        console.log('📱 WhatsNewScreen: No internet, using cached logs only');
        setLoading(false);
        return;
      }

      // Fetch fresh logs from API
      console.log('📱 WhatsNewScreen: Fetching fresh logs from API...');
      const freshLogs = await fetchAndUpdateWhatsNewLogs();
      console.log('📱 WhatsNewScreen: Received fresh logs:', freshLogs.length);
      setLogs(freshLogs);

      // Mark as visited to remove red dot indicator
      await markWhatsNewAsVisited();
      console.log('📱 WhatsNewScreen: Marked as visited');

    } catch (error) {
      console.error('📱 WhatsNewScreen: Error loading logs:', error);
      Sentry.captureException(error);

      // Try to show cached logs on error
      const cachedLogs = await getCachedWhatsNewLogs();
      setLogs(cachedLogs);
    } finally {
      setLoading(false);
    }
  };

  // Load logs when screen mounts
  useEffect(() => {
    loadLogs();
  }, []);

  return (
    <View style={styles.mainContainer}>
      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="light-content"
      />

      <TouchableOpacity
        style={styles.backButton}
        onPress={async () => {
          await playSound('tapEffect1');
          if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          navigation.goBack();
        }}
      >
        <ArrowRightIcon width={48} height={48} stroke="white" />
      </TouchableOpacity>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <View style={styles.container}>
          <Text style={[typography.heading2, styles.title]}>
            {t('whatsNewTitle')}
          </Text>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#F3EBDC" />
            </View>
          ) : logs.length > 0 ? (
            logs.map((log) => (
              <WhatsNewLog
                key={log.id}
                type={log.type}
                date={log.date}
                title={log.title || log.title_en || 'No title'}
                description={log.description || log.description_en || 'No description'}
                videoUrl={log.videoUrl}
                updateApp={log.updateApp}
              />
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <GiftIncon width={120} height={120} />
              <Text style={styles.emptyText}>{t('whatsNewEmpty')}</Text>
              <Text style={[typography.nunitoSemiBold, styles.emptySubtext]}>{t('whatsNewEmptySubtext')}</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    position: "relative",
    flex: 1,
    backgroundColor: '#131416',
  },
  backButton: {
    zIndex: 999,
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 8,
    left: 8,
    padding: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 72,
    paddingHorizontal: 28,
    paddingBottom: 28,
  },
  title: {
    marginBottom: 32,
    textAlign: 'center',
    maxWidth: 280,
    color: "#F3EBDC",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 120,
  },
  emptyText: {
    color: '#F3EBDC',
    fontSize: 28,
    fontFamily: 'Melindya',
    textTransform: 'uppercase',
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 12,
  },
  emptySubtext: {
    color: '#A0A0A0',
    fontSize: 20,
    textAlign: 'center',
    letterSpacing: 1,
    lineHeight: 28,
  },
});