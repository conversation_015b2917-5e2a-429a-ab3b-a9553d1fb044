import React, { useState, useRef, useEffect } from 'react';
import { StackActions, useNavigation, useFocusEffect } from '@react-navigation/native';
import { Platform, View, Text, StyleSheet, GestureResponderEvent, TouchableOpacity, Dimensions, Modal, Animated as RNAnimated, AppState } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'react-native';
import { typography } from '../components/ThemedText';
import { useGameContext } from '../components/game/GameContext';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  FadeIn,
  FadeOut,
  ZoomIn,
  BounceOut,
  Easing,
  runOnJS
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import AsyncStorage from '@react-native-async-storage/async-storage';
import HomeIcon from '@/assets/redesign/icon_control_home.svg';
import ResetIcon from '@/assets/redesign/icon_checkmark_small.svg';
import ArrowRightIcon from '@/assets/redesign/icon_control_arrowRight.svg'
import NetInfo from '@react-native-community/netinfo';
import Toast, { ErrorToast } from 'react-native-toast-message';
import { useLanguage } from '@/services/i18nService';
import { useSound } from '@/components/game/playSound';
import { useAppSettings } from '@/context/AppSettingsContext';
import { useAdBasedAccess } from '@/context/AdBasedAccessContext';
import { useAdSettings } from '@/context/AdSettingsContext';
import { getCachedGameContent } from '@/services/contentService';
import * as Sentry from '@sentry/react-native';
import { getRandomPenalty } from '@/services/penaltyService';
import {
  saveQuestionPool,
  loadQuestionPool,
  saveUsedQuestionIds,
  loadUsedQuestionIds,
  saveGlobalUsedQuestionIds,
  loadGlobalUsedQuestionIds,
  PoolItem
} from '@/services/poolStorageService';
import { Audio } from 'expo-av';
import LottieView from 'lottie-react-native';
import RevenueCatUI from 'react-native-purchases-ui';

import MascotDialogEn from '@/assets/redesign/mascot_dialog.svg';
import MascotDialogEs from '@/assets/redesign/mascot_dialog_es.svg';
import Touchpoint from '@/assets/redesign/touchpoint.svg';
import ModalHeader from '@/assets/redesign/modal_header.svg';
import ModalActions from '@/assets/redesign/modal_actions.svg';
import SettingsIcon from '@/assets/redesign/icon_control_setting.svg';
import InGameBanner from '@/components/game/InGameBanner';
import StoreReviewBanner from '@/components/game/StoreReviewBanner';
import { shouldShowStoreReviewBanner, handleStoreReviewRequest } from '@/services/storeReviewService';

// Push Notifications
import PushBanner from '@/components/notifications/PushBanner';
import BannerCloseIcon from '@/assets/notifications/icon_close.svg';
import {
  pushNotificationService,
  handlePushNotificationPermissionFlow,
  showPushNotificationSettingsAlert
} from '@/services/pushNotificationService';
import * as Notifications from 'expo-notifications';

// Penalty
import PenaltyDialog from '@/components/game/PenaltyDialog';
import RefuseIcon from '@/assets/icons/penalty/icon_refuse.svg';
import PenaltyWalkthroughEN from '@/assets/icons/penalty/penalty_walkthrough_en.svg';
import PenaltyWalkthroughES from '@/assets/icons/penalty/penalty_walkthrough_es.svg';

// Unlock Dialog
import UnlockDialog from '@/components/ads/UnlockDialog';

interface TouchPoint {
  identifier: number;
  pageX: number;
  pageY: number;
  isHighlighted: boolean;
  isSelected: boolean;
  seq: number;
}

// Premium content samples
const PREMIUM_CONTENT_SAMPLES: PoolItem[] = [
  {
    id: 'premium-sample-1',
    type: 'challenge' as const,
    category: 'spicy_dares',
    text_en: 'Get on all fours and let someone get behind you.',
    text_es: 'Ponte de perrito y deja que alguien se te pegue por detrás.',
    text_dom: 'Ponte en cuatro y deja que alguien se te pegue por detrás.'
  },
  {
    id: 'premium-sample-2',
    type: 'question' as const,
    category: 'spicy_questions',
    text_en: 'What\'s the most amount of times you\'ve cummed in a 24-hour period?',
    text_es: '¿Cuántas veces tú te has venido en un solo día?',
    text_dom: '¿Cuántas veces tú te has venío en un solo día?'
  },
  {
    id: 'premium-sample-3',
    type: 'question' as const,
    category: 'spicy_questions',
    text_en: 'How long can you eat pussy/suck dick for?',
    text_es: '¿Cuánto tiempo puedes durar haciendo un oral?',
    text_dom: '¿Cuánto tiempo tú puedes durar mamando?'
  },
  {
    id: 'premium-sample-4',
    type: 'challenge' as const,
    category: 'spicy_dares',
    text_en: 'Pick a wild sex position and reenact it with a player.',
    text_es: 'Elige una posición sexual loca y recréala con un jugador/a.',
    text_dom: 'Elige una posición sexual loca y recréala con un jugador/a.'
  }
];

export default function TouchGameScreen() {
  const navigation = useNavigation();
  const { t, language } = useLanguage();
  const { selectedModes, challengeDifficulty, selectedCategories, resetGame } = useGameContext();
  const [touchPoints, setTouchPoints] = useState<TouchPoint[]>([]);
  const nextSeq = useRef(1);
  const startOffset = useRef(0);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameComplete, setGameComplete] = useState(false);
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);
  const selectionTimerRef = useRef<NodeJS.Timeout | null>(null);
  const countingSoundRef = useRef<Audio.Sound | null>(null);
  const countdownSoundRef = useRef<Audio.Sound | null>(null);
  const animationScale = useSharedValue(1);
  const dialogScale = useSharedValue(0);
  const headerButtonsOpacity = useSharedValue(1);
  const bannerOpacity = useSharedValue(0);
  const penaltyDialogScale = useSharedValue(0);
  const penaltyWalkthroughOpacity = useSharedValue(0);

  // Premium content preview feature state
  const [questionCount, setQuestionCount] = useState(0);
  const [showingPremiumContent, setShowingPremiumContent] = useState(false);
  const [showBanner, setShowBanner] = useState(false);
  const bannerTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [isSequentialBanner, setIsSequentialBanner] = useState(false);
  const [showPaywall, setShowPaywall] = useState(false);

  // Store review banner state
  const [showStoreReviewBanner, setShowStoreReviewBanner] = useState(false);
  const storeReviewBannerOpacity = useSharedValue(0);
  const storeReviewBannerTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Push notification banner state
  const [showPushBanner, setShowPushBanner] = useState(false);
  const pushBannerOpacity = useSharedValue(0);

  // UnlockDialog state
  const [showUnlockDialog, setShowUnlockDialog] = useState(false);

  // Function to hide the banner with a smooth fade-out animation
  const hideBanner = () => {
    // Only proceed if the banner is visible and not already fading out
    if (showBanner && bannerOpacity.value !== 0) {
      console.log('Hiding banner with fade-out animation');

      // Clear any existing timer
      if (bannerTimerRef.current) {
        clearTimeout(bannerTimerRef.current);
        bannerTimerRef.current = null;
      }

      // Fade out the banner smoothly
      bannerOpacity.value = withTiming(0, {
        duration: 400,
        easing: Easing.inOut(Easing.ease)
      });

      // After the fade-out animation completes, hide the banner completely
      setTimeout(() => {
        console.log('Banner fade-out complete - removing from DOM');
        setShowBanner(false);

        // Reset sequential banner state if it was active
        if (isSequentialBanner) {
          setIsSequentialBanner(false);
        }
      }, 400);
    } else {
      console.log('Banner is already hidden or fading out - no action needed');
    }
  };

  // Function to start the banner auto-hide timer
  const startBannerTimer = () => {
    // Clear any existing timer first
    if (bannerTimerRef.current) {
      clearTimeout(bannerTimerRef.current);
      bannerTimerRef.current = null;
    }

    console.log(`Starting banner auto-hide timer (isSequential: ${isSequentialBanner})`);

    // Set a new timer to hide the banner after exactly 2 minutes (120 seconds)
    // Always hide the banner after 2 minutes, regardless of isSequential value
    bannerTimerRef.current = setTimeout(() => {
      console.log(`Banner timer expired - hiding banner now (isSequential: ${isSequentialBanner})`);
      hideBanner();
    }, 120 * 1000); // 120 seconds = 2 minutes
  };

  // Function to hide the store review banner with a smooth fade-out animation
  const hideStoreReviewBanner = () => {
    // Only proceed if the banner is visible and not already fading out
    if (showStoreReviewBanner && storeReviewBannerOpacity.value !== 0) {
      console.log('Hiding store review banner with fade-out animation');

      // Clear any existing timer
      if (storeReviewBannerTimerRef.current) {
        clearTimeout(storeReviewBannerTimerRef.current);
        storeReviewBannerTimerRef.current = null;
      }

      // Fade out the banner smoothly
      storeReviewBannerOpacity.value = withTiming(0, {
        duration: 400,
        easing: Easing.inOut(Easing.ease)
      });

      // After the fade-out animation completes, hide the banner completely
      setTimeout(() => {
        console.log('Store review banner fade-out complete - removing from DOM');
        setShowStoreReviewBanner(false);
      }, 400);
    } else {
      console.log('Store review banner is already hidden or fading out - no action needed');
    }
  };

  // Function to start the store review banner auto-hide timer
  const startStoreReviewBannerTimer = () => {
    // Clear any existing timer first
    if (storeReviewBannerTimerRef.current) {
      clearTimeout(storeReviewBannerTimerRef.current);
      storeReviewBannerTimerRef.current = null;
    }

    console.log('Starting store review banner auto-hide timer');

    // Set a new timer to hide the banner after exactly 2 minutes (120 seconds)
    storeReviewBannerTimerRef.current = setTimeout(() => {
      console.log('Store review banner timer expired - hiding banner now');
      hideStoreReviewBanner();
    }, 120 * 1000); // 120 seconds = 2 minutes
  };

  const fadeAnim = useRef(new RNAnimated.Value(0)).current;

  const headerButtonsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerButtonsOpacity.value,
  }));

  const bannerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: bannerOpacity.value,
  }));

  const storeReviewBannerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: storeReviewBannerOpacity.value,
  }));

  const pushBannerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: pushBannerOpacity.value,
  }));

  const penaltyDialogAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: penaltyDialogScale.value },
        { translateY: penaltyDialogScale.value === 0 ? 50 : 0 }
      ],
      opacity: penaltyDialogScale.value,
    };
  });

const penaltyWalkthroughAnimatedStyle = useAnimatedStyle(() => {
  return {
    opacity: penaltyWalkthroughOpacity.value,
    transform: [
      { scale: penaltyWalkthroughOpacity.value === 0 ? 0.8 : 1 }
    ],
  };
});

  const [countdown, setCountdown] = useState<number | null>(null);

  const { getLocalizedTextForItem } = useGameContext();
  const [question, setQuestion] = useState<PoolItem | null>(null);
  const [availableQuestions, setAvailableQuestions] = useState<PoolItem[]>([]);

  const [showModal, setShowModal] = useState(false);
  const modalDelayInSeconds = 1; // Adjust this value to control delay before modal appears

  const { playSound } = useSound();
  const { isHapticsOn, isSoundOn, isPremiumUnlocked, unlockPremium, selectedIndividualPenalties } = useAppSettings();
  const {
    hasTemporaryPremiumAccess,
    needsToWatchAdsAgain,
    incrementQuestionsAnswered,
    resetQuestionsAnsweredCount,
    resetAllSessionData,
    decrementFreeContent
  } = useAdBasedAccess();
  const { freeContentLimit } = useAdSettings();

  // Check if user has any form of premium access (subscription or ad-based)
  const hasAnyPremiumAccess = isPremiumUnlocked || hasTemporaryPremiumAccess;

  // Check if penalties are enabled (user has selected at least one penalty)
  const arePenaltiesEnabled = selectedIndividualPenalties.length > 0;

  // For now, we're using the Spanish dialog for Dominican Spanish until we have a specific one
  const LocalizedMascotDialog = language === 'es' ? MascotDialogEs :
                               language === 'dom' ? MascotDialogEs :
                               MascotDialogEn;

  const [showPenalty, setShowPenalty] = useState(false);
  const [currentPenaltyText, setCurrentPenaltyText] = useState<string>('');
  const [currentContentPenalty, setCurrentContentPenalty] = useState<string | null>(null);
  const [penaltyContentId, setPenaltyContentId] = useState<string | null>(null);

  // Penalty Walkthrough State
  const [showPenaltyWalkthrough, setShowPenaltyWalkthrough] = useState(false);
  const [walkthroughCompleted, setWalkthroughCompleted] = useState(false);
  const [userInteractedWithSettings, setUserInteractedWithSettings] = useState(false);
  const walkthroughDelayTimer = useRef<NodeJS.Timeout | null>(null);
  const walkthroughAutoHideTimer = useRef<NodeJS.Timeout | null>(null);

  // Dynamic gradient colors for preview vs. penalty vs. default
  const defaultGradientColors = ['#FF4EE2', '#C119FA'] as const;
  const previewGradientColors = ['#FFB84D', '#FF4500'] as const; // adjust to your preferred preview colors
  const penaltyGradientColors = ['#FF4274', '#FF4E51'] as const; // red gradient for penalty theme

  // Priority: penalty > premium preview > default
  const containerGradientColors = showPenalty
    ? penaltyGradientColors
    : showingPremiumContent
      ? previewGradientColors
      : defaultGradientColors;

  useEffect(() => {
  const updateHeaderButtonsOpacity = async () => {
    let targetOpacity = 1;
    if (countdown !== null) {
      // Hide buttons only when countdown is active
      targetOpacity = 0;
    } else if (showModal) {
      // Show when the question modal is up
      targetOpacity = 1;
    } else {
      targetOpacity = 1;
    }
    headerButtonsOpacity.value = withTiming(targetOpacity, { duration: 300, easing: Easing.ease });
  };
  updateHeaderButtonsOpacity();
}, [countdown, showModal]);

  // This function only loads cached content without fetching from API
  const loadContentFromStorage = async () => {
    try {
      // Use the imported getCachedGameContent function from contentService.ts
      const content = await getCachedGameContent();

      if (content) {
        console.log('Successfully loaded cached content');
        return content;
      } else {
        console.log('No cached content available');
        return null;
      }
    } catch (error) {
      console.error('Error loading cached content:', error);
      Sentry.captureException(error);
      return null;
    }
  };

  // Function to create pool from game content
  const createPoolFromContent = (content: any) => {
    if (!content) return [];

    // Map between visible categories and corresponding challenge difficulty
    const challengeCategoryMap: Record<string, string> = {
      casual_questions: 'casual_dares',
      mild_questions: 'mild_dares',
      spicy_questions: 'spicy_dares',
      no_limits_questions: 'no_limits_dares',
      couple_questions: 'couple_dares'
    };

    let questionsPool: { id: string; text_en: string; text_es: string; text_dom: string; type: 'question' | 'challenge'; category: string }[] = [];

    // For questions: go through each selected category and load the content
    if (selectedModes.includes('questions')) {
      for (const category of selectedCategories) {
        if (content.questions && content.questions[category]) {
          console.log(`Adding ${content.questions[category].length} questions from ${category}`);
          questionsPool.push(
            ...content.questions[category].map((item: any) => ({
              ...item,
              type: 'question',
              category: category
            }))
          );
        }
      }
    }

    // For challenges: go through each selected category, map it to its corresponding challenge and load it
    if (selectedModes.includes('challenges')) {
      for (const category of selectedCategories) {
        const mappedChallengeCategory = challengeCategoryMap[category];
        if (mappedChallengeCategory && content.dares && content.dares[mappedChallengeCategory]) {
          console.log(`Adding ${content.dares[mappedChallengeCategory].length} challenges from ${mappedChallengeCategory}`);
          questionsPool.push(
            ...content.dares[mappedChallengeCategory].map((item: any) => ({
              ...item,
              type: 'challenge',
              category: mappedChallengeCategory
            }))
          );
        }
      }
    }

    // Shuffle the pool to mix questions and challenges
    for (let i = questionsPool.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [questionsPool[i], questionsPool[j]] = [questionsPool[j], questionsPool[i]];
    }

    return questionsPool;
  };

  // Flag to track if component is unmounting
  const isUnmounting = useRef(false);

  // Set this flag when leaving the screen
  useEffect(() => {
    return () => {
      console.log('UNMOUNT: TouchGameScreen is unmounting');
      isUnmounting.current = true;
    };
  }, []);

  useEffect(() => {
    // If the component is unmounting, skip loading content
    if (isUnmounting.current) {
      return;
    }

    // Load game content
    const loadGameContentAndCreatePool = async () => {
      console.log('Loading pool from persistent storage...');

      // Create a storage key that includes both categories and modes
      // This ensures we have different pools for different game modes
      const storageKey = [...selectedCategories, ...selectedModes].sort();

      // Load the saved pool using the combined key of categories and modes
      const savedPool = await loadQuestionPool(storageKey);
      const savedUsedIds = await loadUsedQuestionIds(storageKey);

      // Load global used IDs (independent of modes)
      const savedGlobalUsedIds = await loadGlobalUsedQuestionIds();

      // Check again if unmounting after async operation
      if (isUnmounting.current) {
        return;
      }

      // Set the global used IDs from storage if available, otherwise use empty set
      if (savedGlobalUsedIds) {
        console.log(`Loaded ${savedGlobalUsedIds.size} global used question IDs from persistent storage`);
        setGlobalUsedIds(savedGlobalUsedIds);
      } else {
        setGlobalUsedIds(new Set());
      }

      // If we have a saved pool, use it
      if (savedPool && savedPool.length > 0) {
        console.log(`Loaded saved pool with ${savedPool.length} items`);
        // Merge with any new content items from latest cached content
        const content = await loadContentFromStorage();
        const freshPool = createPoolFromContent(content);
        const savedIdsSet = new Set(savedPool.map(item => item.id));
        const newItems = freshPool.filter(item => !savedIdsSet.has(item.id));
        let mergedPool = savedPool;
        if (newItems.length > 0) {
          console.log(`Merging ${newItems.length} new items into saved pool`);
          mergedPool = [...savedPool, ...newItems];
          // Persist the merged pool
          await saveQuestionPool(mergedPool, storageKey);
          console.log(`Saved updated pool with ${mergedPool.length} items`);
        }
        // Update state
        setFullContentPool(mergedPool);
        setAvailableQuestions(mergedPool);
        // Restore used question IDs
        if (savedUsedIds) {
          console.log(`Loaded ${savedUsedIds.size} used question IDs`);
          setUsedQuestionIds(savedUsedIds);
        } else {
          setUsedQuestionIds(new Set());
        }
        setPoolLoadedFromStorage(true);
        return;
      }

      // If no saved pool or it's empty, we need to handle this case
      console.log('No saved pool found or empty pool for the current categories');

      // Load the latest content from storage (without fetching from API)
      // This ensures we always use the most up-to-date content with any text changes
      const content = await loadContentFromStorage();

      // Check again if unmounting after async operation
      if (isUnmounting.current) {
        return;
      }

      // Create the question pool from the latest cached content
      const allQuestions = createPoolFromContent(content);

      // Final check before updating state
      if (isUnmounting.current) {
        return;
      }

      if (allQuestions.length > 0) {
        console.log(`Created new pool with ${allQuestions.length} items from cached content`);

        // Store the complete set of questions for this session
        setFullContentPool(allQuestions);

        // Reset the used questions set
        setUsedQuestionIds(new Set());

        // Set the full pool as available questions
        setAvailableQuestions(allQuestions);

        // Save the new pool to persistent storage with categories and modes
        await saveQuestionPool(allQuestions, storageKey);
        console.log(`Saved new pool to persistent storage for categories: ${selectedCategories.join(', ')} and modes: ${selectedModes.join(', ')}`);
      } else {
        // No need to log errors - just silently reset the pool
        console.log('No questions available for the current selection');
        setAvailableQuestions([]);
        setFullContentPool([]);
        setUsedQuestionIds(new Set());
      }
    };

    loadGameContentAndCreatePool();
  }, [selectedModes, challengeDifficulty, selectedCategories]);

  const animation = useRef<LottieView>(null);
  useEffect(() => {
    if (countdown === 3 && animation.current) {
      animation.current?.play();
    }
  }, [countdown]);


  // Reference to all possible questions (only used when resetting)
  const [fullContentPool, setFullContentPool] = useState<PoolItem[]>([]);
  // Set of used question IDs (true pool implementation)
  const [usedQuestionIds, setUsedQuestionIds] = useState<Set<string>>(new Set());

  // Set of globally used question IDs (persists across mode changes)
  const [globalUsedIds, setGlobalUsedIds] = useState<Set<string>>(new Set());

  // Flag to track if we've loaded the pool from storage (used for debugging)
  const [, setPoolLoadedFromStorage] = useState(false);

  // Function to get a random question, avoiding repetition until all have been shown
  const getRandomQuestion = async () => {
    // STANDARD POOL BEHAVIOR:
    // - Keep track of all used questions with usedQuestionIds
    // - Only reset when ALL questions have been shown
    // - Pick from questions that haven't been shown this cycle

    // Case 1: We have questions in our current pool that haven't been used
    if (availableQuestions.length > 0) {
      // Make a copy of the current available questions
      const currentPool = [...availableQuestions];

      // Filter out questions that have already been used (either in current mode or globally)
      const unusedQuestions = currentPool.filter(q =>
        !usedQuestionIds.has(q.id) && !globalUsedIds.has(q.id)
      );

      // If we have unused questions, pick one randomly
      if (unusedQuestions.length > 0) {
        const randomIndex = Math.floor(Math.random() * unusedQuestions.length);
        const selectedQuestion = unusedQuestions[randomIndex];

        // Add this question ID to both the mode-specific and global used sets
        setUsedQuestionIds(prev => {
          const newUsedIds = new Set([...prev, selectedQuestion.id]);
          // Create a storage key that includes both categories and modes
          const storageKey = [...selectedCategories, ...selectedModes].sort();
          // Save the updated used IDs to persistent storage with categories and modes
          saveUsedQuestionIds(newUsedIds, storageKey).catch(err =>
            console.error('Error saving used question IDs:', err)
          );
          return newUsedIds;
        });

        // Also update the global used IDs set
        setGlobalUsedIds(prev => {
          const newGlobalUsedIds = new Set([...prev, selectedQuestion.id]);
          // Save the updated global used IDs to persistent storage
          saveGlobalUsedQuestionIds(newGlobalUsedIds).catch(err => {
            console.error('Error saving global used question IDs:', err);
            Sentry.captureException(err);
          });
          return newGlobalUsedIds;
        });

        console.log(`🧠 Showing (${unusedQuestions.length - 1} unused remaining):`,
                   selectedQuestion.type.toUpperCase(), '-',
                   selectedQuestion.category.toUpperCase(), '-',
                   selectedQuestion.text_en);

        return selectedQuestion;
      }
    }

    // Case 2: We've used all questions in the current cycle, need to reload or reset
    console.log('All questions have been shown this cycle, resetting pool');

    // First check if we've already loaded all possible content
    if (fullContentPool.length > 0) {
      console.log(`Resetting from ${fullContentPool.length} cached questions`);

      // Create a storage key that includes both categories and modes
      const storageKey = [...selectedCategories, ...selectedModes].sort();
      // Reset the mode-specific used question IDs and save to persistent storage
      setUsedQuestionIds(new Set());
      saveUsedQuestionIds(new Set(), storageKey).catch(err =>
        console.error('Error saving reset used question IDs:', err)
      );

      // Note: We do NOT reset the global used IDs here
      // This ensures content remains marked as "used" across mode changes

      // Check if we have a saved pool with potentially updated text
      const savedPool = await loadQuestionPool(storageKey);

      // If we have a saved pool, use it (it will have the most up-to-date text)
      // Otherwise, use the in-memory fullContentPool
      const poolToUse = savedPool && savedPool.length > 0 ? savedPool : fullContentPool;

      // If we loaded from storage, update our in-memory pool to stay in sync
      if (savedPool && savedPool.length > 0) {
        console.log(`Using saved pool with ${savedPool.length} items from persistent storage (may contain updated text)`);
        setFullContentPool(savedPool);
      }

      // Create a shuffled copy of the pool for the next cycle
      const newPool = [...poolToUse];

      // Shuffle the pool
      for (let i = newPool.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newPool[i], newPool[j]] = [newPool[j], newPool[i]];
      }

      // Filter out questions that have already been shown globally
      // This ensures we don't show the same content repeatedly when resetting the pool
      const unusedQuestions = newPool.filter(q => !globalUsedIds.has(q.id));

      // If we have unused questions, pick one randomly
      let selectedQuestion;
      if (unusedQuestions.length > 0) {
        console.log(`Found ${unusedQuestions.length} unused questions out of ${newPool.length} total`);
        const randomIndex = Math.floor(Math.random() * unusedQuestions.length);
        selectedQuestion = unusedQuestions[randomIndex];
      } else {
        // If all questions have been shown globally, reset the global used IDs for this category
        // This is a rare case but ensures the game can continue even if all content has been seen
        console.log('All questions have been shown globally, starting a new cycle');

        // Reset global used IDs for just this category's items
        const categoryItems = newPool.map(item => item.id);
        const updatedGlobalUsedIds = new Set([...globalUsedIds].filter(id => !categoryItems.includes(id)));
        setGlobalUsedIds(updatedGlobalUsedIds);
        saveGlobalUsedQuestionIds(updatedGlobalUsedIds).catch(err =>
          console.error('Error saving reset global used question IDs:', err)
        );

        // Pick a random question from the full pool
        const randomIndex = Math.floor(Math.random() * newPool.length);
        selectedQuestion = newPool[randomIndex];
      }

      // Mark it as used in both mode-specific and global storage
      setUsedQuestionIds(new Set([selectedQuestion.id]));
      saveUsedQuestionIds(new Set([selectedQuestion.id]), storageKey).catch(err =>
        console.error('Error saving used question ID:', err)
      );

      // Also update the global used IDs set
      setGlobalUsedIds(prev => {
        const newGlobalUsedIds = new Set([...prev, selectedQuestion.id]);
        // Save the updated global used IDs to persistent storage
        saveGlobalUsedQuestionIds(newGlobalUsedIds).catch(err =>
          console.error('Error saving global used question ID:', err)
        );
        return newGlobalUsedIds;
      });

      // Set available questions to full pool
      setAvailableQuestions(newPool);

      console.log(`🧠 Showing (pool reset with ${newPool.length} total questions):`,
                 selectedQuestion.type.toUpperCase(), '-',
                 selectedQuestion.category.toUpperCase(), '-',
                 selectedQuestion.text_en);

      return selectedQuestion;
    }

    // Case 3: Need to load content first time or refresh from storage
    try {
      // Load the latest content from storage to ensure we have the most up-to-date text
      const content = await loadContentFromStorage();

      // Create a pool from the latest content
      const loadedQuestions = createPoolFromContent(content);

      if (loadedQuestions.length > 0) {
        console.log(`Loaded ${loadedQuestions.length} items into content pool`);

        // Store the full set for future cycles
        setFullContentPool(loadedQuestions);

        // Create a storage key that includes both categories and modes
        const storageKey = [...selectedCategories, ...selectedModes].sort();
        // Reset mode-specific used question IDs and save to persistent storage
        setUsedQuestionIds(new Set());
        saveUsedQuestionIds(new Set(), storageKey).catch(err =>
          console.error('Error saving reset used question IDs:', err)
        );

        // Note: We do NOT reset the global used IDs here
        // This ensures content remains marked as "used" across mode changes

        // Set available questions to all loaded questions
        setAvailableQuestions(loadedQuestions);

        // Filter out questions that have already been shown globally
        // This ensures we don't show the same content repeatedly when loading fresh content
        const unusedQuestions = loadedQuestions.filter(q => !globalUsedIds.has(q.id));

        // If we have unused questions, pick one randomly
        let selectedQuestion;
        if (unusedQuestions.length > 0) {
          console.log(`Found ${unusedQuestions.length} unused questions out of ${loadedQuestions.length} total`);
          const randomIndex = Math.floor(Math.random() * unusedQuestions.length);
          selectedQuestion = unusedQuestions[randomIndex];
        } else {
          // If all questions have been shown globally, reset the global used IDs for this category
          console.log('All questions have been shown globally, starting a new cycle');

          // Reset global used IDs for just this category's items
          const categoryItems = loadedQuestions.map(item => item.id);
          const updatedGlobalUsedIds = new Set([...globalUsedIds].filter(id => !categoryItems.includes(id)));
          setGlobalUsedIds(updatedGlobalUsedIds);
          saveGlobalUsedQuestionIds(updatedGlobalUsedIds).catch(err =>
            console.error('Error saving reset global used question IDs:', err)
          );

          // Pick a random question from the full pool
          const randomIndex = Math.floor(Math.random() * loadedQuestions.length);
          selectedQuestion = loadedQuestions[randomIndex];
        }

        // Mark it as used in both mode-specific and global storage
        setUsedQuestionIds(new Set([selectedQuestion.id]));
        saveUsedQuestionIds(new Set([selectedQuestion.id]), storageKey).catch(err =>
          console.error('Error saving initial used question ID:', err)
        );

        // Also update the global used IDs set
        setGlobalUsedIds(prev => {
          const newGlobalUsedIds = new Set([...prev, selectedQuestion.id]);
          // Save the updated global used IDs to persistent storage
          saveGlobalUsedQuestionIds(newGlobalUsedIds).catch(err =>
            console.error('Error saving global used question ID:', err)
          );
          return newGlobalUsedIds;
        });

        console.log(`🧠 Showing (loaded ${loadedQuestions.length} total questions):`,
                   selectedQuestion.type.toUpperCase(), '-',
                   selectedQuestion.category.toUpperCase(), '-',
                   selectedQuestion.text_en);

        return selectedQuestion;
      }
    } catch (error) {
      console.error('Failed to load content:', error);
      Sentry.captureException(error);
    }

    // If we get here, we couldn't load any content
    console.error('No content available');
    return null;
  };

  // Function to show premium content preview
  const showPremiumContentPreview = () => {
    // Clear any existing banner timer
    if (bannerTimerRef.current) {
      clearTimeout(bannerTimerRef.current);
      bannerTimerRef.current = null;
    }

    // Select a random premium sample
    const randomIndex = Math.floor(Math.random() * PREMIUM_CONTENT_SAMPLES.length);
    const premiumSample = PREMIUM_CONTENT_SAMPLES[randomIndex];

    // Save current question to restore normal content flow later
    setShowingPremiumContent(true);

    // Replace current question with premium sample
    setQuestion(premiumSample);

    // Clear penalty for the premium content (premium content doesn't use penalties)
    setCurrentContentPenalty(null);
    setPenaltyContentId(null);

    // Make sure game is in complete state to show the question
    setGameComplete(true);

    // Reset and show the modal with proper animation sequence
    // 1. Reset scale first
    dialogScale.value = 0;

    // 2. Make sure the modal is visible
    setShowModal(true);

    // 3. Once mounted, trigger the scale+translate animation
    requestAnimationFrame(() => {
      dialogScale.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.back(1.5))
      });
    });

    // Change banner to sequential mode
    setIsSequentialBanner(true);

    // Restart the banner timer for the sequential banner
    startBannerTimer();

    console.log('Showing premium content preview:', premiumSample.text_en);
  };

  // Function to handle banner press
  const handleBannerPress = async () => {
    // Check if penalty dialog is open and handle accordingly
    if (showPenalty) {
      if (!isSequentialBanner) {
        // If penalty dialog is open and banner is in preview content state,
        // dismiss the penalty dialog first, then show premium content preview
        console.log('Dismissing penalty dialog to show premium content preview');

        const showPreviewAfterPenaltyDismiss = () => {
          // Clear any existing banner timer
          if (bannerTimerRef.current) {
            clearTimeout(bannerTimerRef.current);
            bannerTimerRef.current = null;
          }

          // Select a random premium sample
          const randomIndex = Math.floor(Math.random() * PREMIUM_CONTENT_SAMPLES.length);
          const premiumSample = PREMIUM_CONTENT_SAMPLES[randomIndex];

          // Set up premium content state directly
          setShowPenalty(false);
          setShowingPremiumContent(true);
          setQuestion(premiumSample);
          setGameComplete(true);
          setShowModal(true);
          setIsSequentialBanner(true);

          // Clear penalty for the premium content (premium content doesn't use penalties)
          setCurrentContentPenalty(null);
          setPenaltyContentId(null);

          // Animate dialog scale directly to show premium content
          dialogScale.value = withTiming(1, {
            duration: 400,
            easing: Easing.out(Easing.back(1.5))
          });

          // Restart the banner timer for the sequential banner
          startBannerTimer();

          console.log('Showing premium content preview after penalty dismiss:', premiumSample.text_en);
        };

        // Hide penalty dialog first
        penaltyDialogScale.value = withTiming(0, {
          duration: 300,
          easing: Easing.inOut(Easing.ease)
        }, (finished) => {
          if (finished) {
            runOnJS(showPreviewAfterPenaltyDismiss)();
          }
        });
        return;
      } else {
        // If penalty dialog is open and banner is in payment dialog state,
        // proceed with paywall without dismissing penalty dialog
        console.log('Opening paywall while penalty dialog is open (sequential banner)');
      }
    }

    if (isSequentialBanner) {
      // If already showing sequential banner, open RevenueCat paywall
      try {
        console.log(`Opening RevenueCat paywall from sequential banner on ${Platform.OS}`);

        if (Platform.OS === 'android') {
          // For Android, use the presentPaywall method instead of the Modal component
          // This avoids the ViewTreeLifecycleOwner issue
          try {
            console.log('Using RevenueCatUI.presentPaywall for Android');
            const result = await RevenueCatUI.presentPaywall({
              fontFamily: "Melindya",
            });
            console.log('Paywall result:', result);

            // Check if purchase was successful
            if (result === 'PURCHASED' || result === 'RESTORED') {
              console.log('Purchase completed via presentPaywall, updating premium status');
              unlockPremium();
            }
          } catch (paywallError) {
            console.error('Error presenting paywall on Android:', paywallError);
            Sentry.captureException(paywallError);
          }
        } else {
          // For iOS, continue using the Modal approach
          setShowPaywall(true);
        }
      } catch (error) {
        console.error('Error showing paywall:', error);
        Sentry.captureException(error);
      }
    } else {
      // First press shows premium content preview
      showPremiumContentPreview();
    }
  };

  const handleSkipTransition = () => {
    setGameComplete(false);

    // Check if current question is premium
    if (question && !isPremiumUnlocked) {
      const isPremiumCategory =
        question.category === 'spicy_questions' ||
        question.category === 'no_limits_questions' ||
        question.category === 'spicy_dares' ||
        question.category === 'no_limits_dares' ||
        question.category === 'couple_questions' ||
        question.category === 'couple_dares';

      if (isPremiumCategory) {
        // If user has temporary premium access, consume content
        if (hasTemporaryPremiumAccess) {
          // Decrement free content counter for this category
          decrementFreeContent(question.category as 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares');

          // Increment questions answered for ad-based access tracking (skip counts as content consumed)
          incrementQuestionsAnswered();

          // Check if user needs to watch ads again (every 6 questions/dares)
          if (needsToWatchAdsAgain) {
            console.log('TouchGameScreen: User needs to watch ads again after 6 questions/dares (skip button)');

            // RESET AD STATE IMMEDIATELY when access is exhausted
            console.log('TouchGameScreen: Resetting ad state because access exhausted (skip)');
            resetAllSessionData();
            resetQuestionsAnsweredCount();

            // Show UnlockDialog in sequential mode immediately
            setShowUnlockDialog(true);
            return; // Don't proceed to next question, show unlock dialog instead
          }
        } else {
          // User doesn't have premium access, show unlock dialog
          console.log('TouchGameScreen: User tried to skip premium content without access, showing unlock dialog');
          setShowUnlockDialog(true);
          return; // Don't proceed to next question, show unlock dialog instead
        }
      }
    }

    // If we were showing premium content, go back to normal content flow
    if (showingPremiumContent) {
      setShowingPremiumContent(false);
      // Don't increment question count for premium samples
    }

    // If a sequential banner is showing, hide it when user presses Skip
    if (isSequentialBanner && showBanner) {
      console.log('Hiding sequential banner because user pressed Skip');
      hideBanner();

      // Wait for the animation to complete before proceeding to the next question
      setTimeout(() => {
        proceedToNextQuestion();
      }, 400); // Same duration as the fade-out animation
    } else {
      // If no banner to hide, proceed to next question immediately
      proceedToNextQuestion();
    }

    // Reset the dialog scale to ensure it's properly hidden
    dialogScale.value = 0;

    // Reset animation scale to ensure consistent sizing for next dialog
    animationScale.value = withTiming(1, { duration: 300 });
  };

  // Helper function to get the next question
  const proceedToNextQuestion = () => {
    // Reset animation scale to ensure consistent sizing for all dialogs
    animationScale.value = withTiming(1, { duration: 300 });

    // Just get the next question - the getRandomQuestion function will handle
    // pool management and resets automatically
    getRandomQuestion().then(nextQuestion => {
      setTimeout(() => {
        if (nextQuestion) {
          setQuestion(nextQuestion);
          setGameComplete(true);

          // Clear penalty for the new content item
          setCurrentContentPenalty(null);
          setPenaltyContentId(null);

          // Ensure animation scale is at 1.0 before showing dialog
          animationScale.value = 1;
          dialogScale.value = withTiming(1, { duration: 400, easing: Easing.out(Easing.back(1.5)) });

          // Check if we need to show banner for this question
          if (!isPremiumUnlocked && !showBanner && !showingPremiumContent) {
            const isCasualOrMild =
              nextQuestion.category === 'casual_questions' ||
              nextQuestion.category === 'mild_questions' ||
              nextQuestion.category === 'casual_dares' ||
              nextQuestion.category === 'mild_dares';

            if (questionCount >= 2 && isCasualOrMild) {
              setShowBanner(true);
              bannerOpacity.value = withTiming(1, { duration: 400, easing: Easing.inOut(Easing.ease) });
              // Start the banner auto-hide timer
              startBannerTimer();
            }
          }
        } else {
          // Handle the unlikely case where no question was returned
          console.error('No question available after skip');
          handleResetTransition();
        }
      }, 100);
    }).catch(error => {
      console.error('Error getting next question:', error);
      Sentry.captureException(error);
      handleResetTransition();
    });
  };

  // Function to show penalty dialog
  const showPenaltyDialog = async () => {
    // Check if we already have a penalty for the current content item
    const currentQuestionId = question?.id;
    let penaltyText: string;

    if (currentQuestionId && penaltyContentId === currentQuestionId && currentContentPenalty) {
      // Use the existing penalty for this content item
      penaltyText = currentContentPenalty;
      console.log('🎯 Using existing penalty for content:', currentQuestionId);
    } else {
      // Generate a new penalty for this content item
      const languageKey = language === 'dom' ? 'dom' : language === 'es' ? 'es' : 'en';
      const newPenaltyText = await getRandomPenalty(languageKey);
      penaltyText = newPenaltyText || t('penaltyNotSelected');

      // Store the penalty for this content item
      if (currentQuestionId) {
        setCurrentContentPenalty(penaltyText);
        setPenaltyContentId(currentQuestionId);
        console.log('🎯 Generated new penalty for content:', currentQuestionId, '- Penalty:', penaltyText);
      }
    }

    // Set the penalty text for display
    setCurrentPenaltyText(penaltyText);

    const showPenalty = () => {
      setShowModal(false);
      setShowPenalty(true);

      // Show penalty dialog with animation
      penaltyDialogScale.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.back(1.5))
      });

      // Show walkthrough if conditions are met
      showWalkthrough();
    };

    // Hide the main question dialog first
    dialogScale.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, (finished) => {
      if (finished) {
        runOnJS(showPenalty)();
      }
    });
  };

  // Function to go back to content dialog from penalty
  const backToContentDialog = () => {
    const showContent = () => {
      setShowPenalty(false);
      setShowModal(true);

      // Show main dialog with animation
      dialogScale.value = withTiming(1, {
        duration: 400,
        easing: Easing.out(Easing.back(1.5))
      });
    };

    // Hide penalty dialog first
    penaltyDialogScale.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, (finished) => {
      if (finished) {
        runOnJS(showContent)();
      }
    });
  };

  // Function to handle penalty next match (reset game)
  const handlePenaltyNextMatch = () => {
    const resetGame = () => {
      setShowPenalty(false);
      handleResetTransition();
    };

    // Hide penalty dialog first
    penaltyDialogScale.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, (finished) => {
      if (finished) {
        runOnJS(resetGame)();
      }
    });
  };

  // Penalty Walkthrough Functions
  const shouldShowWalkthrough = async (): Promise<boolean> => {
    try {
      // For premium users, check if they've completed the walkthrough
      if (isPremiumUnlocked) {
        const completed = await AsyncStorage.getItem('penaltyWalkthroughCompleted');
        return completed !== 'true';
      }

      // For free users, always check if enough time has passed since last shown
      const lastShown = await AsyncStorage.getItem('penaltyWalkthroughLastShown');
      if (lastShown) {
        const lastShownTime = parseInt(lastShown, 10);
        const now = Date.now();
        const eightHours = 8 * 60 * 60 * 1000; // 8 hours in milliseconds

        return now - lastShownTime >= eightHours;
      }

      // If no lastShown time exists, show walkthrough
      return true;
    } catch (error) {
      console.error('Error checking walkthrough conditions:', error);
      Sentry.captureException(error);
      return false;
    }
  };

  const loadWalkthroughState = async () => {
    try {
      // For premium users, load completion state
      if (isPremiumUnlocked) {
        const completed = await AsyncStorage.getItem('penaltyWalkthroughCompleted');
        if (completed === 'true') {
          setWalkthroughCompleted(true);
        }
      }
      // For free users, no initial state loading needed - timing is checked on demand
    } catch (error) {
      console.error('Error loading walkthrough state:', error);
      Sentry.captureException(error);
    }
  };

  const saveWalkthroughState = async (completed: boolean, userInteracted: boolean) => {
    try {
      if (completed && userInteracted && isPremiumUnlocked) {
        // Only save completion state for premium users
        await AsyncStorage.setItem('penaltyWalkthroughCompleted', 'true');
      } else {
        // Always save the last shown time for both free and premium users
        await AsyncStorage.setItem('penaltyWalkthroughLastShown', Date.now().toString());
      }
    } catch (error) {
      console.error('Error saving walkthrough state:', error);
      Sentry.captureException(error);
    }
  };

  const showWalkthrough = async () => {
    // Check if walkthrough should be shown using centralized logic
    const shouldShow = await shouldShowWalkthrough();
    if (!shouldShow) {
      return;
    }

    // Clear any existing timers
    clearWalkthroughTimers();

    // Show walkthrough after 500ms delay
    walkthroughDelayTimer.current = setTimeout(() => {
      setShowPenaltyWalkthrough(true);
      penaltyWalkthroughOpacity.value = withTiming(1, { duration: 300 });

      // Auto-hide after 10 seconds
      walkthroughAutoHideTimer.current = setTimeout(() => {
        hideWalkthrough(false); // false = not user-initiated
      }, 10000);
    }, 500);
  };

  const hideWalkthrough = (userInteracted: boolean = false) => {
    if (userInteracted) {
      setUserInteractedWithSettings(true);
    }

    // Clear timers
    clearWalkthroughTimers();

    // Create a function that can be safely called from runOnJS
    const handleWalkthroughHidden = () => {
      setShowPenaltyWalkthrough(false);

      // Save state based on user interaction (async calls wrapped safely)
      if (userInteracted && isPremiumUnlocked) {
        setWalkthroughCompleted(true);
        // Use setTimeout to safely call async function
        setTimeout(() => {
          saveWalkthroughState(true, true);
        }, 0);
      } else {
        // Use setTimeout to safely call async function
        setTimeout(() => {
          saveWalkthroughState(false, false);
        }, 0);
        // Note: Removed the 8-hour re-display timer as requested
        // Free users will see the walkthrough every 8 hours when penalty dialog opens
      }
    };

    // Hide walkthrough with animation
    penaltyWalkthroughOpacity.value = withTiming(0, { duration: 300 }, (finished) => {
      if (finished) {
        runOnJS(handleWalkthroughHidden)();
      }
    });
  };

  const clearWalkthroughTimers = () => {
    if (walkthroughDelayTimer.current) {
      clearTimeout(walkthroughDelayTimer.current);
      walkthroughDelayTimer.current = null;
    }
    if (walkthroughAutoHideTimer.current) {
      clearTimeout(walkthroughAutoHideTimer.current);
      walkthroughAutoHideTimer.current = null;
    }
    // Removed walkthroughReDisplayTimer as it's no longer used
  };

  // Note: Gradient determination logic is currently not being used
  // but kept for future reference

  const handleTouchStart = (event: GestureResponderEvent) => {
    const touches = event.nativeEvent.touches;

    setTouchPoints(prevPoints => {
      const newPoints = [...prevPoints];

      touches.forEach(touch => {
        const identifier = Number(touch.identifier);
        // Add new finger if it's not already tracked
        if (!newPoints.some(p => p.identifier === identifier)) {
          newPoints.push({
            identifier,
            pageX: touch.pageX,
            pageY: touch.pageY,
            isHighlighted: false,
            isSelected: false,
            seq: nextSeq.current++,
          });
        }
      });
      console.log('[TouchStart] touchPoints:', newPoints.map(p => ({
        id: p.identifier,
        seq: p.seq,
        x: p.pageX,
        y: p.pageY
      })));
      return newPoints;
    });

    if (touches.length >= 2 && !gameStarted && !gameComplete) {
      startGame(touches);
    }
  };

  // Handle Touch Move
  const handleTouchMove = (event: GestureResponderEvent) => {
    if (gameStarted && !gameComplete) {
      const touches = event.nativeEvent.touches;
      setTouchPoints(prevPoints => {
        const updatedPoints = [...prevPoints];
        for (let i = 0; i < touches.length; i++) {
          const touch = touches[i];
          const existingIndex = updatedPoints.findIndex(p => p.identifier === Number(touch.identifier));
          if (existingIndex !== -1) {
            updatedPoints[existingIndex] = {
              ...updatedPoints[existingIndex],
              pageX: touch.pageX,
              pageY: touch.pageY
            };
          }
        }
        return updatedPoints;
      });
    }
  };

  const handleTouchEnd = (event: GestureResponderEvent) => {
    const touches = event.nativeEvent.touches;
    console.log('[TouchEnd] touches remaining:', touches.length);

    // Pre-game: remove touchPoints when fingers change before starting
    if (!gameStarted) {
      setTouchPoints(prevPoints => {
        const activeIds = new Set(Array.from(touches).map(t => Number(t.identifier)));
        return prevPoints.filter(p => activeIds.has(p.identifier));
      });
      return;
    }

    // In-game: handle reset and filtering based on active touches
    if (gameStarted && !gameComplete) {
      setTouchPoints(prevPoints => {
        if (touches.length < 2) {
          // Fade out counting sound
          if (countingSoundRef.current) {
            let volume = 1.0;
            const fadeOutInterval = setInterval(async () => {
              if (volume > 0.1) {
                volume -= 0.1;
                await countingSoundRef.current?.setVolumeAsync(volume);
              } else {
                clearInterval(fadeOutInterval);
                await countingSoundRef.current?.stopAsync();
                await countingSoundRef.current?.unloadAsync();
                countingSoundRef.current = null;
              }
            }, 50);
          }
          // Fade out countdown sound
          if (countdownSoundRef.current) {
            let volume = 1.0;
            const fadeOutInterval = setInterval(async () => {
              if (volume > 0.1) {
                volume -= 0.1;
                await countdownSoundRef.current?.setVolumeAsync(volume);
              } else {
                clearInterval(fadeOutInterval);
                await countdownSoundRef.current?.stopAsync();
                await countdownSoundRef.current?.unloadAsync();
                countdownSoundRef.current = null;
              }
            }, 50);
          }
          resetGameState();
          return [];
        }
        const activeIds = new Set(Array.from(touches).map(t => Number(t.identifier)));
        return prevPoints.filter(p => activeIds.has(p.identifier));
      });
    }
  };

  const startGame = (touches: GestureResponderEvent['nativeEvent']['touches']) => {
    const initialPoints: TouchPoint[] = touches.map(touch => ({
      identifier: Number(touch.identifier),
      pageX: touch.pageX,
      pageY: touch.pageY,
      isHighlighted: false,
      isSelected: false,
      seq: nextSeq.current++,
    }));

    setTouchPoints(initialPoints);
    setGameStarted(true);
    selectRandomFinger(initialPoints);
  };

  // Updated selectRandomFinger to store interval IDs in refs
  const selectRandomFinger = (points: TouchPoint[]) => {
    if (points.length < 2) return;

    setCountdown(3); // Start countdown at 3
    let countdownValue = 3;
    // Play countdown sound
    if (!countdownSoundRef.current && isSoundOn) {
      (async () => {
        const { sound } = await Audio.Sound.createAsync(require('@/assets/sounds/countdown_effect.mp3'), {
          shouldPlay: true,
        });
        countdownSoundRef.current = sound;
      })();
    }
    const countdownInterval = setInterval(() => {
      countdownValue -= 1;
      setCountdown(countdownValue);

      if (countdownValue < 0) {
        clearInterval(countdownInterval);
        countdownTimerRef.current = null;
        setCountdown(null); // Hide countdown when done

        // Let countdown sound finish naturally - don't force stop it
        // The sound will complete on its own timeline independent of animation/countdown
        if (countdownSoundRef.current) {
          // Set a timer to clean up the sound reference after it naturally finishes
          setTimeout(() => {
            if (countdownSoundRef.current) {
              countdownSoundRef.current.unloadAsync();
              countdownSoundRef.current = null;
            }
          }, 1000); // Give sound time to finish naturally
        }

        // Log selection details
        console.log('[selectRandomFinger] points before selection:', points.map(p => ({
          id: p.identifier,
          seq: p.seq,
          x: p.pageX,
          y: p.pageY
        })));

        // Step-count cycle selection (1–10 random steps through insertion order, rotating start)
        const stepCount = Math.floor(Math.random() * 10) + 1;
        console.log('[selectRandomFinger] stepCount:', stepCount);
        const ordered = [...points].sort((a, b) => a.seq - b.seq);
        console.log('[selectRandomFinger] insertion order seq:', ordered.map(o => o.seq));
        const startIndex = startOffset.current % ordered.length;
        console.log('[selectRandomFinger] startIndex:', startIndex);
        const winnerIndex = (startIndex + (stepCount - 1)) % ordered.length;
        console.log('[selectRandomFinger] winnerIndex:', winnerIndex);
        const winnerId = ordered[winnerIndex].identifier;
        console.log('[selectRandomFinger] winnerId:', winnerId);
        // Prepare for next match
        startOffset.current = (startOffset.current + 1) % ordered.length;
        setTouchPoints(prevPoints =>
          prevPoints.map((point) => ({
            ...point,
            isSelected: point.identifier === winnerId,
            isHighlighted: false,
          }))
        );
        // After selection, remove non-selected points to trigger exit
        setTimeout(() => {
          setTouchPoints(prev => prev.filter(p => p.isSelected));
        }, 300);
        if (isHapticsOn) Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // Define a JS function to handle the delayed animation reset
        const resetAnimationScale = () => {
          setTimeout(() => {
            animationScale.value = withTiming(1, { duration: 200 });
          }, 200);
        };

        // Use a temporary scale animation for the touchpoint selection effect
        // but reset it before showing the dialog to ensure consistent dialog sizing
        animationScale.value = withTiming(1.2, {
          duration: 350,
          easing: Easing.out(Easing.exp)
        }, (finished) => {
          if (finished) {
            // Use runOnJS with a named function reference
            runOnJS(resetAnimationScale)();
          }
        });

        // Delay success sound to let countdown sound finish naturally
        if (isSoundOn) {
          setTimeout(() => {
            playSound('successEffect');
          }, 800); // Let countdown sound play for a bit longer
        }
        setGameComplete(true);

        // Get the first question using the async method
        getRandomQuestion().then(nextQuestion => {
          if (nextQuestion) {
            setQuestion(nextQuestion);

            // Clear penalty for the new content item
            setCurrentContentPenalty(null);
            setPenaltyContentId(null);

            // Increment question count for banner logic and ad-based access
            setQuestionCount(prevCount => {
              const newCount = prevCount + 1;

              // Check if user is using ad-based premium access and needs to watch ads again
              const isPremiumCategory =
                nextQuestion.category === 'spicy_questions' ||
                nextQuestion.category === 'no_limits_questions' ||
                nextQuestion.category === 'spicy_dares' ||
                nextQuestion.category === 'no_limits_dares' ||
                nextQuestion.category === 'couple_questions' ||
                nextQuestion.category === 'couple_dares';

              if (isPremiumCategory && !isPremiumUnlocked) {
                // If user has temporary premium access, consume content
                if (hasTemporaryPremiumAccess) {
                  // Decrement free content counter for this category
                  decrementFreeContent(nextQuestion.category as 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares');

                  // Increment questions answered for ad-based access tracking
                  incrementQuestionsAnswered();

                  // Check if user needs to watch ads again (every 6 questions/dares)
                  if (needsToWatchAdsAgain) {
                    console.log('TouchGameScreen: User needs to watch ads again after 6 questions/dares');

                    // RESET AD STATE IMMEDIATELY when access is exhausted
                    console.log('TouchGameScreen: Resetting ad state because access exhausted');
                    resetAllSessionData();
                    resetQuestionsAnsweredCount();

                    // Show UnlockDialog in sequential mode immediately
                    setShowUnlockDialog(true);
                  }
                } else {
                  // User doesn't have premium access, show unlock dialog
                  console.log('TouchGameScreen: User tried to proceed to premium content without access, showing unlock dialog');
                  setShowUnlockDialog(true);
                }
              }

              // Show InGameBanner on second question if not premium and category is casual or mild
              const isCasualOrMild =
                nextQuestion.category === 'casual_questions' ||
                nextQuestion.category === 'mild_questions' ||
                nextQuestion.category === 'casual_dares' ||
                nextQuestion.category === 'mild_dares';

              console.log(`Banner check: questionCount=${newCount}, isPremiumUnlocked=${isPremiumUnlocked}, isCasualOrMild=${isCasualOrMild}`);

              if (newCount === 2 && !isPremiumUnlocked && isCasualOrMild) {
                console.log('Showing InGameBanner - conditions met!');

                // Hide PushBanner if it's currently visible before showing InGameBanner
                if (showPushBanner) {
                  console.log('Hiding PushBanner to show InGameBanner');
                  hidePushBanner();
                }

                // Show banner with fade in animation
                setShowBanner(true);
                bannerOpacity.value = withTiming(1, { duration: 400, easing: Easing.inOut(Easing.ease) });

                // Set timer to hide banner after 2 minutes if not interacted with
                startBannerTimer();
              }

              // Show PushNotificationBanner on first question/dare if conditions are met
              if (newCount === 1 && !showPushBanner && !showBanner && !showStoreReviewBanner) {
                console.log('🔔 [TouchGameScreen] Checking push notification banner conditions...');
                console.log('🔔 [TouchGameScreen] Banner states - showPushBanner:', showPushBanner, 'showBanner:', showBanner, 'showStoreReviewBanner:', showStoreReviewBanner);

                // Debug current banner state
                pushNotificationService.debugBannerState();

                // Check if push banner should be shown asynchronously
                pushNotificationService.shouldShowBanner().then(shouldShow => {
                  console.log('🔔 [TouchGameScreen] shouldShowBanner result:', shouldShow);
                  if (shouldShow) {
                    console.log('🔔 [TouchGameScreen] ✅ Showing push notification banner - conditions met!');
                    setShowPushBanner(true);
                    pushBannerOpacity.value = withTiming(1, { duration: 400, easing: Easing.inOut(Easing.ease) });
                  } else {
                    console.log('🔔 [TouchGameScreen] ❌ Not showing push notification banner - conditions not met');
                  }
                }).catch(error => {
                  console.error('🔔 [TouchGameScreen] Error checking push notification banner conditions:', error);
                });
              } else {
                console.log('🔔 [TouchGameScreen] Not checking push banner - newCount:', newCount, 'showPushBanner:', showPushBanner, 'showBanner:', showBanner, 'showStoreReviewBanner:', showStoreReviewBanner);
              }

              // Show StoreReviewBanner - premium users on 2nd question, free users on 6th question
              const shouldCheckStoreReview =
                (newCount === 4 && isPremiumUnlocked) ||
                (newCount === 12 && !isPremiumUnlocked);

              if (shouldCheckStoreReview && !showStoreReviewBanner) {
                console.log(`Checking store review banner conditions (${isPremiumUnlocked ? 'premium' : 'free'} user, question ${newCount})...`);
                // Check store review conditions asynchronously
                shouldShowStoreReviewBanner(isPremiumUnlocked).then(shouldShow => {
                  if (shouldShow) {
                    console.log('Showing store review banner - conditions met!');

                    // Hide InGameBanner if it's currently visible before showing store review banner
                    if (showBanner) {
                      console.log('Hiding InGameBanner to show store review banner');
                      hideBanner();
                    }

                    // Hide PushBanner if it's currently visible before showing store review banner
                    if (showPushBanner) {
                      console.log('Hiding PushBanner to show store review banner');
                      hidePushBanner();
                    }

                    setShowStoreReviewBanner(true);
                    storeReviewBannerOpacity.value = withTiming(1, { duration: 400, easing: Easing.inOut(Easing.ease) });

                    // Start the auto-hide timer for store review banner
                    startStoreReviewBannerTimer();
                  }
                }).catch(error => {
                  console.error('Error checking store review banner conditions:', error);
                  Sentry.captureException(error);
                });
              }

              return newCount;
            });

            setTimeout(() => {
              // Ensure animation scale is reset to 500ms before showing dialog
              animationScale.value = 1;
              setShowModal(true);
              dialogScale.value = withTiming(1, { duration: 400, easing: Easing.out(Easing.back(1.5)) });
            }, modalDelayInSeconds * 500);
          } else {
            console.error('Failed to get initial question');
            handleResetTransition();
          }
        }).catch(error => {
          console.error('Error getting initial question:', error);
          Sentry.captureException(error);
          handleResetTransition();
        });
      }
    }, 500); // Reduced from 1000ms to 500ms (0.5 seconds faster)
    countdownTimerRef.current = countdownInterval;
  };

  const resetGameState = () => {
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }
    if (selectionTimerRef.current) {
      clearInterval(selectionTimerRef.current);
      selectionTimerRef.current = null;
    }
    if (bannerTimerRef.current) {
      clearTimeout(bannerTimerRef.current);
      bannerTimerRef.current = null;
    }
    if (storeReviewBannerTimerRef.current) {
      clearTimeout(storeReviewBannerTimerRef.current);
      storeReviewBannerTimerRef.current = null;
    }

    // Reset premium content state
    setShowingPremiumContent(false);

    // Reset paywall state
    setShowPaywall(false);

    // Reset penalty dialog state
    setShowPenalty(false);
    penaltyDialogScale.value = 0;

    // Clear penalty content association
    setCurrentContentPenalty(null);
    setPenaltyContentId(null);

    // Reset banner state but keep it visible if it was showing
    // Only restore banner opacity if we're not in the process of hiding it
    if (showBanner && bannerOpacity.value !== 0) {
      bannerOpacity.value = withTiming(1, { duration: 300 });
    }

    setTouchPoints([]);
    setGameStarted(false);
    setGameComplete(false);
    setQuestion(null);
    setCountdown(null);
    dialogScale.value = 0;
  };

const handleResetTransition = () => {
  animationScale.value = withTiming(1, { duration: 300 });

  // If a sequential banner is showing, hide it when user presses Done
  if (isSequentialBanner && showBanner) {
    console.log('Hiding sequential banner because user pressed Done');
    // Hide banner with animation and delay the resetGameState call
    hideBanner();
    // Wait for the animation to complete before resetting game state
    setTimeout(() => {
      resetGameState();
    }, 400); // Same duration as the fade-out animation
  } else {
    // If no banner to hide, reset game state immediately
    resetGameState();
  }

  // Explicitly reset the dialog scale to ensure it's properly hidden
  dialogScale.value = 0;

  // Don't clear the pool when pressing reset/done
  // This preserves the pool state so users don't see repetitive questions
};

  // Content fetching is handled by HomeScreen

  const goHome = () => {
    // Reset premium content and banner state
    setShowingPremiumContent(false);
    setIsSequentialBanner(false);
    setShowPaywall(false);

    // Fade out banner if it's visible
    if (showBanner) {
      bannerOpacity.value = withTiming(0, { duration: 300 });
      setTimeout(() => setShowBanner(false), 300);
    }

    // Fade out store review banner if it's visible (for both free and premium users)
    if (showStoreReviewBanner) {
      hideStoreReviewBanner();
    }

    // Fade out push notification banner if it's visible
    if (showPushBanner) {
      hidePushBanner();
    }

    // Reset question count for next game session
    setQuestionCount(0);

    // Go back two screens without delay or reload
    navigation.dispatch(StackActions.pop(2));
    nextSeq.current = 1;

    // Delay state reset to avoid the gradient flash
    setTimeout(() => {
      console.log('HOME: Delayed reset executing');
      resetGameState();
      resetGame();

      // We no longer clear the pool when going home
      // The pool will persist even when navigating away
      console.log('HOME: Transition is completed, pool is preserved');

      // No need to refresh content here anymore
      // HomeScreen will handle content refresh when it receives focus
    }, 400); // Adjust the delay as needed
  };

  useEffect(() => {
    if (showPaywall) {
      fadeAnim.setValue(0);
      setTimeout(() => {
        RNAnimated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }, 300); // delay before fade-in starts
    }
  }, [showPaywall]);

  // Effect to handle banner timer when showBanner state changes
  useEffect(() => {
    // When banner becomes visible, always start the timer regardless of isSequential value
    if (showBanner && !isPremiumUnlocked) {
      startBannerTimer();
    }

    // When banner is hidden, clear the timer
    if (!showBanner && bannerTimerRef.current) {
      clearTimeout(bannerTimerRef.current);
      bannerTimerRef.current = null;
    }

    // Clean up on unmount or when banner is hidden
    return () => {
      if (bannerTimerRef.current) {
        clearTimeout(bannerTimerRef.current);
        bannerTimerRef.current = null;
      }
    };
  }, [showBanner, isPremiumUnlocked]);

  // Store review banner logic - banner is shown in question count logic
  // Both free and premium users can see the banner (at different question counts)
  // No need to hide banner based on premium status changes since both user types can see it

  // Handle store review banner press
  const handleStoreReviewBannerPress = async () => {
    console.log('Store review banner pressed');

    // Hide the banner immediately using the dedicated function
    hideStoreReviewBanner();

    // Request the store review
    const success = await handleStoreReviewRequest();
    if (success) {
      console.log('Store review request successful');
    } else {
      console.log('Store review request failed or was cancelled');
    }
  };

  // Function to hide the push notification banner with a smooth fade-out animation
  const hidePushBanner = () => {
    // Only proceed if the banner is visible and not already fading out
    if (showPushBanner && pushBannerOpacity.value !== 0) {
      console.log('Hiding push notification banner with fade-out animation');

      // Fade out the banner smoothly
      pushBannerOpacity.value = withTiming(0, {
        duration: 400,
        easing: Easing.inOut(Easing.ease)
      });

      // Hide the banner state after animation completes
      setTimeout(() => {
        setShowPushBanner(false);
      }, 400);
    }
  };

  // Handle push notification banner press
  const handlePushBannerPress = async () => {
    console.log('Push notification banner pressed');

    try {
      // Check current permission status
      const { status } = await Notifications.getPermissionsAsync();
      console.log('🔔 [TouchGameScreen] Current permission status:', status);

      if (status === 'denied') {
        // Permissions were previously denied, guide user to settings
        console.log('🔔 [TouchGameScreen] Permissions denied, showing settings alert');

        // Hide the banner first
        hidePushBanner();

        // Show settings alert with localized text
        await showPushNotificationSettingsAlert({
          title: t('pushNotificationDisabledTitle'),
          message: t('pushNotificationDisabledMessage'),
          openSettings: t('pushNotificationOpenSettings'),
          cancel: t('pushNotificationCancel'),
          error: t('pushNotificationSettingsError'),
        });
      } else {
        // Permissions not yet requested or other status, try normal flow
        console.log('🔔 [TouchGameScreen] Attempting normal permission flow');

        // Hide the banner immediately
        hidePushBanner();

        // Request push notification permissions
        await handlePushNotificationPermissionFlow();
      }
    } catch (error) {
      console.error('🔔 [TouchGameScreen] Error handling push banner press:', error);
      Sentry.captureException(error);

      // Fallback to normal flow
      hidePushBanner();
      await handlePushNotificationPermissionFlow();
    }
  };

  // Handle push notification banner close button press
  const handlePushBannerClose = async () => {
    console.log('Push notification banner close button pressed');

    // Hide the banner
    hidePushBanner();

    // Mark banner as dismissed for 8 hours
    await pushNotificationService.setBannerDismissed();
  };

  useEffect(() => {
    return () => {
      // Clean up all timers when component unmounts
      if (countdownTimerRef.current) {
        clearInterval(countdownTimerRef.current);
        countdownTimerRef.current = null;
      }
      if (selectionTimerRef.current) {
        clearTimeout(selectionTimerRef.current);
        selectionTimerRef.current = null;
      }
      if (bannerTimerRef.current) {
        clearTimeout(bannerTimerRef.current);
        bannerTimerRef.current = null;
      }
      if (storeReviewBannerTimerRef.current) {
        clearTimeout(storeReviewBannerTimerRef.current);
        storeReviewBannerTimerRef.current = null;
      }
      // Clean up walkthrough timers
      clearWalkthroughTimers();
    };
  }, []);

  // Load walkthrough state on component mount
  useEffect(() => {
    loadWalkthroughState();
  }, []);

  // Handle language changes during walkthrough
  useEffect(() => {
    // If walkthrough is visible and language changes, keep it visible but update the component
    // The conditional rendering will handle showing the correct language version
  }, [language]);

  // Load walkthrough state on component mount and when premium status changes
  useEffect(() => {
    loadWalkthroughState();
  }, [isPremiumUnlocked]);

  // Handle component unmounting and app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        // Clear timers when app goes to background
        clearWalkthroughTimers();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
      clearWalkthroughTimers();
    };
  }, []);

  // Clean up walkthrough when penalty dialog is hidden
  useEffect(() => {
    if (!showPenalty && showPenaltyWalkthrough) {
      hideWalkthrough(false);
    }
  }, [showPenalty]);

  // Hide UnlockDialog when user returns with premium access
  useFocusEffect(
    React.useCallback(() => {
      // If user has premium access and unlock dialog is showing, hide it
      if (hasTemporaryPremiumAccess && showUnlockDialog) {
        console.log('TouchGameScreen: User returned with premium access, hiding unlock dialog');
        setShowUnlockDialog(false);
      }
    }, [hasTemporaryPremiumAccess, showUnlockDialog])
  );

  // Handle UnlockDialog close when user exhausts ad-based premium access
  const handleUnlockDialogClose = () => {
    // Hide the unlock dialog and navigate back
    setShowUnlockDialog(false);
    navigation.goBack();
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: animationScale.value }],
    };
  });

  // IMPORTANT: This animation style is applied to the dialog overlay.
  // For consistent dialog sizing, always ensure animationScale.value is set to 1.0
  // before showing any dialog. The container uses animationScale while the dialog
  // uses dialogScale, and both affect the final rendered size.
  const dialogAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: dialogScale.value },
        { translateY: dialogScale.value === 0 ? 50 : 0 }
      ],
      opacity: dialogScale.value,
    };
  });

  const touchPointScaleStyle = useAnimatedStyle(() => {
    const offset = 74 * (animationScale.value - 1); // 125 / 2 = 62.5
    return {
      transform: [
        { translateX: -offset },
        { translateY: -offset },
        { scale: animationScale.value }
      ]
    };
  });

  const getFadeInAnimation = (delay: number) =>
    FadeIn.duration(400).delay(delay).easing(Easing.ease);

  const getFadeOutAnimation = (delay: number) =>
    FadeOut.duration(400).delay(delay).easing(Easing.ease);

  const getBounceOutAnimation = (delay: number) =>
    BounceOut.duration(400).delay(delay).easing(Easing.bounce);

  return (
    <LinearGradient
      colors={containerGradientColors}
      style={styles.mainContainer}
    >
      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="light-content" // Makes icons and text white
      />

      <Animated.View style={[styles.homeButton, headerButtonsAnimatedStyle]}>
        <TouchableOpacity
          onPress={async () => {
            await playSound('tapEffect1');
            if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            goHome();
          }}
        >
          <HomeIcon width={44} height={44} />
        </TouchableOpacity>
      </Animated.View>

      <Animated.View style={[styles.settingsButton, headerButtonsAnimatedStyle]}>
        <TouchableOpacity
          onPress={async () => {
            await playSound('tapEffect1');
            if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

            // Hide walkthrough if visible (user interacted with settings)
            if (showPenaltyWalkthrough) {
              hideWalkthrough(true);
            }

            navigation.navigate('Settings' as never);
          }}
        >
          <SettingsIcon width={48} height={48} />
        </TouchableOpacity>
      </Animated.View>

      <Animated.View
        style={[
          styles.walkthroughOverlay,
          penaltyWalkthroughAnimatedStyle,
          {
            opacity: showPenaltyWalkthrough ? penaltyWalkthroughOpacity.value : 0,
            pointerEvents: showPenaltyWalkthrough ? 'auto' : 'none',
          },
        ]}
      >
        {language === 'es' || language === 'dom' ? (
          <PenaltyWalkthroughES width={255} height={110} />
        ) : (
          <PenaltyWalkthroughEN width={255} height={110} />
        )}
      </Animated.View>

      {countdown !== null && (
        <Animated.View
          pointerEvents="none"
          entering={getFadeInAnimation(400)}
          exiting={getBounceOutAnimation(0)}
          style={styles.countdownOverlay}>
          <LottieView
            ref={animation}
            speed={2.5} // Speeds up the animation (2.5x faster - 0.5s faster than before)
            style={{
              width: 200,
              height: 200,
            }}
            source={require('../assets/lottie/mascot_countdown.json')}
          />
        </Animated.View>
      )}

      <Animated.View
        style={[styles.container, animatedStyle]}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >

        {!gameStarted && !gameComplete && (
          <Animated.View
            pointerEvents="none"
            entering={getFadeInAnimation(100)}
            exiting={getFadeOutAnimation(0)}
            style={styles.instructionOverlay}>
            <LocalizedMascotDialog />
              <LottieView
                ref={animation}
                autoPlay
                loop
                speed={0.8}
                style={styles.mascotLoop}
                source={require('../assets/lottie/mascot_in_circle.json')}
              />
          </Animated.View>
        )}

        {touchPoints.map((point) => (
          <Animated.View
            key={point.identifier}
            entering={ZoomIn.duration(300).easing(Easing.out(Easing.exp))}
            exiting={!point.isSelected ? BounceOut.duration(300).easing(Easing.out(Easing.exp)) : undefined}
            style={[
              styles.touchPoint,
              point.isHighlighted && styles.highlightedTouchPoint,
              {
                opacity: gameComplete
                  ? point.isSelected ? 1 : 0
                  : point.isHighlighted ? 1 : 0.5
              },
              point.isSelected && styles.selectedTouchPoint,
              point.isSelected && touchPointScaleStyle,
              {
                left: point.pageX - 74,
                top: point.pageY - 74,
              },
            ]}
          >
            <Touchpoint />
          </Animated.View>
        ))}

        <Animated.View
          style={[
            styles.questionOverlay,
            dialogAnimatedStyle,
            {
              opacity: gameComplete && question && showModal ? dialogScale.value : 0,
              pointerEvents: gameComplete && question && showModal ? 'auto' : 'none',
            },
          ]}
        >
          <View style={styles.questionContainer} >
            <View style={styles.modalHeader}>
              <Text style={[typography.heading4, styles.modalHeaderText]}>
                {question?.type === 'challenge' ? t('modalChallenge') : t('modalQuestion')}
              </Text>
              <ModalHeader style={styles.modalHeaderBg}/>
            </View>
            <View style={styles.modalContent}>
              <Text style={[
                typography.subtitle1,
                styles.questionText,
                // Add special styling for error messages
                (question?.id === 'no-internet' || question?.id === 'error') && styles.errorMessage
              ]}>
                {getLocalizedTextForItem(question)}
              </Text>
            </View>

            <View style={styles.modalActions}>
              {/* SKIP */}
              <TouchableOpacity
                style={styles.skipButton}
                onPress={async () => {
                  if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  await playSound('tapEffect1');
                  dialogScale.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, (finished) => {
                    if (finished) {
                      runOnJS(handleSkipTransition)();
                    }
                  });
                }}
              >
                <ArrowRightIcon
                  style={styles.skipButtonIcon}
                  width={26}
                  height={26}
                />

                <Text style={[typography.subtitle2, styles.skipButtonLabel]}>{t('skipButton')}</Text>
              </TouchableOpacity>

              {/* REFUSE - Only show if penalties are enabled */}
              {arePenaltiesEnabled && (
                <TouchableOpacity
                  style={styles.refuseButton}
                  onPress={async () => {
                    if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    await playSound('tapEffect1');
                    showPenaltyDialog();
                  }}
                >
                  <RefuseIcon
                    style={styles.refuseButtonIcon}
                    width={26}
                    height={26}
                  />

                  <Text style={[typography.subtitle2, styles.refuseButtonLabel]}>{t('refurseButton')}</Text>
                </TouchableOpacity>
              )}

              {/* RESTART GAME */}
              <TouchableOpacity
                style={styles.resetButton}
                onPress={async () => {
                  await playSound('tapEffect3');
                  if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  dialogScale.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, (finished) => {
                    if (finished) {
                      runOnJS(handleResetTransition)();
                    }
                  });
                }}
              >
                <ResetIcon
                  style={styles.resetButtonIcon}
                  width={24}
                  height={24}
                />
                <Text style={[typography.subtitle2, styles.resetButtonLabel]}>{t('resetButton')}</Text>
              </TouchableOpacity>

              <ModalActions style={styles.modalActionsBg}/>
            </View>
          </View>
        </Animated.View>
      </Animated.View>

      {/* No Internet Connection */}
      <Toast
        config={{
          error: (props) => (
            <ErrorToast
              {...props}
              style={{
                borderLeftColor: '#FF4A79',
                borderLeftWidth: 3,
                borderRadius: 16,
                zIndex: 9999,
                position: 'absolute',
                bottom: 32,

              }}
              contentContainerStyle={{
                paddingHorizontal: 15
              }}
              text1Style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: '#110163',
              }}
            />
          ),
        }}
      />

      {/* Premium Banner - Only show if not premium and banner state is true */}
      {showBanner && !isPremiumUnlocked && (
        <View style={styles.inGameBanner}>
          <Animated.View style={[bannerAnimatedStyle]}>
            <InGameBanner
              isSequential={isSequentialBanner}
              onPress={handleBannerPress}
            />
          </Animated.View>
        </View>
      )}

      {/* Store Review Banner - Show for both free and premium iOS users with internet */}
      {showStoreReviewBanner && (
        <View style={styles.storeReviewBanner}>
          <Animated.View style={[storeReviewBannerAnimatedStyle]}>
            <StoreReviewBanner
              onPress={handleStoreReviewBannerPress}
            />
          </Animated.View>
        </View>
      )}

      {/* Push Notification Banner - Show on first question/dare if conditions are met */}
      {showPushBanner && (
        <View style={styles.pushNotificationBanner}>
          <Animated.View style={[pushBannerAnimatedStyle]}>
            <PushBanner
              onPress={handlePushBannerPress}
            />
            <TouchableOpacity
              style={styles.bannerCloseButton}
              onPress={handlePushBannerClose}
            >
              <BannerCloseIcon
                width={32}
                height={32}
              />
            </TouchableOpacity>
          </Animated.View>
        </View>
      )}

      {/* Penalty Dialog */}
      <Animated.View
        style={[
          styles.penaltyOverlay,
          penaltyDialogAnimatedStyle,
          {
            opacity: showPenalty ? penaltyDialogScale.value : 0,
            pointerEvents: showPenalty ? 'auto' : 'none',
          },
        ]}
      >
        <PenaltyDialog
          onBackToContent={backToContentDialog}
          onNextMatch={handlePenaltyNextMatch}
          penaltyText={currentPenaltyText}
        />
      </Animated.View>

      {/* RevenueCat Paywall Modal - iOS only */}
      {showPaywall && Platform.OS === 'ios' && (
        <Modal
          transparent
          visible
          animationType="none"
          onRequestClose={() => {
            console.log('Paywall modal closed by back button');
            setShowPaywall(false);
          }}
        >
          <RNAnimated.View style={{ flex: 1, opacity: fadeAnim }}>
            <RevenueCatUI.Paywall
              options={{
                fontFamily: "Melindya",
                displayCloseButton: true,
              }}
              onDismiss={() => {
                console.log('Paywall dismissed');
                setShowPaywall(false);
              }}
              onPurchaseCompleted={() => {
                console.log('Purchase completed, updating premium status');
                unlockPremium();
                setShowPaywall(false);
              }}
              onPurchaseError={(error) => {
                console.error('Purchase error:', error);
                Sentry.captureException(error);
                setShowPaywall(false);
              }}
            />
          </RNAnimated.View>
        </Modal>
      )}

      {/* UnlockDialog for ad-based premium access */}
      {showUnlockDialog && (
        <View style={styles.unlockDialogOverlay}>
          <UnlockDialog
            isSequential={true}
            onClose={handleUnlockDialogClose}
          />
        </View>
      )}
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  countdownOverlay: {
    position: 'absolute',
    top: '40%',
    left: '30%',
    // transform: [{ translateX: -80 }, { translateY: -80 }], <--- Hidding because of animation styles conflic
    width: 160,
    height: 160,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 5,
  },
  mainContainer: {
    position: "relative",
    flex: 1,
  },
  homeButton: {
    zIndex: 9999,
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 8,
    left: 8,
    padding: 16,
  },
  settingsButton: {
    zIndex: 9999,
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 8,
    right: 8,
    padding: 16,
  },
  container: {
    zIndex: 1,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  instructionOverlay: {
    zIndex: 2,
    position: 'absolute',
    gap: 12,
    color: '#FFFFFF',
  },
  mascotLoop: {
    width: 200,
    height: 200,
    transform: [{ translateX: -52}, {translateY: -16 }],
  },
  touchPoint: {
    zIndex: 3,
    position: 'absolute',
    width: 148,
    height: 148,
    opacity: 0.5,
  },
  highlightedTouchPoint: {
    opacity: 1,
  },
  selectedTouchPoint: {
    opacity: 1,
  },
  questionOverlay: {
    zIndex: 4,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionContainer: {
    width: 312,
    alignItems: 'center',
    overflow: 'hidden',
  },
  modalHeader: {
    position: "relative",
    width: 312,
    height: 72,
  },
  modalHeaderText: {
    zIndex: 2,
    top: 18,
    textAlign: 'center',
    color: '#F3EBDC',
    fontSize: 37,
    lineHeight: 48,
  },
  modalHeaderBg: {
    zIndex: 1,
    position: "absolute",
  },
  modalContent: {
    paddingTop: 24,
    paddingHorizontal: 24,
    backgroundColor: "#131416",
    width: "100%",
    borderTopWidth: 3,
    borderStyle: 'solid',
    borderTopColor: '#2E2F31',
  },
  questionText: {
    marginBottom: 24,
    textAlign: 'center',
    color: '#F3EBDC',
    fontSize: 38,
    lineHeight: 42,
    letterSpacing: 2,
  },
  modalActions: {
    position: 'relative',
    top: -2,
    flexDirection: 'row',
    width: 312,
  },
  modalActionsBg: {
    zIndex: 1,
    position: "absolute",
    bottom: 0,
    width: 312,
  },
  errorMessage: {
    color: '#FF6B6B', // Red color for error messages
    fontSize: 28,
    textAlign: 'center',
  },
  resetButton: {
    zIndex: 2,
    flex: 1,
    height: 94,
    justifyContent: "center",
    alignItems: "center",
    paddingLeft: 12,
    paddingRight: 20,
    gap: 8,
    borderTopWidth: 3,
    borderStyle: 'solid',
    borderTopColor: '#2E2F31',
  },
  resetButtonLabel: {
    top: 2,
    fontSize: 22,
    lineHeight: 27,
    color: "#42FFB7",
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  resetButtonIcon: {
    // No styles for icons for now
  },
  skipButton: {
    zIndex: 2,
    flex: 1,
    height: 94,
    justifyContent: "center",
    alignItems: "center",
    paddingLeft: 20,
    paddingRight: 12,
    gap: 6,
    borderRightWidth: 3,
    borderStyle: 'solid',
    borderRightColor: '#2E2F31',
    borderTopWidth: 3,
    borderTopColor: '#2E2F31',
  },
  skipButtonLabel: {
    top: 1,
    fontSize: 22,
    lineHeight: 27,
    color: "#F3EBDC",
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  skipButtonIcon: {
    // No styles for icons for now
  },
  refuseButton: {
    zIndex: 2,
    width: 104,
    height: 94,
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
    borderRightWidth: 3,
    borderStyle: 'solid',
    borderRightColor: '#2E2F31',
    borderTopWidth: 3,
    borderTopColor: '#2E2F31',
  },
  refuseButtonLabel: {
    top: 1,
    fontSize: 22,
    lineHeight: 27,
    color: "#FF4274",
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  refuseButtonIcon: {
    left: 0,
  },
  feedbackActions: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 16,
  },
  inGameBanner: {
    position: 'absolute',
    bottom: 40,
    alignSelf: 'center',
    // width: '40%',
    zIndex: 999,
  },
  storeReviewBanner: {
    position: 'absolute',
    bottom: 40,
    alignSelf: 'center',
    zIndex: 999,
  },
  iosPaywallContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  androidPaywallContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  // Penalty
  penaltyOverlay: {
    zIndex: 4,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  walkthroughOverlay: {
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 56,
    right: 64,
    zIndex: 9999,
  },
  unlockDialogOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10000, // Higher than all other elements
  },
  // Push Notifications
  pushNotificationBanner: {
    position: 'absolute',
    bottom: 40,
    alignSelf: 'center',
    zIndex: 999,
  },
  bannerCloseButton: {
    position: 'absolute',
    top: -8,
    right: -8,
  },
});
