import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Dimensions, Animated as RNAnimated } from 'react-native';
import { useLanguage } from '@/services/i18nService';
import { Linking, Platform, View, Text, TouchableOpacity, ScrollView, Modal, StyleSheet, ActivityIndicator, Alert, Clipboard } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { Switch } from 'react-native-switch';
import { StatusBar } from 'react-native';
import { typography } from '@/components/ThemedText';
import { useSound } from '@/components/game/playSound';
import { useAppSettings } from '@/context/AppSettingsContext';
import { useAdBasedAccess } from '@/context/AdBasedAccessContext';
import * as Haptics from 'expo-haptics';
import Animated, { FadeInDown, Easing } from 'react-native-reanimated';
import { PenaltyContent, PenaltyItem, loadPenaltyContent } from '@/services/penaltyService';

import ArrowRightIcon from '@/assets/redesign/icon_control_chevronLeft.svg';
import LanguageIcon from '@/assets/icons/settings/language_icon.svg';
import EffectsIcon from '@/assets/icons/settings/effects_icon.svg';
import NotificationsIcon from '@/assets/icons/settings/notifications_icon.svg';
import ChevronRight from '@/assets/redesign/icon_control_arrowRight.svg'; // Reusing icon for now
import CheckMarkIcon from '@/assets/redesign/icon_selected.svg';
import EnglishFlag from '@/assets/icons/settings/english_flag.svg';
import DomFlag from '@/assets/icons/settings/dom_flag.svg';
import SpainFlag from '@/assets/icons/settings/spain_flag.svg';
import CloseIcon from '@/assets/icons/settings/close.svg';
import { LinearGradient } from 'expo-linear-gradient';
import { getUserID } from '@/services/revenueCatService';
import { pushNotificationService, handlePushNotificationPermissionFlow } from '@/services/pushNotificationService';
import * as Notifications from 'expo-notifications';
import * as Sentry from '@sentry/react-native';

// Penalty
import PenaltyIcon from '@/assets/icons/penalty/icon_penalty_medium.svg';
import PenaltyIconSmall from '@/assets/icons/penalty/icon_penalty_small.svg';
import PremiumBadge from '@/assets/icons/penalty/premium_badge.svg';
import UncheckedIcon from '@/assets/icons/penalty/icon_unchecked.svg';

// Unlock
import UnlockDialog from '@/components/ads/UnlockDialog';

// Screen
export default function SettingsScreen() {
  const navigation = useNavigation();
  const { language, setAppLanguage, t } = useLanguage();
  const [selectedSetting, setSelectedSetting] = useState<string | null>(null);
  const { playSound } = useSound();
  const { isSoundOn, isHapticsOn, toggleSound, toggleHaptics, selectedIndividualPenalties, updateIndividualPenaltySelections, isPremiumUnlocked, unlockPremium, notificationPreference, setNotificationPreference } = useAppSettings();
  const { hasTemporaryPremiumAccess } = useAdBasedAccess();
  const [flagsLoaded, setFlagsLoaded] = useState(Platform.OS !== 'android'); // Inicialmente cargado para iOS
  const [userId, setUserId] = useState<string>('');
  const [idCopied, setIdCopied] = useState(false);
  const [penaltyContent, setPenaltyContent] = useState<PenaltyContent | null>(null);

  const [showUnlockDialog, setShowUnlockDialog] = useState(false);

  // Debounce timer for notification toggle
  const notificationToggleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Visual state for immediate toggle feedback (separate from actual preference)
  const [visualNotificationState, setVisualNotificationState] = useState(notificationPreference);

  // Check if user has any form of premium access (subscription or ad-based)
  const hasAnyPremiumAccess = isPremiumUnlocked || hasTemporaryPremiumAccess;

  // Animation value for modal background overlay opacity
  // Starts at 0 (transparent) and animates to 1 (visible) with delay
  const backgroundOpacity = useRef(new RNAnimated.Value(0)).current;

  // Preload penalty content when component mounts
  useEffect(() => {
    const preloadPenaltyContent = async () => {
      try {
        const content = await loadPenaltyContent();
        setPenaltyContent(content);
        if (!content) {
          console.log('No penalty content in cache - penalties will be available after app restart');
        }
      } catch (error) {
        console.error('Error preloading penalty content:', error);
        Sentry.captureException(error);
      }
    };

    preloadPenaltyContent();
  }, []);

  // Efecto para simular la carga de SVGs en Android
  useEffect(() => {
    if (Platform.OS === 'android' && selectedSetting === 'language') {
      // Simular tiempo de carga para los SVGs en Android
      const timer = setTimeout(() => {
        setFlagsLoaded(true);
      }, 300); // 300ms es suficiente para que el modal aparezca y luego se carguen los SVGs

      return () => {
        clearTimeout(timer);
      };
    }
  }, [selectedSetting]);

  // Resetear el estado de carga cuando se cierra el modal
  useEffect(() => {
    if (!selectedSetting && Platform.OS === 'android') {
      setFlagsLoaded(false);
    }
  }, [selectedSetting]);

  // Handle modal background overlay animation
  // The modal content uses built-in slide animation, but we want the background
  // overlay to fade in with a 200ms delay for a smoother visual effect
  useEffect(() => {
    if (selectedSetting) {
      // Modal is opening: fade in background overlay after 100ms delay
      RNAnimated.timing(backgroundOpacity, {
        toValue: 1,
        duration: 200,
        delay: 200, // 200ms delay before background appears
        useNativeDriver: true,
      }).start();
    } else {
      // Modal is closing: immediately hide background overlay
      backgroundOpacity.setValue(0);
    }
  }, [selectedSetting]);



  // Fetch user ID when support option is selected
  useEffect(() => {
    if (selectedSetting === 'support') {
      const fetchUserId = async () => {
        const id = await getUserID();
        setUserId(id);
      };
      fetchUserId();
    }
  }, [selectedSetting]);

  // Handle screen focus to hide unlock dialog when returning from WatchAdsScreen
  useFocusEffect(
    React.useCallback(() => {
      // Check if user has completed ad watching and has temporary premium access
      if (hasTemporaryPremiumAccess && showUnlockDialog) {
        console.log('SettingsScreen: User returned with temporary premium access, hiding unlock dialog');
        setShowUnlockDialog(false);
      }
    }, [hasTemporaryPremiumAccess, showUnlockDialog])
  );

  // Sync visual state with actual notification preference
  useEffect(() => {
    setVisualNotificationState(notificationPreference);
  }, [notificationPreference]);

  // Cleanup notification toggle timeout on unmount
  useEffect(() => {
    return () => {
      if (notificationToggleTimeoutRef.current) {
        clearTimeout(notificationToggleTimeoutRef.current);
      }
    };
  }, []);

  const handleSelectLanguage = (lang: 'en' | 'es' | 'dom') => {
    setAppLanguage(lang);
  };

  const copyToClipboard = async () => {
    try {
      await Clipboard.setString(userId);
      setIdCopied(true);

      // Hide feedback after 2 seconds
      setTimeout(() => setIdCopied(false), 2000);

    } catch (error) {
      console.error('Error copying to clipboard:', error);
      Sentry.captureException(error);
    }
  };

  // Helper function to get localized text for a penalty item
  const getLocalizedPenaltyText = (penalty: PenaltyItem): string => {
    if (language === 'es') return penalty.text_es;
    if (language === 'dom') return penalty.text_dom;
    return penalty.text_en; // Default to English
  };

  // Helper function to get all penalties sorted alphabetically
  const getAllPenaltiesSorted = (): PenaltyItem[] => {
    if (!penaltyContent) return [];

    const allPenalties: PenaltyItem[] = [];
    Object.values(penaltyContent).forEach((categoryPenalties: PenaltyItem[]) => {
      allPenalties.push(...categoryPenalties);
    });

    // Sort by premium status first (free first, then premium), then alphabetically by localized text
    return allPenalties.sort((a, b) => {
      // First, sort by premium status (free penalties first)
      if (a.isPremium !== b.isPremium) {
        return a.isPremium ? 1 : -1; // Free penalties (isPremium: false) come first
      }

      // Within the same premium status, sort alphabetically by localized text
      const textA = getLocalizedPenaltyText(a).toLowerCase();
      const textB = getLocalizedPenaltyText(b).toLowerCase();
      return textA.localeCompare(textB);
    });
  };

  const getPenaltyDisplayValue = () => {
    if (selectedIndividualPenalties.length === 0) {
      return t('penaltyNone');
    } else if (selectedIndividualPenalties.length === 1) {
      return `1 ${t('penaltySelected')}`;
    } else {
      return `${selectedIndividualPenalties.length} ${t('penaltySelected')}`;
    }
  };

  // Handle premium penalty tap - show unlock dialog for free users
  const handlePremiumPenaltyTap = async () => {
    console.log('Opening unlock dialog for premium penalty');
    setShowUnlockDialog(true);
  };

  // Handle notification toggle with debounced API requests
  const handleNotificationToggle = useCallback(async (enabled: boolean) => {
    try {
      console.log('🔔 [SettingsScreen] Toggle changed to:', enabled);

      // Update visual state immediately for instant feedback
      setVisualNotificationState(enabled);

      // Clear any existing timeout
      if (notificationToggleTimeoutRef.current) {
        clearTimeout(notificationToggleTimeoutRef.current);
      }

      if (enabled) {
        // Check current permission status immediately
        const { status } = await Notifications.getPermissionsAsync();
        console.log('🔔 [SettingsScreen] Current permission status:', status);

        if (status === 'denied') {
          // Permissions were previously denied, show settings alert IMMEDIATELY
          console.log('🔔 [SettingsScreen] Permissions denied, showing settings alert immediately');
          await pushNotificationService.showNotificationSettingsAlert({
            title: t('pushNotificationDisabledTitle'),
            message: t('pushNotificationDisabledMessage'),
            openSettings: t('pushNotificationOpenSettings'),
            cancel: t('pushNotificationCancel'),
            error: t('pushNotificationSettingsError')
          });
          // Reset visual state since we can't enable notifications
          setVisualNotificationState(false);
          return;
        } else if (status === 'granted') {
          // Permissions are granted, use 3-second delay for API request
          notificationToggleTimeoutRef.current = setTimeout(async () => {
            try {
              console.log('🔔 [SettingsScreen] Processing delayed notification enable for granted permissions');
              await setNotificationPreference(true);
            } catch (error) {
              console.error('Error in delayed notification enable:', error);
              Sentry.captureException(error);
            }
          }, 3000);
        } else {
          // Permissions not yet requested, handle IMMEDIATELY (no delay for first-time users)
          console.log('🔔 [SettingsScreen] First-time permission request, handling immediately');
          await handlePushNotificationPermissionFlow();

          // After permission flow, check if permissions were granted and update preference
          const { status: newStatus } = await Notifications.getPermissionsAsync();
          if (newStatus === 'granted') {
            await setNotificationPreference(true);
          } else {
            // If permissions were denied, reset visual state
            setVisualNotificationState(false);
          }
        }
      } else {
        // When turning off, only use delay if permissions are granted (for API request)
        const { status } = await Notifications.getPermissionsAsync();
        if (status === 'granted') {
          // Use 3-second delay for API request
          notificationToggleTimeoutRef.current = setTimeout(async () => {
            try {
              console.log('🔔 [SettingsScreen] Processing delayed notification disable');
              await setNotificationPreference(false);
            } catch (error) {
              console.error('Error in delayed notification disable:', error);
              Sentry.captureException(error);
            }
          }, 3000);
        } else {
          // No delay needed if permissions aren't granted
          await setNotificationPreference(false);
        }
      }
    } catch (error) {
      console.error('Error handling notification toggle:', error);
      Sentry.captureException(error);
    }
  }, [t, setNotificationPreference]);

  const settingsData = [
    {
      key: 'penalty',
      label: t('penaltyDialogTitle'),
      value: getPenaltyDisplayValue(),
      icon: PenaltyIcon,
    },
    {
      key: 'language',
      label: t('settingLanguage'),
      value: language === 'en'
        ? t('languageEnglish')
        : language === 'es'
          ? t('languageSpanish')
          : t('languageDominican'),
      icon: LanguageIcon,
    },
    {
      key: 'effects',
      label: t('settingEffects'),
      value:
        isSoundOn && isHapticsOn
          ? t('soundEffectsAllOn')
          : isSoundOn
          ? t('soundEffectsSomeOn')
          : isHapticsOn
          ? t('soundEffectsSomeOn')
          : t('soundEffectsAllOff'),
      icon: EffectsIcon,
    },
    {
      key: 'notifications',
      label: t('settingNotifications'),
      value: visualNotificationState ? t('notificationsOn') : t('notificationsOff'),
      icon: NotificationsIcon,
    },
  ];

  const getFadeInAnimation = (delay: number) =>
    FadeInDown.duration(400).delay(delay).easing(Easing.bounce);

  return (
    <View style={styles.mainContainer}>
      <StatusBar translucent backgroundColor="transparent" barStyle="light-content" />
      <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={async () => {
            await playSound('tapEffect1');
            navigation.goBack();
            if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
        >
          <ArrowRightIcon width={48} height={48} />
        </TouchableOpacity>

        <View style={styles.container}>
          <Animated.Text style={[typography.heading2, styles.title]} entering={getFadeInAnimation(100)}>
            {t('settingTitle')}
          </Animated.Text>

          <View style={styles.buttonStack}>
            {settingsData.map((item, index) => {
              const Icon = item.icon;
              return (
                <Animated.View key={item.key} entering={getFadeInAnimation(350 + index * 150)}>
                  <TouchableOpacity
                    style={styles.settingItem}
                    onPress={async () => {
                      setSelectedSetting(item.key);
                      await playSound('tapEffect3');
                      if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    }}
                  >
                    <View style={styles.icon}>
                      <Icon width={96} height={96} />
                    </View>
                    <View>
                      <Text style={[typography.subtitle1, styles.label]}>{item.label}</Text>
                      <Text style={[typography.subtitle2, styles.value]}>{item.value}</Text>
                    </View>
                  </TouchableOpacity>
                </Animated.View>
              );
            })}
          </View>
        </View>


        <Animated.View
            style={styles.footerContainer}
            entering={getFadeInAnimation(400)}>
          <TouchableOpacity onPress={async () => {
            setSelectedSetting('support');
            await playSound('tapEffect3');
            if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}>
            <Text style={[typography.subtitle3, styles.footerLink]}>{t('supportLink')}</Text>
          </TouchableOpacity>
          <Text style={[typography.subtitle3, styles.footerLink]}>·</Text>
          <TouchableOpacity onPress={() => Linking.openURL('https://www.taptrap.app/privacy')}>
            <Text style={[typography.subtitle3, styles.footerLink]}>{t('privacyLink')}</Text>
          </TouchableOpacity>
          <Text style={[typography.subtitle3, styles.footerLink]}>·</Text>
          <TouchableOpacity onPress={() => Linking.openURL('https://www.taptrap.app/terms')}>
            <Text style={[typography.subtitle3, styles.footerLink]}>{t('termsLink')}</Text>
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>

      <Modal transparent visible={!!selectedSetting} animationType="slide">
        <View style={styles.modalContainerTransparent}>
          <RNAnimated.View
            style={[styles.modalBackground, { opacity: backgroundOpacity }]}
          />
          <View style={styles.bottomSheet}>
            {selectedSetting === 'language' && (
              <View>
                <Text style={[typography.heading4, styles.bottomTitle]}>
                  {t('settingLanguage')}
                </Text>

                {Platform.OS === 'android' && !flagsLoaded ? (
                  // Mostrar spinner de carga solo en Android mientras se cargan los SVGs
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#42FFB7" />
                  </View>
                ) : (
                  // Mostrar opciones de idioma una vez cargados los SVGs
                  <>
                    <TouchableOpacity
                      style={language === 'en' ? styles.settingControlSeleted : styles.settingControl}
                      onPress={async () => {
                        setSelectedSetting(null);
                        handleSelectLanguage('en');
                        await playSound('tapEffect1');
                        if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      }}
                    >
                      <EnglishFlag width={56} height={56} />
                      <Text style={[typography.subtitle2, styles.settingControlTitle]}>{t('languageEnglish')}</Text>
                      {language === 'en' && <CheckMarkIcon width={48} height={48} />}
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={language === 'es' ? styles.settingControlSeleted : styles.settingControl}
                      onPress={async () => {
                        setSelectedSetting(null);
                        handleSelectLanguage('es');
                        await playSound('tapEffect1');
                        if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      }}
                    >
                      <SpainFlag width={56} height={56} />
                      <Text style={[typography.subtitle2, styles.settingControlTitle]}>{t('languageSpanish')}</Text>
                      {language === 'es' && <CheckMarkIcon width={48} height={48} />}
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={language === 'dom' ? styles.settingControlSeleted : styles.settingControl}
                      onPress={async () => {
                        setSelectedSetting(null);
                        handleSelectLanguage('dom');
                        await playSound('tapEffect1');
                        if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      }}
                    >
                      <DomFlag width={56} height={56} />
                      <Text style={[typography.subtitle2, styles.settingControlTitle]} numberOfLines={1} ellipsizeMode="tail">{t('languageDominican')}</Text>
                      {language === 'dom' && <CheckMarkIcon width={48} height={48} />}
                    </TouchableOpacity>
                  </>
                )}
              </View>
            )}

            {/* Effects */}
            {selectedSetting === 'effects' && (
              <View>
                <Text style={[typography.heading4, styles.bottomTitle]}>
                  {settingsData.find(item => item.key === selectedSetting)?.label}
                </Text>
                <View style={[styles.settingControl]}>
                  <Text style={[typography.subtitle2, styles.settingControlTitle]}>{t('soundEffectSounds')}</Text>
                  <LinearGradient style={styles.switchContainer} colors={['#42E0FF', '#42FFB7']}>
                    <Switch
                      value={isSoundOn}
                      disabled={false}
                      circleSize={28}
                      onValueChange={toggleSound}
                      barHeight={38}
                      circleBorderWidth={0}
                      backgroundActive={'transparent'}
                      backgroundInactive={'#2E2F31'}
                      circleActiveColor={'#FFF'}
                      circleInActiveColor={'#FFF'}
                      changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
                      innerCircleStyle={{ alignItems: "center", justifyContent: "center" }} // style for inner animated circle for what you (may) be rendering inside the circle
                      outerCircleStyle={{}} // style for outer animated circle
                      renderActiveText={false}
                      renderInActiveText={false}
                      switchLeftPx={3} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
                      switchRightPx={3} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
                      switchWidthMultiplier={2} // multiplied by the `circleSize` prop to calculate total width of the Switch
                      switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
                    />
                  </LinearGradient>
                </View>

                <View style={[styles.settingControl]}>
                  <Text style={[typography.subtitle2, styles.settingControlTitle]}>{t('soundEffectVibration')}</Text>
                  <LinearGradient style={styles.switchContainer} colors={['#42E0FF', '#42FFB7']}>
                    <Switch
                      value={isHapticsOn}
                      disabled={false}
                      circleSize={28}
                      onValueChange={toggleHaptics}
                      barHeight={38}
                      circleBorderWidth={0}
                      backgroundActive={'transparent'}
                      backgroundInactive={'#2E2F31'}
                      circleActiveColor={'#FFF'}
                      circleInActiveColor={'#FFF'}
                      changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
                      innerCircleStyle={{ alignItems: "center", justifyContent: "center" }} // style for inner animated circle for what you (may) be rendering inside the circle
                      outerCircleStyle={{}} // style for outer animated circle
                      renderActiveText={false}
                      renderInActiveText={false}
                      switchLeftPx={3} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
                      switchRightPx={3} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
                      switchWidthMultiplier={2} // multiplied by the `circleSize` prop to calculate total width of the Switch
                      switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
                    />
                  </LinearGradient>
                </View>
              </View>
            )}

            {/* Notifications */}
            {selectedSetting === 'notifications' && (
              <View>
                <Text style={[typography.heading4, styles.bottomTitle]}>
                  {settingsData.find(item => item.key === selectedSetting)?.label}
                </Text>

                <View style={[styles.settingControl]}>
                  <Text style={[typography.subtitle2, styles.settingControlTitle]}>{t('notificationsNewUpdate')}</Text>
                  <LinearGradient style={styles.switchContainer} colors={visualNotificationState ? ['#42E0FF', '#42FFB7'] : ['transparent', 'transparent']}>
                    <Switch
                      value={visualNotificationState}
                      disabled={false}
                      circleSize={28}
                      onValueChange={handleNotificationToggle}
                      barHeight={38}
                      circleBorderWidth={0}
                      backgroundActive={'transparent'}
                      backgroundInactive={'#2E2F31'}
                      circleActiveColor={'#FFF'}
                      circleInActiveColor={'#FFF'}
                      changeValueImmediately={true} // if rendering inside circle, change state immediately or wait for animation to complete
                      innerCircleStyle={{ alignItems: "center", justifyContent: "center" }} // style for inner animated circle for what you (may) be rendering inside the circle
                      outerCircleStyle={{}} // style for outer animated circle
                      renderActiveText={false}
                      renderInActiveText={false}
                      switchLeftPx={3} // denominator for logic when sliding to TRUE position. Higher number = more space from RIGHT of the circle to END of the slider
                      switchRightPx={3} // denominator for logic when sliding to FALSE position. Higher number = more space from LEFT of the circle to BEGINNING of the slider
                      switchWidthMultiplier={2} // multiplied by the `circleSize` prop to calculate total width of the Switch
                      switchBorderRadius={30} // Sets the border Radius of the switch slider. If unset, it remains the circleSize.
                    />
                  </LinearGradient>
                </View>
              </View>
            )}

            {/* Penalty */}
            {selectedSetting === 'penalty' && (
              <View style={[styles.penaltyContainer]}>
                <Text style={[typography.heading4, styles.bottomTitle]}>
                  {t('penaltyDialogTitle')}
                </Text>

                {!penaltyContent ? (
                  // Show loading state when penalty content is not available
                  <View style={styles.penaltyLoadingContainer}>
                    <ActivityIndicator size="large" color="#42FFB7" />
                    <Text style={[typography.subtitle2, styles.penaltyLoadingText]}>
                      Loading penalties...
                    </Text>
                  </View>
                ) : (
                  <ScrollView contentContainerStyle={[styles.penaltyList]} showsVerticalScrollIndicator={false}>
                    <View style={[styles.penaltyCallout]}>
                      <PenaltyIconSmall width={32} height={32}/>
                      <Text style={[typography.subtitle2, styles.penaltyCalloutText]}>{t('penaltyCalloutText')}</Text>
                    </View>

                    {/* Individual penalty items sorted alphabetically */}
                    {getAllPenaltiesSorted().map((penalty) => {
                      const isSelected = selectedIndividualPenalties.includes(penalty.id);
                      const penaltyText = getLocalizedPenaltyText(penalty);
                      const isPremiumPenalty = penalty.isPremium || false;
                      const canSelect = hasAnyPremiumAccess || !isPremiumPenalty;

                      return (
                        <TouchableOpacity
                          key={penalty.id}
                          style={[
                            isSelected ? styles.settingControlSmallSeleted : styles.settingControlSmall,
                            !canSelect && styles.settingControlDisabled
                          ]}
                          onPress={async () => {
                            if (isPremiumPenalty && !hasAnyPremiumAccess) {
                              // Show unlock dialog for premium penalties
                              await handlePremiumPenaltyTap();
                              return;
                            }

                            // Normal penalty selection logic
                            const newSelections = isSelected
                              ? selectedIndividualPenalties.filter(id => id !== penalty.id)
                              : [...selectedIndividualPenalties, penalty.id];

                            updateIndividualPenaltySelections(newSelections);
                            await playSound('tapEffect1');
                            if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                          }}
                        >
                           {isSelected ? <CheckMarkIcon width={44} height={44} /> : <UncheckedIcon width={44} height={44} />}
                          <Text
                            style={[
                              typography.subtitle2,
                              styles.settingControlTitle,
                              !canSelect && styles.settingControlTitleDisabled
                            ]}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >
                            {penaltyText}
                          </Text>

                          {isPremiumPenalty && !hasAnyPremiumAccess && <PremiumBadge style={styles.premiumBadge} width={79} height={28} />}
                        </TouchableOpacity>
                      );
                    })}
                  </ScrollView>
                )}
              </View>
            )}
            {/* Support */}
            {selectedSetting === 'support' && (
              <View>
                <Text style={[typography.heading4, styles.bottomTitle]}>
                  {t('supportTitle')}
                </Text>

                {/* Contact Us Button */}
                <TouchableOpacity
                  style={[styles.settingControl, { backgroundColor: '#2E2F31' }]}
                  onPress={() => {
                    setSelectedSetting(null);
                    Linking.openURL('https://eliezerpujols.notion.site/TapTrap-1ccdba9e944c80eda339f0efac05a4f1?pvs=4');
                  }}
                >
                  <Text style={[typography.subtitle2, styles.settingControlTitle]}>{t('contactUs')}</Text>
                  <ChevronRight width={32} height={32} />
                </TouchableOpacity>

                {/* User ID Section */}
                <TouchableOpacity
                  onPress={copyToClipboard}
                  style={[
                    styles.userIdContainer,
                    idCopied && styles.copyActiveContainer
                  ]}
                >
                  <Text
                    style={[
                      typography.subtitle2,
                      styles.userIdTitle,
                      idCopied && styles.copyActiveText
                    ]}
                  >
                    {idCopied ? t('idCopied') : t('userIdTitle')}
                  </Text>


                  <View style={styles.userIdTextContainer}>
                    <Text style={styles.userIdText}>{userId}</Text>
                  </View>
                  <Text style={styles.userIdDescription}>{t('userIdDescription')}</Text>
                </TouchableOpacity>
              </View>
            )}

            <TouchableOpacity
              style={styles.closeButton}
              onPress={async () => {
                setSelectedSetting(null);
                await playSound('tapEffect2');
                if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <CloseIcon width={32} height={32} />
            </TouchableOpacity>
          </View>

          {/* UnlockDialog inside Modal to ensure it appears above modal content */}
          {showUnlockDialog && (
            <View style={styles.unlockDialogOverlay}>
              <UnlockDialog
                isSequential={false}
                isInsideModal={true}
                onClose={() => {
                  setShowUnlockDialog(false);
                  // Also close the settings modal to allow navigation to work properly
                  setSelectedSetting(null);
                }}
              />
            </View>
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    position: 'relative',
    backgroundColor: "#131416",
  },
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    paddingTop: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 96,
    paddingHorizontal: 28,
  },
  backButton: {
    textAlign: 'center',
    zIndex: 999,
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 8,
    left: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 0,
  },
  title: {
    color: "#F3EBDC",
    fontSize: 48,
    lineHeight: 54,
    textAlign: 'center',
    marginBottom: 48
  },
  buttonStack: {
    width: '100%',
    maxWidth: 340,
    paddingHorizontal: 28,
    gap: 42,
    marginBottom: 40,
  },
  settingItem: {
    position: 'relative',
    justifyContent: 'center',
    backgroundColor: '#F3EBDC',
    borderRadius: 28,
    paddingLeft: 72,
    height: 100,
  },
  icon: {
    position: 'absolute',
    top: -32,
    left: -38,
    width: 100,
    height: 100,
  },
  label: {
    color: '#131416',
    fontSize: 36,
    lineHeight: 40,
    marginTop: 6,
    marginBottom: 0,
    letterSpacing: 2,
    textTransform: 'uppercase',
    width: '100%'
  },
  value: {
    fontSize: 24,
    lineHeight: 28,
    letterSpacing: 1.5,
    color: '#6D6F75',
    textTransform: 'uppercase',
  },
  modalContainerTransparent: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(84, 83, 83, 0.7)',
  },
  bottomSheet: {
    position: 'relative',
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    padding: 28,
    minHeight: 500, // Increased minimum height for consistency
    backgroundColor: '#131416',
  },
  bottomTitle: {
    top: 4,
    color: '#F3EBDC',
    width: '100%',
    fontSize: 36,
    lineHeight: 40,
    textAlign: 'center',
    marginBottom: 32,
  },
  closeButton: {
    zIndex: 2,
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 16
  },
  settingControl: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 32,
    borderWidth: 6,
    borderStyle: 'solid',
    borderColor: '#2E2F31',
    padding: 24,
    marginBottom: 24,
    gap: 20,
  },
  settingControlTitle: {
    top: 2,
    flex: 1,
    fontSize: 27,
    lineHeight: 36,
    letterSpacing: 1.5,
    color: '#F3EBDC',
    textTransform: 'uppercase',
  },
  settingControlDisabled: {
    opacity: 0.5,
  },
  settingControlTitleDisabled: {
    opacity: 0.5,
  },
  settingControlSeleted: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 32,
    borderWidth: 6,
    borderStyle: 'solid',
    borderColor: '#42FFB7',
    padding: 24,
    marginBottom: 24,
    gap: 20,
  },
  settingControlSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 32,
    borderWidth: 6,
    borderStyle: 'solid',
    borderColor: '#2E2F31',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 24,
    gap: 12,
  },
  settingControlSmallSeleted: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 32,
    borderWidth: 6,
    borderStyle: 'solid',
    borderColor: '#42FFB7',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 24,
    gap: 12,
  },
  switchContainer: {
    borderRadius: 32,
  },
  // Penalty
  penaltyContainer: {
    width: '100%',
    maxHeight: Dimensions.get('window').height - 130,
  },
  penaltyList: {
    paddingBottom: 40,
  },
  penaltyCallout: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 20,
    backgroundColor: '#232325',
    borderRadius: 32,
    padding: 24,
    paddingVertical: 20,
    paddingHorizontal: 24,
    marginBottom: 32,
  },
  penaltyCalloutText: {
    flex: 1,
    color: '#F3EBDC',
    fontSize: 32,
    lineHeight: 34,
    letterSpacing: 1.2,
  },
  premiumBadge: {
    position: 'absolute',
    right: 16,
    top: -18,
  },
  // Footer
  footerContainer: {
    flexDirection: "row",
    justifyContent: 'center',
    gap: 8,
    width: '100%',
    marginTop: 32,
    paddingBottom: 56,
  },
  footerLink: {
    fontSize: 20,
    color: '#6D6F75',
    letterSpacing: 2,
    textTransform: 'uppercase',
  },
  loadingContainer: {
    height: 420,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userIdContainer: {
    borderRadius: 32,
    borderWidth: 6,
    borderStyle: 'solid',
    borderColor: '#2E2F31',
    padding: 24,
    marginBottom: 24,
  },
  userIdTitle: {
    fontSize: 27,
    lineHeight: 36,
    letterSpacing: 1.5,
    color: '#F3EBDC',
    textTransform: 'uppercase',
    marginBottom: 12,
  },
  userIdTextContainer: {
    marginBottom: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: '#1E1F22',
    borderRadius: 16,
  },
  userIdText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#42FFB7',
  },
  userIdDescription: {
    fontSize: 16,
    lineHeight: 22,
    color: '#737985',
  },
  copyActiveContainer: {
    borderColor: '#42FFB7',
  },
  copyActiveText: {
    color: '#42FFB7',
  },
  penaltyLoadingContainer: {
    height: 300,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  penaltyLoadingText: {
    color: '#F3EBDC',
    fontSize: 24,
    lineHeight: 28,
    textAlign: 'center',
  },
  unlockDialogOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10000, // Higher than all other elements
  },
});