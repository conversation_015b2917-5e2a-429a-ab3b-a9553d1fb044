#!/usr/bin/env node

/**
 * Font Configuration Test Script
 * 
 * This script verifies that the font configuration in app.config.js
 * is properly set up and that all required font files exist.
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

function main() {
  log('🔍 Testing TapTrap Font Configuration...', colors.blue);
  log('');

  // Load app.config.js
  let appConfig;
  try {
    // Clear require cache to get fresh config
    delete require.cache[require.resolve('../app.config.js')];
    appConfig = require('../app.config.js');
    log('✅ app.config.js loaded successfully', colors.green);
  } catch (error) {
    log('❌ Failed to load app.config.js:', colors.red);
    log(error.message, colors.red);
    process.exit(1);
  }

  // Check if expo-font plugin is configured
  const plugins = appConfig.expo.plugins || [];
  const fontPlugin = plugins.find(plugin => 
    Array.isArray(plugin) && plugin[0] === 'expo-font'
  );

  if (!fontPlugin) {
    log('❌ expo-font plugin not found in app.config.js', colors.red);
    process.exit(1);
  }

  log('✅ expo-font plugin found in configuration', colors.green);

  // Check font configuration
  const fontConfig = fontPlugin[1];
  if (!fontConfig || !fontConfig.fonts) {
    log('❌ Font configuration not found in expo-font plugin', colors.red);
    process.exit(1);
  }

  log('✅ Font configuration found', colors.green);
  log(`📁 Configured fonts: ${fontConfig.fonts.length}`, colors.blue);

  // Check each font file
  let allFontsExist = true;
  const expectedFonts = [
    './assets/fonts/Melindya.ttf',
    './assets/fonts/Nunito-Bold.ttf',
    './assets/fonts/Nunito-SemiBold.ttf'
  ];

  fontConfig.fonts.forEach((fontPath, index) => {
    log(`  ${index + 1}. ${fontPath}`, colors.blue);
    
    // Convert relative path to absolute
    const absolutePath = path.resolve(__dirname, '..', fontPath);
    
    if (checkFileExists(absolutePath)) {
      log(`     ✅ File exists`, colors.green);
    } else {
      log(`     ❌ File not found: ${absolutePath}`, colors.red);
      allFontsExist = false;
    }
  });

  // Check if all expected fonts are configured
  log('');
  log('🔍 Checking expected fonts are configured...', colors.blue);
  
  expectedFonts.forEach(expectedFont => {
    if (fontConfig.fonts.includes(expectedFont)) {
      log(`✅ ${expectedFont} is configured`, colors.green);
    } else {
      log(`❌ ${expectedFont} is missing from configuration`, colors.red);
      allFontsExist = false;
    }
  });

  // Check for unexpected fonts
  const unexpectedFonts = fontConfig.fonts.filter(font => !expectedFonts.includes(font));
  if (unexpectedFonts.length > 0) {
    log('');
    log('⚠️  Unexpected fonts found in configuration:', colors.yellow);
    unexpectedFonts.forEach(font => {
      log(`   - ${font}`, colors.yellow);
    });
  }

  // Final result
  log('');
  if (allFontsExist && unexpectedFonts.length === 0) {
    log('🎉 Font configuration test PASSED!', colors.green);
    log('✅ All fonts are properly configured and files exist', colors.green);
    log('');
    log('Next steps:', colors.blue);
    log('1. Run: npx expo prebuild --clean', colors.blue);
    log('2. Build your app to test the font loading', colors.blue);
    process.exit(0);
  } else {
    log('❌ Font configuration test FAILED!', colors.red);
    if (!allFontsExist) {
      log('   - Some font files are missing or not configured', colors.red);
    }
    if (unexpectedFonts.length > 0) {
      log('   - Unexpected fonts found in configuration', colors.red);
    }
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
