import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import { typography } from '@/components/ThemedText';

// Services
import { useLanguage } from '@/services/i18nService';
import { useAppSettings } from '@/context/AppSettingsContext';

// Effects
import * as Haptics from 'expo-haptics';
import { useSound } from '@/components/game/playSound';

// SVGs
import ModalHeader from '@/assets/icons/penalty/bg_modal_header.svg';
import ModalActions from '@/assets/icons/penalty/bg_modal_actions.svg';
import NextIcon from '@/assets/icons/penalty/icon_next.svg';
import PenaltyIcon from '@/assets/icons/penalty/icon_penalty_large.svg';
import FlipIcon from '@/assets/icons/penalty/icon_flip_card.svg';

// Dialog Props
interface Props {
  onPress?: () => void;
  style?: ViewStyle;
  isSequential?: boolean;
  onBackToContent?: () => void;
  onNextMatch?: () => void;
  penaltyText?: string;
}

// Component
const PenaltyDialog: React.FC<Props> = ({ onPress, style, isSequential = false, onBackToContent, onNextMatch, penaltyText }) => {
  const { t } = useLanguage();
  const { playSound } = useSound();
  const { isHapticsOn, isSoundOn } = useAppSettings();

  const handleBackToContent = () => {
    if (isHapticsOn) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    if (isSoundOn) {
      playSound('tapEffect1');
    }

    if (onBackToContent) {
      onBackToContent();
    }
  };

  const handleNextMatch = () => {
    if (isHapticsOn) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    if (isSoundOn) {
      playSound('tapEffect3');
    }

    if (onNextMatch) {
      onNextMatch();
    }
  };

  return (
    <View style={[styles.penaltyOverlay]}>
      <View style={styles.penaltyContainer} >
        <View style={styles.penaltyHeader}>
          <TouchableOpacity
            style={styles.backToContentDialogButton}
            onPress={handleBackToContent}
          >
            <FlipIcon
              width={32}
              height={32}
            />
          </TouchableOpacity>
          <PenaltyIcon
              style={styles.penaltyHeaderIcon}
              width={80}
              height={80}
            />
          <Text style={[typography.heading4, styles.penaltyHeaderText]}>{t('penaltyDialogTitle')}</Text>
          <ModalHeader style={styles.penaltyHeaderBg}/>
        </View>
        <View style={styles.penaltyContent}>
          <Text style={[typography.subtitle1, styles.penaltyContentText]}>
            {penaltyText || 'No penalty selected.'}
          </Text>
        </View>

        <View style={styles.penaltyActions}>
          {/* Next Match Button */}
          <TouchableOpacity
            style={styles.nextMatchButton}
            onPress={handleNextMatch}
          >
            <Text style={[typography.subtitle2, styles.nextMatchButtonLabel]}>{t('penaltyDialogButton')}</Text>
            <NextIcon
              style={styles.nextMatchButtonIcon}
              width={40}
              height={40}
            />
          </TouchableOpacity>

          <ModalActions style={styles.penaltyActionsBg}/>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  penaltyOverlay: {
    zIndex: 4,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  penaltyContainer: {
    width: 312,
    alignItems: 'center',
  },
  penaltyHeader: {
    position: "relative",
    width: 312,
    height: 80,
  },
  penaltyHeaderIcon: {
    zIndex: 2,
    position: 'absolute',
    top: -40,
    left: '50%',
    transform: [{ translateX: -40 }], // Half of the icon width (80/2 = 40)
  },
  penaltyHeaderText: {
    zIndex: 2,
    top: 40,
    justifyContent: "center",
    alignItems: "center",
    textAlign: 'center',
    color: '#FF4B5C',
    fontSize: 32,
    lineHeight: 46,
  },
  penaltyHeaderBg: {
    zIndex: 1,
    position: "absolute",
  },
  backToContentDialogButton : {
    zIndex: 3,
    position: 'absolute',
    top: 12,
    left: 12,
    padding: 8,
  },
  penaltyContent: {
    paddingTop: 24,
    paddingHorizontal: 24,
    backgroundColor: "#131416",
    width: "100%",
  },
  penaltyContentText: {
    marginBottom: 24,
    textAlign: 'center',
    color: '#F3EBDC',
    fontSize: 38,
    lineHeight: 42,
    letterSpacing: 2,
  },
  penaltyActions: {
    position: 'relative',
    top: -2,
    width: 312,
    paddingHorizontal: 18,
    paddingBottom: 16,
  },
  penaltyActionsBg: {
    zIndex: 1,
    position: "absolute",
    bottom: 0,
  },
  nextMatchButton: {
    zIndex: 2,
    width: '100%',
    flexDirection: 'row',
    height: 64,
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
    borderRadius: 200,
    paddingLeft: 32,
    paddingRight: 16,
    backgroundColor: '#242529',
  },
  nextMatchButtonLabel: {
    top: 3,
    fontSize: 26,
    color: "#F3EBDC",
    lineHeight: 32,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  nextMatchButtonIcon: {
    left: 0,
  },
});

export default PenaltyDialog;
