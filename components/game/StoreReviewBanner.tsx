import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import { typography } from '@/components/ThemedText';
import StarIcon from '@/assets/review/icon_star.svg';
import ArrowRightIcon from '@/assets/review/icon_blue_right_arrow.svg'
import { useLanguage } from '@/services/i18nService';

interface Props {
  onPress?: () => void;
  style?: ViewStyle;
}

const StoreReviewBanner: React.FC<Props> = ({ onPress, style}) => {
  const { t } = useLanguage();

  return (
    <TouchableOpacity onPress={onPress} style={[styles.storeReview, style]}>
      <StarIcon style={styles.storeReviewIcon} width={96} height={96} />
      <View style={styles.storeReviewContent}>
        <Text style={[typography.subtitle2, styles.storeReviewTitle]}>{t('storeReviewBannerTitle')}</Text>
        <View style={[styles.storeReviewButton]}>
          <Text style={[typography.subtitle2, styles.storeReviewButtonText ]}>{t('storeReviewBannerButton')}</Text>
          <ArrowRightIcon width={24} height={32} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  storeReview: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingLeft: 80,
    paddingRight: 32,
    backgroundColor: '#131416',
    borderRadius: 30,
  },
  storeReviewContent: {

  },
  storeReviewTitle: {
    color: '#F3EBDC',
    textTransform: 'uppercase',
    fontSize: 26,
    lineHeight: 40,
    // letterSpacing: 1.2,
  },
  storeReviewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  storeReviewButtonText: {
    fontSize: 22,
    lineHeight: 32,
    color: '#43F9C4',
    textTransform: 'uppercase',
    letterSpacing: 1.2,
  },
  storeReviewIcon: {
    position: 'absolute',
    left: -24,
    top: -32,
    transform: [{ rotate: '-15deg' }],
  },
});

export default StoreReviewBanner;