import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import { typography } from '@/components/ThemedText';
import IconSpicy from '@/assets/redesign/icon_spicy.svg';
import ArrowRightIcon from '@/assets/icons/arrowRightOrange.svg';
import { useLanguage } from '@/services/i18nService';

interface Props {
  onPress?: () => void;
  style?: ViewStyle;
  isSequential?: boolean;
}

const InGameBanner: React.FC<Props> = ({ onPress, style, isSequential = false }) => {
  const { t } = useLanguage();

  // Use different translation keys based on isSequential parameter
  const titleKey = isSequential ? 'inGameBannerSequentialTitle' : 'inGameBannerTitle';
  const buttonKey = isSequential ? 'inGameBannerSequentialButton' : 'inGameBannerButton';

  return (
    <TouchableOpacity onPress={onPress} style={[styles.inGameBanner, style]}>
      <IconSpicy style={styles.inGameBannerIcon} width={96} height={96} />
      <View style={styles.inGameBannerContent}>
        <Text style={[typography.subtitle2, styles.inGameBannerTitle]}>{t(titleKey)}</Text>
        <View style={[styles.inGameBannerButton]}>
          <Text style={[typography.subtitle2, styles.inGameBannerButtonText ]}>{t(buttonKey)}</Text>
          <ArrowRightIcon width={24} height={32} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  inGameBanner: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingLeft: 80,
    paddingRight: 32,
    backgroundColor: '#131416',
    borderRadius: 30,
  },
  inGameBannerContent: {

  },
  inGameBannerTitle: {
    color: '#F3EBDC',
    textTransform: 'uppercase',
    fontSize: 26,
    lineHeight: 40,
    // letterSpacing: 1.2,
  },
  inGameBannerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  inGameBannerButtonText: {
    fontSize: 22,
    lineHeight: 32,
    color: '#FFBE42',
    textTransform: 'uppercase',
    letterSpacing: 1.2,
  },
  inGameBannerIcon: {
    position: 'absolute',
    left: -24,
    top: -32,
    transform: [{ rotate: '-15deg' }],
  },
});

export default InGameBanner;