import { Audio } from 'expo-av';
import { useAppSettings } from '@/context/AppSettingsContext';

// Map of sound keys to their respective file paths
const soundFiles: { [key: string]: any } = {
  tapEffect3: require('@/assets/sounds/tap_effect_3.mp3'),
  tapEffect2: require('@/assets/sounds/tap_effect_2.mp3'),
  tapEffect1: require('@/assets/sounds/tap_effect_1.mp3'),
  successEffect: require('@/assets/sounds/success_effect.mp3'),
};

// Pure async play function (used internally)
const playSoundInternal = async (soundKey: keyof typeof soundFiles) => {
  try {
    const { sound } = await Audio.Sound.createAsync(soundFiles[soundKey]);
    await sound.playAsync();
  } catch (error) {
    console.error('Error playing sound:', error);
  }
};

// Hook-based sound trigger
export const useSound = () => {
  const { isSoundOn } = useAppSettings();

  const playSound = async (soundKey: keyof typeof soundFiles) => {
    if (!isSoundOn) return;
    await playSoundInternal(soundKey);
  };

  return { playSound };
};