import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ImageBackground, Linking } from 'react-native';
import { typography } from '@/components/ThemedText';
import { LinearGradient } from 'expo-linear-gradient';
import PlayIcon from '@/assets/whatsnew/icon_play_small.svg';
import { useLanguage } from '@/services/i18nService';

// SVGs
import InfoIcon from '@/assets/icons/penalty/icon_penalty_small.svg';

// Component
export type WhatsNewLogType = 'dark' | 'red' | 'cyan' | 'purple';

export interface WhatsNewLogProps {
  type: WhatsNewLogType;
  date: string;
  title: string;
  description: string;
  videoUrl?: string;
  updateApp?: boolean;
}

const getGradientColors = (type: WhatsNewLogType): [string, string] => {
  switch (type) {
    case 'dark':
      return ['#7F7F90', '#43434A'];
    case 'purple':
      return ['#FF4EE2', '#C119FA'];
    case 'red':
      return ['#FF4B5C', '#FF5C25'];
    case 'cyan':
      return ['#42E0FF', '#42FFB7'];
    default:
      return ['#7F7F90', '#43434A'];
  }
};

const getButtonColor = (type: WhatsNewLogType): string => {
  switch (type) {
    case 'dark':
      return '#5E5E69';
    case 'purple':
      return '#FF4EE2';
    case 'red':
      return '#FF4757';
    case 'cyan':
      return '#4ECDC4';
    default:
      return '#5E5E69';
  }
};

const getTextColor = (type: WhatsNewLogType): string => {
  switch (type) {
    case 'dark':
      return '#F3EBDC';
    case 'purple':
      return '#F3EBDC';
    case 'red':
      return '#F3EBDC';
    case 'cyan':
      return '#131416';
    default:
      return '#F3EBDC';
  }
};

export default function WhatsNewLog({ type, date, title, description, videoUrl, updateApp }: WhatsNewLogProps) {
  const { t } = useLanguage();

  const handleWatchVideo = async () => {
    if (videoUrl) {
      try {
        await Linking.openURL(videoUrl);
      } catch (error) {
        console.error('Error opening video URL:', error);
      }
    }
  };

  return (
    <View style={styles.container}>
      {/* Top Section with Gradient and Pattern */}
      <LinearGradient
        colors={getGradientColors(type)}
        style={styles.topSection}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <ImageBackground
          source={require('@/assets/whatsnew/strip_pattern.png')}
          style={styles.patternBackground}
          resizeMode="cover"
        >
          {/* Date Badge */}
          <View style={styles.dateBadge}>
            <Text style={styles.dateText}>{date}</Text>
          </View>

          {/* Title */}
          <Text style={[styles.title, { color: getTextColor(type) }]}>{title}</Text>
        </ImageBackground>
      </LinearGradient>

      {/* Bottom Section */}
      <View style={styles.bottomSection}>
        <Text style={[typography.nunitoBold,styles.description]}>{description}</Text>

        {videoUrl && (
          <TouchableOpacity
            style={[styles.watchButton, { backgroundColor: getButtonColor(type) }]}
            onPress={handleWatchVideo}
          >
            <PlayIcon width={16} height={16} />
            <Text style={[styles.watchButtonText, { color: getTextColor(type) } ]}>
              {t('watchVideo')}
            </Text>
          </TouchableOpacity>
        )}

        {updateApp && (
          <View style={styles.updateAppContainer}>
            <InfoIcon width={20} height={20} />
            <Text style={styles.updateAppText}>
              {t('updateApp')}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 28,
    overflow: 'hidden',
    marginBottom: 32,
  },
  topSection: {
    minHeight: 140,
    position: 'relative',
  },
  patternBackground: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  dateBadge: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(19, 20, 22, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 20,
  },
  dateText: {
    top: 2,
    color: '#F3EBDC',
    fontFamily: 'Melindya',
    fontSize: 14,
    fontWeight: '400',
    letterSpacing: 1,
    textTransform: 'uppercase',
  },
  title: {
    color: '#131416',
    fontFamily: 'Melindya',
    fontSize: 32,
    fontWeight: '400',
    lineHeight: 47,
    letterSpacing: 2,
    textTransform: 'uppercase',
    marginTop: 'auto',
  },
  bottomSection: {
    display: 'flex',
    padding: 20,
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 14,
    alignSelf: 'stretch',
    backgroundColor: '#232325',
  },
  description: {
    color: '#F3EBDC',
    fontSize: 19,
    fontWeight: '400',
    lineHeight: 28,
  },
  watchButton: {
    display: 'flex',
    paddingVertical: 8,
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 6,
    borderRadius: 16,
    flexDirection: 'row',
  },
  watchButtonText: {
    top: 2,
    fontFamily: 'Melindya',
    fontSize: 14,
    fontWeight: '400',
    letterSpacing: 1,
    textTransform: 'uppercase',
  },
  updateAppContainer: {
    width: '100%',
    paddingTop: 16,
    borderTopWidth: 2,
    borderTopColor: '#303237',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  updateAppText: {
    top: 3,
    fontFamily: 'Melindya',
    fontSize: 18,
    letterSpacing: 1,
    color: '#FF4B5C',
  },
});