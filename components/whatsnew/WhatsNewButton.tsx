import React, { useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ViewStyle } from 'react-native';
import { typography } from '@/components/ThemedText';
import { useLanguage } from '@/services/i18nService';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate
} from 'react-native-reanimated';

// SVGs
import GiftIcon from '@/assets/whatsnew/icon_gift.svg';

interface Props {
  onPress?: () => void;
  style?: ViewStyle;
  showDot?: boolean;
}

const WhatsNewButton: React.FC<Props> = ({ onPress, style, showDot = true }) => {
  const { t } = useLanguage();

  // Animation for the pulsating button
  const pulseAnimation = useSharedValue(0);

  useEffect(() => {
    if (showDot) {
      // Start slow pulsating animation when dot should be shown
      pulseAnimation.value = withRepeat(
        withTiming(1, { duration: 500 }), // 2 second duration for slow pulse
        -1, // Infinite repeat
        true // Reverse animation (pulse in and out)
      );
    } else {
      // Stop animation when dot is hidden
      pulseAnimation.value = 0;
    }
  }, [showDot]);

  // Animated style for the entire button
  const animatedButtonStyle = useAnimatedStyle(() => {
    const scale = interpolate(pulseAnimation.value, [0, 1], [1, 1.11]);
    const opacity = interpolate(pulseAnimation.value, [0, 1], [1, 0.8]);

    return {
      transform: [{ scale }],
      opacity,
    };
  });

  return (
    <Animated.View style={[showDot && animatedButtonStyle]}>
      <TouchableOpacity onPress={onPress} style={[styles.whatsNewButton, style]}>
        <GiftIcon width={36} height={36 } />
        <Text style={[typography.subtitle2, styles.whatsNewTitle]}>
          {t('whatsNewTitle')}
        </Text>
        {showDot && <View style={styles.whatsNewDot}/>}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  whatsNewButton: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    gap: 8,
    backgroundColor: '#232325',
    borderRadius: 30,
    paddingVertical: 7,
    paddingHorizontal: 10,
  },
  whatsNewTitle: {
    top: 3,
    color: '#F3EBDC',
    fontSize: 22,
    letterSpacing: 1,
    lineHeight: 24,
    textTransform: 'uppercase',
    marginRight: 8,
  },
  whatsNewDot: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: 15,
    height: 15,
    borderWidth: 3,
    borderColor: '#131416',
    borderRadius: 10,
    backgroundColor: '#FF4B5C',
  },
});

export default WhatsNewButton;