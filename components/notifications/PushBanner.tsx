import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';
import { typography } from '@/components/ThemedText';

// Services
import { useLanguage } from '@/services/i18nService';

// SVGs
import NotificationIcon from '@/assets/notifications/notifications_icon.svg';
import ArrowRightIcon from '@/assets/icons/arrowRightOrange.svg';

interface Props {
  onPress?: () => void;
  style?: ViewStyle;
  isSequential?: boolean;
}

const PushNotificationBanner: React.FC<Props> = ({ onPress, style, isSequential = false }) => {
  const { t } = useLanguage();

  return (
    <TouchableOpacity onPress={onPress} style={[styles.pushNotificationBanner, style]}>
      <NotificationIcon style={styles.pushNotificationBannerIcon} width={96} height={96} />
      <View style={styles.pushNotificationBannerContent}>
        <Text style={[typography.subtitle2, styles.pushNotificationBannerTitle]}>{t('pushNotificationBannerTitle')}</Text>
        <View style={[styles.pushNotificationBannerButton]}>
          <Text style={[typography.subtitle2, styles.pushNotificationBannerButtonText ]}>{t('pushNotificationBannerButton')}</Text>
          <ArrowRightIcon width={24} height={32} />
        </View>
      </View>
      
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  pushNotificationBanner: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingLeft: 80,
    paddingRight: 32,
    backgroundColor: '#131416',
    borderRadius: 30,
  },
  pushNotificationBannerContent: {
  },

  pushNotificationBannerTitle: {
    color: '#F3EBDC',
    textTransform: 'uppercase',
    fontSize: 26,
    lineHeight: 40,
    // letterSpacing: 1.2,
  },
  pushNotificationBannerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  pushNotificationBannerButtonText: {
    fontSize: 22,
    lineHeight: 32,
    color: '#FFBE42',
    textTransform: 'uppercase',
    letterSpacing: 1.2,
  },
  pushNotificationBannerIcon: {
    position: 'absolute',
    left: -24,
    top: -32,
    transform: [{ rotate: '-15deg' }],
  },
});

export default PushNotificationBanner;