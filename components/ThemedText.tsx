import { Platform, Text, type TextProps, StyleSheet } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export const typography = StyleSheet.create({
  // Font family styles
  nunitoBold: {
    fontFamily: 'Nunito-Bold',
  },
  nunitoSemiBold: {
    fontFamily: 'Nunito-SemiBold',
  },

  // Existing Melindya heading styles
  headingJumbo: {
    fontFamily: 'Melindya',
    fontSize: 138,
    lineHeight: 160,
    textTransform: 'uppercase',
  },
  heading1: {
    fontFamily: 'Melindya',
    fontSize: 88,
    lineHeight: 104,
    textTransform: 'uppercase',
  },
  heading2: {
    fontFamily: 'Melindya',
    fontSize: 45,
    lineHeight: 48,
    textTransform: 'uppercase',
  },
  heading3: {
    fontFamily: 'Melindya',
    fontSize: 35,
    lineHeight: 42,
    textTransform: 'uppercase',
    letterSpacing: 1.7,
  },
  heading4: {
    fontFamily: 'Melindya',
    fontSize: 28,
    lineHeight: 42,
    textTransform: 'uppercase',
  },
  subtitle1: {
    fontFamily: 'Melindya',
    fontSize: 24,
    lineHeight: 36,
  },
  subtitle2: {
    fontFamily: 'Melindya',
    fontSize: 20,
    lineHeight: 24,
  },
  subtitle3: {
    fontFamily: 'Melindya',
    fontSize: 16,
    lineHeight: 24,
  },
});

const styles = StyleSheet.create({
  default: {
    fontSize: 16,
    lineHeight: 24,
  },
  defaultSemiBold: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '600',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 32,
  },
  subtitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  link: {
    lineHeight: 30,
    fontSize: 16,
    color: '#0a7ea4',
  },
});
