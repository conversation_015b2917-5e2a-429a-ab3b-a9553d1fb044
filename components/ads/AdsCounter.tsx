import React from 'react';
import { Text, StyleSheet } from 'react-native';
import { typography } from '@/components/ThemedText';
import { useLanguage } from '@/services/i18nService';

interface AdsCounterProps {
  watchedCount: number;
  totalCount: number;
  style?: any;
}

const AdsCounter: React.FC<AdsCounterProps> = ({ 
  watchedCount, 
  totalCount, 
  style 
}) => {
  const { t } = useLanguage();

  return (
    <Text style={[typography.subtitle3, styles.adsCounter, style]}>
      {watchedCount}/{totalCount} {t('watchAdsWatched')}
    </Text>
  );
};

const styles = StyleSheet.create({
  adsCounter: {
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.8,
  },
});

export default AdsCounter;
