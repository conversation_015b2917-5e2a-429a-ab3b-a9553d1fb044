/**
 * <PERSON><PERSON><PERSON> to remove test/development tokens from the database
 * 
 * This script identifies and removes obvious test tokens
 * Run with: node remove-test-tokens.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const PushToken = require('./src/models/PushToken');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function removeTestTokens() {
  try {
    // Connect to MongoDB
    log('Connecting to MongoDB...', 'blue');
    await mongoose.connect(process.env.MONGODB_URI);
    log('Connected to MongoDB successfully', 'green');

    // Find tokens that look like test tokens
    const testTokenPatterns = [
      /test-token/i,
      /test_token/i,
      /development/i,
      /dev-token/i,
      /dummy/i,
      /fake/i,
      /sample/i,
      /example/i
    ];

    log('\nSearching for test tokens...', 'blue');
    
    const allTokens = await PushToken.find({});
    const testTokens = [];

    for (const token of allTokens) {
      // Check token string
      const isTestToken = testTokenPatterns.some(pattern => 
        pattern.test(token.token) || 
        pattern.test(token.deviceId || '')
      );

      if (isTestToken) {
        testTokens.push(token);
      }
    }

    log(`Found ${testTokens.length} potential test tokens out of ${allTokens.length} total tokens`, 'cyan');

    if (testTokens.length === 0) {
      log('No test tokens found!', 'green');
      return;
    }

    // Show test tokens
    log('\n🧪 Test tokens found:', 'yellow');
    testTokens.forEach((token, index) => {
      log(`${index + 1}. Token: ${token.token}`, 'yellow');
      log(`   Device ID: ${token.deviceId}`, 'yellow');
      log(`   Platform: ${token.platform}, Language: ${token.language}`, 'yellow');
      log(`   Active: ${token.isActive}, Created: ${token.createdAt}`, 'yellow');
      log('', 'reset');
    });

    // Ask for confirmation
    log('⚠️  Do you want to remove these test tokens? (y/N)', 'red');
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      rl.question('', resolve);
    });
    rl.close();

    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      log('\nRemoving test tokens...', 'blue');
      
      const tokenIds = testTokens.map(t => t._id);
      const result = await PushToken.deleteMany({ _id: { $in: tokenIds } });

      log(`✅ Successfully removed ${result.deletedCount} test tokens`, 'green');
    } else {
      log('Removal cancelled by user', 'yellow');
    }

  } catch (error) {
    log(`Error during removal: ${error.message}`, 'red');
    console.error(error);
  } finally {
    await mongoose.disconnect();
    log('\nDisconnected from MongoDB', 'blue');
  }
}

// Run the removal
if (require.main === module) {
  removeTestTokens()
    .then(() => {
      log('\nRemoval completed', 'green');
      process.exit(0);
    })
    .catch((error) => {
      log(`Removal failed: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { removeTestTokens };
