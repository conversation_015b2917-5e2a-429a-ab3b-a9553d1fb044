/**
 * <PERSON><PERSON>t to clean up invalid push notification tokens
 * 
 * This script identifies and removes invalid push tokens from the database
 * Run with: node cleanup-invalid-tokens.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const PushToken = require('./src/models/PushToken');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testTokenWithExpo(token) {
  try {
    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: token,
        title: 'Test',
        body: 'Token validation test',
        data: { test: true }
      }),
    });

    const result = await response.json();
    
    // Check if it's a single response or array response
    if (result.data && Array.isArray(result.data)) {
      return result.data[0];
    } else if (Array.isArray(result)) {
      return result[0];
    } else {
      return result;
    }
  } catch (error) {
    log(`Error testing token: ${error.message}`, 'red');
    return { status: 'error', message: error.message };
  }
}

async function cleanupInvalidTokens() {
  try {
    // Connect to MongoDB
    log('Connecting to MongoDB...', 'blue');
    await mongoose.connect(process.env.MONGODB_URI);
    log('Connected to MongoDB successfully', 'green');

    // Get all active tokens
    log('\nFetching all active push tokens...', 'blue');
    const tokens = await PushToken.find({ isActive: true });
    log(`Found ${tokens.length} active tokens`, 'cyan');

    if (tokens.length === 0) {
      log('No active tokens to check', 'yellow');
      return;
    }

    // Test tokens in batches to avoid overwhelming Expo API
    const batchSize = 10;
    const invalidTokens = [];
    const validTokens = [];
    
    log('\nTesting tokens with Expo API...', 'blue');
    
    for (let i = 0; i < tokens.length; i += batchSize) {
      const batch = tokens.slice(i, i + batchSize);
      log(`Testing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(tokens.length / batchSize)} (${batch.length} tokens)`, 'cyan');
      
      for (const tokenDoc of batch) {
        const result = await testTokenWithExpo(tokenDoc.token);
        
        if (result.status === 'error') {
          if (result.message && result.message.includes('not a registered push notification recipient')) {
            log(`  ❌ Invalid token: ${tokenDoc.token.substring(0, 30)}... (${tokenDoc.deviceId})`, 'red');
            invalidTokens.push(tokenDoc);
          } else if (result.details && result.details.error === 'DeviceNotRegistered') {
            log(`  ❌ Device not registered: ${tokenDoc.token.substring(0, 30)}... (${tokenDoc.deviceId})`, 'red');
            invalidTokens.push(tokenDoc);
          } else {
            log(`  ⚠️  Unknown error for token ${tokenDoc.token.substring(0, 30)}...: ${result.message}`, 'yellow');
          }
        } else if (result.status === 'ok') {
          log(`  ✅ Valid token: ${tokenDoc.token.substring(0, 30)}... (${tokenDoc.deviceId})`, 'green');
          validTokens.push(tokenDoc);
        }
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      // Longer delay between batches
      if (i + batchSize < tokens.length) {
        log('  Waiting 2 seconds before next batch...', 'yellow');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Summary
    log('\n📊 Token Validation Summary:', 'blue');
    log(`Valid tokens: ${validTokens.length}`, 'green');
    log(`Invalid tokens: ${invalidTokens.length}`, 'red');

    if (invalidTokens.length === 0) {
      log('\nNo invalid tokens found. Database is clean!', 'green');
      return;
    }

    // Show invalid tokens details
    log('\n🗑️  Invalid tokens to be cleaned up:', 'red');
    invalidTokens.forEach((token, index) => {
      log(`${index + 1}. Device: ${token.deviceId}, Platform: ${token.platform}, Language: ${token.language}`, 'red');
      log(`   Token: ${token.token.substring(0, 50)}...`, 'red');
      log(`   Created: ${token.createdAt}`, 'red');
    });

    // Ask for confirmation
    log('\n⚠️  Do you want to deactivate these invalid tokens? (y/N)', 'yellow');
    
    // For automated cleanup, you can uncomment the next line and comment out the readline part
    // const answer = 'y';
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      rl.question('', resolve);
    });
    rl.close();

    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      log('\nDeactivating invalid tokens...', 'blue');
      
      const tokenIds = invalidTokens.map(t => t._id);
      const result = await PushToken.updateMany(
        { _id: { $in: tokenIds } },
        { 
          isActive: false, 
          updatedAt: new Date(),
          deactivatedReason: 'Invalid token - not registered with Expo'
        }
      );

      log(`✅ Successfully deactivated ${result.modifiedCount} invalid tokens`, 'green');
    } else {
      log('Cleanup cancelled by user', 'yellow');
    }

  } catch (error) {
    log(`Error during cleanup: ${error.message}`, 'red');
    console.error(error);
  } finally {
    await mongoose.disconnect();
    log('\nDisconnected from MongoDB', 'blue');
  }
}

// Run the cleanup
if (require.main === module) {
  cleanupInvalidTokens()
    .then(() => {
      log('\nCleanup completed', 'green');
      process.exit(0);
    })
    .catch((error) => {
      log(`Cleanup failed: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { cleanupInvalidTokens, testTokenWithExpo };
