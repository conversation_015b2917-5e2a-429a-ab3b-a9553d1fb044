# TapTrap Backend Production Guide

This document provides guidance for deploying the TapTrap backend to production.

## Production Checklist

### 1. Database Setup

✅ Switch from in-memory to persistent MongoDB

- See [MONGODB_SETUP.md](./MONGODB_SETUP.md) for detailed instructions
- Create database backups before major deployments
- Use MongoDB Atlas for managed database service

### 2. Environment Variables

Create a secure `.env` file for production:

```
NODE_ENV=production
PORT=5001
MONGODB_URI=mongodb+srv://<username>:<password>@cluster0.mongodb.net/taptrap?retryWrites=true&w=majority
JWT_SECRET=<generate-a-long-random-string>
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=<secure-password>
CORS_ORIGINS=https://taptrap.app,https://admin.taptrap.app
```

Important security notes:
- Generate a strong, random JWT_SECRET (at least 32 characters)
- Use a secure admin password
- Keep environment variables protected and out of source control

### 3. Security Hardening

Update the following files:

1. `src/middleware/auth.js`:
   - Remove development mode bypasses
   - Implement proper JWT verification
   - Add rate limiting for login attempts

2. `src/controllers/adminController.js`:
   - Remove development mode shortcuts
   - Implement proper error handling
   - Add validation for all endpoints

3. Frontend security (`admin/src/services/api.js`):
   - Remove development mode workarounds
   - Add proper error handling
   - Implement secure token storage

### 4. CORS Configuration

The backend now supports configuring CORS origins through the environment variable:

```
CORS_ORIGINS=https://taptrap.app,https://admin.taptrap.app
```

This allows you to easily update allowed origins without changing the code. The CORS configuration in `src/index.js` will automatically use the origins specified in this environment variable.

If the environment variable is not set, it will fall back to the default allowed origins:

```javascript
app.use(cors({
  origin: function(origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if(!origin) return callback(null, true);
    // Allow all origins in development
    if(process.env.NODE_ENV === 'development') return callback(null, true);

    // Get allowed origins from environment variable or use default list
    let allowedOrigins = [
      'https://taptrap.app',
      'https://taptrap-admin.vercel.app'
      // other default origins...
    ];

    // If CORS_ORIGINS is defined in .env, use it instead
    if (process.env.CORS_ORIGINS) {
      allowedOrigins = process.env.CORS_ORIGINS.split(',').map(origin => origin.trim());
    }

    // Check if the request origin is in the allowed list
    if(allowedOrigins.indexOf(origin) !== -1 || !origin) {
      callback(null, true);
    } else {
      callback(null, true); // Currently allowing all origins for compatibility
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'x-auth-token', 'Authorization'],
  credentials: true
}));
```

### 5. Deployment Options

#### Option A: Vercel (Recommended)

1. Create a `vercel.json` file:
```json
{
  "version": 2,
  "builds": [
    {
      "src": "src/index.js",
      "use": "@vercel/node"
    },
    {
      "src": "admin/package.json",
      "use": "@vercel/static-build",
      "config": { "distDir": "build" }
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "src/index.js"
    },
    {
      "src": "/admin",
      "dest": "admin/build/index.html"
    },
    {
      "src": "/admin/(.*)",
      "dest": "admin/build/index.html"
    },
    {
      "src": "/(.*)",
      "dest": "admin/build/$1"
    }
  ]
}
```

2. Deploy:
```bash
vercel --prod
```

#### Option B: Railway/Render

1. Create `Procfile`:
```
web: node src/index.js
```

2. Deploy API and admin UI separately:
   - API: Deploy the `backend` directory
   - Admin UI: Build and deploy the `backend/admin` directory

### 6. Pre-Deployment Tasks

1. **Build the admin frontend**:
```bash
cd backend/admin
npm run build
```

2. **Test the production build locally**:
```bash
cd backend
NODE_ENV=production npm start
```

3. **Create admin user**:
```bash
NODE_ENV=production npm run seed:admin
```

4. **Import initial content** (if needed):
```bash
NODE_ENV=production npm run import:airtable
```

### 7. Custom Domain Setup

1. Configure your domain (taptrap.app) to point to your deployment
2. Set up SSL certificates (automatic with most providers)
3. Update the mobile app to use the new API URL

### 8. Monitoring and Maintenance

1. Set up logging with a service like Papertrail or Logtail
2. Configure monitoring for uptime and performance
3. Implement regular database backups
4. Create a maintenance plan for updates

## Frontend Integration

Update the mobile app to use your new production API:

1. Edit `/services/contentService.ts`:
```typescript
const API_URL = 'https://api.taptrap.app/api';
```

2. Build and publish updated mobile app

## Common Production Issues

1. **CORS errors**: Check CORS configuration and allowed origins
2. **Authentication failures**: Verify JWT secret and token handling
3. **Database connection issues**: Check MongoDB URI and network settings
4. **Memory leaks**: Monitor server performance and implement proper error handling

## Support and Help

If you encounter issues during deployment, refer to:
- MongoDB Atlas documentation: https://docs.atlas.mongodb.com/
- Vercel documentation: https://vercel.com/docs
- Express.js production best practices: https://expressjs.com/en/advanced/best-practice-performance.html