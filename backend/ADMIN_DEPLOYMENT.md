# Admin Dashboard Deployment Guide

Now that your API is successfully deployed to Vercel, let's deploy the admin dashboard:

## Steps to Deploy Admin Dashboard

1. **Redeploy with Complete Configuration**

We've restored the full vercel.json configuration that includes both the API and admin dashboard. To redeploy:

```bash
cd /Users/<USER>/Documents/Projects/TapTrap/backend
vercel --prod
```

This will deploy both the API and admin dashboard to production.

2. **Access the Admin Dashboard**

Once deployed, you can access the admin dashboard at:

```
https://taptrap-backend.vercel.app/admin
```

Or with your preview URL:

```
https://taptrap-backend-cbkm6ff6k-eliezers-projects-eceb92de.vercel.app/admin
```

3. **Login Credentials**

Use the admin credentials you've set in your environment variables:
- Email: The value of your ADMIN_EMAIL environment variable
- Password: The value of your ADMIN_PASSWORD environment variable

**IMPORTANT**: Never use default or weak passwords in production. Make sure you've set strong, unique credentials in your environment variables.

## Troubleshooting Admin Dashboard Deployment

If you encounter issues with the admin dashboard:

1. **Check the Build Logs**

```bash
vercel logs taptrap-backend.vercel.app
```

Look for any errors in the admin panel build process.

2. **Environment Variables**

Confirm the environment variables are correctly set in Vercel:
- Make sure `REACT_APP_API_URL=/api` is in the admin's .env.production file
- All other environment variables should be set in Vercel's dashboard

3. **Path Issues**

If you see 404 errors when accessing the admin dashboard:
- Check the routes configuration in vercel.json
- Ensure the build directory is set correctly to "admin/build"

4. **API Connection Issues**

If the admin dashboard loads but can't connect to the API:
- Open the browser console for error messages
- Try a test API endpoint like /api/health in the browser directly

## Updating the Admin Dashboard

When you make changes to the admin dashboard:

1. Test locally first:
```bash
cd /Users/<USER>/Documents/Projects/TapTrap/backend/admin
npm start
```

2. Commit your changes and redeploy:
```bash
cd /Users/<USER>/Documents/Projects/TapTrap/backend
vercel --prod
```