{"name": "taptrap-backend", "version": "1.0.0", "description": "Backend API for TapTrap to replace Airtable", "main": "src/index.js", "engines": {"node": ">=18.x"}, "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "seed:admin": "node src/utils/seedDatabase.js", "import:airtable": "node src/utils/importAirtableData.js", "migrate:penalties": "node scripts/migrate-penalties.js", "test": "echo \"Error: no test specified\" && exit 1", "build": "echo \"Backend build complete\"", "copy-env": "node copy-env.js"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongodb-memory-server": "^8.16.1", "mongoose": "^7.8.7", "morgan": "^1.10.0", "openai": "^4.67.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}