# TapTrap Backend Deployment Guide

This guide outlines the steps to deploy the TapTrap backend and admin dashboard.

## Deployment Options

### Option 1: Vercel (Recommended)

Vercel is ideal for this type of project as it provides easy deployment for both the API and admin dashboard.

1. **Prepare backend for deployment**

   Create a `vercel.json` file in the backend directory:

   ```json
   {
     "version": 2,
     "builds": [
       {
         "src": "src/index.js",
         "use": "@vercel/node"
       },
       {
         "src": "admin/package.json",
         "use": "@vercel/static-build",
         "config": { "distDir": "build" }
       }
     ],
     "routes": [
       {
         "src": "/api/(.*)",
         "dest": "src/index.js"
       },
       {
         "src": "/admin",
         "dest": "admin/build/index.html"
       },
       {
         "src": "/admin/(.*)",
         "dest": "admin/build/index.html"
       },
       {
         "src": "/(.*)",
         "dest": "admin/build/$1"
       }
     ]
   }
   ```

2. **Set up environment variables in Vercel**

   - MONGODB_URI (MongoDB connection string)
   - JWT_SECRET (Secret for JWT tokens)
   - ADMIN_EMAIL
   - ADMIN_PASSWORD

3. **Deploy to Vercel**

   ```bash
   npm install -g vercel
   vercel login
   cd backend
   vercel
   ```

4. **Configure custom domain**

   - Add your domain (taptrap.app) in the Vercel dashboard
   - Update DNS settings with your domain provider

### Option 2: MongoDB Atlas + Render

1. **Create MongoDB Atlas cluster**

   - Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
   - Create a free cluster
   - Configure network access to allow connections from anywhere
   - Create a database user
   - Get your connection string

2. **Deploy API on Render**

   - Create a new Web Service
   - Connect your GitHub repository
   - Set the build command: `cd backend && npm install`
   - Set the start command: `cd backend && npm start`
   - Add environment variables (MONGODB_URI, JWT_SECRET, etc.)

3. **Deploy admin dashboard on Render**

   - Create a new Static Site
   - Connect your GitHub repository
   - Set the build command: `cd backend/admin && npm install && npm run build`
   - Set the publish directory: `backend/admin/build`
   - Add environment variable: `REACT_APP_API_URL=https://your-api-url.render.com/api`

4. **Configure custom domain**

   - Add your domain in the Render dashboard
   - Update DNS settings with your domain provider

## Importing Content from Airtable

After deploying your backend, you need to import your existing Airtable data:

1. **Export Airtable data**

   - Go to your Airtable base
   - View the GameContent table
   - Click on "Download CSV"
   - Convert CSV to JSON format

2. **Prepare the import file**

   Place the JSON file in the `backend/data` directory as `airtableExport.json`

3. **Run the import script**

   ```bash
   cd backend
   npm run import:airtable
   ```

## Creating the Admin User

After deploying:

1. **Option 1: Using the seed script locally**

   ```bash
   cd backend
   npm run seed:admin
   ```

2. **Option 2: Create directly in MongoDB Atlas**

   - Connect to your MongoDB Atlas cluster
   - Insert an admin user document with hashed password

## Updating the Mobile App

Update the API URL in the mobile app's `services/contentService.ts` file to point to your new backend:

```typescript
const API_URL = 'https://api.taptrap.app/api';
```

Then build and deploy the updated mobile app.

## Testing the Deployment

1. Verify the API is working:
   - Access `https://api.taptrap.app/api/health`
   - Should return `{"status":"ok"}`

2. Access the admin dashboard:
   - Go to `https://taptrap.app/admin`
   - Log in with your admin credentials

3. Test the mobile app:
   - Ensure it can fetch content from the new backend
   - Test all functionalities to ensure they work as expected