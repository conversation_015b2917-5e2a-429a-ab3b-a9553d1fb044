# MongoDB Implementation for TapTrap

This document describes the MongoDB implementation for the TapTrap application, including the database service, entities, and DTOs.

## Table of Contents

1. [Overview](#overview)
2. [Database Configuration](#database-configuration)
3. [Database Service](#database-service)
4. [Entity Models](#entity-models)
5. [Data Transfer Objects (DTOs)](#data-transfer-objects-dtos)
6. [Services](#services)
7. [Usage Examples](#usage-examples)

## Overview

The TapTrap application uses MongoDB as its primary database, with <PERSON><PERSON><PERSON> as the ODM (Object Document Mapper). The implementation follows a service-oriented architecture with the following components:

- **Database Configuration**: Centralizes MongoDB connection settings
- **Database Service**: Handles MongoDB connection and provides database operations
- **Entity Models**: Define the structure of the data stored in MongoDB
- **DTOs (Data Transfer Objects)**: Validate and transform data between the API and the database
- **Services**: Provide business logic for interacting with the database

## Database Configuration

The database configuration is centralized in `backend/src/config/database.config.js`. This file provides different connection options based on the environment:

```javascript
// Default MongoDB connection options optimized for performance
const defaultMongooseOptions = {
  serverSelectionTimeoutMS: 15000, // Timeout for server selection
  socketTimeoutMS: 30000,         // Timeout for socket operations
  connectTimeoutMS: 30000,        // Timeout for initial connection
  dbName: 'test'                  // Database name to match MongoDB Atlas
};

// Options specifically for serverless environments (like Vercel)
const serverlessMongooseOptions = {
  ...defaultMongooseOptions,
  maxPoolSize: 5,                 // Reduce pool size for serverless
  minPoolSize: 1,                 // Keep at least one connection
  maxIdleTimeMS: 10000,           // Close idle connections quicker
};
```

## Database Service

The database service (`backend/src/services/database.service.js`) provides methods for connecting to and interacting with MongoDB:

```javascript
class DatabaseService {
  constructor() {
    this.mongoServer = null;
    this.isConnected = false;
    this.connectionString = process.env.MONGODB_URI;
  }

  async connect(options = {}) {
    // Connect to MongoDB with appropriate options
  }

  async disconnect() {
    // Disconnect from MongoDB
  }

  getConnectionStatus() {
    // Get current connection status
  }

  getConnection() {
    // Get the mongoose connection
  }
}
```

## Entity Models

Entity models define the structure of the data stored in MongoDB. All models extend a base entity that provides common functionality:

### Base Entity

The base entity (`backend/src/models/base.entity.js`) provides common fields and functionality for all entities:

```javascript
const createBaseSchema = (definition, options = {}) => {
  // Add common fields to all schemas
  const baseDefinition = {
    ...definition,
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  };

  // Create schema with provided options
  const schema = new mongoose.Schema(baseDefinition, options);

  // Add pre-save hook to update the updatedAt field
  schema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
  });

  return schema;
};
```

### Content Entity

The Content entity (`backend/src/models/content.js`) defines the structure of game content:

```javascript
const contentSchemaDefinition = {
  id: {
    type: String,
    required: true,
    unique: true
  },
  text_en: {
    type: String,
    required: true
  },
  text_es: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'casual_questions', 'mild_questions', 'spicy_questions', 'no_limits_questions',
      'casual_dares', 'mild_dares', 'spicy_dares', 'no_limits_dares'
    ]
  },
  gameMode: {
    type: String,
    required: true,
    enum: ['questions', 'dares']
  },
  active: {
    type: Boolean,
    default: true
  }
};
```

### User Entity

The User entity (`backend/src/models/user.js`) defines the structure of user data:

```javascript
const userSchemaDefinition = {
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['admin'],
    default: 'admin'
  }
};
```

## Data Transfer Objects (DTOs)

DTOs validate and transform data between the API and the database:

### Base DTO

The base DTO (`backend/src/dtos/base.dto.js`) provides common validation and transformation methods:

```javascript
class BaseDTO {
  static validateRequired(data, requiredFields) {
    // Validate required fields
  }

  static toEntity(data) {
    // Transform data from API to entity format
  }

  static fromEntity(entity) {
    // Transform entity to API format
  }
}
```

### Content DTO

The Content DTO (`backend/src/dtos/content.dto.js`) validates and transforms content data:

```javascript
class ContentDTO extends BaseDTO {
  static validate(data) {
    // Validate content data
  }

  static toEntity(data) {
    // Transform content data from API to entity format
  }

  static fromEntity(entity) {
    // Transform content entity to API format
  }
}
```

### User DTO

The User DTO (`backend/src/dtos/user.dto.js`) validates and transforms user data:

```javascript
class UserDTO extends BaseDTO {
  static validate(data) {
    // Validate user data
  }

  static toEntity(data) {
    // Transform user data from API to entity format
  }

  static fromEntity(entity) {
    // Transform user entity to API format (without sensitive data)
  }
}
```

## Services

Services provide business logic for interacting with the database:

### Content Service

The Content service (`backend/src/services/content.service.js`) provides methods for managing content:

```javascript
class ContentService {
  async getAllContent(filter = {}) {
    // Get all content items
  }

  async getContentById(id) {
    // Get content by ID
  }

  async createContent(contentData) {
    // Create new content
  }

  async updateContent(id, contentData) {
    // Update content
  }

  async deleteContent(id) {
    // Delete content
  }

  async getFormattedContent() {
    // Get content formatted for the app
  }
}
```

### User Service

The User service (`backend/src/services/user.service.js`) provides methods for managing users:

```javascript
class UserService {
  async createUser(userData) {
    // Create a new user
  }

  async authenticateUser(email, password) {
    // Authenticate user and generate JWT token
  }

  async getUserById(id) {
    // Get user by ID
  }

  async updateUser(id, userData) {
    // Update user
  }
}
```

## Usage Examples

### Connecting to MongoDB

```javascript
const databaseService = require('./services/database.service');

// Connect to MongoDB
await databaseService.connect();
console.log('Database connection established');
```

### Creating Content

```javascript
const contentService = require('./services/content.service');

// Create new content
const newContent = await contentService.createContent({
  text_en: "What's your favorite color?",
  text_es: "¿Cuál es tu color favorito?",
  category: "casual_questions",
  gameMode: "questions",
  active: true
});

console.log('Created content:', newContent);
```

### Authenticating a User

```javascript
const userService = require('./services/user.service');

// Authenticate user
const authResult = await userService.authenticateUser('<EMAIL>', 'password123');
console.log('Authentication result:', authResult);
```
