# Encriptación de Respuestas en la API de TapTrap

Este documento describe la implementación de encriptación de respuestas en la API de TapTrap, incluyendo la configuración, uso y ejemplos de integración en el cliente.

## Tabla de Contenidos

1. [Visión General](#visión-general)
2. [Configuración](#configuración)
3. [Endpoints con Encriptación](#endpoints-con-encriptación)
4. [Formato de Respuesta](#formato-de-respuesta)
5. [Implementación en el Cliente](#implementación-en-el-cliente)
6. [Consideraciones de Seguridad](#consideraciones-de-seguridad)
7. [Solución de Problemas](#solución-de-problemas)

## Visión General

La API de TapTrap implementa encriptación AES para proteger datos sensibles en las respuestas de ciertos endpoints. Cuando la encriptación está habilitada, los datos se encriptan en el servidor antes de enviarse al cliente, y deben ser desencriptados en el cliente utilizando la misma clave.

La encriptación se implementa utilizando la biblioteca `crypto-js` y se activa mediante la variable de entorno `ENCRYPTION_KEY`.

## Configuración

### Variables de Entorno

Para habilitar la encriptación, se debe configurar la siguiente variable de entorno:

```
ENCRYPTION_KEY=tu-clave-secreta-de-encriptacion
```

Esta clave debe ser compartida de forma segura con los clientes que necesiten desencriptar los datos.

### Instalación de Dependencias

La encriptación utiliza la biblioteca `crypto-js`:

```bash
npm install crypto-js
```

## Endpoints con Encriptación

Actualmente, los siguientes endpoints implementan encriptación:

| Endpoint | Método | Descripción |
|----------|--------|-------------|
| `/api/content` | GET | Obtiene todo el contenido del juego formateado para la app móvil |

## Formato de Respuesta

### Respuesta Sin Encriptación

Cuando la encriptación está deshabilitada (ENCRYPTION_KEY no está configurada), la respuesta tiene el formato normal:

```json
{
  "questions": {
    "casual_questions": [
      {
        "id": "q1",
        "text_en": "What's your favorite color?",
        "text_es": "¿Cuál es tu color favorito?"
      },
      // Más preguntas...
    ],
    // Más categorías...
  },
  "dares": {
    // Categorías de retos...
  }
}
```

### Respuesta Con Encriptación

Cuando la encriptación está habilitada, la respuesta tiene el siguiente formato:

```json
{
  "encrypted": true,
  "data": "U2FsdGVkX1+A2KKGrSWUVXDFRNrTsXYeT9Lx3VQ6jWYn9..."
}
```

El campo `encrypted` indica que los datos están encriptados, y el campo `data` contiene los datos encriptados como una cadena.

## Implementación en el Cliente

### Ejemplo en JavaScript/TypeScript

```javascript
import CryptoJS from 'crypto-js';

// Función para desencriptar datos
const decryptData = (encryptedData, secretKey) => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
  const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
  return JSON.parse(decryptedString);
};

// Ejemplo de uso
const fetchGameContent = async () => {
  const response = await fetch('/api/content');
  const data = await response.json();
  
  if (data.encrypted) {
    // Los datos están encriptados, desencriptar
    const decryptedData = decryptData(data.data, 'tu-clave-de-encriptacion');
    return decryptedData;
  } else {
    // Los datos no están encriptados
    return data;
  }
};
```

### Ejemplo en React Native

```javascript
import CryptoJS from 'crypto-js';
import { ENCRYPTION_KEY } from '@env'; // Usando react-native-dotenv

// Función para desencriptar datos
const decryptData = (encryptedData, secretKey) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedString);
  } catch (error) {
    console.error('Error al desencriptar datos:', error);
    throw new Error('No se pudieron desencriptar los datos');
  }
};

// Ejemplo de uso en un servicio
export const fetchGameContent = async () => {
  try {
    const response = await fetch('https://api.taptrap.app/api/content');
    const data = await response.json();
    
    if (data.encrypted) {
      // Los datos están encriptados, desencriptar
      return decryptData(data.data, ENCRYPTION_KEY);
    } else {
      // Los datos no están encriptados
      return data;
    }
  } catch (error) {
    console.error('Error al obtener contenido del juego:', error);
    throw error;
  }
};
```

## Consideraciones de Seguridad

1. **Almacenamiento de Claves**: La clave de encriptación debe almacenarse de forma segura:
   - En el servidor: Como variable de entorno
   - En el cliente móvil: Utilizando almacenamiento seguro como Keychain (iOS) o Keystore (Android)
   - En el cliente web: Considerar no almacenar la clave y solicitarla al usuario

2. **Transmisión de Claves**: Nunca transmitir la clave de encriptación a través de la red sin protección adicional.

3. **Rotación de Claves**: Implementar un sistema para rotar periódicamente la clave de encriptación.

4. **Longitud de Clave**: Utilizar una clave de encriptación suficientemente larga (al menos 32 caracteres).

## Solución de Problemas

### Problemas Comunes

1. **Error "Malformed UTF-8 data"**: Indica que la clave de encriptación utilizada para desencriptar no coincide con la utilizada para encriptar.

2. **Error "Cannot read property 'questions' of undefined"**: Indica que los datos desencriptados no tienen el formato esperado.

3. **La encriptación no se activa**: Verificar que la variable de entorno `ENCRYPTION_KEY` está correctamente configurada y que el servidor se ha reiniciado después de configurarla.

### Verificación de Configuración

Para verificar que la variable de entorno `ENCRYPTION_KEY` está correctamente configurada, ejecutar:

```bash
node check-env.js
```

Este script mostrará si la variable está definida y su longitud.
