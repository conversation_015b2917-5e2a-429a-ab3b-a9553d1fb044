/**
 * Script para verificar las variables de entorno
 * 
 * <PERSON>ste script carga las variables de entorno desde el archivo .env
 * y muestra si la variable ENCRYPTION_KEY está definida.
 */

require('dotenv').config();

console.log('Verificando variables de entorno:');
console.log('- NODE_ENV:', process.env.NODE_ENV || 'no definido');
console.log('- PORT:', process.env.PORT || 'no definido');
console.log('- MONGODB_URI:', process.env.MONGODB_URI ? 'definido (valor oculto)' : 'no definido');
console.log('- JWT_SECRET:', process.env.JWT_SECRET ? 'definido (valor oculto)' : 'no definido');
console.log('- ENCRYPTION_KEY:', process.env.ENCRYPTION_KEY ? 'definido (valor oculto)' : 'no definido');

if (process.env.ENCRYPTION_KEY) {
  console.log('- ENCRYPTION_KEY longitud:', process.env.ENCRYPTION_KEY.length);
} else {
  console.log('ENCRYPTION_KEY no está definida. Asegúrate de que:');
  console.log('1. El archivo .env existe en la raíz del proyecto');
  console.log('2. El archivo .env contiene la línea: ENCRYPTION_KEY=tu-clave-secreta');
  console.log('3. Has reiniciado el servidor después de modificar el archivo .env');
}

console.log('\nVariables de entorno disponibles:', Object.keys(process.env).join(', '));
