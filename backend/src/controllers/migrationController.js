const MigrationService = require('../services/migrationService');

/**
 * Migration Controller
 * Handles admin endpoints for managing migration settings
 */

// Get current migration settings
exports.getMigrationSettings = async (req, res) => {
  try {
    console.log('Fetching current migration settings');
    
    const settings = await MigrationService.getCurrentSettings();
    
    console.log('Migration settings retrieved:', {
      migrationEnabled: settings.migrationEnabled,
      lastModifiedBy: settings.lastModifiedBy,
      migrationEnabledAt: settings.migrationEnabledAt,
      migrationDisabledAt: settings.migrationDisabledAt
    });
    
    res.status(200).json({
      success: true,
      data: {
        migrationEnabled: settings.migrationEnabled,
        migrationMessage: settings.migrationMessage,
        migrationEnabledAt: settings.migrationEnabledAt,
        migrationDisabledAt: settings.migrationDisabledAt,
        lastModifiedBy: settings.lastModifiedBy,
        createdAt: settings.createdAt,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error('Error fetching migration settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch migration settings',
      error: error.message
    });
  }
};

// Toggle migration on/off
exports.toggleMigration = async (req, res) => {
  try {
    const { enabled } = req.body;
    const modifiedBy = req.user?.email || 'admin';
    
    console.log(`Migration toggle request: ${enabled ? 'enable' : 'disable'} by ${modifiedBy}`);
    
    // Validate input
    if (typeof enabled !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'enabled field must be a boolean value'
      });
    }
    
    const settings = await MigrationService.toggleMigration(enabled, modifiedBy);
    
    console.log(`Migration successfully ${enabled ? 'enabled' : 'disabled'}`);
    
    res.status(200).json({
      success: true,
      message: `Migration ${enabled ? 'enabled' : 'disabled'} successfully`,
      data: {
        migrationEnabled: settings.migrationEnabled,
        migrationMessage: settings.migrationMessage,
        migrationEnabledAt: settings.migrationEnabledAt,
        migrationDisabledAt: settings.migrationDisabledAt,
        lastModifiedBy: settings.lastModifiedBy,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error('Error toggling migration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle migration',
      error: error.message
    });
  }
};

// Update migration message
exports.updateMigrationMessage = async (req, res) => {
  try {
    const { migrationMessage } = req.body;
    const modifiedBy = req.user?.email || 'admin';
    
    console.log('Migration message update request by', modifiedBy);
    
    // Validate input
    if (!migrationMessage || typeof migrationMessage !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'migrationMessage must be an object with field_en, field_es, and field_dom'
      });
    }
    
    const { field_en, field_es, field_dom } = migrationMessage;
    
    if (!field_en || !field_es || !field_dom) {
      return res.status(400).json({
        success: false,
        message: 'migrationMessage must contain field_en, field_es, and field_dom'
      });
    }
    
    const settings = await MigrationService.updateMigrationMessage(migrationMessage, modifiedBy);
    
    console.log('Migration message updated successfully');
    
    res.status(200).json({
      success: true,
      message: 'Migration message updated successfully',
      data: {
        migrationEnabled: settings.migrationEnabled,
        migrationMessage: settings.migrationMessage,
        lastModifiedBy: settings.lastModifiedBy,
        updatedAt: settings.updatedAt
      }
    });
  } catch (error) {
    console.error('Error updating migration message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update migration message',
      error: error.message
    });
  }
};

// Get migration status (lightweight endpoint for quick checks)
exports.getMigrationStatus = async (req, res) => {
  try {
    const isEnabled = await MigrationService.isMigrationEnabled();
    
    res.status(200).json({
      success: true,
      data: {
        migrationEnabled: isEnabled
      }
    });
  } catch (error) {
    console.error('Error getting migration status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get migration status',
      error: error.message
    });
  }
};
