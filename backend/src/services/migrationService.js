const MigrationSettings = require('../models/migrationSettings');

/**
 * Migration Service
 * Handles migration settings and provides utility functions for API v1 migration logic
 */

class MigrationService {
  /**
   * Get current migration settings
   * @returns {Promise<Object>} Migration settings object
   */
  static async getCurrentSettings() {
    try {
      const settings = await MigrationSettings.getCurrentSettings();
      return settings;
    } catch (error) {
      console.error('Error getting migration settings:', error);
      // Return default settings if there's an error
      return {
        migrationEnabled: false,
        migrationMessage: {
          field_en: 'Please download the new version',
          field_es: 'Por favor descarga la nueva versión',
          field_dom: 'Por favor descarga la nueva versión'
        }
      };
    }
  }

  /**
   * Check if migration is currently enabled
   * @returns {Promise<boolean>} True if migration is enabled
   */
  static async isMigrationEnabled() {
    try {
      const settings = await this.getCurrentSettings();
      return settings.migrationEnabled;
    } catch (error) {
      console.error('Error checking migration status:', error);
      return false;
    }
  }

  /**
   * Toggle migration on/off
   * @param {boolean} enabled - Whether to enable migration
   * @param {string} modifiedBy - Who made the change
   * @returns {Promise<Object>} Updated settings
   */
  static async toggleMigration(enabled, modifiedBy = 'admin') {
    try {
      const settings = await MigrationSettings.toggleMigration(enabled, modifiedBy);
      console.log(`Migration ${enabled ? 'enabled' : 'disabled'} by ${modifiedBy}`);
      return settings;
    } catch (error) {
      console.error('Error toggling migration:', error);
      throw error;
    }
  }

  /**
   * Get migration message in all languages
   * @returns {Promise<Object>} Migration message object with field_en, field_es, field_dom
   */
  static async getMigrationMessage() {
    try {
      const settings = await this.getCurrentSettings();
      return settings.migrationMessage;
    } catch (error) {
      console.error('Error getting migration message:', error);
      // Return default message if there's an error
      return {
        field_en: 'Please download the new version',
        field_es: 'Por favor descarga la nueva versión',
        field_dom: 'Por favor descarga la nueva versión'
      };
    }
  }

  /**
   * Create empty content structure with migration message
   * Used by API v1 when migration is enabled
   * @returns {Promise<Object>} Empty content structure with migration messages
   */
  static async createMigrationContentResponse() {
    try {
      const message = await this.getMigrationMessage();
      
      // Create a single migration item for each category
      const migrationItem = {
        id: 'migration-message',
        text_en: message.field_en,
        text_es: message.field_es,
        text_dom: message.field_dom
      };

      return {
        questions: {
          casual_questions: [migrationItem],
          mild_questions: [migrationItem],
          spicy_questions: [migrationItem],
          no_limits_questions: [migrationItem],
          couple_questions: [migrationItem]
        },
        dares: {
          casual_dares: [migrationItem],
          mild_dares: [migrationItem],
          spicy_dares: [migrationItem],
          no_limits_dares: [migrationItem],
          couple_dares: [migrationItem]
        }
      };
    } catch (error) {
      console.error('Error creating migration content response:', error);
      throw error;
    }
  }

  /**
   * Create empty penalties structure with migration message
   * Used by API v1 when migration is enabled
   * @returns {Promise<Object>} Empty penalties structure with migration messages
   */
  static async createMigrationPenaltiesResponse() {
    try {
      const message = await this.getMigrationMessage();
      
      // Create a single migration item for each penalty category
      const migrationItem = {
        id: 'migration-message',
        text_en: message.field_en,
        text_es: message.field_es,
        text_dom: message.field_dom,
        isPremium: false,
        isDefaultFree: false,
        isDefaultPremium: false
      };

      return {
        drinking: [migrationItem],
        physical: [migrationItem],
        social: [migrationItem],
        silly: [migrationItem],
        creative: [migrationItem]
      };
    } catch (error) {
      console.error('Error creating migration penalties response:', error);
      throw error;
    }
  }

  /**
   * Update migration message
   * @param {Object} newMessage - New message object with field_en, field_es, field_dom
   * @param {string} modifiedBy - Who made the change
   * @returns {Promise<Object>} Updated settings
   */
  static async updateMigrationMessage(newMessage, modifiedBy = 'admin') {
    try {
      const settings = await MigrationSettings.getCurrentSettings();
      settings.migrationMessage = {
        field_en: newMessage.field_en || settings.migrationMessage.field_en,
        field_es: newMessage.field_es || settings.migrationMessage.field_es,
        field_dom: newMessage.field_dom || settings.migrationMessage.field_dom
      };
      settings.lastModifiedBy = modifiedBy;
      await settings.save();
      
      console.log(`Migration message updated by ${modifiedBy}`);
      return settings;
    } catch (error) {
      console.error('Error updating migration message:', error);
      throw error;
    }
  }
}

module.exports = MigrationService;
