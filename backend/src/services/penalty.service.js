const Penalty = require('../models/penalty');

class PenaltyService {
  /**
   * Get all penalties
   * @returns {Promise<Array>} - Array of penalties
   */
  async getAllPenalties() {
    try {
      return await Penalty.find().sort({ createdAt: -1 });
    } catch (error) {
      console.error('Error fetching all penalties:', error);
      throw error;
    }
  }

  /**
   * Get penalty by ID
   * @param {string} id - Penalty ID
   * @returns {Promise<Object>} - Penalty object
   */
  async getPenaltyById(id) {
    try {
      const penalty = await Penalty.findById(id);
      if (!penalty) {
        throw new Error('Penalty not found');
      }
      return penalty;
    } catch (error) {
      console.error('Error fetching penalty by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new penalty
   * @param {Object} penaltyData - Penalty data
   * @returns {Promise<Object>} - Created penalty
   */
  async createPenalty(penaltyData) {
    try {
      const penalty = new Penalty(penaltyData);
      return await penalty.save();
    } catch (error) {
      console.error('Error creating penalty:', error);
      throw error;
    }
  }

  /**
   * Update penalty by ID
   * @param {string} id - Penalty ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} - Updated penalty
   */
  async updatePenalty(id, updateData) {
    try {
      const penalty = await Penalty.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );
      
      if (!penalty) {
        throw new Error('Penalty not found');
      }
      
      return penalty;
    } catch (error) {
      console.error('Error updating penalty:', error);
      throw error;
    }
  }

  /**
   * Delete penalty by ID
   * @param {string} id - Penalty ID
   * @returns {Promise<Object>} - Deleted penalty
   */
  async deletePenalty(id) {
    try {
      const penalty = await Penalty.findByIdAndDelete(id);
      if (!penalty) {
        throw new Error('Penalty not found');
      }
      return penalty;
    } catch (error) {
      console.error('Error deleting penalty:', error);
      throw error;
    }
  }

  /**
   * Get penalties by category
   * @param {string} category - Penalty category
   * @returns {Promise<Array>} - Array of penalties in category
   */
  async getPenaltiesByCategory(category) {
    try {
      return await Penalty.find({ category, active: true }).sort({ createdAt: -1 });
    } catch (error) {
      console.error('Error fetching penalties by category:', error);
      throw error;
    }
  }

  /**
   * Get penalty statistics
   * @returns {Promise<Object>} - Statistics object
   */
  async getPenaltyStats() {
    try {
      const totalPenalties = await Penalty.countDocuments();
      const activePenalties = await Penalty.countDocuments({ active: true });
      
      const categoryCounts = await Penalty.aggregate([
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]);

      const categoryStats = {};
      categoryCounts.forEach(item => {
        categoryStats[item._id] = item.count;
      });

      return {
        total: totalPenalties,
        active: activePenalties,
        inactive: totalPenalties - activePenalties,
        categories: categoryStats
      };
    } catch (error) {
      console.error('Error fetching penalty statistics:', error);
      throw error;
    }
  }

  /**
   * Get penalties formatted for the app
   * @returns {Promise<Object>} - Formatted penalties
   */
  async getFormattedPenalties() {
    try {
      // Get all active penalties
      const penalties = await Penalty.find({ active: true });

      // Format penalties for the app
      const formattedPenalties = {
        drinking: [],
        physical: [],
        social: [],
        silly: [],
        creative: []
      };

      // Organize penalties by category
      penalties.forEach(penalty => {
        const { id, text_en, text_es, text_dom, category } = penalty;

        if (formattedPenalties[category]) {
          formattedPenalties[category].push({
            id,
            text_en,
            text_es,
            text_dom
          });
        }
      });

      return formattedPenalties;
    } catch (error) {
      console.error('Error formatting penalties:', error);
      throw error;
    }
  }
}

module.exports = new PenaltyService();
