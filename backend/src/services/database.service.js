/**
 * Database Service
 * Handles MongoDB connection and provides methods for database operations
 */

const mongoose = require('mongoose');
const { getMongooseOptions } = require('../config/database.config');

class DatabaseService {
  constructor() {
    this.isConnected = false;
    this.connectionString = process.env.MONGODB_URI;
  }

  /**
   * Initialize the database connection
   * @param {Object} options - Additional connection options
   * @returns {Promise<boolean>} - Connection status
   */
  async connect(options = {}) {
    try {
      const environment = process.env.NODE_ENV || 'development';
      const isServerless = !!process.env.VERCEL;
      const mongooseOptions = getMongooseOptions(environment, isServerless);

      // Merge any additional options
      const connectionOptions = { ...mongooseOptions, ...options };

      // Always use the MongoDB URI from environment variable
      console.log('Using persistent MongoDB database from MONGODB_URI');

      // Validate the connection string
      if (!this.connectionString) {
        console.error('\nERROR: MONGODB_URI environment variable is not set or empty!');
        console.error('Please make sure you have a .env file with MONGODB_URI defined.');
        console.error('Expected format: mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>');
        throw new Error('MongoDB URI is not provided. Please set the MONGODB_URI environment variable.');
      }

      // Don't log the full URI for security reasons
      console.log('MongoDB URI format:',
        this.connectionString.startsWith('mongodb+srv') ? 'Valid Atlas URI' : 'Not Atlas URI format'
      );
      console.log('MongoDB URI prefix:', this.connectionString.substring(0, this.connectionString.indexOf('@') + 1) + '...');

      // Connect to MongoDB
      await mongoose.connect(this.connectionString, connectionOptions);

      this.isConnected = true;
      console.log('Connected to MongoDB successfully');

      return true;
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Disconnect from the database
   * @returns {Promise<boolean>} - Disconnection status
   */
  async disconnect() {
    try {
      if (mongoose.connection.readyState !== 0) {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
      }

      this.isConnected = false;
      return true;
    } catch (error) {
      console.error('Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Get the current connection status
   * @returns {boolean} - Connection status
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      // 0 = disconnected, 1 = connected, 2 = connecting, 3 = disconnecting
      readyStateText: ['disconnected', 'connected', 'connecting', 'disconnecting'][mongoose.connection.readyState] || 'unknown'
    };
  }

  /**
   * Get the current database instance
   * @returns {Object} - Mongoose connection
   */
  getConnection() {
    return mongoose.connection;
  }
}

// Create a singleton instance
const databaseService = new DatabaseService();

module.exports = databaseService;
