/**
 * Content Service
 * Handles operations related to content entities
 */

const Content = require('../models/content');
const ContentDTO = require('../dtos/content.dto');
const { v4: uuidv4 } = require('uuid');

class ContentService {
  /**
   * Get all content items
   * @param {Object} filter - Filter criteria
   * @returns {Promise<Array>} - Array of content items
   */
  async getAllContent(filter = {}) {
    try {
      const contents = await Content.find(filter);
      return contents.map(content => ContentDTO.fromEntity(content));
    } catch (error) {
      console.error('Error getting all content:', error);
      throw error;
    }
  }

  /**
   * Get content by ID
   * @param {string} id - Content ID
   * @returns {Promise<Object>} - Content item
   */
  async getContentById(id) {
    try {
      const content = await Content.findOne({ id });
      if (!content) {
        throw new Error('Content not found');
      }
      return ContentDTO.fromEntity(content);
    } catch (error) {
      console.error(`Error getting content with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create new content
   * @param {Object} contentData - Content data
   * @returns {Promise<Object>} - Created content
   */
  async createContent(contentData) {
    try {
      // Validate content data
      const validation = ContentDTO.validate(contentData);
      if (!validation.isValid) {
        throw new Error(`Invalid content data: ${JSON.stringify(validation.errors)}`);
      }

      // Transform to entity format
      const contentEntity = ContentDTO.toEntity({
        ...contentData,
        id: contentData.id || uuidv4()
      });

      // Create and save new content
      const newContent = new Content(contentEntity);
      await newContent.save();

      return ContentDTO.fromEntity(newContent);
    } catch (error) {
      console.error('Error creating content:', error);
      throw error;
    }
  }

  /**
   * Update content
   * @param {string} id - Content ID
   * @param {Object} contentData - Updated content data
   * @returns {Promise<Object>} - Updated content
   */
  async updateContent(id, contentData) {
    try {
      // Find content by ID
      const existingContent = await Content.findOne({ id });
      if (!existingContent) {
        throw new Error('Content not found');
      }

      // Validate content data
      const validation = ContentDTO.validate({
        ...ContentDTO.fromEntity(existingContent),
        ...contentData
      });

      if (!validation.isValid) {
        throw new Error(`Invalid content data: ${JSON.stringify(validation.errors)}`);
      }

      // Update content fields
      Object.keys(contentData).forEach(key => {
        if (key !== 'id' && key !== 'createdAt') {
          existingContent[key] = contentData[key];
        }
      });

      // Save updated content
      await existingContent.save();

      return ContentDTO.fromEntity(existingContent);
    } catch (error) {
      console.error(`Error updating content with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete content
   * @param {string} id - Content ID
   * @returns {Promise<boolean>} - Deletion result
   */
  async deleteContent(id) {
    try {
      const result = await Content.deleteOne({ id });
      if (result.deletedCount === 0) {
        throw new Error('Content not found');
      }
      return true;
    } catch (error) {
      console.error(`Error deleting content with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get content formatted for the app
   * @returns {Promise<Object>} - Formatted content
   */
  async getFormattedContent() {
    try {
      // Get all active content
      const contents = await Content.find({ active: true });

      // Format content for the app
      const formattedContent = {
        questions: {
          casual_questions: [],
          mild_questions: [],
          spicy_questions: [],
          no_limits_questions: [],
          couple_questions: []
        },
        dares: {
          casual_dares: [],
          mild_dares: [],
          spicy_dares: [],
          no_limits_dares: [],
          couple_dares: []
        }
      };

      // Organize content by gameMode and category
      contents.forEach(content => {
        const { id, text_en, text_es, text_dom, gameMode, category } = content;

        if (formattedContent[gameMode] && formattedContent[gameMode][category]) {
          formattedContent[gameMode][category].push({
            id,
            text_en,
            text_es,
            text_dom
          });
        }
      });

      return formattedContent;
    } catch (error) {
      console.error('Error getting formatted content:', error);
      throw error;
    }
  }
}

// Create a singleton instance
const contentService = new ContentService();

module.exports = contentService;
