/**
 * MongoDB Database Configuration
 * This file centralizes all MongoDB connection settings
 */

// Default MongoDB connection options optimized for performance
const defaultMongooseOptions = {
  serverSelectionTimeoutMS: 15000, // Timeout for server selection
  socketTimeoutMS: 30000,         // Timeout for socket operations
  connectTimeoutMS: 30000,        // Timeout for initial connection
  dbName: 'test'                  // Database name to match MongoDB Atlas
};

// Options specifically for serverless environments (like Vercel)
const serverlessMongooseOptions = {
  ...defaultMongooseOptions,
  maxPoolSize: 5,                 // Reduce pool size for serverless
  minPoolSize: 1,                 // Keep at least one connection
  maxIdleTimeMS: 10000,           // Close idle connections quicker
};

// Options for development environment
const developmentMongooseOptions = {
  ...defaultMongooseOptions,
  // Add any development-specific options here
};

// Options for production environment
const productionMongooseOptions = {
  ...defaultMongooseOptions,
  // Add any production-specific options here
};

/**
 * Get MongoDB connection options based on environment
 * @param {string} environment - The current environment (development, production, etc.)
 * @param {boolean} isServerless - Whether the app is running in a serverless environment
 * @returns {Object} MongoDB connection options
 */
const getMongooseOptions = (environment = 'development', isServerless = false) => {
  if (isServerless) {
    return serverlessMongooseOptions;
  }

  switch (environment) {
    case 'production':
      return productionMongooseOptions;
    case 'development':
    default:
      return developmentMongooseOptions;
  }
};

module.exports = {
  getMongooseOptions,
  defaultMongooseOptions,
  serverlessMongooseOptions,
  developmentMongooseOptions,
  productionMongooseOptions
};
