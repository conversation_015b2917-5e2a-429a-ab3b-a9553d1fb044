/**
 * Content DTO
 * Data Transfer Object for Content entity
 */

const { v4: uuidv4 } = require('uuid');
const BaseDTO = require('./base.dto');

class ContentDTO extends BaseDTO {
  /**
   * Validate content data
   * @param {Object} data - Content data to validate
   * @returns {Object} - Validation result with isValid and errors
   */
  static validate(data) {
    // Check required fields
    const requiredFields = ['text_en', 'text_es', 'text_dom', 'category', 'gameMode'];
    const requiredValidation = this.validateRequired(data, requiredFields);

    if (!requiredValidation.isValid) {
      return requiredValidation;
    }

    // Additional validations
    const errors = {};
    let isValid = true;

    // Validate category
    const validCategories = [
      'casual_questions', 'mild_questions', 'spicy_questions', 'no_limits_questions', 'couple_questions',
      'casual_dares', 'mild_dares', 'spicy_dares', 'no_limits_dares', 'couple_dares'
    ];

    if (!validCategories.includes(data.category)) {
      errors.category = `Category must be one of: ${validCategories.join(', ')}`;
      isValid = false;
    }

    // Validate gameMode
    const validGameModes = ['questions', 'dares'];
    if (!validGameModes.includes(data.gameMode)) {
      errors.gameMode = `Game mode must be one of: ${validGameModes.join(', ')}`;
      isValid = false;
    }

    return { isValid, errors };
  }

  /**
   * Transform content data from API to entity format
   * @param {Object} data - Content data from API
   * @returns {Object} - Content entity
   */
  static toEntity(data) {
    return {
      id: data.id || uuidv4(),
      text_en: data.text_en,
      text_es: data.text_es,
      text_dom: data.text_dom,
      category: data.category,
      gameMode: data.gameMode,
      active: data.active === false ? false : true,
      // createdAt and updatedAt are handled by the model
    };
  }

  /**
   * Transform content entity to API format
   * @param {Object} entity - Content entity
   * @returns {Object} - Content data for API
   */
  static fromEntity(entity) {
    // If entity is a Mongoose document, convert to plain object
    const plainEntity = entity.toObject ? entity.toObject() : entity;

    return {
      id: plainEntity.id,
      text_en: plainEntity.text_en,
      text_es: plainEntity.text_es,
      text_dom: plainEntity.text_dom,
      category: plainEntity.category,
      gameMode: plainEntity.gameMode,
      active: plainEntity.active,
      createdAt: plainEntity.createdAt,
      updatedAt: plainEntity.updatedAt
    };
  }
}

module.exports = ContentDTO;
