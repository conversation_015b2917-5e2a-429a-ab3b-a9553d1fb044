/**
 * Base DTO
 * Provides common functionality for all DTOs
 */

/**
 * Base DTO class with common validation and transformation methods
 */
class BaseDTO {
  /**
   * Validate that required fields are present
   * @param {Object} data - Data to validate
   * @param {Array<string>} requiredFields - List of required field names
   * @returns {Object} - Validation result with isValid and errors
   */
  static validateRequired(data, requiredFields) {
    const errors = {};
    let isValid = true;

    requiredFields.forEach(field => {
      if (data[field] === undefined || data[field] === null || data[field] === '') {
        errors[field] = `${field} is required`;
        isValid = false;
      }
    });

    return { isValid, errors };
  }

  /**
   * Transform data from API format to database format
   * @param {Object} data - Data to transform
   * @returns {Object} - Transformed data
   */
  static toEntity(data) {
    // Base implementation just returns the data
    // Override in subclasses for specific transformations
    return { ...data };
  }

  /**
   * Transform data from database format to API format
   * @param {Object} entity - Entity to transform
   * @returns {Object} - Transformed data
   */
  static fromEntity(entity) {
    // Base implementation just returns the entity
    // Override in subclasses for specific transformations
    return { ...entity };
  }
}

module.exports = BaseDTO;
