const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'TapTrap API',
      version: '2.0.0',
      description: 'API documentation for TapTrap game backend. Includes both API v1 (legacy) and API v2 (current) endpoints.',
      contact: {
        name: 'TapTrap Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: '/api',
        description: 'API v1 (Legacy)'
      },
      {
        url: '/api/v2',
        description: 'API v2 (Current)'
      },
      {
        url: 'http://localhost:5002/api',
        description: 'Development server v1'
      },
      {
        url: 'http://localhost:5002/api/v2',
        description: 'Development server v2'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT Authorization header using the Bearer scheme. Example: "Authorization: Bearer {token}"'
        },
        apiKeyAuth: {
          type: 'apiKey',
          in: 'header',
          name: 'x-auth-token',
          description: 'API key in x-auth-token header. Example: "x-auth-token: {token}"'
        }
      },
      schemas: {
        Content: {
          type: 'object',
          required: ['id', 'text_en', 'text_es', 'text_dom', 'category', 'gameMode'],
          properties: {
            id: {
              type: 'string',
              description: 'Unique identifier for the content'
            },
            text_en: {
              type: 'string',
              description: 'Content text in English'
            },
            text_es: {
              type: 'string',
              description: 'Content text in Spanish'
            },
            text_dom: {
              type: 'string',
              description: 'Content text in Dominican Spanish'
            },
            category: {
              type: 'string',
              enum: [
                'casual_questions', 'mild_questions', 'spicy_questions', 'no_limits_questions',
                'casual_dares', 'mild_dares', 'spicy_dares', 'no_limits_dares'
              ],
              description: 'Content category'
            },
            gameMode: {
              type: 'string',
              enum: ['questions', 'dares'],
              description: 'Game mode'
            },
            active: {
              type: 'boolean',
              description: 'Whether the content is active'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update timestamp'
            }
          }
        },
        User: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: {
              type: 'string',
              format: 'email',
              description: 'User email'
            },
            password: {
              type: 'string',
              format: 'password',
              description: 'User password'
            },
            role: {
              type: 'string',
              enum: ['admin'],
              description: 'User role'
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'Error message'
            }
          }
        },
        Penalty: {
          type: 'object',
          required: ['id', 'text_en', 'text_es', 'text_dom', 'category'],
          properties: {
            id: {
              type: 'string',
              description: 'Unique identifier for the penalty'
            },
            text_en: {
              type: 'string',
              description: 'Penalty text in English'
            },
            text_es: {
              type: 'string',
              description: 'Penalty text in Spanish'
            },
            text_dom: {
              type: 'string',
              description: 'Penalty text in Dominican Spanish'
            },
            category: {
              type: 'string',
              enum: ['drinking', 'physical', 'social', 'silly', 'creative'],
              description: 'Penalty category'
            },
            active: {
              type: 'boolean',
              description: 'Whether the penalty is active'
            },
            isPremium: {
              type: 'boolean',
              description: 'Whether the penalty is premium-only'
            },
            isDefaultFree: {
              type: 'boolean',
              description: 'Whether the penalty is default for free users'
            },
            isDefaultPremium: {
              type: 'boolean',
              description: 'Whether the penalty is default for premium users'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update timestamp'
            }
          }
        }
      }
    }
  },
  apis: [
    './src/routes/*.js',
    './src/controllers/*.js',
    './src/v2/routes/*.js',
    './src/v2/controllers/*.js'
  ]
};

const specs = swaggerJsdoc(options);

module.exports = specs;
