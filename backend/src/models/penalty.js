const mongoose = require('mongoose');
const { createBaseSchema } = require('./base.entity');

// Define the penalty schema definition
const penaltySchemaDefinition = {
  id: {
    type: String,
    required: true,
    unique: true
  },
  text_en: {
    type: String,
    required: true
  },
  text_es: {
    type: String,
    required: true
  },
  text_dom: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'drinking', 'physical', 'social', 'silly', 'creative'
    ]
  },
  active: {
    type: Boolean,
    default: true
  },
  isPremium: {
    type: Boolean,
    default: false
  },
  isDefaultFree: {
    type: Boolean,
    default: false
  },
  isDefaultPremium: {
    type: Boolean,
    default: false
  }
};

// Create the penalty schema using the base schema
const penaltySchema = createBaseSchema(penaltySchemaDefinition);

// Create and export the Penalty model
const Penalty = mongoose.model('Penalty', penaltySchema);

module.exports = Penalty;
