const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { createBaseSchema } = require('./base.entity');

// Define the user schema definition
const userSchemaDefinition = {
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['admin'],
    default: 'admin'
  }
};

// Create the schema using the base schema creator
const userSchema = createBaseSchema(userSchemaDefinition);

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function(candidatePassword) {
  console.log('Comparing passwords...');

  // Only log password info in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('DB Password (hashed):', this.password);
    console.log('Candidate Password length:', candidatePassword.length);
  }

  // For development bypass, use environment variable
  if (process.env.NODE_ENV === 'development' && process.env.BYPASS_AUTH === 'true') {
    console.log('Development mode with BYPASS_AUTH: Bypassing password check');
    return true;
  }

  // Normal password comparison
  const isMatch = await bcrypt.compare(candidatePassword, this.password);

  if (process.env.NODE_ENV === 'development') {
    console.log('Password match result:', isMatch);
  }

  return isMatch;
};

// Explicitly specify the collection name as 'users' to match MongoDB Atlas collection
const User = mongoose.model('User', userSchema, 'users');

module.exports = User;