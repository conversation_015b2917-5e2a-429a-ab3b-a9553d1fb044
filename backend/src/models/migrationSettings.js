const mongoose = require('mongoose');
const { createBaseSchema } = require('./base.entity');

// Define the migration settings schema definition
const migrationSettingsSchemaDefinition = {
  // Unique identifier for the settings document
  settingsId: {
    type: String,
    required: true,
    unique: true,
    default: 'default'
  },
  // Main migration toggle - when enabled, API v1 returns migration messages
  migrationEnabled: {
    type: Boolean,
    required: true,
    default: false
  },
  // Migration message for content endpoints (localized)
  migrationMessage: {
    field_en: {
      type: String,
      required: true,
      default: 'Please download the new version'
    },
    field_es: {
      type: String,
      required: true,
      default: 'Por favor descarga la nueva versión'
    },
    field_dom: {
      type: String,
      required: true,
      default: 'Por favor descarga la nueva versión'
    }
  },
  // Optional: Track when migration was enabled/disabled
  migrationEnabledAt: {
    type: Date,
    default: null
  },
  migrationDisabledAt: {
    type: Date,
    default: null
  },
  // Optional: Admin who made the change
  lastModifiedBy: {
    type: String,
    default: 'system'
  }
};

// Create the schema using the base entity pattern
const migrationSettingsSchema = createBaseSchema(migrationSettingsSchemaDefinition, {
  collection: 'migrationSettings'
});

// Add pre-save hook to track when migration status changes
migrationSettingsSchema.pre('save', function(next) {
  if (this.isModified('migrationEnabled')) {
    if (this.migrationEnabled) {
      this.migrationEnabledAt = new Date();
      this.migrationDisabledAt = null;
    } else {
      this.migrationDisabledAt = new Date();
      this.migrationEnabledAt = null;
    }
  }
  next();
});

// Static method to get current migration settings
migrationSettingsSchema.statics.getCurrentSettings = async function() {
  let settings = await this.findOne({ settingsId: 'default' });
  
  // Create default settings if none exist
  if (!settings) {
    settings = new this({
      settingsId: 'default',
      migrationEnabled: false
    });
    await settings.save();
  }
  
  return settings;
};

// Static method to toggle migration
migrationSettingsSchema.statics.toggleMigration = async function(enabled, modifiedBy = 'admin') {
  const settings = await this.getCurrentSettings();
  settings.migrationEnabled = enabled;
  settings.lastModifiedBy = modifiedBy;
  await settings.save();
  return settings;
};

// Create and export the model
const MigrationSettings = mongoose.model('MigrationSettings', migrationSettingsSchema);

module.exports = MigrationSettings;
