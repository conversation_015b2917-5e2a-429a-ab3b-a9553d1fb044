const mongoose = require('mongoose');

const PenaltyMetadataSchema = new mongoose.Schema({
  metadataId: {
    type: String,
    required: true,
    unique: true,
    default: 'penalty_metadata'
  },
  lastModified: {
    type: Date,
    default: Date.now
  },
  totalItems: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('PenaltyMetadata', PenaltyMetadataSchema);
