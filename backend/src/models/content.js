const mongoose = require('mongoose');
const { createBaseSchema } = require('./base.entity');

// Define the content schema definition
const contentSchemaDefinition = {
  id: {
    type: String,
    required: true,
    unique: true
  },
  text_en: {
    type: String,
    required: true
  },
  text_es: {
    type: String,
    required: true
  },
  text_dom: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: [
      'casual_questions', 'mild_questions', 'spicy_questions', 'no_limits_questions', 'couple_questions',
      'casual_dares', 'mild_dares', 'spicy_dares', 'no_limits_dares', 'couple_dares'
    ]
  },
  gameMode: {
    type: String,
    required: true,
    enum: ['questions', 'dares']
  },
  active: {
    type: Boolean,
    default: true
  }
};

// Create the schema using the base schema creator
const contentSchema = createBaseSchema(contentSchemaDefinition);

// Explicitly specify the collection name as 'test.content' to match MongoDB Atlas collection
const Content = mongoose.model('Content', contentSchema, 'test.content');

module.exports = Content;