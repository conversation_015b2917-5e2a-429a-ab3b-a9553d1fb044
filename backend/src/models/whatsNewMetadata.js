const mongoose = require('mongoose');

const WhatsNewMetadataSchema = new mongoose.Schema({
  metadataId: {
    type: String,
    required: true,
    unique: true,
    default: 'whatsnew_metadata'
  },
  lastModified: {
    type: Date,
    default: Date.now
  },
  totalItems: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('WhatsNewMetadata', WhatsNewMetadataSchema);
