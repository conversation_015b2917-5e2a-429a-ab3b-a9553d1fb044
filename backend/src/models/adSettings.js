const mongoose = require('mongoose');
const { createBaseSchema } = require('./base.entity');

// Define the ad settings schema definition
const adSettingsSchemaDefinition = {
  // Unique identifier for the settings document
  settingsId: {
    type: String,
    required: true,
    unique: true,
    default: 'default'
  },
  // LEGACY: Controls whether the "watch ads" button is shown in UnlockDialog (for backward compatibility)
  watchAdsButtonEnabled: {
    type: Boolean,
    required: false, // Made optional for backward compatibility
    default: true
  },
  // OS-specific controls for watch ads button
  watchAdsButtonEnabledIOS: {
    type: Boolean,
    required: true,
    default: true
  },
  watchAdsButtonEnabledAndroid: {
    type: Boolean,
    required: true,
    default: true
  },
  // Number of ads users must watch to unlock premium access
  requiredAdsCount: {
    type: Number,
    required: true,
    default: 1,
    min: 1,
    max: 10
  },
  // Number of free premium content items before requiring ads or premium
  freeContentLimit: {
    type: Number,
    required: true,
    default: 6,
    min: 1,
    max: 20
  }
};

// Create the schema using the base entity pattern
const adSettingsSchema = createBaseSchema(adSettingsSchemaDefinition, {
  collection: 'adSettings'
});

// Create and export the model
const AdSettings = mongoose.model('AdSettings', adSettingsSchema);

module.exports = AdSettings;
