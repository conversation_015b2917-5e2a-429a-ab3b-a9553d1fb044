const mongoose = require('mongoose');

const ContentMetadataSchema = new mongoose.Schema({
  metadataId: {
    type: String,
    required: true,
    unique: true,
    default: 'content_metadata'
  },
  lastModified: {
    type: Date,
    default: Date.now
  },
  totalItems: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('ContentMetadata', ContentMetadataSchema);
