const express = require('express');
const router = express.Router();
const penaltyController = require('../controllers/penaltyController');

/**
 * @swagger
 * tags:
 *   name: Penalties
 *   description: Public penalty endpoints
 */

/**
 * @swagger
 * /penalties:
 *   get:
 *     summary: Get all penalty content
 *     tags: [Penalties]
 *     description: Retrieves all active penalty content formatted for the mobile app. May return encrypted data if ENCRYPTION_KEY is set.
 *     responses:
 *       200:
 *         description: A structured object containing all penalty content or encrypted data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 drinking:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: Unique identifier for the penalty
 *                       text_en:
 *                         type: string
 *                         description: Penalty text in English
 *                       text_es:
 *                         type: string
 *                         description: Penalty text in Spanish
 *                       text_dom:
 *                         type: string
 *                         description: Penalty text in Dominican Spanish
 *                 physical:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: Unique identifier for the penalty
 *                       text_en:
 *                         type: string
 *                         description: Penalty text in English
 *                       text_es:
 *                         type: string
 *                         description: Penalty text in Spanish
 *                       text_dom:
 *                         type: string
 *                         description: Penalty text in Dominican Spanish
 *                 social:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: Unique identifier for the penalty
 *                       text_en:
 *                         type: string
 *                         description: Penalty text in English
 *                       text_es:
 *                         type: string
 *                         description: Penalty text in Spanish
 *                       text_dom:
 *                         type: string
 *                         description: Penalty text in Dominican Spanish
 *                 silly:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: Unique identifier for the penalty
 *                       text_en:
 *                         type: string
 *                         description: Penalty text in English
 *                       text_es:
 *                         type: string
 *                         description: Penalty text in Spanish
 *                       text_dom:
 *                         type: string
 *                         description: Penalty text in Dominican Spanish
 *                 creative:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: Unique identifier for the penalty
 *                       text_en:
 *                         type: string
 *                         description: Penalty text in English
 *                       text_es:
 *                         type: string
 *                         description: Penalty text in Spanish
 *                       text_dom:
 *                         type: string
 *                         description: Penalty text in Dominican Spanish
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', penaltyController.getAllPenalties);

module.exports = router;
