const express = require('express');
const router = express.Router();
const ExcludedContentService = require('../services/excludedContentService');
const auth = require('../middleware/auth');

/**
 * @route   POST /api/admin/excluded-content
 * @desc    Add content to exclusion list
 * @access  Private (Admin only)
 */
router.post('/', auth, async (req, res) => {
  try {
    const { contentData, reason } = req.body;

    if (!contentData || !contentData.text_en || !contentData.text_es || !contentData.text_dom) {
      return res.status(400).json({
        success: false,
        message: 'Missing required content data (text_en, text_es, text_dom)'
      });
    }

    if (!contentData.gameMode || !contentData.category) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: gameMode and category'
      });
    }

    // Check if content is already excluded
    const isAlreadyExcluded = await ExcludedContentService.isContentExcluded(
      contentData.gameMode,
      contentData.category,
      contentData.text_es
    );

    if (isAlreadyExcluded) {
      return res.status(409).json({
        success: false,
        message: 'Content is already excluded'
      });
    }

    const excludedContent = await ExcludedContentService.excludeContent(contentData, reason);

    res.status(201).json({
      success: true,
      message: 'Content successfully excluded',
      data: excludedContent
    });

  } catch (error) {
    console.error('Error excluding content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to exclude content',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/admin/excluded-content
 * @desc    Get all excluded content or filter by gameMode/category
 * @access  Private (Admin only)
 */
router.get('/', auth, async (req, res) => {
  try {
    const { gameMode, category } = req.query;

    let excludedContent;
    
    if (gameMode && category) {
      excludedContent = await ExcludedContentService.getExcludedContent(gameMode, category);
    } else {
      excludedContent = await ExcludedContentService.getAllExcludedContent();
    }

    res.json({
      success: true,
      data: excludedContent,
      count: excludedContent.length
    });

  } catch (error) {
    console.error('Error getting excluded content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get excluded content',
      error: error.message
    });
  }
});

/**
 * @route   DELETE /api/admin/excluded-content/:id
 * @desc    Remove content from exclusion list
 * @access  Private (Admin only)
 */
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const removed = await ExcludedContentService.removeExclusion(id);

    if (!removed) {
      return res.status(404).json({
        success: false,
        message: 'Excluded content not found'
      });
    }

    res.json({
      success: true,
      message: 'Content exclusion removed successfully'
    });

  } catch (error) {
    console.error('Error removing exclusion:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove exclusion',
      error: error.message
    });
  }
});

/**
 * @route   DELETE /api/admin/excluded-content
 * @desc    Reset all excluded content (clear entire collection)
 * @access  Private (Admin only)
 */
router.delete('/', auth, async (req, res) => {
  try {
    const result = await ExcludedContentService.resetAllExcludedContent();

    res.json({
      success: true,
      message: result.message,
      deletedCount: result.deletedCount
    });

  } catch (error) {
    console.error('Error resetting excluded content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset excluded content',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/admin/excluded-content/stats
 * @desc    Get excluded content statistics
 * @access  Private (Admin only)
 */
router.get('/stats', auth, async (req, res) => {
  try {
    const stats = await ExcludedContentService.getExclusionStats();

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error getting exclusion stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get exclusion stats',
      error: error.message
    });
  }
});

module.exports = router;
