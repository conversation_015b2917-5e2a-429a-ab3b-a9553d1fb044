const { v4: uuidv4 } = require('uuid');
const Content = require('../models/content');
const User = require('../models/user');
const seedWhatsNewLogs = require('./seedWhatsNew');

/**
 * Seeds initial content to the database for development purposes
 */
async function seedInitialContent() {
  try {
    console.log('Checking if initial content needs to be seeded...');

    // Check if we already have content
    const contentCount = await Content.countDocuments();
    if (contentCount > 0) {
      console.log(`Database already has ${contentCount} content items. Skipping seed.`);
      return;
    }

    console.log('No content found. Seeding initial content...');

    // Create admin user if it doesn't exist and if environment variables are set
    const adminEmail = process.env.ADMIN_EMAIL;
    const adminPassword = process.env.ADMIN_PASSWORD;

    if (!adminEmail || !adminPassword) {
      console.log('Skipping admin user creation: ADMIN_EMAIL and/or ADMIN_PASSWORD environment variables not set');
    } else {
      const adminExists = await User.findOne({ email: adminEmail });
      if (!adminExists) {
        console.log('Creating admin user...');
        const adminUser = new User({
          email: adminEmail,
          password: adminPassword,
          role: 'admin'
        });
        await adminUser.save();
        console.log('Admin user created successfully');
      } else {
        console.log('Admin user already exists, skipping creation');
      }
    }

    // Sample content items - add more as needed
    const sampleContent = [
      {
        id: uuidv4(),
        text_en: "What was your most embarrassing moment?",
        text_es: "¿Cuál fue tu momento más vergonzoso?",
        category: "casual_questions",
        gameMode: "questions",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "What is your favorite movie of all time?",
        text_es: "¿Cuál es tu película favorita de todos los tiempos?",
        category: "casual_questions",
        gameMode: "questions",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "What is your biggest fear?",
        text_es: "¿Cuál es tu mayor miedo?",
        category: "mild_questions",
        gameMode: "questions",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "What is your biggest regret?",
        text_es: "¿Cuál es tu mayor arrepentimiento?",
        category: "spicy_questions",
        gameMode: "questions",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "Describe your wildest fantasy",
        text_es: "Describe tu fantasía más salvaje",
        category: "no_limits_questions",
        gameMode: "questions",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "Sing the chorus of your favorite song",
        text_es: "Canta el coro de tu canción favorita",
        category: "casual_dares",
        gameMode: "dares",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "Do your best animal impression",
        text_es: "Haz tu mejor imitación de un animal",
        category: "casual_dares",
        gameMode: "dares",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "Tell an embarrassing story about yourself",
        text_es: "Cuenta una historia vergonzosa sobre ti",
        category: "mild_dares",
        gameMode: "dares",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "Show the most embarrassing photo on your phone",
        text_es: "Muestra la foto más vergonzosa de tu teléfono",
        category: "spicy_dares",
        gameMode: "dares",
        active: true
      },
      {
        id: uuidv4(),
        text_en: "Take off an item of clothing",
        text_es: "Quítate una prenda de ropa",
        category: "no_limits_dares",
        gameMode: "dares",
        active: true
      }
    ];

    // Insert all content items
    await Content.insertMany(sampleContent);
    console.log(`Successfully seeded ${sampleContent.length} initial content items`);

    // Seed What's New logs
    await seedWhatsNewLogs();

  } catch (error) {
    console.error('Error seeding initial content:', error);
  }
}

module.exports = seedInitialContent;