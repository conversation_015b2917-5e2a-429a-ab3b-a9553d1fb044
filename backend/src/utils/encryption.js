/**
 * Utility for encrypting and decrypting data
 * 
 * This module provides functions to encrypt and decrypt data using AES encryption
 * from the crypto-js library.
 */

const CryptoJS = require('crypto-js');

/**
 * Encrypts data using AES encryption
 * 
 * @param {Object|Array|string} data - The data to encrypt
 * @param {string} secretKey - The secret key for encryption
 * @returns {string} - The encrypted data as a string
 */
const encrypt = (data, secretKey) => {
  try {
    // Convert data to JSON string if it's an object or array
    const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);
    
    // Encrypt the data
    const encrypted = CryptoJS.AES.encrypt(dataString, secretKey).toString();
    
    return encrypted;
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
};

/**
 * Decrypts data that was encrypted with AES
 * 
 * @param {string} encryptedData - The encrypted data string
 * @param {string} secretKey - The secret key for decryption
 * @returns {Object|Array|string} - The decrypted data
 */
const decrypt = (encryptedData, secretKey) => {
  try {
    // Decrypt the data
    const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
    
    // Try to parse as JSON, otherwise return as string
    try {
      return JSON.parse(decryptedString);
    } catch (e) {
      return decryptedString;
    }
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
};

/**
 * Generates a hash for change detection
 *
 * @param {Object|Array|string} data - The data to hash
 * @returns {string} - A hash string for change detection
 */
const generateHash = (data) => {
  try {
    // Convert data to JSON string if it's an object or array
    const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);

    // Generate MD5 hash for change detection (not for security)
    const hash = CryptoJS.MD5(dataString).toString();

    return hash;
  } catch (error) {
    console.error('Hash generation error:', error);
    throw new Error('Failed to generate hash');
  }
};

module.exports = {
  encrypt,
  decrypt,
  generateHash
};
