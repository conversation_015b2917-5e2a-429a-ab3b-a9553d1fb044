require('dotenv').config();
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const User = require('../models/user');

const createAdminUser = async () => {
  let mongoServer;
  
  try {
    // Always use MongoDB Memory Server for local development
    console.log('Using MongoDB Memory Server for development');
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    console.log('MongoDB Memory Server URI:', mongoUri);
    await mongoose.connect(mongoUri);
    
    console.log('Connected to MongoDB');
    
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: process.env.ADMIN_EMAIL });
    
    if (existingAdmin) {
      console.log('Admin user already exists. Skipping creation.');
    } else {
      // Create new admin user
      const adminUser = new User({
        email: process.env.ADMIN_EMAIL,
        password: process.env.ADMIN_PASSWORD,
        role: 'admin'
      });
      
      await adminUser.save();
      console.log('Admin user created successfully.');
    }
    
    // Disconnect from database
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    // Stop MongoDB Memory Server if we created one
    if (mongoServer) {
      await mongoServer.stop();
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seeder
createAdminUser();