# TapTrap Modular Prompt System

## Overview

The TapTrap Modular Prompt System is a sophisticated, maintainable architecture for AI content generation prompts. It separates concerns by organizing prompts into modular components that can be easily maintained, customized, and extended.

## Architecture

### Directory Structure

```
backend/src/prompts/
├── README.md                    # This documentation
├── PromptManager.js            # Central prompt orchestrator
├── test-prompts.js             # Test script for the prompt system
├── base/
│   └── basePrompt.js           # Base prompt template and utilities
└── categories/
    ├── casual_questions.js     # Casual questions prompts
    ├── mild_questions.js       # Mild questions prompts
    ├── spicy_questions.js      # Spicy questions prompts
    ├── no_limits_questions.js  # No limits questions prompts
    ├── casual_dares.js         # Casual dares prompts
    ├── mild_dares.js           # Mild dares prompts
    ├── spicy_dares.js          # Spicy dares prompts
    └── no_limits_dares.js      # No limits dares prompts
```

## Components

### 1. PromptManager.js

The central orchestrator that:
- Loads and manages all category-specific prompts
- Validates prompt configurations
- Builds complete prompts by combining base and category-specific elements
- Provides utilities for content validation and metadata access

**Key Methods:**
- `buildPrompt(gameMode, category, existingContent, options)` - Builds complete prompt
- `validatePromptConfiguration(gameMode, category)` - Validates inputs
- `validateContentForCategory(content, category)` - Validates generated content
- `getAllCategoriesMetadata()` - Gets metadata for all categories

### 2. Base Prompt (base/basePrompt.js)

Contains foundational elements shared across all prompts:
- System message defining AI behavior
- Core requirements for all content
- Multi-language guidelines
- Quality standards
- JSON format specifications
- Utility functions for building prompt sections

### 3. Category-Specific Prompts

Each category file contains:
- **Metadata**: Category information and descriptions
- **Guidelines**: Specific rules for that category
- **Tone and Style**: Requirements for content tone
- **Themes**: Recommended topics and content areas
- **Examples**: Sample content for context
- **Avoid Topics**: Content to avoid
- **Special Considerations**: Category-specific requirements
- **Language Guidelines**: Language-specific instructions
- **Validation**: Content validation logic

## Usage

### Basic Usage

```javascript
const PromptManager = require('./prompts/PromptManager');

// Build a complete prompt
const promptConfig = PromptManager.buildPrompt(
  'questions',           // gameMode
  'casual_questions',    // category
  existingContent,       // array of existing content for context
  {                      // options
    count: 8,
    includeExamples: true,
    useExistingContent: true
  }
);

// Use with OpenAI
const completion = await openai.chat.completions.create({
  model: promptConfig.generationParams.model,
  messages: [
    { role: "system", content: promptConfig.systemMessage },
    { role: "user", content: promptConfig.userPrompt }
  ],
  max_tokens: promptConfig.generationParams.maxTokens,
  temperature: promptConfig.generationParams.temperature
});
```

### Validation

```javascript
// Validate configuration
const validation = PromptManager.validatePromptConfiguration('questions', 'casual_questions');
if (!validation.isValid) {
  console.error('Invalid configuration:', validation.errors);
}

// Validate generated content
const isValid = PromptManager.validateContentForCategory(generatedItem, 'casual_questions');
```

### Getting Information

```javascript
// Get all categories
const categories = PromptManager.getAllCategoriesMetadata();

// Get examples for a category
const examples = PromptManager.getCategoryExamples('casual_questions');

// Get statistics
const stats = PromptManager.getPromptStats();
```

## Category Structure

Each category file follows this structure:

```javascript
const categoryName = {
  metadata: {
    category: "category_name",
    gameMode: "questions|dares",
    difficulty: "casual|mild|spicy|no_limits",
    description: "Human-readable description"
  },

  guidelines: [
    "Specific guideline 1",
    "Specific guideline 2"
  ],

  toneAndStyle: {
    tone: "Description of tone",
    style: "Description of style",
    mood: "Description of mood",
    complexity: "Complexity level",
    length: "Expected length"
  },

  themes: [
    "Theme 1",
    "Theme 2"
  ],

  examples: [
    {
      text_en: "English example",
      text_es: "Spanish example",
      text_dom: "Dominican example"
    }
  ],

  avoidTopics: [
    "Topic to avoid 1",
    "Topic to avoid 2"
  ],

  specialConsiderations: [
    "Special consideration 1",
    "Special consideration 2"
  ],

  languageSpecific: {
    english: { style: "...", tone: "...", considerations: [...] },
    spanish: { style: "...", tone: "...", considerations: [...] },
    dominican: { style: "...", tone: "...", considerations: [...] }
  },

  buildCategoryPrompt: function() {
    // Returns formatted category-specific prompt section
  },

  getExamples: function() {
    // Returns array of examples
  },

  validateContent: function(content) {
    // Returns boolean indicating if content fits category
  }
};
```

## Benefits

### 1. Maintainability
- Each category is self-contained and easy to modify
- Changes to one category don't affect others
- Clear separation of concerns

### 2. Consistency
- Base prompt ensures consistent core requirements
- Standardized structure across all categories
- Unified validation and metadata handling

### 3. Extensibility
- Easy to add new categories
- Simple to modify existing prompts
- Flexible configuration options

### 4. Quality Control
- Category-specific validation ensures appropriate content
- Comprehensive guidelines prevent inappropriate content
- Multi-language support with cultural considerations

### 5. Testing and Debugging
- Individual components can be tested separately
- Clear error messages and validation
- Comprehensive logging and debugging information

## Testing

Run the test script to verify the prompt system:

```bash
node src/prompts/test-prompts.js
```

The test script validates:
- Basic PromptManager functionality
- Configuration validation
- Prompt building for all categories
- Category examples and multi-language support
- Sample prompt generation

## Adding New Categories

To add a new category:

1. Create a new file in `categories/` following the standard structure
2. Add the category to the `categoryPrompts` map in `PromptManager.js`
3. Update the `validCategories` array if needed
4. Run the test script to verify integration
5. Update documentation

## Best Practices

### Prompt Engineering
- Keep guidelines specific and actionable
- Provide clear examples for each category
- Include cultural considerations for multi-language content
- Balance creativity with appropriateness

### Content Validation
- Implement meaningful validation logic
- Consider edge cases and boundary conditions
- Provide helpful error messages
- Log validation failures for debugging

### Maintenance
- Regularly review and update prompts based on AI output quality
- Monitor validation success rates
- Update examples based on successful generations
- Keep documentation current

## Integration with AIContentService

The modular prompt system integrates seamlessly with the existing `AIContentService`:

```javascript
// In aiContentService.js
const promptConfig = this.buildPrompt(gameMode, category, existingContent);
const completion = await this.openai.chat.completions.create({
  model: promptConfig.generationParams.model,
  messages: [
    { role: "system", content: promptConfig.systemMessage },
    { role: "user", content: promptConfig.userPrompt }
  ],
  max_tokens: promptConfig.generationParams.maxTokens,
  temperature: promptConfig.generationParams.temperature
});
```

This ensures backward compatibility while providing enhanced functionality and maintainability.
