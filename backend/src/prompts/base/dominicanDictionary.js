/**
 * Dominican Spanish Dictionary and Language Guidelines
 * This file contains Dominican expressions, slang, and language patterns
 * to be used in AI content generation for authentic Dominican Spanish
 */

const dominicanDictionary = {
  // Core Dominican expressions and their meanings
  expressions: {
    "kelokeeee": "Esto se usa aquí pa' saludarnos entre si le llega (captas)",
    "ta amotinao": "Que está en líos, problemas",
    "ta activa'o": "Que anda de ánimo o anda de fiesta",
    "y cual e' la dema": "Que cual es la envidia",
    "dame banda": "Que lo deje en paz, que no joda",
    "vamo' hace un coro": "Hacer chercha, junte",
    "dame la lu'": "Que le ponga la cosa clara",
    "ta desacatoo": "Dis challenge a to'",
    "mira que bélico": "Que feo",
    "eso no ta'": "Que no está de acuerdo o que no va",
    "el juidero": "Correr, huir",
    "se armó, se armó": "Hay problemas, líos",
    "hame el coro": "Que le ponga atención o que hagas lo que él hace",
    "la macate, lo dañate": "Que cometiste un error",
    "manga ahi": "Disfrutar de algo de alguien. Mangar: Es como andar con alguien sin compromisos",
    "ta bien me sacate lo' pie' na' pa' lante": "Dejar de lado alguien",
    "toy quillao": "Anda molesto",
    "te llenan el tanque": "Te dejaron embarazada",
    "tumba eso": "Que olvide eso",
    "mete mano ahi": "Que haga algo",
    "chapeadora": "Es como prostituirse, dan el culo pa' que le den lo cualto (dinero)",
    "a caco pelao": "Que te la meten si condon",
    "manin te burlate del sistema": "Como pana, hiciste algo jevi (cool)",
    "a lo callao": "Que haces algo en secreto",
    "bajale algo, devuelvete que te pasate": "Que hiciste o dijiste algo fuera de lugar",
    "kloke dicen la menore": "Un tiguere saludando a las mujeres menor de 18",
    "ute si es pariguayo": "Que es pendejo",
    "dando cotorra": "Palabrerías que usa el hombre pa' enamorar una mujer. O también dar excusas",
    "cual es el meneo": "Que es lo que está pasando",
    "ella ta como'e o pero una planta de mujer": "Que la mujer se ve bien, que está buena",
    "dime abe mi loco": "Saludándote. Y mi loco, es como mi amigo, mi pana",
    "tu no ere' de na, lo mayo e bulto": "Gente que privan en algo, que no, que solo es hablando y al final no son na",
    "no te meta en es vuelta compai": "Pana no te metas en lío",
    "ta rullio, ta en olla, ta cortao": "Que no tiene dinero",
    "toy aficion'a": "Que está enamorado",
    "ute si es lambon": "Que pide mucho o también que limpia saco, un alcahuete",
    "dame una bola": "Que le dé un aventón",
    "satélite": "Que la para todas, metiche",
    "deje su palomería": "Persona que hace tonterías, estupideces",
    "tumba polvo": "Una persona aduladora",
    "tu ta quedao": "Que estás fuera de moda",
    "que farisea": "Persona hipócrita, falsa",
    "ta liga'o": "Que tiene dinero",
    "que locotron": "Que es un loco, que vacano (cool)",
    "lo que me tripea": "La palabra subrayada es que burlo, riéndome",
    "eso no da la nota": "Que no la da, que no va, que no llama la atención",
    "que jartura": "La palabra subrayada está lleno",
    "que lo que anda brechando ahí": "Que anda mirando, espiando",
    "se fajan": "Que estaban peleando",
    "que demagogo": "Persona envidiosa, mentirosa",
    "tu si ere bultero": "Persona que lujeando algo",
    "un tiguere": "Una persona, que esa sabia, astuta",
    "siga boyando": "Eso es que no pare bola, dar banda, seguí pa lante",
    "yo ando jociando": "Eso es que yo estoy trabajando en lo que sea pa' buscarme el efe (dinero)",
    "tenemo lo podere": "Aquí en República Dominicana quiere decir que tienen dinero, ropa cara, casa, carro, to la baina",
    "loco date manso": "Que se esté quieto",
    "papá deme un minuto": "Aquí es eso que te presten para hacer una llamada",
    "me hicite un bulto": "Te hicieron una bulla, show",
    "tu ere el final": "O sea que eres lo grande, genial",
    "haga su diligencia": "Que busque trabajo, dinero. También hacer cosas",
    "me gustaría hacerte un coro": "Aquí se aplica la frase: 'vamo hacer un corito sano'",
    "te voy a dar un pecoson": "Que te van a dar un golpe",
    "ya va hace un dembow": "Es cuando alguien metió la pata y pasó la misa el entierro",
    "loco deje su para": "Que deje el miedo",
    "porque maría me corta los ojos": "O sea María te está mirando mal",
    "si un/a dominicano te da una pele e' lengua": "O sea te está insultando",
    "yo no se brega con": "O sea que no sabe cómo tratar",
    "el te da muela": "Te está tratando de convencer",
    "yo te lo meto frio": "Que es directo, no te engaña",
    "tu ere un verdugo": "Que eres un sabiondo, un experto",
    "tu si eres baboso": "Cuando habla incoherencia o sea mucha mierda",
    "coño tu si fuñe/jode": "O sea que molestas",
    "tiene una rechura. anda arrecha": "Que anda caliente, falta de sexo",
    "y o tengo un queso": "Esto es el hombre cuando tiene dos meses sin meterla en un hueco",
    "cuidao si te apea": "O sea que no te bajes de ahí",
    "tu ere panadero pa ta servando": "O sea que si no eres panadero no te estás acariciando",
    "pegando los cuernos": "Es que te están siendo infiel",
    "cuidao si patina": "Cuidado si resbala",
    "que jumo me di": "Se dio una emborrachada",
    "me voy a echar agua": "O sea que me voy a bañar",
    "nosotros no peleamos te comemos vivo": "Te tragamos",
    "no tenanique": "No te eches pa tras, no cojas miedo",
    "cojelo a 10 mija": "O sea que lo cojas suave, no te desesperes",
    "a ti como que te patina el coco": "Que se te va la guagua, que si te en droga, o sea loca",
    "tu quiere que te de una galleta": "Darte una abofetada",
    "tamo' clarinete": "Aquí una forma de decir que estamos claro, que entendimos",
    "la pampara ta prendía": "Eso quiere decir como que la cosa tan on fire",
    "trucho": "Aquí en RD trucho es como que toy dao (borracho) con una nota",
    "baje con trenza": "Eso significa como que tiene flow malagueton, jevi",
    "toy solteroski por si te gutoski": "También es una expresión que se usa en forma de chercha",
    "la máxima": "Palabra usada por un artista urbano",
    "hay bobo, que bobo": "Que hay problema, que lio o también que fuerte"
  },

  // Sexual/vulgar terms for spicy/no limits content
  vulgarTerms: {
    "singar": "Coger, tener sexo",
    "ñema": "glande",
    "guevo": "pene",
    "toto": "vagina",
    "culo": "trasero",
    "mamao": "cansado, agotado"
  },

  // Common Dominican expressions for "better/best"
  betterExpressions: {
    "la rompe": "el/la mejor en algo, quien se destaca",
    "se las trae": "es bueno/buena en algo",
    "ta' de to'": "es muy bueno/buena",
    "un/a duro/a": "es excelente en algo"
  },

  // Question starters that should be used more often
  questionStarters: [
    "¿Cuál fue",
    "¿Qué fue",
    "¿Qué es",
    "¿Te han",
    "¿Quién fue",
    "¿Y si"
  ],

  // Avoid these patterns and incorrect usage
  avoidPatterns: [
    "¿Tú también...?",
    "¿Soy la única que...?",
    "¿Eres una app dominicana...",
    "Conversaciones entre dos personas",
    "Preguntas poéticas o profundas",
    "Frases muy largas o adornadas"
  ],

  // Words to NEVER use or use incorrectly
  forbiddenOrIncorrectUsage: [
    "mamagouevo - NO significa 'mejor' o 'best' - no usar para expresar superioridad",
    "pilonear - NO usar para sexo en general - solo se refiere específicamente a mujer encima",
    "mamaguevo/mamagueva - NO usar, son insultos, no expresiones positivas",
    "palabras raras del que no se encuentran en el diccionario"
  ],

  // Language guidelines for different categories
  languageGuidelines: {
    casual: {
      tone: "Curious, reflexive, funny, inviting open conversation without putting player at risk",
      dominicanLevel: "Light Dominican expressions when necessary",
      vulgarTerms: false,
      examples: ["¿Qué vaina tú puedes hacer mil veces y nunca te jarta?"]
    },
    mild: {
      tone: "Bold, uncomfortable, challenging questions that provoke laughter or tension, without being sexual",
      dominicanLevel: "Moderate Dominican expressions",
      vulgarTerms: false,
      examples: ["¿A cuál ex tú le harías el coro de nuevo si te dice: 'toy solteroski'?"]
    },
    spicy: {
      tone: "Direct, short, morbid, Dominican. Like coming from a mind that lives messing around",
      dominicanLevel: "Heavy Dominican expressions with sexual slang",
      vulgarTerms: true,
      examples: ["¿Cuál ha sido el sitio más perro en donde has singao?"]
    },
    no_limits: {
      tone: "Super vulgar, third person, dirty and daring words, one sentence max, direct and morbid",
      dominicanLevel: "100% Dominican with heavy sexual slang",
      vulgarTerms: true,
      examples: ["¿Dónde prefieres la leche: en la boca o en el toto?"]
    }
  },

  // Build Dominican language prompt section
  buildDominicanPrompt: function(category) {
    const guidelines = this.languageGuidelines[category];
    if (!guidelines) return "";

    let prompt = `\nDominican Spanish Guidelines for ${category}:\n`;
    prompt += `- Tone: ${guidelines.tone}\n`;
    prompt += `- Dominican Level: ${guidelines.dominicanLevel}\n`;

    // Add critical usage restrictions
    prompt += `\nCRITICAL - NEVER use these words incorrectly:\n`;
    this.forbiddenOrIncorrectUsage.forEach(restriction => {
      prompt += `- ${restriction}\n`;
    });

    // Add proper expressions for "better/best"
    prompt += `\nFor expressing "better/best" use these Dominican expressions:\n`;
    Object.entries(this.betterExpressions).forEach(([term, meaning]) => {
      prompt += `- "${term}": ${meaning}\n`;
    });

    if (guidelines.vulgarTerms) {
      prompt += `\nApproved vulgar terms (use naturally, don't force): ${Object.keys(this.vulgarTerms).join(', ')}\n`;
    }

    prompt += `\nPreferred question starters: ${this.questionStarters.join(', ')}\n`;
    prompt += `Avoid patterns: ${this.avoidPatterns.join(', ')}\n`;

    prompt += `\nIMPORTANT: Use Dominican Spanish NATURALLY. Don't force rare dictionary words that aren't commonly used. Stick to expressions that real Dominicans actually use in daily conversation.\n`;

    if (guidelines.examples) {
      prompt += `Example style: ${guidelines.examples.join(', ')}\n`;
    }

    return prompt;
  }
};

module.exports = dominicanDictionary;
