/**
 * Couple Dares Prompt Configuration
 *
 * Prompts and guidelines specifically for generating couple dares
 * Content designed for romantic partners and couples
 */

const coupleDares = {
  /**
   * Category metadata
   */
  metadata: {
    category: "couple_dares",
    gameMode: "dares",
    difficulty: "couple",
    description: "Couple Dares - Romantic and intimate dares specifically designed for couples"
  },

  /**
   * Specific guidelines for couple dares
   */
  guidelines: [
    "MUST be direct commands or actions for couples to perform",
    "Focus on romantic, intimate, and couple-bonding activities",
    "Include physical affection and romantic gestures",
    "One sentence maximum, clear and actionable",
    "Use imperative form (Dale..., Haz..., Besa..., Abraza...)",
    "Make dares that strengthen couple bonds and intimacy",
    "Include both playful and romantic couple activities",
    "Dares should be appropriate for couples in a group setting",
    "Focus on positive romantic interactions",
    "Include creative romantic expressions and gestures"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Romantic, playful, intimate",
    style: "Dominican casual language with romantic focus",
    mood: "Loving, fun, couple-bonding",
    complexity: "Simple but meaningful romantic actions",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Romantic physical affection (kisses, hugs, caresses)",
    "Romantic compliments and expressions",
    "Couple dancing and movement",
    "Romantic role-playing and scenarios",
    "Love declarations and sweet words",
    "Romantic surprises and gestures",
    "Intimate conversations and sharing",
    "Couple games and playful activities",
    "Romantic recreations and memories",
    "Affectionate touches and closeness",
    "Romantic creativity and expression",
    "Couple bonding and connection"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "Give your partner a 30-second romantic compliment",
      text_es: "Dale a tu pareja un cumplido romántico de 30 segundos",
      text_dom: "Dale a tu pareja un cumplido romántico de 30 segundo'"
    },
    {
      text_en: "Recreate your first kiss with your partner",
      text_es: "Recrea tu primer beso con tu pareja",
      text_dom: "Recrea tu primer beso con tu pareja"
    },
    {
      text_en: "Dance slowly with your partner for one minute",
      text_es: "Baila lento con tu pareja por un minuto",
      text_dom: "Baila lento con tu pareja por un minuto"
    },
    {
      text_en: "Tell your partner three things you love about them",
      text_es: "Dile a tu pareja tres cosas que amas de ella/él",
      text_dom: "Dile a tu pareja tre' cosa' que tú amas de ella/él"
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Explicitly sexual content (use spicy category for that)",
    "Very long or complex instructions",
    "Dares that could embarrass couples in front of others",
    "Actions that require privacy (keep it group-appropriate)",
    "Harmful or uncomfortable physical activities",
    "Dares that could cause relationship conflicts",
    "Overly intimate actions inappropriate for group settings",
    "Generic dares without romantic focus"
  ],

  /**
   * Special considerations for couple dares
   */
  specialConsiderations: [
    "Dares should be actionable and clear",
    "Use Dominican casual language but keep it romantic",
    "One sentence maximum, no explanations or embellishments",
    "Use imperative form: Dale, Haz, Besa, Abraza, Dile, Baila",
    "Focus on positive romantic interactions",
    "Make dares that couples can comfortably do in a group",
    "Include both physical and verbal romantic expressions",
    "Language should be Dominican but accessible to couples"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Direct, romantic commands that are clear for B2 English users",
      tone: "Loving, playful, intimate",
      considerations: ["Use romantic language", "Keep it couple-focused", "Make it actionable"]
    },
    spanish: {
      style: "Standard Spanish with romantic imperative forms",
      tone: "Romantic and intimate",
      considerations: ["Use romantic commands", "Keep it couple-focused", "Make it meaningful"]
    },
    dominican: {
      style: "Dominican casual with romantic imperative focus",
      tone: "Loving, playful, intimate",
      considerations: ["Use Dominican expressions", "Keep romantic focus", "Make it relatable for couples"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for Couple Dares:

✅ MUST BE:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

❌ MUST NOT BE:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Special Requirements:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check for couple/romantic dare elements
    const coupleDareKeywords = ['partner', 'kiss', 'hug', 'dance', 'tell', 'give', 'romantic', 'love', 'compliment'];
    const hasCoupleDareElement = coupleDareKeywords.some(keyword => text.includes(keyword));

    // Check if it's an actionable command
    const commandWords = ['give', 'tell', 'kiss', 'hug', 'dance', 'recreate', 'show', 'express'];
    const isCommand = commandWords.some(word => text.includes(word));

    // Check length (should be concise for dares)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount < 5 || wordCount > 20) return false;

    return hasCoupleDareElement && isCommand;
  }
};

module.exports = coupleDares;
