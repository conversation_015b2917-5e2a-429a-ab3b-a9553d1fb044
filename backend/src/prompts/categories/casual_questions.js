/**
 * Casual Questions Prompt Configuration
 *
 * Curious, reflexive or funny questions that invite open conversation,
 * without putting the player at risk.
 */

const casualQuestions = {
  /**
   * Category metadata
   */
  metadata: {
    category: "casual_questions",
    gameMode: "questions",
    difficulty: "casual",
    description: "Casual Questions - Curious, reflexive, funny questions for open conversation"
  },

  /**
   * Specific guidelines for casual questions
   */
  guidelines: [
    "MUST be in third person directed at the player (¿Cuál es..., ¿Qué has..., ¿Te han...)",
    "Include group involvement questions (¿Quién del grupo crees que...?, ¿Quién coro crees que...?)",
    "Include hypothetical questions (<PERSON> tuvieras..., <PERSON> pudieras..., Si fueras..., <PERSON> te ofrecieran...)",
    "Light Dominican expressions when necessary, but not heavy slang",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Cuál es, ¿Qué has, ¿Te han, ¿<PERSON><PERSON><PERSON> del grupo, ¿<PERSON><PERSON>én coro, <PERSON> tuvier<PERSON>, <PERSON> pudier<PERSON>, <PERSON> fueras",
    "Focus on concepts, experiences and human contradictions that are spicy but reflexive",
    "Mix personal questions with group dynamics questions and hypothetical scenarios",
    "Don't accuse the person directly - be more observational than confessional",
    "Make them feel like mental traps: you think you can answer easily... until you open your mouth"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Curious, reflexive, funny",
    style: "Inviting open conversation without putting player at risk",
    mood: "Light but thought-provoking",
    complexity: "Simple but loaded with meaning",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Human contradictions and experiences",
    "Situations that make you think",
    "Funny or awkward life moments",
    "Universal experiences everyone relates to",
    "Observational questions about behavior",
    "Situations that reveal character",
    "Mental traps and thought-provoking scenarios",
    "Experiences that teach lessons",
    "Moments of realization",
    "Common human struggles and victories"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "When was the last time you tried to flex and nobody even noticed?",
      text_es: "¿Alguna vez le declaraste tu amor a alguien y te dejaron en visto emocionalmente?",
      text_dom: "¿Te has ido en sentimiento con alguien y de la nada te sacaron lo' pie'?"
    },
    {
      text_en: "What's the worst pickup line someone used on you and you still fell for it?",
      text_es: "¿Cuál es la labia más mala que te han dado y tú te la creíste?",
      text_dom: "¿Cuál es la cotorra más mala que te han dado y tú te la creíste?"
    },
    {
      text_en: "What's the most pathetic thing poverty has forced you to do?",
      text_es: "¿Cuál es la cosa más patética que la pobreza te ha obligado a hacer?",
      text_dom: "¿Qué fue lo más humillante que tuviste que hacer por estar en olla?"
    },
    {
      text_en: "What's something you can do a thousand times and never get tired of?",
      text_es: "¿Qué cosa tú puedes hacer mil veces y nunca te cansa?",
      text_dom: "¿Qué vaina tú puedes hacer mil veces y nunca te jarta?"
    },
    {
      text_en: "What mistake do you think everyone should make once to learn from it?",
      text_es: "¿Qué error crees que todo el mundo debería cometer al menos una vez para aprender?",
      text_dom: "¿Qué huevo crees que to' el mundo debería poner al menos una vez pa' coger cabeza?"
    },
    {
      text_en: "If you could read minds for one day, whose thoughts would you avoid at all costs?",
      text_es: "Si pudieras leer mentes por un día, ¿de quién evitarías los pensamientos a toda costa?",
      text_dom: "Si pudieras leer mentes por un día, ¿de quién evitarías los pensamientos a toda costa?"
    },
    {
      text_en: "If you had to choose someone from the group to survive a zombie apocalypse with, who would it be?",
      text_es: "Si tuvieras que elegir a alguien del grupo para sobrevivir un apocalipsis zombie, ¿quién sería?",
      text_dom: "Si tuvieras que elegir a alguien coro para sobrevivir un apocalipsis zombie, ¿quién sería?"
    },
    {
      text_en: "If you were forced to live without one of your senses, which would you choose?",
      text_es: "Si te obligaran a vivir sin uno de tus sentidos, ¿cuál elegirías?",
      text_dom: "Si te obligaran a vivir sin uno de tus sentidos, ¿cuál elegirías?"
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Too many questions starting with '¿Tú...' - use varied starters",
    "Conversations between two people (nothing like '¿tú también...?', '¿soy la única que...?')",
    "Poetic, deep, or soft emotional questions. This is not reflection, it's about experiences",
    "Very long or embellished phrases",
    "Questions based on specific people ('tu ex', 'tu amigo', etc.)",
    "Direct accusations to the person answering",
    "Adult themes or sexual content",
    "Extremely personal or sensitive topics"
  ],

  /**
   * Special considerations for casual questions
   */
  specialConsiderations: [
    "Questions should be in third person directed at the player (¿Cuál es..., ¿Qué has..., ¿Te han...)",
    "Include group involvement questions (¿Quién del grupo crees que...?, ¿Quién coro crees que...?)",
    "Include hypothetical scenarios (Si tuvieras..., Si pudieras..., Si fueras..., Si te ofrecieran..., Si vas...)",
    "Light Dominican expressions when necessary, but not heavy slang",
    "One sentence maximum, no explanations or embellishments",
    "Use varied question starters: ¿Cuál es, ¿Qué has, ¿Te han, ¿Quién del grupo, ¿Quién coro, Si tuvieras, Si pudieras, Si fueras",
    "Focus on concepts, experiences and human contradictions that are spicy but reflexive",
    "Mix personal questions with group dynamics questions and hypothetical scenarios",
    "Hypothetical questions should create interesting dilemmas or thought-provoking scenarios",
    "Make them feel like mental traps: you think you can answer easily... until you open your mouth",
    "More observational than confessional"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Use simple, clear language",
      tone: "Friendly and approachable",
      considerations: ["Avoid complex vocabulary", "Use common expressions"]
    },
    spanish: {
      style: "Use standard Spanish accessible to all regions",
      tone: "Warm and inclusive",
      considerations: ["Avoid regional slang", "Use formal 'usted' when appropriate"]
    },
    dominican: {
      style: "Include local expressions but keep understandable",
      tone: "Casual and friendly",
      considerations: ["Use 'tú' form", "Include some Dominican expressions", "Keep it natural"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for Casual Questions:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Complexity: ${this.toneAndStyle.complexity}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

Topics to Avoid:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Special Considerations:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check length (should be reasonable for casual questions)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount > 20) return false;

    return true;
  }
};

module.exports = casualQuestions;
