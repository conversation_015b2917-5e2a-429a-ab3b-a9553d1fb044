/**
 * Casual Dares Prompt Configuration
 *
 * Fun, engaging dares that invite open participation,
 * without putting the player at risk.
 */

const casualDares = {
  /**
   * Category metadata
   */
  metadata: {
    category: "casual_dares",
    gameMode: "dares",
    difficulty: "casual",
    description: "Casual Dares - Fun, engaging dares for open participation"
  },

  /**
   * Specific guidelines for casual dares
   */
  guidelines: [
    "Address the player directly with commands (Haz..., Imita..., Baila..., etc.)",
    "Include group involvement dares (Elige a alguien del grupo para..., Haz que alguien coro...)",
    "Dares should be fun and engaging without putting player at risk",
    "Light Dominican expressions when necessary, but not heavy slang",
    "One sentence maximum, clear and direct instructions",
    "Focus on actions that are entertaining but not embarrassing",
    "Avoid dares involving objects, talking to objects, or acting with inanimate things",
    "Avoid dares about telling someone in the group something - those are stupid",
    "No dares like 'apologize to the air as if you cheated on WiFi' - that makes no sense",
    "Mix solo dares with group interaction dares",
    "Focus on physical actions, creative expressions, or social interactions that make sense"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Fun, engaging, entertaining",
    style: "Clear direct instructions that make sense",
    mood: "Light and entertaining without embarrassment",
    complexity: "Simple and straightforward",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Physical actions and movements",
    "Creative expressions and performances",
    "Fun vocal challenges",
    "Social interactions that make sense",
    "Dance moves and gestures",
    "Funny expressions and impersonations",
    "Simple games and activities",
    "Creative storytelling",
    "Group activities and interactions",
    "Phone-based dares (calls, messages)",
    "Physical challenges",
    "Performance dares"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "Send a voice note to your ex saying you dreamed about them.",
      text_es: "Envíale un audio a tu ex diciendo que soñaste con él/ella.",
      text_dom: "Mándale un audio a tu ex diciendo que soñaste con él/ella."
    },
    {
      text_en: "Call a random number and ask if the shipment arrived.",
      text_es: "Llama a un número aleatorio y pregúntale si le llegó el cargamento.",
      text_dom: "Llama a un número random y pregúntale si le llegó el paquete."
    },
    {
      text_en: "Call your mom and tell her you're moving to another country with your partner.",
      text_es: "Llama a tu mamá y dile que te vas a mudar con tu pareja a otro país.",
      text_dom: "Llama a tu mamá y dile que te vas a mudar con tu pareja a otro país."
    },
    {
      text_en: "Do 10 push-ups yelling a different pick-up line on each one.",
      text_es: "Haz 10 lagartijas mientras gritas un piropo diferente en cada una.",
      text_dom: "Haz 10 pechadas mientras gritas un piropo diferente en cada una."
    },
    {
      text_en: "Give your unlocked phone to the person next to you for 30 seconds.",
      text_es: "Pásale tu celular desbloqueado al de al lado por 30 segundos.",
      text_dom: "Pásale tu celular desbloqueado al de al lado por 30 segundos."
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Dares involving acting with objects or talking to inanimate things",
    "Dares about telling someone in the group something - those are stupid",
    "Nonsensical dares like 'apologize to the air as if you cheated on WiFi'",
    "Anything that could cause physical harm",
    "Extremely embarrassing or humiliating actions",
    "Actions that could damage property",
    "Dares that require special equipment not commonly available",
    "Adult themes or sexual content",
    "Actions that could be dangerous"
  ],

  /**
   * Special considerations for casual dares
   */
  specialConsiderations: [
    "Dares should be fun and engaging without putting player at risk",
    "Light Dominican expressions when necessary, but not heavy slang",
    "One sentence maximum, clear and direct instructions",
    "Focus on actions that are entertaining but not embarrassing",
    "Avoid dares involving objects, talking to objects, or acting with inanimate things",
    "Focus on physical actions, creative expressions, or social interactions that make sense",
    "Dares should be performable in most social settings",
    "Keep dares short and entertaining"
  ],

  /**
   * Safety guidelines
   */
  safetyGuidelines: [
    "No physical contact with others without consent",
    "No actions that could cause injury",
    "No dares involving heights or dangerous positions",
    "No actions that could damage clothing or belongings",
    "No dares that could cause allergic reactions",
    "No actions in unsafe environments",
    "Always consider the safety of the performer and others"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Use clear, simple action words",
      tone: "Encouraging and fun",
      considerations: ["Use imperative mood", "Keep instructions clear", "Make it sound fun"]
    },
    spanish: {
      style: "Use direct commands with enthusiasm",
      tone: "Playful and encouraging",
      considerations: ["Use imperative form", "Include enthusiastic language", "Keep it simple"]
    },
    dominican: {
      style: "Use natural Dominican command forms",
      tone: "Fun and encouraging",
      considerations: ["Use local expressions", "Keep it playful", "Use familiar command forms"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for Casual Dares:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Complexity: ${this.toneAndStyle.complexity}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

Topics to Avoid:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Safety Guidelines:
${this.safetyGuidelines.map(guideline => `- ${guideline}`).join('\n')}

Special Considerations:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check for action words (dares should be actionable)
    const actionWords = ['do', 'sing', 'dance', 'act', 'perform', 'make', 'say', 'tell', 'show', 'pretend'];
    const hasActionWord = actionWords.some(word => text.includes(word));

    // Check length (should be concise for casual dares)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount > 20) return false;

    return hasActionWord;
  }
};

module.exports = casualDares;
