/**
 * Spicy Dares Prompt Configuration
 *
 * Direct, short, morbid and Dominican dares with sexual content.
 * Like coming from a mind that lives messing around.
 */

const spicyDares = {
  /**
   * Category metadata
   */
  metadata: {
    category: "spicy_dares",
    gameMode: "dares",
    difficulty: "spicy",
    description: "Spicy Dares - Direct, morbid, Dominican dares with sexual content"
  },

  /**
   * Specific guidelines for spicy dares
   */
  guidelines: [
    "Address the player directly with commands (Haz..., Baila..., Toca..., etc.)",
    "Include group involvement dares (Elige a alguien del grupo para..., Haz que alguien coro...)",
    "Use dirty and daring words like singar, ñema, toto, mamao, culo, pilonear, etc.",
    "One sentence maximum, no explanations or embellishments",
    "Direct, short, morbid and Dominican. Like coming from a mind that lives messing around",
    "Can involve sexual actions, touching, and provocative behavior",
    "Sometimes include clarifications like: 'Si eres mujer, dale a skip' if dare is for men or vice versa",
    "Language should be 100% Dominican, loaded with double meaning",
    "Mix solo sexual dares with group interaction dares",
    "Focus on sexual actions, touching, and provocative physical dares"
  ],

  /**
   * Tone and style requirements
   */
  toneAndStyle: {
    tone: "Direct, morbid, malicious, horny",
    style: "Dominican street language, loaded with sexual double meaning",
    mood: "Provocative, sexual, pressure-inducing",
    complexity: "Simple but loaded with sexual meaning",
    length: "One sentence maximum"
  },

  /**
   * Content themes and topics
   */
  themes: [
    "Sexual actions and touching",
    "Provocative physical dares",
    "Sexual performances and demonstrations",
    "Intimate touching and contact",
    "Sexual positions and movements",
    "Provocative dancing and grinding",
    "Sexual communication and whispering",
    "Touching private areas (consensual)",
    "Sexual simulations and demonstrations",
    "Provocative undressing or exposure",
    "Sexual phone calls and messages",
    "Intimate physical challenges"
  ],

  /**
   * Content examples for inspiration (DO NOT COPY THESE - they are for reference only)
   */
  examples: [
    {
      text_en: "Do 10 squats on top of someone.",
      text_es: "Haz 10 sentadillas encima de alguien.",
      text_dom: "Haz 10 sentadillas encima de alguien."
    },
    {
      text_en: "Dance erotically rubbing your body with someone of your choice for 15 seconds.",
      text_es: "Baila erótico y pegadito con quien tú elijas por 15 segundos.",
      text_dom: "Baila erótico, sobándote con quien tú elijas por 15 segundos."
    },
    {
      text_en: "Pick a wild sex position and reenact it with a player.",
      text_es: "Elige una posición sexual loca y recréala con un jugador/a.",
      text_dom: "Elige una posición sexual loca y recréala con un jugador/a."
    },
    {
      text_en: "Simulate how you give head using a nearby object.",
      text_es: "Simula cómo haces un oral con un objeto que encuentres cerca.",
      text_dom: "Simula cómo tú mamas con un objeto que encuentres cerca."
    },
    {
      text_en: "Let someone grab your neck and inner thigh at the same time for 10 seconds.",
      text_es: "Deja que alguien te agarre el cuello y la entrepierna al mismo tiempo por 10 segundos.",
      text_dom: "Deja que alguien te agarre el cuello y la entrepierna al mismo tiempo por 10 segundos."
    }
  ],

  /**
   * Things to avoid for this category
   */
  avoidTopics: [
    "Non-consensual physical contact",
    "Anything that could cause serious embarrassment",
    "Actions that could damage relationships permanently",
    "Overly explicit or crude actions",
    "Dares involving strangers inappropriately",
    "Actions that could be considered harassment",
    "Anything involving nudity or exposure",
    "Dares that could cause emotional harm",
    "Actions that violate personal boundaries severely",
    "Anything that could get someone in serious trouble"
  ],

  /**
   * Special considerations for spicy dares
   */
  specialConsiderations: [
    "Always emphasize consent and comfort levels",
    "Ensure dares are appropriate for the group dynamic",
    "Include options for modification based on comfort",
    "Consider relationship statuses within the group",
    "Balance excitement with respect",
    "Ensure dares don't cross established boundaries",
    "Make sure everyone feels safe to participate or decline",
    "Consider cultural sensitivities around physical contact"
  ],

  /**
   * Safety and consent guidelines
   */
  safetyGuidelines: [
    "All physical contact must be consensual",
    "No actions that could cause physical harm",
    "Respect personal and relationship boundaries",
    "Ensure everyone can opt out without judgment",
    "No dares that could damage reputation seriously",
    "Consider the comfort level of all participants",
    "Maintain respect even in bold challenges"
  ],

  /**
   * Language-specific guidelines
   */
  languageSpecific: {
    english: {
      style: "Use sophisticated, tasteful language",
      tone: "Bold but respectful",
      considerations: ["Avoid crude language", "Use mature vocabulary", "Keep it classy"]
    },
    spanish: {
      style: "Use passionate, expressive Spanish",
      tone: "Bold and warm",
      considerations: ["Use romantic language", "Include passionate expressions", "Maintain elegance"]
    },
    dominican: {
      style: "Use natural Dominican expressions with passion",
      tone: "Bold and intimate",
      considerations: ["Include local passionate expressions", "Keep it bold but respectful", "Use familiar tone"]
    }
  },

  /**
   * Build category-specific prompt section
   * @returns {string} - Formatted category-specific prompt
   */
  buildCategoryPrompt: function() {
    return `Category Guidelines for Spicy Dares:
${this.guidelines.map(guideline => `- ${guideline}`).join('\n')}

Tone and Style:
- Tone: ${this.toneAndStyle.tone}
- Style: ${this.toneAndStyle.style}
- Mood: ${this.toneAndStyle.mood}
- Complexity: ${this.toneAndStyle.complexity}
- Length: ${this.toneAndStyle.length}

Recommended Themes:
${this.themes.map(theme => `- ${theme}`).join('\n')}

Topics to Avoid:
${this.avoidTopics.map(topic => `- ${topic}`).join('\n')}

Safety and Consent Guidelines:
${this.safetyGuidelines.map(guideline => `- ${guideline}`).join('\n')}

Special Considerations:
${this.specialConsiderations.map(consideration => `- ${consideration}`).join('\n')}

`;
  },

  /**
   * Get example content for context
   * @returns {Array} - Array of example content
   */
  getExamples: function() {
    return this.examples;
  },

  /**
   * Validate if content fits this category
   * @param {Object} content - Content to validate
   * @returns {boolean} - Whether content is appropriate for this category
   */
  validateContent: function(content) {
    if (!content || !content.text_en) return false;

    const text = content.text_en.toLowerCase();

    // Check for avoided topics
    const hasAvoidedTopics = this.avoidTopics.some(topic => {
      const keywords = topic.toLowerCase().split(' ');
      return keywords.some(keyword => text.includes(keyword));
    });

    if (hasAvoidedTopics) return false;

    // Check for spicy themes
    const spicyKeywords = ['sexy', 'sensual', 'seductive', 'flirty', 'romantic', 'massage', 'whisper', 'seduce', 'passionate', 'intimate'];
    const hasSpicyElement = spicyKeywords.some(keyword => text.includes(keyword));

    // Check for action words
    const actionWords = ['give', 'do', 'dance', 'whisper', 'text', 'seduce', 'perform', 'show'];
    const hasActionWord = actionWords.some(word => text.includes(word));

    // Check length (should be substantial for spicy dares)
    const wordCount = content.text_en.split(' ').length;
    if (wordCount < 8 || wordCount > 30) return false;

    return hasSpicyElement && hasActionWord;
  }
};

module.exports = spicyDares;
