# TapTrap Database Migrations

This directory contains database migration scripts for TapTrap.

## Available Migrations

### 1. Add Premium Fields to Penalties (`add-premium-fields-to-penalties.js`)

**Purpose**: Adds premium-related fields to existing penalty documents in the database.

**Fields Added**:
- `isPremium`: <PERSON><PERSON><PERSON> (default: false) - Indicates if penalty is premium-only
- `isDefaultFree`: <PERSON><PERSON>an (default: false) - Indicates if penalty is default for free users
- `isDefaultPremium`: <PERSON><PERSON>an (default: false) - Indicates if penalty is default for premium users

**When to Run**: After deploying the premium penalty system updates to ensure all existing penalties have the new fields.

## Running Migrations

### Method 1: Using the Migration Runner Script

```bash
# Navigate to backend directory
cd backend

# Make sure environment variables are set
export MONGODB_URI="your-mongodb-connection-string"

# Run the migration
node scripts/migrate-penalties.js
```

### Method 2: Direct Migration Execution

```bash
# Navigate to backend directory
cd backend

# Make sure environment variables are set
export MONGODB_URI="your-mongodb-connection-string"

# Run the migration directly
node migrations/add-premium-fields-to-penalties.js
```

### Method 3: Using npm script (if added to package.json)

```bash
# Add this to backend/package.json scripts section:
# "migrate:penalties": "node scripts/migrate-penalties.js"

npm run migrate:penalties
```

## Migration Safety

- **Backup First**: Always backup your database before running migrations
- **Test Environment**: Run migrations in a test environment first
- **Idempotent**: Migrations are designed to be safe to run multiple times
- **Verification**: Each migration includes verification steps to confirm success

## Environment Variables Required

- `MONGODB_URI`: MongoDB connection string

## Migration Output

The migration will output:
- Connection status
- Number of penalties found
- Number of penalties updated
- Verification results
- Success/failure status

Example output:
```
🚀 Starting penalty premium fields migration...
✅ Connected to MongoDB
🔄 Starting penalty migration...
📊 Found 25 penalties to migrate
✅ Migration completed successfully!
📈 Updated 25 penalties
📋 Matched 25 penalties
✅ Verification: 25/25 penalties now have premium fields
🎉 All penalties successfully migrated!
🏁 Migration process completed
🔌 Database connection closed
```

## Troubleshooting

### Common Issues

1. **Connection Error**: Check MONGODB_URI environment variable
2. **Permission Error**: Ensure database user has write permissions
3. **Timeout Error**: Check network connectivity to MongoDB

### Manual Verification

After running the migration, you can verify it worked by checking the database:

```javascript
// In MongoDB shell or admin panel
db.penalties.findOne({}, {isPremium: 1, isDefaultFree: 1, isDefaultPremium: 1})
```

Should return a document with the new fields:
```javascript
{
  "_id": ObjectId("..."),
  "isPremium": false,
  "isDefaultFree": false,
  "isDefaultPremium": false
}
```

## Rollback

If you need to remove the premium fields (not recommended after the system is in use):

```javascript
// WARNING: This will remove the premium fields from all penalties
db.penalties.updateMany(
  {},
  {
    $unset: {
      isPremium: "",
      isDefaultFree: "",
      isDefaultPremium: ""
    }
  }
)
```
