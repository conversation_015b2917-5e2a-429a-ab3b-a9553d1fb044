# Server configuration
PORT=5000
NODE_ENV=development

# Database configuration
MONGODB_URI=mongodb://localhost:27017/taptrap

# Security settings - IMPORTANT: Use strong, unique values in production!
JWT_SECRET=generate-a-strong-random-string-at-least-32-chars
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=use-a-strong-password-with-mixed-case-numbers-symbols
ENCRYPTION_KEY=generate-a-different-strong-key-for-encryption

# AI Content Generation
OPENAI_API_KEY=your-openai-api-key-here
GROK_API_KEY=your-grok-api-key-here

# CORS configuration
CORS_ORIGINS=https://yourdomain.com,http://localhost:3000

# Development settings
# BYPASS_AUTH=true  # Uncomment to bypass authentication in development