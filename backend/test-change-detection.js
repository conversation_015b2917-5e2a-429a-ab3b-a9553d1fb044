#!/usr/bin/env node

/**
 * Test script for change detection endpoints
 * 
 * This script tests all the newly implemented change detection endpoints
 * to ensure they're working correctly and returning consistent hashes.
 * 
 * Usage: node test-change-detection.js [base-url]
 * Example: node test-change-detection.js http://localhost:5002
 */

const https = require('https');
const http = require('http');

// Default base URL
const BASE_URL = process.argv[2] || 'http://localhost:5002';

// Test endpoints
const ENDPOINTS = [
  {
    name: 'Content Changes',
    path: '/api/v2/content/check-changes',
    expectedFields: ['hash', 'timestamp', 'itemCount']
  },
  {
    name: 'Penalty Changes',
    path: '/api/v2/penalties/check-changes',
    expectedFields: ['hash', 'timestamp', 'itemCount']
  },
  {
    name: 'Ad Settings Changes',
    path: '/api/v2/admin/ad-settings/check-changes',
    expectedFields: ['hash', 'timestamp', 'settingsId']
  },
  {
    name: 'What\'s New Changes',
    path: '/api/v2/whatsnew/check-changes',
    expectedFields: ['hash', 'timestamp', 'logCount']
  }
];

/**
 * Make HTTP request
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const startTime = Date.now();
    
    client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData,
            responseTime,
            headers: res.headers
          });
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Test a single endpoint
 */
async function testEndpoint(endpoint) {
  const url = `${BASE_URL}${endpoint.path}`;
  
  console.log(`\n🔍 Testing: ${endpoint.name}`);
  console.log(`   URL: ${url}`);
  
  try {
    // Make first request
    const response1 = await makeRequest(url);
    
    if (response1.statusCode !== 200) {
      console.log(`   ❌ Failed: HTTP ${response1.statusCode}`);
      console.log(`   Response:`, response1.data);
      return false;
    }
    
    // Check response structure
    const missingFields = endpoint.expectedFields.filter(field => 
      !response1.data.hasOwnProperty(field)
    );
    
    if (missingFields.length > 0) {
      console.log(`   ❌ Missing fields: ${missingFields.join(', ')}`);
      return false;
    }
    
    // Validate hash format
    const hash = response1.data.hash;
    if (!hash || typeof hash !== 'string' || hash.length < 10) {
      console.log(`   ❌ Invalid hash format: ${hash}`);
      return false;
    }
    
    console.log(`   ✅ Response time: ${response1.responseTime}ms`);
    console.log(`   ✅ Hash: ${hash.substring(0, 8)}...`);
    console.log(`   ✅ Timestamp: ${response1.data.timestamp}`);
    
    // Log additional fields
    endpoint.expectedFields.forEach(field => {
      if (field !== 'hash' && field !== 'timestamp') {
        console.log(`   ✅ ${field}: ${response1.data[field]}`);
      }
    });
    
    // Make second request to test consistency
    console.log(`   🔄 Testing hash consistency...`);
    const response2 = await makeRequest(url);
    
    if (response2.statusCode !== 200) {
      console.log(`   ⚠️  Second request failed: HTTP ${response2.statusCode}`);
      return true; // First request was successful
    }
    
    if (response1.data.hash === response2.data.hash) {
      console.log(`   ✅ Hash consistency: PASS`);
    } else {
      console.log(`   ⚠️  Hash consistency: DIFFERENT (this might be expected if data changed)`);
      console.log(`      First:  ${response1.data.hash.substring(0, 8)}...`);
      console.log(`      Second: ${response2.data.hash.substring(0, 8)}...`);
    }
    
    return true;
    
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 TapTrap Change Detection Endpoint Tests');
  console.log(`📡 Base URL: ${BASE_URL}`);
  console.log('=' .repeat(60));
  
  let passedTests = 0;
  let totalTests = ENDPOINTS.length;
  
  for (const endpoint of ENDPOINTS) {
    const success = await testEndpoint(endpoint);
    if (success) {
      passedTests++;
    }
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Change detection endpoints are working correctly.');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

/**
 * Test hash generation consistency
 */
async function testHashConsistency() {
  console.log('\n🔬 Testing Hash Generation Consistency');
  console.log('-'.repeat(40));
  
  // Test each endpoint multiple times to ensure hash consistency
  for (const endpoint of ENDPOINTS) {
    const url = `${BASE_URL}${endpoint.path}`;
    console.log(`\n   Testing: ${endpoint.name}`);
    
    try {
      const hashes = [];
      const requests = 3;
      
      for (let i = 0; i < requests; i++) {
        const response = await makeRequest(url);
        if (response.statusCode === 200) {
          hashes.push(response.data.hash);
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      if (hashes.length === requests) {
        const allSame = hashes.every(hash => hash === hashes[0]);
        if (allSame) {
          console.log(`   ✅ Hash consistency: All ${requests} requests returned same hash`);
        } else {
          console.log(`   ⚠️  Hash inconsistency detected:`);
          hashes.forEach((hash, index) => {
            console.log(`      Request ${index + 1}: ${hash.substring(0, 12)}...`);
          });
        }
      } else {
        console.log(`   ❌ Failed to get ${requests} successful responses`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error testing consistency: ${error.message}`);
    }
  }
}

// Run tests
if (require.main === module) {
  runTests()
    .then(() => testHashConsistency())
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  makeRequest,
  testEndpoint,
  runTests,
  testHashConsistency
};
