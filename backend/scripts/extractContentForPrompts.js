/**
 * <PERSON>ript to extract existing content from MongoDB for prompt reference
 * This will help us get examples of current content for each category
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Content = require('../src/models/content');
const fs = require('fs');
const path = require('path');

async function extractContent() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      serverSelectionTimeoutMS: 15000,
      socketTimeoutMS: 30000,
      connectTimeoutMS: 30000,
      dbName: 'test'
    });

    console.log('Connected to MongoDB');

    // Categories to extract
    const categories = [
      'casual_questions',
      'casual_dares', 
      'mild_questions',
      'mild_dares',
      'spicy_questions',
      'spicy_dares',
      'no_limits_questions',
      'no_limits_dares'
    ];

    const extractedContent = {};

    for (const category of categories) {
      console.log(`Extracting content for ${category}...`);
      
      const content = await Content.find({
        category: category,
        active: true
      }).limit(20); // Get up to 20 examples per category

      extractedContent[category] = content.map(item => ({
        text_en: item.text_en,
        text_es: item.text_es,
        text_dom: item.text_dom
      }));

      console.log(`Found ${content.length} items for ${category}`);
    }

    // Save to JSON file for reference
    const outputPath = path.join(__dirname, 'extracted_content.json');
    fs.writeFileSync(outputPath, JSON.stringify(extractedContent, null, 2));
    
    console.log(`Content extracted and saved to ${outputPath}`);
    
    // Also create individual files for each category
    for (const [category, items] of Object.entries(extractedContent)) {
      if (items.length > 0) {
        const categoryPath = path.join(__dirname, `${category}_examples.json`);
        fs.writeFileSync(categoryPath, JSON.stringify(items, null, 2));
        console.log(`${category}: ${items.length} examples saved`);
      }
    }

    await mongoose.disconnect();
    console.log('Extraction complete!');

  } catch (error) {
    console.error('Error extracting content:', error);
    process.exit(1);
  }
}

extractContent();
