const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// Import the Penalty model
const Penalty = require('../src/models/penalty');

// Sample penalty data
const samplePenalties = [
  // Drinking penalties
  {
    id: uuidv4(),
    text_en: 'Take a shot.',
    text_es: 'Toma un trago.',
    text_dom: 'Tómate un trago.',
    category: 'drinking',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Drink a full beer.',
    text_es: 'Bébete una cerveza completa.',
    text_dom: 'Tómate una cerveza completa.',
    category: 'drinking',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Take two shots back to back.',
    text_es: 'Toma dos tragos seguidos.',
    text_dom: 'Tómate dos tragos seguidos.',
    category: 'drinking',
    active: true
  },

  // Physical penalties
  {
    id: uuidv4(),
    text_en: 'Do 10 push-ups.',
    text_es: 'Haz 10 flexiones.',
    text_dom: 'Haz 10 flexiones.',
    category: 'physical',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Do 20 jumping jacks.',
    text_es: 'Haz 20 saltos de tijera.',
    text_dom: 'Haz 20 saltos de tijera.',
    category: 'physical',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Hold a plank for 30 seconds.',
    text_es: 'Mantén una plancha por 30 segundos.',
    text_dom: 'Mantén una plancha por 30 segundos.',
    category: 'physical',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Do 15 squats.',
    text_es: 'Haz 15 sentadillas.',
    text_dom: 'Haz 15 sentadillas.',
    category: 'physical',
    active: true
  },

  // Social penalties
  {
    id: uuidv4(),
    text_en: 'Post an embarrassing photo on social media.',
    text_es: 'Publica una foto vergonzosa en redes sociales.',
    text_dom: 'Publica una foto vergonzosa en las redes.',
    category: 'social',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Call your ex and say you miss them.',
    text_es: 'Llama a tu ex y dile que lo extrañas.',
    text_dom: 'Llama a tu ex y dile que lo extrañas.',
    category: 'social',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Text your crush a flirty message.',
    text_es: 'Envía un mensaje coqueto a tu crush.',
    text_dom: 'Mándale un mensaje coqueto a tu crush.',
    category: 'social',
    active: true
  },

  // Silly penalties
  {
    id: uuidv4(),
    text_en: 'Sing the national anthem in a funny voice.',
    text_es: 'Canta el himno nacional con voz graciosa.',
    text_dom: 'Canta el himno nacional con voz graciosa.',
    category: 'silly',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Do your best impression of a chicken for 1 minute.',
    text_es: 'Imita a un pollo por 1 minuto.',
    text_dom: 'Imita a un pollo por 1 minuto.',
    category: 'silly',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Speak in a British accent for the next 3 rounds.',
    text_es: 'Habla con acento británico por las próximas 3 rondas.',
    text_dom: 'Habla con acento británico por las próximas 3 rondas.',
    category: 'silly',
    active: true
  },

  // Creative penalties
  {
    id: uuidv4(),
    text_en: 'Write and perform a 30-second rap about the person to your left.',
    text_es: 'Escribe y presenta un rap de 30 segundos sobre la persona a tu izquierda.',
    text_dom: 'Escribe y presenta un rap de 30 segundos sobre la persona a tu izquierda.',
    category: 'creative',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Draw a portrait of someone in the group using your non-dominant hand.',
    text_es: 'Dibuja un retrato de alguien del grupo usando tu mano no dominante.',
    text_dom: 'Dibuja un retrato de alguien del grupo usando tu mano no dominante.',
    category: 'creative',
    active: true
  },
  {
    id: uuidv4(),
    text_en: 'Create a short story using only words that start with the letter "B".',
    text_es: 'Crea una historia corta usando solo palabras que empiecen con "B".',
    text_dom: 'Crea una historia corta usando solo palabras que empiecen con "B".',
    category: 'creative',
    active: true
  }
];

async function addSamplePenalties() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing penalties (optional)
    console.log('Clearing existing penalties...');
    await Penalty.deleteMany({});

    // Add sample penalties
    console.log('Adding sample penalties...');
    const results = await Penalty.insertMany(samplePenalties);
    console.log(`Successfully added ${results.length} sample penalties`);

    // Display summary
    const stats = await Penalty.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } }
    ]);

    console.log('\nPenalty statistics:');
    stats.forEach(stat => {
      console.log(`- ${stat._id}: ${stat.count} penalties`);
    });

    console.log('\nSample penalties added successfully!');
  } catch (error) {
    console.error('Error adding sample penalties:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  addSamplePenalties();
}

module.exports = { addSamplePenalties, samplePenalties };
