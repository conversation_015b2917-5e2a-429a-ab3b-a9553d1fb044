// Test script to verify environment variables are loaded correctly
require('dotenv').config();

console.log('Testing environment variables loading:');
console.log('API_ENCRYPTION_KEY from process.env:', process.env.API_ENCRYPTION_KEY ? 'DEFINED' : 'UNDEFINED');

// Simulate how app.config.js would pass this to the app
const expoConfig = {
  extra: {
    apiEncryptionKey: process.env.API_ENCRYPTION_KEY,
  }
};

console.log('API_ENCRYPTION_KEY from simulated Constants:', expoConfig.extra.apiEncryptionKey ? 'DEFINED' : 'UNDEFINED');

// Show the first few characters of the key for verification (don't show the full key for security)
if (process.env.API_ENCRYPTION_KEY) {
  console.log('First 10 characters of API_ENCRYPTION_KEY:', process.env.API_ENCRYPTION_KEY.substring(0, 10) + '...');
}
