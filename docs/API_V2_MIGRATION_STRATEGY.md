# TapTrap API v2 Migration Strategy

## Overview

The TapTrap API v2 migration strategy provides a controlled way to force users to update to newer app versions while maintaining backward compatibility and operational flexibility. This system allows administrators to gradually migrate users from API v1 to API v2 without breaking existing functionality.

## Architecture

### API Versioning Structure

```
/api/           → API v1 (Legacy - responds to migration toggle)
/api/v2/        → API v2 (Current - always returns normal content)
```

### Migration Flow

```mermaid
graph TD
    A[User Request] --> B{API Version?}
    B -->|v1| C{Migration Enabled?}
    B -->|v2| D[Normal Content]
    C -->|Yes| E[Migration Message]
    C -->|No| F[Normal Content]
    E --> G[User sees: "Please download new version"]
    F --> H[User sees: Normal game content]
    D --> H
```

## Components

### 1. Backend Components

#### API v2 Structure
- **Location**: `backend/src/v2/`
- **Routes**: Mirror of v1 routes with `/api/v2/` prefix
- **Controllers**: Identical functionality to v1, but unaffected by migration
- **Database**: Shared with v1 (no data duplication)

#### Migration System
- **Model**: `backend/src/models/migrationSettings.js`
- **Service**: `backend/src/services/migrationService.js`
- **Controller**: `backend/src/controllers/migrationController.js`
- **Routes**: `backend/src/routes/migration.js`

#### Modified v1 Controllers
- **Content Controller**: Checks migration status before returning content
- **Penalty Controller**: Checks migration status before returning penalties
- **Unchanged**: AdSettings and WhatsNew (always return normal data)

### 2. Frontend Components

#### Mobile App
- **API Service**: `services/api.ts` → Points to API v2
- **Behavior**: Unaffected by migration toggle

#### Admin Panel
- **Migration Page**: `admin/src/pages/MigrationSettings.js`
- **Service**: `admin/src/services/migration.service.js`
- **Navigation**: Added to main menu with refresh icon

## Migration Settings Model

### Database Schema

```javascript
{
  settingsId: String,           // 'default'
  migrationEnabled: Boolean,    // Main toggle
  migrationMessage: {
    field_en: String,          // English message
    field_es: String,          // Spanish message
    field_dom: String          // Dominican Spanish message
  },
  migrationEnabledAt: Date,     // When enabled
  migrationDisabledAt: Date,    // When disabled
  lastModifiedBy: String,       // Admin who made change
  createdAt: Date,
  updatedAt: Date
}
```

### Default Values

```javascript
{
  migrationEnabled: false,
  migrationMessage: {
    field_en: "Please download the new version",
    field_es: "Por favor descarga la nueva versión",
    field_dom: "Por favor descarga la nueva versión"
  }
}
```

## API Endpoints

### Migration Management (Admin Only)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/admin/migration` | Get current migration settings |
| POST | `/api/admin/migration/toggle` | Enable/disable migration |
| PUT | `/api/admin/migration/message` | Update migration message |
| GET | `/api/admin/migration/status` | Get migration status (lightweight) |

### Content Endpoints Behavior

| Endpoint | Migration OFF | Migration ON |
|----------|---------------|--------------|
| `/api/content` | Normal content | Migration message |
| `/api/penalties` | Normal penalties | Migration message |
| `/api/admin/ad-settings` | Normal data | Normal data (unchanged) |
| `/api/whatsnew` | Normal data | Normal data (unchanged) |
| `/api/v2/content` | Normal content | Normal content (unaffected) |
| `/api/v2/penalties` | Normal penalties | Normal penalties (unaffected) |

## Usage Guide

### For Administrators

#### 1. Enabling Migration (Force App Update)

```bash
# Via API
curl -X POST \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"enabled": true}' \
  http://localhost:5002/api/admin/migration/toggle
```

**Via Admin Panel:**
1. Navigate to "Migration Settings"
2. Toggle "Start Migration" to ON
3. Confirm the action

#### 2. Customizing Migration Message

```bash
# Via API
curl -X PUT \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "migrationMessage": {
      "field_en": "Please update to the latest version",
      "field_es": "Por favor actualiza a la última versión",
      "field_dom": "Por favor actualiza a la última versión"
    }
  }' \
  http://localhost:5002/api/admin/migration/message
```

**Via Admin Panel:**
1. Go to "Migration Settings"
2. Edit messages in the text areas
3. Click "Update Message"

#### 3. Disabling Migration

```bash
# Via API
curl -X POST \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"enabled": false}' \
  http://localhost:5002/api/admin/migration/toggle
```

**Via Admin Panel:**
1. Navigate to "Migration Settings"
2. Toggle "Start Migration" to OFF

### For Developers

#### Checking Migration Status

```javascript
// In backend controllers
const MigrationService = require('../services/migrationService');

const isMigrationEnabled = await MigrationService.isMigrationEnabled();
if (isMigrationEnabled) {
  // Return migration message
  const migrationContent = await MigrationService.createMigrationContentResponse();
  return res.json(migrationContent);
}
// Return normal content
```

#### Adding New Endpoints

When adding new endpoints that should be affected by migration:

1. **API v1**: Add migration check in controller
2. **API v2**: No migration check needed
3. **Update MigrationService**: Add method to create migration response for new endpoint type

## Testing

### Local Testing Setup

1. **Start local backend**: `npm run dev` in `backend/`
2. **Admin panel points to local**: `http://localhost:5002/api/v2`
3. **Mobile app points to production**: `https://taptrap-backend.vercel.app/api/v2`

### Test Scenarios

#### 1. Migration Toggle Test
```bash
# Enable migration
curl -X POST -H "Authorization: Bearer <token>" \
  -d '{"enabled": true}' \
  http://localhost:5002/api/admin/migration/toggle

# Test API v1 returns migration message
curl http://localhost:5002/api/content

# Test API v2 returns normal content
curl http://localhost:5002/api/v2/content

# Disable migration
curl -X POST -H "Authorization: Bearer <token>" \
  -d '{"enabled": false}' \
  http://localhost:5002/api/admin/migration/toggle
```

#### 2. Selective Endpoint Test
```bash
# Enable migration
curl -X POST -H "Authorization: Bearer <token>" \
  -d '{"enabled": true}' \
  http://localhost:5002/api/admin/migration/toggle

# These should return migration messages
curl http://localhost:5002/api/content
curl http://localhost:5002/api/penalties

# These should return normal data (unaffected)
curl http://localhost:5002/api/admin/ad-settings
curl http://localhost:5002/api/whatsnew
```

## Security Considerations

### Authentication
- All migration endpoints require admin authentication
- JWT tokens with admin role required
- Migration changes are logged with admin email

### Data Protection
- Migration messages support encryption (same as normal content)
- No sensitive data exposed in migration responses
- Database changes are atomic and reversible

### Access Control
- Only admin users can modify migration settings
- Migration status is tracked with timestamps and user attribution
- All changes are logged for audit purposes

## Deployment

### Production Deployment Checklist

1. **✅ Deploy API v2 endpoints**
2. **✅ Update mobile app to use API v2**
3. **✅ Update admin panel to use API v2**
4. **✅ Test migration toggle in staging**
5. **✅ Verify AdSettings/WhatsNew unaffected**
6. **✅ Test encryption with migration messages**
7. **✅ Deploy to production**

### Rollback Plan

If issues occur:
1. **Immediate**: Disable migration via admin panel
2. **Emergency**: Direct database update to disable migration
3. **Fallback**: Revert mobile app to API v1 if needed

```javascript
// Emergency disable migration (direct DB)
db.migrationSettings.updateOne(
  { settingsId: 'default' },
  { $set: { migrationEnabled: false } }
);
```

## Monitoring

### Key Metrics to Monitor

1. **Migration Status**: Current enabled/disabled state
2. **API Usage**: v1 vs v2 endpoint usage
3. **Error Rates**: Migration-related errors
4. **User Impact**: App update adoption rates

### Logging

Migration events are logged with:
- Timestamp
- Admin user who made the change
- Previous and new state
- Migration message changes

## Troubleshooting

### Common Issues

#### Migration Not Working
- **Check**: Migration enabled in database
- **Check**: API v1 controllers have migration logic
- **Check**: MigrationService is properly imported

#### Admin Panel Not Loading Migration Settings
- **Check**: Migration routes added to server
- **Check**: Admin panel API service points to correct URL
- **Check**: Authentication token is valid

#### API v2 Affected by Migration
- **Issue**: API v2 should never be affected by migration
- **Solution**: Ensure v2 controllers don't import MigrationService

### Debug Commands

```bash
# Check migration status
curl -H "Authorization: Bearer <token>" \
  http://localhost:5002/api/admin/migration/status

# Check database directly
mongo
use taptrap
db.migrationSettings.find()
```

## Future Enhancements

### Planned Features
1. **Scheduled Migration**: Set future date/time for automatic migration
2. **Gradual Rollout**: Enable migration for percentage of users
3. **A/B Testing**: Different migration messages for different user groups
4. **Analytics Integration**: Track migration effectiveness

### API v3 Preparation
When API v3 is needed:
1. Create `backend/src/v3/` structure
2. Update mobile app to use v3
3. Use same migration strategy for v2 → v3 transition
