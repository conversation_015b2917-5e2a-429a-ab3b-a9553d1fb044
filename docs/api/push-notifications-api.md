# API de Push Notifications - TapTrap

## Descripción General

El sistema de push notifications de TapTrap permite enviar notificaciones dirigidas a usuarios basándose en su plataforma y preferencia de idioma. El sistema está construido sobre Expo Push Notifications y MongoDB.

## Arquitectura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Backend API   │    │   Admin Panel   │
│                 │    │                 │    │                 │
│ • Registro      │───▶│ • Almacenamiento│◀───│ • Gestión       │
│ • Per<PERSON><PERSON>      │    │ • <PERSON><PERSON><PERSON>      │    │ • Env<PERSON>         │
│ • Tokens        │    │ • Envío         │    │ • Estadísticas  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │    MongoDB      │
                    │  (PushTokens)   │
                    └─────────────────┘
```

## Endpoints del API

### 1. Registro de Token Push

**POST** `/api/push-tokens`

Registra un nuevo token de push notification para un dispositivo.

#### Request Body
```json
{
  "token": "ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]",
  "platform": "ios",
  "deviceId": "device-unique-id",
  "language": "es"
}
```

#### Parámetros
| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `token` | string | ✅ | Token de Expo Push Notifications |
| `platform` | string | ✅ | Plataforma: `ios` o `android` |
| `deviceId` | string | ❌ | ID único del dispositivo |
| `language` | string | ❌ | Idioma: `en`, `es`, `dom` (default: `en`) |

#### Respuestas

**201 Created** - Token registrado exitosamente
```json
{
  "message": "Push token registered successfully",
  "token": {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "token": "ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]",
    "platform": "ios",
    "deviceId": "device-unique-id",
    "language": "es",
    "isActive": true,
    "createdAt": "2023-09-06T10:30:00.000Z",
    "updatedAt": "2023-09-06T10:30:00.000Z"
  }
}
```

**200 OK** - Token ya existe (actualizado si es necesario)
```json
{
  "message": "Token already registered",
  "token": { /* objeto token */ }
}
```

**400 Bad Request** - Datos inválidos
```json
{
  "message": "Token and platform are required"
}
```

### 2. Obtener Tokens Registrados (Admin)

**GET** `/api/admin/push-tokens`

Obtiene todos los tokens de push notification registrados.

#### Headers
```
Authorization: Bearer <admin-jwt-token>
```

#### Respuesta

**200 OK**
```json
{
  "tokens": [
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "token": "ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]",
      "platform": "ios",
      "deviceId": "device-unique-id",
      "language": "es",
      "isActive": true,
      "createdAt": "2023-09-06T10:30:00.000Z",
      "updatedAt": "2023-09-06T10:30:00.000Z",
      "lastUsed": "2023-09-06T10:30:00.000Z"
    }
  ],
  "total": 1
}
```

### 3. Enviar Push Notifications (Admin)

**POST** `/api/admin/push-notifications/send`

Envía push notifications a dispositivos registrados, con filtrado opcional por idioma.

#### Headers
```
Authorization: Bearer <admin-jwt-token>
```

#### Request Body
```json
{
  "title": "¡Nuevo Contenido!",
  "body": "Revisa las nuevas preguntas y retos disponibles",
  "language": "es",
  "data": {
    "type": "content_update",
    "category": "spicy"
  }
}
```

#### Parámetros
| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| `title` | string | ✅ | Título de la notificación |
| `body` | string | ✅ | Mensaje de la notificación |
| `language` | string | ❌ | Filtro de idioma: `en`, `es`, `dom` |
| `data` | object | ❌ | Datos adicionales para la notificación |

#### Respuestas

**200 OK** - Notificaciones enviadas
```json
{
  "message": "Notifications sent to es users: 15 successful, 0 failed",
  "sent": 15,
  "failed": 0,
  "language": "es",
  "totalTargeted": 15
}
```

**200 OK** - Sin dispositivos encontrados
```json
{
  "message": "No registered devices found for language: dom",
  "sent": 0,
  "failed": 0,
  "language": "dom"
}
```

## Modelo de Datos

### PushToken Schema

```javascript
{
  token: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  platform: {
    type: String,
    required: true,
    enum: ['ios', 'android']
  },
  deviceId: {
    type: String,
    default: 'unknown'
  },
  language: {
    type: String,
    required: true,
    enum: ['en', 'es', 'dom'],
    default: 'en'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastUsed: {
    type: Date,
    default: Date.now
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Índices de Base de Datos

```javascript
// Índices para consultas eficientes
{ platform: 1, isActive: 1 }
{ language: 1, isActive: 1 }
{ platform: 1, language: 1, isActive: 1 }
{ createdAt: -1 }
{ token: 1 } // único
```

## Métodos Estáticos del Modelo

### getActiveTokens(platform, language)
```javascript
// Obtener tokens activos con filtros opcionales
const iosTokens = await PushToken.getActiveTokens('ios');
const spanishTokens = await PushToken.getActiveTokens(null, 'es');
const iosSpanishTokens = await PushToken.getActiveTokens('ios', 'es');
```

### cleanupOldTokens(daysOld)
```javascript
// Limpiar tokens inactivos antiguos
await PushToken.cleanupOldTokens(30); // 30 días
```

## Códigos de Error

| Código | Descripción |
|--------|-------------|
| 400 | Datos de request inválidos |
| 401 | No autorizado (token JWT inválido) |
| 500 | Error interno del servidor |

## Ejemplos de Uso

### Registro desde App Móvil
```javascript
const response = await fetch('/api/push-tokens', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    token: 'ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]',
    platform: 'ios',
    deviceId: 'unique-device-id',
    language: 'es'
  })
});
```

### Envío desde Admin Panel
```javascript
const response = await fetch('/api/admin/push-notifications/send', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + adminToken
  },
  body: JSON.stringify({
    title: 'Nueva Actualización',
    body: '¡Contenido nuevo disponible!',
    language: 'es'
  })
});
```

## Consideraciones de Seguridad

1. **Autenticación**: Los endpoints de admin requieren JWT válido
2. **Validación**: Todos los inputs son validados antes del procesamiento
3. **Rate Limiting**: Se recomienda implementar rate limiting para endpoints públicos
4. **Tokens Únicos**: Los tokens de push son únicos en la base de datos
5. **Cleanup**: Tokens inactivos se limpian automáticamente

## Monitoreo y Logs

- Todos los errores se registran en logs del servidor
- Respuestas de Expo Push Service se procesan y reportan
- Estadísticas de envío se incluyen en respuestas del API
