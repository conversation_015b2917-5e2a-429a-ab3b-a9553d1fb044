# AI Content Generation System

## Overview

The TapTrap AI Content Generation System is a sophisticated feature that uses OpenAI's GPT models to automatically generate questions and dares for the game. The system features a modular prompt architecture that ensures high-quality, contextually appropriate content for different categories and difficulty levels.

## Architecture

### Modular Prompt System

The system uses a modular approach to prompt engineering, separating concerns into maintainable components:

```
backend/src/prompts/
├── PromptManager.js            # Central prompt orchestrator
├── base/basePrompt.js          # Base prompt template
├── categories/                 # Category-specific prompts
│   ├── casual_questions.js     # Casual questions
│   ├── mild_questions.js       # Mild questions
│   ├── spicy_questions.js      # Spicy questions
│   ├── no_limits_questions.js  # No limits questions
│   ├── casual_dares.js         # Casual dares
│   ├── mild_dares.js           # Mild dares
│   ├── spicy_dares.js          # Spicy dares
│   └── no_limits_dares.js      # No limits dares
└── test-prompts.js             # Testing utilities
```

### Key Components

#### 1. PromptManager
- Central orchestrator for all prompt operations
- Validates configurations and builds complete prompts
- Manages category-specific content validation
- Provides utilities for metadata and statistics

#### 2. Base Prompt Template
- Foundational requirements shared across all categories
- Multi-language guidelines (English, Spanish, Dominican Spanish)
- Quality standards and content validation rules
- JSON format specifications

#### 3. Category-Specific Prompts
Each category contains:
- **Metadata**: Category information and descriptions
- **Guidelines**: Specific content rules and requirements
- **Tone & Style**: Voice and writing style requirements
- **Themes**: Recommended topics and content areas
- **Examples**: Sample content for AI context
- **Validation**: Content quality validation logic

## Content Categories

### Questions Categories

#### Casual Questions
- **Target Audience**: All ages, family-friendly
- **Content Style**: Light-hearted, fun, universal topics
- **Examples**: Favorite foods, hobbies, simple preferences
- **Validation**: Ensures family-appropriate content

#### Mild Questions
- **Target Audience**: Teens and adults
- **Content Style**: Slightly personal but comfortable
- **Examples**: Personal experiences, goals, relationships
- **Validation**: Checks for appropriate personal depth

#### Spicy Questions
- **Target Audience**: Close friends, couples, adults
- **Content Style**: Intimate, revealing, provocative
- **Examples**: Romantic experiences, attractions, desires
- **Validation**: Ensures tasteful but bold content

#### No Limits Questions
- **Target Audience**: Very close friends, intimate partners
- **Content Style**: Very personal, bold, adventurous
- **Examples**: Deep secrets, fantasies, taboo topics
- **Validation**: Maintains legal and ethical boundaries

### Dares Categories

#### Casual Dares
- **Target Audience**: All ages, family groups
- **Content Style**: Fun, silly, harmless challenges
- **Examples**: Funny performances, simple actions
- **Safety**: No physical harm or embarrassment

#### Mild Dares
- **Target Audience**: Teens and adults
- **Content Style**: Slightly adventurous but safe
- **Examples**: Social challenges, mild embarrassment
- **Safety**: Reversible actions, no lasting consequences

#### Spicy Dares
- **Target Audience**: Close friends, couples
- **Content Style**: Daring, exciting, provocative
- **Examples**: Romantic challenges, bold actions
- **Safety**: Consensual contact, respectful boundaries

#### No Limits Dares
- **Target Audience**: Very close friends, intimate partners
- **Content Style**: Bold, adventurous, boundary-pushing
- **Examples**: Extreme challenges, intimate actions
- **Safety**: Legal activities, consent-focused

## Multi-Language Support

### Language Implementation
- **English**: Clear, natural language for global audience
- **Spanish**: Standard Spanish accessible to all regions
- **Dominican Spanish**: Local expressions and colloquialisms

### Cultural Considerations
- Content appropriate across all three language cultures
- Respectful of cultural sensitivities
- Natural translations that maintain intent and tone
- Local expressions that enhance authenticity

## Quality Control

### Content Validation
Each category implements specific validation:
- **Field Validation**: Ensures all required language fields
- **Category Validation**: Checks content fits category requirements
- **Safety Validation**: Prevents harmful or inappropriate content
- **Quality Validation**: Ensures engaging, well-formed content

### Prompt Engineering Best Practices
- **Specificity**: Clear, actionable guidelines for each category
- **Context**: Uses existing content for consistency
- **Examples**: Provides high-quality samples for AI reference
- **Constraints**: Clear boundaries for appropriate content

## API Integration

### Generation Endpoint
```
POST /api/admin/content/generate
{
  "gameMode": "questions|dares",
  "category": "casual_questions|mild_questions|..."
}
```

### Bulk Creation Endpoint
```
POST /api/admin/content/bulk-create
{
  "contentItems": [...],
  "gameMode": "questions|dares",
  "category": "category_name"
}
```

### Response Format
```json
{
  "success": true,
  "gameMode": "questions",
  "category": "casual_questions",
  "generatedContent": [
    {
      "text_en": "English version",
      "text_es": "Spanish version",
      "text_dom": "Dominican version"
    }
  ],
  "count": 8
}
```

## Usage Workflow

### 1. Content Generation
1. Admin selects game mode and category
2. System builds category-specific prompt
3. OpenAI generates 8 content items
4. System validates generated content
5. Content displayed for review

### 2. Content Review
1. Admin reviews generated content in table format
2. Individual selection of desired items
3. Bulk selection/deselection options
4. Real-time selection counter

### 3. Content Exclusion System
The improved exclusion system provides immediate feedback and maintains clean content lists:

#### Immediate Exclusion
- Click the "X" button on any generated content item
- Item is **immediately removed** from the current generation list
- Content is added to the global exclusion database
- Selection indices automatically adjust for remaining items
- No visual clutter from excluded items

#### Benefits
- **Clean Interface**: Excluded items disappear immediately, no strikethrough text
- **Consistent Counts**: Content counter always reflects actual available items
- **Better Generations**: Future AI generations avoid excluded content
- **Efficient Workflow**: No need to scroll through previously excluded items

#### Technical Implementation
- Excluded content removed from `generatedContent` array immediately
- Selection indices recalculated to maintain consistency
- Global exclusion database prevents regeneration of excluded content
- Reset functionality available through admin menu

### 4. Content Creation
1. Admin selects items to add
2. Confirmation modal appears
3. Bulk creation in database
4. Success feedback and cleanup

## Configuration

### Environment Variables
```bash
# Required for AI content generation
OPENAI_API_KEY=your-openai-api-key-here
GROK_API_KEY=your-grok-api-key-here
```

### Hybrid AI Provider System
TapTrap uses a hybrid approach with two AI providers:

- **OpenAI (GPT-4o-mini)**: Used for `casual` and `mild` categories
  - More conservative and follows instructions precisely
  - Better for family-friendly content
  - Respects content guidelines strictly

- **Grok AI (grok-2-1212)**: Used for `spicy` and `no_limits` categories
  - More creative and flexible with adult content
  - Better for Dominican Spanish slang and cultural expressions
  - Handles explicit content without restrictions

### Generation Parameters
- **OpenAI Model**: GPT-4o-mini (cost-effective)
- **Grok Model**: grok-2-1212 (latest)
- **Temperature**: 0.8 (high creativity)
- **Max Tokens**: 2000
- **Count**: 8 items per generation

### Provider Selection Logic
```javascript
// Automatic provider selection based on category
const provider = getProviderForCategory(category);

// casual_questions -> OpenAI
// mild_dares -> OpenAI
// spicy_questions -> Grok
// no_limits_dares -> Grok
```

## Error Handling

### Common Errors
- **Missing API Key**: Clear configuration error messages
- **Invalid Category**: Validation error with available options
- **Generation Failure**: Retry suggestions and troubleshooting
- **Validation Failure**: Content quality feedback

### Exclusion System Issues
- **Items Still Appearing**: Check if exclusion database is properly connected
- **Selection Index Errors**: Refresh the page if selection becomes inconsistent
- **Reset Not Working**: Verify admin permissions for exclusion reset functionality
- **Performance Issues**: Large exclusion lists may slow generation, consider periodic cleanup

### Monitoring
- Generation success/failure rates logged
- Content validation statistics tracked
- API usage monitoring through OpenAI dashboard
- Performance metrics for prompt effectiveness
- Exclusion system usage and effectiveness tracking

## Testing

### Automated Testing
```bash
# Run prompt system tests
node backend/src/prompts/test-prompts.js
```

### Test Coverage
- PromptManager functionality validation
- Category prompt building verification
- Content validation testing
- Multi-language support verification
- Error handling validation

## Maintenance

### Adding New Categories
1. Create new category file in `categories/`
2. Implement required methods and properties
3. Add to PromptManager category map
4. Run tests to verify integration
5. Update documentation

### Updating Existing Categories
1. Modify category-specific files
2. Update guidelines, examples, or validation
3. Test changes with generation
4. Monitor content quality improvements
5. Document changes

### Performance Optimization
- Monitor prompt effectiveness through content quality
- Update examples based on successful generations
- Refine validation logic based on edge cases
- Optimize prompt length for cost efficiency

## Security Considerations

### Content Safety
- Category-specific validation prevents inappropriate content
- Multi-layer filtering for harmful content
- Respect for cultural sensitivities across languages
- Legal and ethical boundary enforcement

### API Security
- OpenAI API key stored securely in environment variables
- Admin authentication required for all AI endpoints
- Input validation prevents prompt injection
- Rate limiting and usage monitoring

## Future Enhancements

### Planned Features
- **Custom Prompts**: Admin-configurable prompt templates
- **Batch Generation**: Multiple categories simultaneously
- **Quality Scoring**: Automatic content quality assessment
- **Usage Analytics**: Generation pattern analysis
- **A/B Testing**: Prompt effectiveness comparison

### Exclusion System Enhancements
- **Smart Exclusion**: AI-powered similar content detection
- **Exclusion Analytics**: Track most commonly excluded content patterns
- **Bulk Exclusion**: Select multiple items for exclusion at once
- **Exclusion Categories**: Organize exclusions by reason (inappropriate, duplicate, etc.)
- **Temporary Exclusions**: Time-based exclusions that auto-expire
- **Exclusion Export**: Export exclusion lists for analysis

### Extensibility
- Plugin architecture for new AI providers
- Custom validation rules per admin preference
- Dynamic prompt modification based on success rates
- Integration with content performance metrics
- Exclusion system API for external integrations
