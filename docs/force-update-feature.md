# Force Update Feature Documentation

## Overview

The Force Update feature allows TapTrap administrators to control whether users can dismiss app update notifications. This provides flexibility between optional updates (users can dismiss) and mandatory updates (users cannot dismiss until they update).

## Implementation Details

### Backend Changes (API v2)

#### 1. Database Schema Update
- **File**: `backend/src/models/whatsNew.js`
- **Change**: Added `forceUpdate` boolean field to WhatsNew schema
- **Default**: `false` (optional updates by default)

#### 2. API Controller Updates
- **File**: `backend/src/v2/controllers/whatsNewController.js`
- **Changes**:
  - Added `forceUpdate` parameter to create and update operations
  - Included `forceUpdate` in API responses
  - Added validation and logging for the new field

### Admin Panel Changes

#### 3. Admin Interface Update
- **File**: `admin/src/components/WhatsNewManagement.jsx`
- **Changes**:
  - Added "Force Update" toggle switch to the form
  - Integrated with form state management
  - Added descriptive text explaining the feature
  - Used red color scheme for the toggle to indicate importance

### Mobile App Changes

#### 4. Service Layer Updates
- **File**: `services/whatsNewService.ts`
- **Changes**:
  - Added `forceUpdate` field to `WhatsNewLog` interface
  - Created `checkForAppUpdate()` function to determine update requirements
  - Returns update status, force flag, and associated log data

#### 5. HomeScreen Integration
- **File**: `screens/HomeScreen.tsx`
- **Changes**:
  - Added state management for update toast visibility and force status
  - Integrated update checking into comprehensive API calls
  - Added conditional UI dimming for force updates
  - Implemented close handler for optional updates only
  - Added animated transitions for update toast

## Feature Behavior

### Optional Updates (forceUpdate: false)
- Update toast appears with close button (X)
- Users can dismiss the notification and continue using the app
- UI remains fully interactive
- Update toast can be closed by tapping the X button

### Mandatory Updates (forceUpdate: true)
- Update toast appears without close button
- UI is dimmed to 30% opacity to indicate restricted interaction
- Users cannot dismiss the notification
- Only the "Update" button is interactive
- Users must update the app to continue

## API Endpoints

### Get What's New Logs
```
GET /api/v2/whatsnew
```
Response now includes `forceUpdate` field:
```json
{
  "id": "log_id",
  "type": "purple",
  "date": "DEC 15, 2024",
  "title_en": "Critical Update Required",
  "appVersion": "1.0.15",
  "forceUpdate": true,
  ...
}
```

### Admin Operations
```
POST /api/v2/whatsnew/admin
PUT /api/v2/whatsnew/admin/:id
```
Request body now accepts `forceUpdate` boolean field.

## Usage Guidelines

### When to Use Force Updates
- Critical security fixes
- Breaking changes that require immediate adoption
- Major infrastructure updates
- Bug fixes that affect core functionality

### When to Use Optional Updates
- New features that don't affect existing functionality
- UI improvements
- Performance enhancements
- Minor bug fixes

## Technical Notes

### Update Detection Logic
1. App fetches What's New logs during startup
2. Compares current app version with log's `appVersion` using semantic versioning
3. If current version is lower, marks log as requiring update
4. Checks `forceUpdate` flag to determine dismissibility
5. Shows appropriate toast with correct interaction behavior

### UI State Management
- Update toast has highest z-index (1000) to appear above other toasts
- Force updates dim other UI elements and disable interactions
- Optional updates maintain full UI interactivity
- Proper cleanup when updates are dismissed or completed

### Error Handling
- Graceful fallback if update check fails
- Maintains app functionality even if What's New service is unavailable
- Logs errors to Sentry for monitoring

## Testing

### Admin Panel Testing
1. Create a new What's New log with `forceUpdate: true`
2. Set `appVersion` higher than current app version
3. Verify toggle appears and functions correctly
4. Test both create and edit operations

### Mobile App Testing
1. Launch app with force update log in database
2. Verify update toast appears without close button
3. Verify UI is dimmed and interactions are disabled
4. Test with optional update (close button should appear)
5. Test dismissal functionality for optional updates

## Migration Notes

- This is a v2 API feature only - v1 API remains unchanged
- Existing What's New logs will have `forceUpdate: false` by default
- No breaking changes to existing functionality
- Backward compatible with older app versions
