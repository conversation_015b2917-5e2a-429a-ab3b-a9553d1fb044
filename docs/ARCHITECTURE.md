# TapTrap Technical Architecture

This document provides a detailed overview of the TapTrap application's technical architecture, including component structure, data flow, and key technologies.

## Technology Stack

### Frontend (Mobile App)
- **Framework**: React Native with Expo
- **State Management**: React Context API
- **Navigation**: React Navigation
- **Animations**: React Native Reanimated
- **HTTP Client**: Axios
- **Storage**: Expo FileSystem
- **UI Components**: Custom components with SVG assets
- **Localization**: Custom i18n service

### Backend
- **Server**: Node.js with Express
- **Database**: MongoDB
- **Authentication**: JWT
- **API**: RESTful endpoints
- **Admin Dashboard**: React

## Application Structure

### Core Components

#### Providers
- **GameProvider**: Manages game state, selected categories, and content
- **LanguageProvider**: Handles language selection and text localization
- **AppSettingsProvider**: Manages app settings like sound and haptic feedback

#### Screens
- **HomeScreen**: Main entry point with game mode selection
- **SelectCategoriesScreen**: Category selection for questions and challenges
- **TouchGameScreen**: Core game screen with touch detection and selection
- **SettingsScreen**: App settings configuration

#### Services
- **contentService**: Handles API communication and content caching
- **i18nService**: Manages language detection and text localization
- **revenueCatService**: Handles in-app purchases (if applicable)

## Data Flow

### Game Content Flow

1. **Content Initialization**:
   - App starts and attempts to fetch content from backend API
   - If successful, content is cached to device storage
   - If unsuccessful, app falls back to cached content
   - If no cached content, app uses bundled content

2. **Content Selection**:
   - User selects game modes (questions, challenges, or both)
   - User selects content categories (casual, mild, spicy, no limits)
   - App filters available content based on selections

3. **Game Flow**:
   - User places fingers on screen
   - Countdown begins
   - Random finger is selected
   - Question or challenge is presented
   - User can skip to next question or reset game

### State Management

The app uses React Context API for state management:

```
App
├── AppSettingsProvider
│   └── LanguageProvider
│       └── GameProvider
│           └── Navigation
│               ├── HomeScreen
│               ├── SelectCategoriesScreen
│               ├── TouchGameScreen
│               └── SettingsScreen
```

## Key Technical Components

### Touch Detection System

The touch detection system in `TouchGameScreen.tsx` handles:

1. **Touch Tracking**: Records position and identifier of each finger
2. **Touch Movement**: Updates position as fingers move
3. **Touch Removal**: Detects when fingers are removed
4. **Selection Logic**: Implements the random selection algorithm

```typescript
// Touch point data structure
interface TouchPoint {
  identifier: number;  // Unique ID for each finger
  pageX: number;       // X position
  pageY: number;       // Y position
  isHighlighted: boolean;
  isSelected: boolean;
  seq: number;         // Sequence number for ordering
}
```

### Content Pool Management

The content pool system ensures:

1. **No Repetition**: Tracks used questions to avoid repetition
2. **Complete Cycles**: Only resets the pool when all items have been shown
3. **Mixing**: Shuffles questions and challenges for variety
4. **Persistence**: Maintains state between game sessions

### Animation System

The app uses React Native Reanimated for smooth animations:

1. **Shared Values**: For performant animation state
2. **Animated Styles**: For dynamic UI transformations
3. **Transitions**: For smooth screen and element transitions
4. **Gesture Integration**: For interactive animations

### Offline Support

The offline support system:

1. **Checks Connectivity**: Detects network status
2. **Prioritizes Sources**: API → Cached → Bundled
3. **Transparent Fallback**: Seamlessly switches between sources
4. **Cache Management**: Updates cache when new content is available

## Backend Architecture

### API Server

The backend server provides:

1. **Content Delivery**: Formatted game content for the app
2. **Content Management**: Admin interface for updating content
3. **Authentication**: Secure access to admin features
4. **Data Persistence**: MongoDB storage for all content

### Data Structure

The backend organizes content as:

```json
{
  "questions": {
    "casual_questions": [{ "id": "...", "text_en": "...", "text_es": "..." }],
    "mild_questions": [...],
    "spicy_questions": [...],
    "no_limits_questions": [...]
  },
  "dares": {
    "casual_dares": [...],
    "mild_dares": [...],
    "spicy_dares": [...],
    "no_limits_dares": [...]
  }
}
```

## Performance Considerations

### Optimization Techniques

1. **Memoization**: Prevents unnecessary re-renders
2. **Lazy Loading**: Defers loading of non-critical components
3. **Asset Optimization**: Compressed images and audio
4. **Native Animations**: Uses the native thread for animations
5. **Efficient Touch Handling**: Optimized touch event processing

### Memory Management

1. **Resource Cleanup**: Proper cleanup of timers, sounds, and animations
2. **Component Unmounting**: Tracking unmount state to prevent updates
3. **Audio Handling**: Proper loading and unloading of sound resources

## Security Considerations

1. **Data Storage**: Secure storage of cached content
2. **API Communication**: HTTPS for all API requests
3. **Input Validation**: Validation of all user inputs
4. **Error Handling**: Graceful handling of errors and exceptions

## Future Architecture Considerations

1. **State Management**: Potential migration to Redux or MobX for more complex state
2. **Performance Monitoring**: Integration of performance monitoring tools
3. **Testing**: Expansion of unit and integration testing
4. **Offline-First**: Enhanced offline capabilities and sync
5. **Accessibility**: Improved accessibility features
