# Sistema de Push Notifications TapTrap - Documentación Completa

## Descripción General

El sistema de push notifications de TapTrap es una solución completa que permite enviar notificaciones dirigidas a usuarios basándose en su plataforma (iOS/Android) y preferencia de idioma (English/Spanish/Dominican). El sistema está construido sobre Expo Push Notifications, MongoDB y React.

## Arquitectura del Sistema

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Backend API   │    │   Admin Panel   │
│                 │    │                 │    │                 │
│ • PushBanner    │───▶│ • Token Storage │◀───│ • Device Mgmt   │
│ • Permissions   │    │ • Language      │    │ • Send Notifications │
│ • Token Mgmt    │    │   Filtering     │    │ • Statistics    │
│ • i18n Support  │    │ • Expo Push API │    │ • Language Targeting │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │    MongoDB      │
                    │  • PushTokens   │
                    │  • Multi-index  │
                    │  • Language     │
                    └─────────────────┘
```

## Componentes Principales

### 1. App Móvil (`/services/pushNotificationService.ts`)

**Responsabilidades:**
- Solicitar permisos de push notifications
- Generar y gestionar tokens de Expo
- Detectar idioma del usuario
- Mostrar banner de solicitud de permisos
- Comunicarse con el backend

**Características Clave:**
- Banner aparece solo en primera pregunta/reto
- Cooldown de 8 horas si usuario descarta banner
- Respeta decisión de usuario (no molesta si rechaza)
- Soporte multi-idioma automático

### 2. Backend API (`/backend/src/routes/pushNotifications.js`)

**Endpoints:**
- `POST /api/push-tokens` - Registro de tokens
- `GET /api/admin/push-tokens` - Listar dispositivos (Admin)
- `POST /api/admin/push-notifications/send` - Enviar notificaciones (Admin)

**Características Clave:**
- Filtrado por idioma y plataforma
- Validación de datos de entrada
- Integración con Expo Push Service
- Estadísticas de envío detalladas

### 3. Panel de Administración (`/admin/src/pages/PushNotifications.js`)

**Funcionalidades:**
- Dashboard con estadísticas en tiempo real
- Formulario de envío con selector de idioma
- Lista de dispositivos registrados
- Monitoreo de tasas de éxito/fallo

**Características Clave:**
- Interfaz intuitiva con Chakra UI
- Filtrado visual por idioma y plataforma
- Feedback inmediato de resultados
- Contadores dinámicos de dispositivos objetivo

## Flujo de Trabajo Completo

### 1. Registro de Usuario (App Móvil)

```mermaid
sequenceDiagram
    participant U as Usuario
    participant A as App
    participant B as Backend
    
    U->>A: Inicia primera pregunta
    A->>A: Verifica condiciones banner
    A->>U: Muestra PushBanner
    U->>A: Toca banner
    A->>A: Solicita permisos sistema
    U->>A: Concede permisos
    A->>A: Genera token Expo
    A->>A: Detecta idioma usuario
    A->>B: POST /api/push-tokens
    B->>B: Almacena en MongoDB
    B->>A: Confirma registro
```

### 2. Envío de Notificaciones (Admin)

```mermaid
sequenceDiagram
    participant Admin as Administrador
    participant Panel as Admin Panel
    participant API as Backend API
    participant Expo as Expo Push Service
    participant Users as Usuarios
    
    Admin->>Panel: Accede a Push Notifications
    Panel->>API: GET /admin/push-tokens
    API->>Panel: Lista dispositivos + estadísticas
    Admin->>Panel: Completa formulario
    Admin->>Panel: Selecciona idioma objetivo
    Panel->>API: POST /admin/push-notifications/send
    API->>API: Filtra por idioma
    API->>Expo: Envía notificaciones
    Expo->>Users: Entrega notificaciones
    Expo->>API: Reporta resultados
    API->>Panel: Estadísticas de envío
    Panel->>Admin: Muestra resultados
```

## Modelo de Datos

### PushToken Schema (MongoDB)

```javascript
{
  _id: ObjectId,
  token: String,           // Token único de Expo
  platform: String,       // 'ios' | 'android'
  deviceId: String,        // ID del dispositivo
  language: String,        // 'en' | 'es' | 'dom'
  isActive: Boolean,       // Estado del token
  lastUsed: Date,         // Última actividad
  createdAt: Date,        // Fecha de registro
  updatedAt: Date         // Última actualización
}
```

### Índices de Base de Datos

```javascript
// Índices para consultas eficientes
{ token: 1 }                          // Único
{ platform: 1, isActive: 1 }          // Por plataforma
{ language: 1, isActive: 1 }          // Por idioma
{ platform: 1, language: 1, isActive: 1 } // Combinado
{ createdAt: -1 }                     // Cronológico
```

## Configuración del Sistema

### App Móvil (app.config.js)

```javascript
plugins: [
  [
    "expo-notifications",
    {
      "color": "#ffffff"
    }
  ]
],
ios: {
  infoPlist: {
    NSUserNotificationsUsageDescription: "TapTrap would like to send you notifications about new content and features to enhance your gaming experience."
  }
}
```

### Variables de Entorno

```bash
# Backend
MONGODB_URI=mongodb://localhost:27017/taptrap
JWT_SECRET=your-jwt-secret

# App Móvil (EAS)
API_ENCRYPTION_KEY=your-encryption-key
```

## Casos de Uso Principales

### 1. Nuevo Contenido Disponible

**Escenario:** Se agrega nuevo contenido al juego

```javascript
// Admin envía notificación
{
  title: "¡Nuevo Contenido!",
  body: "Revisa las nuevas preguntas y retos disponibles",
  language: "es" // Solo usuarios en español
}

// Resultado
"Notifications sent to es users: 45 successful, 0 failed"
```

### 2. Actualización de la App

**Escenario:** Nueva versión disponible

```javascript
// Admin envía a todos
{
  title: "App Update Available",
  body: "Update TapTrap for the latest features!",
  language: "" // Todos los idiomas
}

// Resultado
"Notifications sent: 150 successful, 2 failed"
```

### 3. Evento Especial

**Escenario:** Promoción especial para usuarios dominicanos

```javascript
// Admin envía específicamente
{
  title: "¡Oferta Especial!",
  body: "Descuento exclusivo para usuarios dominicanos",
  language: "dom" // Solo Dominican Spanish
}

// Resultado
"Notifications sent to dom users: 12 successful, 0 failed"
```

## Métricas y Monitoreo

### Dashboard de Estadísticas

1. **Dispositivos Totales:** 150 registrados
2. **Por Plataforma:** 90 iOS, 60 Android
3. **Por Idioma:** 75 EN, 45 ES, 30 DOM
4. **Tasa de Éxito:** 98.5% promedio
5. **Dispositivos Activos:** 145 de 150

### Alertas y Monitoreo

- Tokens inválidos se marcan como inactivos
- Cleanup automático de tokens antiguos
- Logs detallados de errores de envío
- Métricas de engagement por idioma

## Seguridad y Mejores Prácticas

### Seguridad

1. **Autenticación:** JWT para endpoints de admin
2. **Validación:** Todos los inputs son validados
3. **Rate Limiting:** Prevención de spam
4. **Logs:** Auditoría completa de actividades

### Mejores Prácticas

1. **UX:** Banner no intrusivo, respeta decisiones del usuario
2. **Timing:** Envíos en horarios apropiados
3. **Localización:** Mensajes en idioma nativo del usuario
4. **Testing:** Pruebas con dispositivos reales antes de envío masivo

## Troubleshooting Común

### Problemas Frecuentes

1. **Token No Generado**
   - Verificar que sea dispositivo físico
   - Confirmar permisos concedidos
   - Revisar configuración de Expo

2. **Notificaciones No Llegan**
   - Verificar token válido en Expo
   - Confirmar dispositivo activo
   - Revisar filtros de idioma

3. **Error de Envío Masivo**
   - Verificar límites de Expo Push Service
   - Confirmar formato de tokens
   - Revisar logs del backend

### Comandos de Debug

```javascript
// Limpiar estado para testing
await AsyncStorage.multiRemove([
  'taptrap_push_token',
  'taptrap_push_permission_denied',
  'taptrap_push_banner_dismissed'
]);

// Verificar tokens en backend
db.pushtokens.find({ isActive: true }).count()

// Probar envío individual
curl -X POST /api/admin/push-notifications/send \
  -H "Authorization: Bearer <token>" \
  -d '{"title":"Test","body":"Test message","language":"es"}'
```

## Documentación Relacionada

- [API Documentation](./api/push-notifications-api.md)
- [Mobile App Implementation](./mobile/push-notifications-app.md)
- [Admin Panel Guide](./admin/push-notifications-admin.md)
- [Multi-Language Support](./mobile/multi-language-push-notifications.md)
