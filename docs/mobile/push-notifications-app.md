# Push Notifications - App Móvil TapTrap

## Descripción General

El sistema de push notifications en la app móvil de TapTrap permite a los usuarios recibir notificaciones sobre nuevo contenido y características. La implementación sigue las mejores prácticas de Expo y se integra perfectamente con el sistema de banners existente.

## Arquitectura de la App

```
┌─────────────────────────────────────────────────────────────┐
│                    TouchGameScreen                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   PushBanner    │  │  InGameBanner   │  │StoreReview  │ │
│  │   (1ra pregunta)│  │  (2da pregunta) │  │   Banner    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                ┌─────────────────────────────┐
                │  PushNotificationService    │
                │  • Per<PERSON><PERSON>                 │
                │  • Tokens                   │
                │  • Estado del Banner        │
                │  • Comunicación Backend     │
                └─────────────────────────────┘
```

## Componentes Principales

### 1. PushNotificationService

**Archivo**: `services/pushNotificationService.ts`

Servicio principal que maneja toda la lógica de push notifications.

#### Métodos Principales

```typescript
interface PushNotificationService {
  requestPermissions(): Promise<boolean>;
  getExpoPushToken(): Promise<string | null>;
  uploadTokenToBackend(token: string): Promise<boolean>;
  hasPermissionBeenDenied(): Promise<boolean>;
  setPermissionDenied(): Promise<void>;
  hasBannerBeenDismissed(): Promise<boolean>;
  setBannerDismissed(): Promise<void>;
  clearBannerDismissed(): Promise<void>;
  shouldShowBanner(): Promise<boolean>;
  getCurrentLanguage(): Promise<string>;
}
```

#### Configuración de Notificaciones
```typescript
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowAlert: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});
```

### 2. PushBanner Component

**Archivo**: `components/notifications/PushBanner.tsx`

Banner visual que solicita permisos de push notifications.

#### Props
```typescript
interface Props {
  onPress?: () => void;
  style?: ViewStyle;
  isSequential?: boolean;
}
```

#### Características
- Diseño atractivo con icono de notificación
- Texto localizado según idioma del usuario
- Animación de entrada suave
- Botón de cierre con cooldown de 8 horas

### 3. Integración en TouchGameScreen

**Archivo**: `screens/TouchGameScreen.tsx`

El banner se muestra en la primera pregunta/reto si se cumplen las condiciones.

#### Lógica de Visualización
```typescript
// Mostrar en primera pregunta/reto si condiciones se cumplen
if (newCount === 1 && !showPushBanner && !showBanner && !showStoreReviewBanner) {
  pushNotificationService.shouldShowBanner().then(shouldShow => {
    if (shouldShow) {
      setShowPushBanner(true);
      pushBannerOpacity.value = withTiming(1, { duration: 400 });
    }
  });
}
```

## Estados del Sistema

### Estados de Permisos

| Estado | Descripción | Banner Visible | Acción |
|--------|-------------|----------------|--------|
| **No Solicitado** | Primera vez del usuario | ✅ | Mostrar banner |
| **Concedido** | Usuario aceptó permisos | ❌ | Token enviado al backend |
| **Denegado** | Usuario rechazó permisos | ❌ | Banner oculto permanentemente |
| **Descartado** | Usuario cerró banner | ❌ | Oculto por 8 horas |

### Prioridad de Banners

1. **PushBanner** (primera pregunta/reto)
2. **InGameBanner** (segunda pregunta/reto, solo casual/mild)
3. **StoreReviewBanner** (varía según tipo de usuario)

Solo un banner se muestra a la vez, con prioridad mayor ocultando los de menor prioridad.

## Flujo de Permisos

### 1. Verificación Inicial
```typescript
async shouldShowBanner(): Promise<boolean> {
  // No mostrar si ya fue denegado
  const permissionDenied = await this.hasPermissionBeenDenied();
  if (permissionDenied) return false;

  // No mostrar si fue descartado en últimas 8 horas
  const bannerDismissed = await this.hasBannerBeenDismissed();
  if (bannerDismissed) return false;

  // No mostrar si ya tiene permisos
  const { status } = await Notifications.getPermissionsAsync();
  if (status === 'granted') return false;

  return true;
}
```

### 2. Solicitud de Permisos
```typescript
async requestPermissions(): Promise<boolean> {
  // Verificar dispositivo físico
  if (!Device.isDevice) return false;

  // Configurar canal Android
  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  // Solicitar permisos
  const { status } = await Notifications.requestPermissionsAsync();

  if (status !== 'granted') {
    await this.setPermissionDenied();
    return false;
  }

  return true;
}
```

### 3. Generación y Envío de Token
```typescript
async getExpoPushToken(): Promise<string | null> {
  const projectId = Constants.expoConfig?.extra?.eas?.projectId;

  const pushTokenString = (
    await Notifications.getExpoPushTokenAsync({ projectId })
  ).data;

  await AsyncStorage.setItem(PUSH_TOKEN_KEY, pushTokenString);
  return pushTokenString;
}

async uploadTokenToBackend(token: string): Promise<boolean> {
  const currentLanguage = await this.getCurrentLanguage();

  // Get unique device ID using the new device ID service
  const deviceId = await getUniqueDeviceId();

  const response = await axios.post(`${API_URL}/push-tokens`, {
    token,
    platform: Platform.OS,
    deviceId,
    language: currentLanguage,
  });

  return response.status === 200 || response.status === 201;
}
```

## Gestión de Estado Local

### Claves de AsyncStorage
```typescript
const PUSH_TOKEN_KEY = 'taptrap_push_token';
const PUSH_PERMISSION_DENIED_KEY = 'taptrap_push_permission_denied';
const PUSH_BANNER_DISMISSED_KEY = 'taptrap_push_banner_dismissed';
```

### Cooldown de Banner (8 horas)
```typescript
async setBannerDismissed(): Promise<void> {
  const dismissedAt = Date.now().toString();
  await AsyncStorage.setItem(PUSH_BANNER_DISMISSED_KEY, dismissedAt);
}

async hasBannerBeenDismissed(): Promise<boolean> {
  const dismissedAt = await AsyncStorage.getItem(PUSH_BANNER_DISMISSED_KEY);
  if (!dismissedAt) return false;

  const dismissedTime = parseInt(dismissedAt, 10);
  const now = Date.now();
  const eightHours = 8 * 60 * 60 * 1000; // 8 horas en ms

  return (now - dismissedTime) < eightHours;
}
```

## Detección de Idioma

### Obtención de Idioma Actual
```typescript
async getCurrentLanguage(): Promise<string> {
  // Intentar obtener idioma almacenado
  const storedLanguage = await AsyncStorage.getItem('appLanguage');
  if (storedLanguage && ['en', 'es', 'dom'].includes(storedLanguage)) {
    return storedLanguage;
  }

  // Usar idioma del dispositivo como fallback
  return getDeviceLanguage();
}
```

## Configuración de la App

### app.config.js
```javascript
plugins: [
  [
    "expo-notifications",
    {
      "color": "#ffffff"
    }
  ]
],
ios: {
  infoPlist: {
    NSUserNotificationsUsageDescription: "TapTrap would like to send you notifications about new content and features to enhance your gaming experience."
  }
}
```

### Dependencias
```json
{
  "expo-notifications": "~0.29.11",
  "expo-device": "~6.0.2"
}
```

## Manejo de Errores

### Errores Comunes y Soluciones

1. **Dispositivo No Físico**
   ```typescript
   if (!Device.isDevice) {
     console.log('Push notifications require a physical device');
     return false;
   }
   ```

2. **Error de Red**
   ```typescript
   try {
     await axios.post(`${API_URL}/push-tokens`, data, { timeout: 15000 });
   } catch (error) {
     console.error('Failed to upload push token:', error);
     return false;
   }
   ```

3. **Token Inválido**
   ```typescript
   try {
     const token = await Notifications.getExpoPushTokenAsync({ projectId });
     return token.data;
   } catch (error) {
     console.error('Error getting Expo push token:', error);
     return null;
   }
   ```

## Testing

### Casos de Prueba

1. **Primera Experiencia**
   - Banner aparece en primera pregunta
   - Permisos se solicitan al tocar banner
   - Token se envía al backend si se conceden

2. **Rechazo de Permisos**
   - Banner no aparece más después del rechazo
   - Estado se guarda en AsyncStorage

3. **Descarte de Banner**
   - Banner no aparece por 8 horas después del descarte
   - Reaparece después del cooldown

4. **Cambio de Idioma**
   - Nuevo idioma se envía en próximo registro
   - Token existente se actualiza con nuevo idioma

### Comandos de Testing
```javascript
// Limpiar AsyncStorage para testing
await AsyncStorage.multiRemove([
  'taptrap_push_token',
  'taptrap_push_permission_denied',
  'taptrap_push_banner_dismissed'
]);

// Verificar estado actual
const token = await AsyncStorage.getItem('taptrap_push_token');
const denied = await AsyncStorage.getItem('taptrap_push_permission_denied');
const dismissed = await AsyncStorage.getItem('taptrap_push_banner_dismissed');
console.log({ token, denied, dismissed });
```

## Mejores Prácticas

1. **Timing del Banner**: Mostrar solo en primera pregunta para no interrumpir el flujo
2. **Respeto al Usuario**: No mostrar más si rechaza permisos
3. **Cooldown Apropiado**: 8 horas es suficiente sin ser molesto
4. **Fallbacks**: Manejar errores graciosamente sin crashear
5. **Idioma Dinámico**: Actualizar idioma cuando usuario cambia configuración
