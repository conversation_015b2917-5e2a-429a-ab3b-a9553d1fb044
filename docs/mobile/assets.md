# TapTrap Mobile App Assets

## Overview

TapTrap uses various assets to create an engaging and visually appealing user experience. This document outlines the different types of assets used in the app and how they are implemented.

## Asset Directory Structure

```
assets/
├── icons/              # UI icons
│   ├── settings/       # Settings screen icons
│   └── ...             # Other icon categories
├── images/             # Game images
│   ├── casual_image.png
│   ├── friends_image.png
│   ├── partners_image.png
│   ├── adults_image.png
│   └── ...
├── lottie/             # Lottie animation files
│   ├── logo_intro.json
│   ├── mascot_countdown.json
│   ├── mascot_in_circle.json
│   └── ...
├── redesign/           # SVG assets for UI
│   ├── logo.svg
│   ├── icon_questions.svg
│   ├── icon_dares.svg
│   ├── icon_selected.svg
│   ├── icon_locked.svg
│   ├── mascot_in_circle.svg
│   ├── mascot_dialog.svg
│   ├── mascot_dialog_es.svg
│   └── ...
└── sounds/             # Game sound effects
    ├── countdown.mp3
    ├── selection.mp3
    ├── tapEffect1.mp3
    ├── tapEffect2.mp3
    ├── tapEffect3.mp3
    └── ...
```

## Image Assets

### Category Images

The app uses images to represent different categories in the game:

```typescript
// Define all the images that need to be preloaded
const IMAGES_TO_PRELOAD = [
  require('@/assets/images/decoration_background.png'),
  require('@/assets/images/questions_image.png'),
  require('@/assets/images/challenges_image.png'),
  
  // Category images - Questions
  require('@/assets/images/casual_image.png'),
  require('@/assets/images/friends_image.png'),
  require('@/assets/images/partners_image.png'),
  require('@/assets/images/adults_image.png'),
  
  // Category images - Challenges
  require('@/assets/images/chill_image.png'),
  require('@/assets/images/daring_image.png'),
  require('@/assets/images/wild_image.png'),
  require('@/assets/images/nolimits_image.png')
];
```

### Image Preloading

Images are preloaded to ensure smooth performance:

```typescript
useEffect(() => {
  const preloadImages = async () => {
    try {
      const imageAssets = IMAGES_TO_PRELOAD.map(image => Asset.fromModule(image).downloadAsync());
      await Promise.all(imageAssets);
      setImagesLoaded(true);
    } catch (error) {
      console.error('Error preloading images:', error);
      // Continue anyway to not block the app
      setImagesLoaded(true);
    }
  };

  preloadImages();
}, []);
```

## SVG Assets

The app uses SVG assets for UI elements, which are imported as React components using a SVG transformer:

```typescript
import Logo from '@/assets/redesign/logo.svg';
import BottomIllustration from '@/assets/redesign/dec_main_menu.svg';
import QuestionsIcon from '@/assets/redesign/icon_questions.svg';
import DaresIcon from '@/assets/redesign/icon_dares.svg';
import SelectedIcon from '@/assets/redesign/icon_selected.svg';
import SettingsIcon from '@/assets/redesign/icon_control_setting.svg';
import ArrowRight from '@/assets/redesign/icon_control_chevronRight.svg';
```

### Usage in Components

SVG components are used directly in the JSX:

```typescript
<TouchableOpacity 
  onPress={() => toggleModeSelection('questions')}
  style={styles.button}
>
  <Text style={styles.buttonText}>{t('questionsIndex')}</Text>
  <QuestionsIcon style={styles.buttomImage}/>
</TouchableOpacity>
```

### Localized SVG Assets

Some SVG assets have language-specific versions:

```typescript
import MascotDialogEn from '@/assets/redesign/mascot_dialog.svg';
import MascotDialogEs from '@/assets/redesign/mascot_dialog_es.svg';

const LocalizedMascotDialog = () => {
  const { language } = useLanguage();
  
  return language === 'es' ? (
    <MascotDialogEs width={300} height={120} style={styles.mascotDialog} />
  ) : (
    <MascotDialogEn width={300} height={120} style={styles.mascotDialog} />
  );
};
```

## Lottie Animations

The app uses Lottie animations for dynamic and engaging UI elements:

```typescript
import LottieView from 'lottie-react-native';

// Logo animation
<LottieView
  autoPlay
  loop={false}
  speed={2}
  style={{
    width: 409, 
    height: 231,
    transform: [{translateY: -72}],
  }}
  source={require('../assets/lottie/logo_intro.json')}
/>

// Mascot animation
<LottieView
  ref={animation}
  autoPlay
  loop
  speed={0.8}
  style={styles.mascotLoop}
  source={require('../assets/lottie/mascot_in_circle.json')}
/>

// Countdown animation
<LottieView
  ref={animation}
  speed={2}
  style={{
    width: 200,
    height: 200,
  }}
  source={require('../assets/lottie/mascot_countdown.json')}
/>
```

### Animation Control

Lottie animations can be controlled using refs:

```typescript
const animation = useRef<LottieView>(null);

// Play animation
animation.current?.play();

// Reset animation
animation.current?.reset();

// Play specific segment
animation.current?.play(30, 60);
```

## Sound Assets

The app uses sound effects to enhance the user experience:

```typescript
// Sound file mapping
const soundFiles = {
  tapEffect1: require('@/assets/sounds/tapEffect1.mp3'),
  tapEffect2: require('@/assets/sounds/tapEffect2.mp3'),
  tapEffect3: require('@/assets/sounds/tapEffect3.mp3'),
  countdown: require('@/assets/sounds/countdown.mp3'),
  selection: require('@/assets/sounds/selection.mp3'),
};

// Sound player hook
export const useSound = () => {
  const { isSoundOn } = useAppSettings();
  const soundsRef = useRef<Record<string, Audio.Sound>>({});
  
  // Load sound
  const loadSound = async (name: SoundName): Promise<Audio.Sound> => {
    if (soundsRef.current[name]) {
      return soundsRef.current[name];
    }
    
    const { sound } = await Audio.Sound.createAsync(soundFiles[name]);
    soundsRef.current[name] = sound;
    return sound;
  };
  
  // Play sound
  const playSound = async (name: SoundName, volume = 1.0) => {
    if (!isSoundOn) return;
    
    try {
      const sound = await loadSound(name);
      await sound.setVolumeAsync(volume);
      await sound.playFromPositionAsync(0);
    } catch (error) {
      console.warn(`Error playing sound ${name}:`, error);
    }
  };
  
  // Clean up sounds on unmount
  useEffect(() => {
    return () => {
      Object.values(soundsRef.current).forEach(sound => {
        sound.unloadAsync();
      });
    };
  }, []);
  
  return { playSound };
};
```

### Usage in Components

```typescript
import { useSound } from '@/components/game/playSound';

function MyComponent() {
  const { playSound } = useSound();
  
  const handlePress = async () => {
    await playSound('tapEffect1');
    // Do something after sound plays
  };
  
  return (
    <Button 
      title="Press Me" 
      onPress={handlePress} 
    />
  );
}
```

## Bundled Game Content

The app includes a bundled version of the game content for offline use:

```typescript
// In contentService.ts
// Try to load bundled content if no cached or API content
if (!cachedContent && !apiContent) {
  try {
    const bundledContent = require('@/assets/game_content.json');
    return bundledContent;
  } catch (error) {
    console.warn('Error loading bundled content:', error);
    return null;
  }
}
```

## Asset Management Best Practices

1. **Optimize Assets**: Compress images and sounds to reduce app size
2. **Use SVG for UI**: SVG assets scale well and reduce bundle size
3. **Preload Critical Assets**: Preload images and sounds needed for immediate use
4. **Lazy Load Non-Critical Assets**: Load assets only when needed
5. **Use Asset Caching**: Cache downloaded assets for offline use
6. **Provide Fallbacks**: Have fallback assets for when primary assets fail to load
7. **Version Assets**: Use versioning for assets that may change with updates
8. **Test on Different Devices**: Ensure assets look good on various screen sizes

## Adding New Assets

When adding new assets to the app:

1. Place the asset in the appropriate directory
2. Import the asset in the component where it will be used
3. Add preloading code if the asset is critical for performance
4. For sounds, add the sound file to the soundFiles mapping
5. For images that need to be preloaded, add to the IMAGES_TO_PRELOAD array
6. Test the asset on different devices to ensure it displays correctly
