# Ad-Based Premium Access - Implementation Guide

## Quick Start

### 1. Install Dependencies

```bash
npm install react-native-google-mobile-ads @react-native-community/netinfo
```

### 2. Add Environment Variables

```env
# .env
ADMOB_REWARDED_AD_UNIT_ID_ANDROID=your-android-ad-unit-id
ADMOB_REWARDED_AD_UNIT_ID_IOS=your-ios-ad-unit-id
```

### 3. Update App Configuration

```javascript
// app.config.js
export default {
  expo: {
    extra: {
      admobRewardedAdUnitIdAndroid: process.env.ADMOB_REWARDED_AD_UNIT_ID_ANDROID,
      admobRewardedAdUnitIdIos: process.env.ADMOB_REWARDED_AD_UNIT_ID_IOS,
    }
  }
}
```

### 4. Add Context Provider

```tsx
// App.tsx
import { AdBasedAccessProvider } from './context/AdBasedAccessContext';

export default function App() {
  return (
    <AppSettingsProvider>
      <AdBasedAccessProvider>
        <LanguageProvider>
          {/* Your app components */}
        </LanguageProvider>
      </AdBasedAccessProvider>
    </AppSettingsProvider>
  );
}
```

## Core Service Usage

### AdMob Service

```typescript
import { adMobService } from '@/services/adMobService';

// Check internet connectivity
const isOnline = await adMobService.isInternetAvailable();

// Load and show an ad
try {
  const ad = await adMobService.getAd();
  if (ad) {
    const wasWatched = await adMobService.showInterstitialAd(ad);
    if (wasWatched) {
      // User successfully watched the ad
      console.log('Ad watched successfully');
    }
  }
} catch (error) {
  console.error('Failed to show ad:', error);
}

// Preload ads for better UX
await adMobService.preloadAds();
```

### Persistent Ad Storage Service

```typescript
import { sessionStorageService } from '@/services/sessionStorageService';

// Initialize service (automatically called by context)
await sessionStorageService.initialize();

// Check current state
const hasAccess = sessionStorageService.hasTemporaryPremiumAccess();
const needsAds = sessionStorageService.needsToWatchAdsAgain();

// Update state (automatically persisted)
sessionStorageService.incrementAdsWatched();
sessionStorageService.incrementQuestionsAnswered();

// Free content management (shared counter for all premium categories)
sessionStorageService.decrementFreeContent('spicy_questions');
const remaining = sessionStorageService.getRemainingFreeContent('spicy_questions'); // Returns shared counter

// All premium categories return the same value
const spicyRemaining = sessionStorageService.getRemainingFreeContent('spicy_questions');
const noLimitsRemaining = sessionStorageService.getRemainingFreeContent('no_limits_questions');
// spicyRemaining === noLimitsRemaining (both use shared counter)

// Reset when needed
sessionStorageService.resetAdsWatchedCount();
sessionStorageService.completeAdWatchingCycle();

// Debug information
console.log(sessionStorageService.getDebugInfo());
```

## Context Usage

### Using the Hook

```tsx
import { useAdBasedAccess } from '@/context/AdBasedAccessContext';

function MyComponent() {
  const {
    hasTemporaryPremiumAccess,
    needsToWatchAdsAgain,
    isInternetAvailable,
    incrementAdsWatched,
    incrementQuestionsAnswered,
    adBasedAccessState,
    freeContentCounters,
    decrementFreeContent,
    getRemainingFreeContent
  } = useAdBasedAccess();

  // Check if user has any premium access
  const { isPremiumUnlocked } = useAppSettings();
  const hasAnyPremiumAccess = isPremiumUnlocked || hasTemporaryPremiumAccess;

  // Get remaining free content for display (shared counter)
  const spicyRemaining = getRemainingFreeContent('spicy_questions');
  const noLimitsRemaining = getRemainingFreeContent('no_limits_questions');
  // spicyRemaining === noLimitsRemaining (both use shared counter)

  // When premium content is consumed
  const handlePremiumContentConsumed = (category) => {
    decrementFreeContent(category); // Decrements shared counter regardless of category
  };

  return (
    <View>
      {hasAnyPremiumAccess ? (
        <PremiumContent />
      ) : (
        <FreeContent />
      )}
      {hasTemporaryPremiumAccess && (
        <View>
          <Text>{spicyRemaining} free spicy questions remaining</Text>
          <Text>{noLimitsRemaining} free no-limits questions remaining</Text>
          {/* Both show the same count since they share the counter */}
        </View>
      )}
    </View>
  );
}
```

## Component Integration

### Category Selection

```tsx
// SelectCategoriesScreen.tsx
const toggleCategory = (category: string) => {
  const isPremium = category === 'spicy_questions' || category === 'no_limits_questions';

  if (isPremium && !hasAnyPremiumAccess) {
    // Show UnlockDialog instead of direct paywall
    setShowUnlockDialog(true);
    return;
  }

  // Normal category selection logic
  // ...
};

return (
  <View>
    {/* Category buttons */}

    {showUnlockDialog && (
      <UnlockDialog
        isSequential={false}
        onClose={() => setShowUnlockDialog(false)}
      />
    )}
  </View>
);
```

### Game Flow Integration

```tsx
// TouchGameScreen.tsx
const handleNextQuestion = () => {
  // Increment question count
  setQuestionCount(prevCount => {
    const newCount = prevCount + 1;

    // Check for ad requirement in premium categories
    const isPremiumCategory =
      nextQuestion.category === 'spicy_questions' ||
      nextQuestion.category === 'no_limits_questions';

    if (isPremiumCategory && hasTemporaryPremiumAccess && !isPremiumUnlocked) {
      incrementQuestionsAnswered();

      if (needsToWatchAdsAgain) {
        // Show UnlockDialog in sequential mode
        setTimeout(() => {
          setShowUnlockDialog(true);
        }, 1000);
      }
    }

    return newCount;
  });
};

return (
  <View>
    {/* Game content */}

    {showUnlockDialog && (
      <UnlockDialog
        isSequential={true}
        onClose={() => {
          setShowUnlockDialog(false);
          resetQuestionsAnsweredCount();
        }}
      />
    )}
  </View>
);
```

## Custom Components

### UnlockDialog Usage

```tsx
import UnlockDialog from '@/components/ads/UnlockDialog';

function MyScreen() {
  const [showUnlockDialog, setShowUnlockDialog] = useState(false);

  return (
    <View>
      <TouchableOpacity onPress={() => setShowUnlockDialog(true)}>
        <Text>Unlock Premium Content</Text>
      </TouchableOpacity>

      {showUnlockDialog && (
        <UnlockDialog
          isSequential={false} // true for recurring ads during gameplay
          onClose={() => setShowUnlockDialog(false)}
        />
      )}
    </View>
  );
}
```

### AdsCounter Component

```tsx
import AdsCounter from '@/components/ads/AdsCounter';

function WatchAdsScreen() {
  const [watchedCount, setWatchedCount] = useState(0);

  return (
    <View>
      <AdsCounter
        watchedCount={watchedCount}
        totalCount={5}
        style={{ marginBottom: 20 }}
      />
    </View>
  );
}
```

## State Management Patterns

### Checking Premium Access

```tsx
// Always check both subscription and ad-based access
const { isPremiumUnlocked } = useAppSettings();
const { hasTemporaryPremiumAccess } = useAdBasedAccess();
const hasAnyPremiumAccess = isPremiumUnlocked || hasTemporaryPremiumAccess;

// Use in conditional rendering
{hasAnyPremiumAccess ? <PremiumFeature /> : <LockedFeature />}
```

### Handling Ad Completion

```tsx
const handleAdWatched = () => {
  // Update ad count
  incrementAdsWatched();

  // Check if all ads are watched
  if (adBasedAccessState.adsWatchedCount >= 5) {
    // User now has temporary premium access
    console.log('Premium access granted!');

    // Navigate back or show success message
    navigation.goBack();
  }
};
```

### Managing Question Count

```tsx
const handleQuestionAnswered = () => {
  // Only track for premium categories with ad-based access
  const isPremiumCategory = /* check category */;

  if (isPremiumCategory && hasTemporaryPremiumAccess && !isPremiumUnlocked) {
    incrementQuestionsAnswered();

    // Check if user needs to watch ads again
    if (needsToWatchAdsAgain) {
      showAdRequirementDialog();
    }
  }
};
```

## Error Handling

### Internet Connectivity

```tsx
const { isInternetAvailable, checkInternetConnectivity } = useAdBasedAccess();

const handleWatchAds = async () => {
  if (!isInternetAvailable) {
    Alert.alert(
      'No Internet',
      'Please check your internet connection and try again.'
    );
    return;
  }

  // Proceed with ad watching
};

// Check connectivity when component mounts
useEffect(() => {
  checkInternetConnectivity();
}, []);
```

### Ad Loading Failures

```tsx
const showAd = async () => {
  try {
    const ad = await adMobService.getAd();

    if (!ad) {
      Alert.alert(
        'Ad Load Error',
        'Failed to load ad. Please try again.'
      );
      return;
    }

    const watched = await adMobService.showInterstitialAd(ad);
    if (watched) {
      handleAdWatched();
    }
  } catch (error) {
    console.error('Ad error:', error);
    Alert.alert(
      'Ad Error',
      'Something went wrong. Please try again.'
    );
  }
};
```

## Testing and Debugging

### Debug Information

```tsx
const { getDebugInfo } = useAdBasedAccess();

// Log current state
console.log(getDebugInfo());

// Example output:
// AdBasedAccess Debug:
// - Has watched ads: true
// - Ads watched: 5/5
// - Questions since ads: 3
// - Last ad time: 2:30:45 PM
// - Has temp access: true
// - Needs ads again: false
```

### Development Testing

```typescript
// In development, AdMob service uses test ad unit IDs
const getAdUnitId = () => {
  if (__DEV__) {
    return TestIds.INTERSTITIAL; // AdMob test ID
  }

  return Platform.OS === 'ios'
    ? ADMOB_REWARDED_AD_UNIT_ID_IOS
    : ADMOB_REWARDED_AD_UNIT_ID_ANDROID;
};
```

## Best Practices

### Performance Optimization

```tsx
// Preload ads when user has temporary access
useEffect(() => {
  if (hasTemporaryPremiumAccess && isInternetAvailable) {
    adMobService.preloadAds().catch(console.error);
  }
}, [hasTemporaryPremiumAccess, isInternetAvailable]);

// Clear ads when app goes to background
useEffect(() => {
  const handleAppStateChange = (nextAppState) => {
    if (nextAppState === 'background') {
      adMobService.clearPreloadedAds();
    }
  };

  const subscription = AppState.addEventListener('change', handleAppStateChange);
  return () => subscription.remove();
}, []);
```

### User Experience

```tsx
// Show loading states
const [isLoadingAd, setIsLoadingAd] = useState(false);

const watchAd = async () => {
  setIsLoadingAd(true);
  try {
    await showAd();
  } finally {
    setIsLoadingAd(false);
  }
};

// Provide haptic feedback
const { isHapticsOn } = useAppSettings();

const handleAdSuccess = () => {
  if (isHapticsOn) {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }
};
```
