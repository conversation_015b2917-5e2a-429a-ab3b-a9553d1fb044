# Ad-Based Premium Access - Troubleshooting Guide

## Common Issues and Solutions

### 1. Ads Not Loading

#### Symptoms
- "Ad Load Error" alert appears
- "Watch Ads" button is disabled
- Consol<PERSON> shows ad loading failures

#### Possible Causes & Solutions

**Internet Connectivity**
```typescript
// Check connectivity
const { isInternetAvailable, checkInternetConnectivity } = useAdBasedAccess();

// Force connectivity check
await checkInternetConnectivity();
console.log('Internet available:', isInternetAvailable);
```

**Invalid Ad Unit IDs**
```env
# Verify .env file has correct ad unit IDs
ADMOB_REWARDED_AD_UNIT_ID_ANDROID=ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX
ADMOB_REWARDED_AD_UNIT_ID_IOS=ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX
```

**AdMob Configuration**
```javascript
// Check app.config.js
export default {
  expo: {
    extra: {
      admobRewardedAdUnitIdAndroid: process.env.ADMOB_REWARDED_AD_UNIT_ID_ANDROID,
      admobRewardedAdUnitIdIos: process.env.ADMOB_REWARDED_AD_UNIT_ID_IOS,
    }
  }
}
```

**Development vs Production**
```typescript
// In development, test IDs should be used
const getAdUnitId = () => {
  if (__DEV__) {
    return TestIds.INTERSTITIAL; // This should work in development
  }
  // Production IDs for release builds
};
```

### 2. Premium Access Not Working

#### Symptoms
- User watches ads but premium content remains locked
- Categories still show lock icons after ad completion
- hasTemporaryPremiumAccess returns false

#### Debugging Steps

**Check Ad Completion**
```typescript
// Verify ads are being counted
const { adBasedAccessState } = useAdBasedAccess();
console.log('Ads watched:', adBasedAccessState.adsWatchedCount);
console.log('Has watched ads:', adBasedAccessState.hasWatchedAds);
```

**Verify State Updates**
```typescript
// Check if incrementAdsWatched is being called
const handleAdWatched = () => {
  console.log('Before increment:', adBasedAccessState.adsWatchedCount);
  incrementAdsWatched();
  console.log('After increment:', adBasedAccessState.adsWatchedCount);
};
```

**Check Premium Access Logic**
```typescript
// Ensure both access types are checked
const { isPremiumUnlocked } = useAppSettings();
const { hasTemporaryPremiumAccess } = useAdBasedAccess();
const hasAnyPremiumAccess = isPremiumUnlocked || hasTemporaryPremiumAccess;

console.log('Subscription premium:', isPremiumUnlocked);
console.log('Ad-based premium:', hasTemporaryPremiumAccess);
console.log('Any premium access:', hasAnyPremiumAccess);
```

### 3. Context Provider Issues

#### Symptoms
- "useAdBasedAccess must be used within an AdBasedAccessProvider" error
- Context values are undefined
- State updates not propagating

#### Solutions

**Check Provider Hierarchy**
```tsx
// Correct order in App.tsx
<AppSettingsProvider>
  <AdBasedAccessProvider>  {/* Must be here */}
    <LanguageProvider>
      <GameProvider>
        {/* App components */}
      </GameProvider>
    </LanguageProvider>
  </AdBasedAccessProvider>
</AppSettingsProvider>
```

**Verify Hook Usage**
```tsx
// Only use hook inside provider
function MyComponent() {
  const adAccess = useAdBasedAccess(); // This should work

  // Don't use outside provider or in provider itself
}
```

### 4. Session Storage Not Resetting

#### Symptoms
- Premium access persists after app restart
- Ad count doesn't reset when app closes
- State inconsistencies

#### Solutions

**Verify Session-Only Storage**
```typescript
// sessionStorageService should NOT use AsyncStorage
// It should only use in-memory storage that resets on app restart

class SessionStorageService {
  private adBasedAccessState: AdBasedAccessState = {
    // This resets on app restart - correct behavior
  };

  // No AsyncStorage.setItem calls should be here
}
```

**Check App State Handling**
```typescript
// Verify app state changes are handled
useEffect(() => {
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (nextAppState === 'background') {
      // Clear preloaded ads but keep session state
      adMobService.clearPreloadedAds();
    }
    // Session state should naturally reset on app restart
  };
}, []);
```

### 5. Ad Frequency Issues

#### Symptoms
- UnlockDialog appears too frequently
- UnlockDialog doesn't appear when expected
- Question count not tracking correctly

#### Debugging

**Check Question Counting**
```typescript
// Verify question increment logic
const handleNextQuestion = () => {
  const isPremiumCategory =
    nextQuestion.category === 'spicy_questions' ||
    nextQuestion.category === 'no_limits_questions';

  console.log('Is premium category:', isPremiumCategory);
  console.log('Has temp access:', hasTemporaryPremiumAccess);
  console.log('Is premium unlocked:', isPremiumUnlocked);

  if (isPremiumCategory && hasTemporaryPremiumAccess && !isPremiumUnlocked) {
    incrementQuestionsAnswered();
    console.log('Questions answered:', adBasedAccessState.questionsAnsweredSinceAds);

    if (needsToWatchAdsAgain) {
      console.log('Showing ad requirement dialog');
      setShowUnlockDialog(true);
    }
  }
};
```

**Verify Reset Logic**
```typescript
// Check if questions are reset after ad completion
const handleAdCycleComplete = () => {
  resetQuestionsAnsweredCount();
  console.log('Questions reset to:', adBasedAccessState.questionsAnsweredSinceAds);
};
```

### 6. Platform-Specific Issues

#### iOS Issues

**RevenueCat Modal Not Showing**
```tsx
// Check iOS modal implementation
{showPaywall && Platform.OS === 'ios' && (
  <RevenueCatUI.Paywall
    options={{
      fontFamily: "Melindya",
      displayCloseButton: true,
    }}
    onDismiss={() => setShowPaywall(false)}
    onPurchaseCompleted={() => {
      unlockPremium();
      setShowPaywall(false);
    }}
  />
)}
```

#### Android Issues

**RevenueCat presentPaywall Errors**
```typescript
// Handle Android paywall errors
try {
  const result = await RevenueCatUI.presentPaywall({
    fontFamily: "Melindya",
  });
  console.log('Paywall result:', result);
} catch (paywallError) {
  console.error('Android paywall error:', paywallError);
  // Fallback to modal approach if needed
}
```

## Debugging Tools

### Debug Information

```typescript
// Get comprehensive debug info
const { getDebugInfo } = useAdBasedAccess();
console.log(getDebugInfo());

// Manual state inspection
const { adBasedAccessState } = useAdBasedAccess();
console.log('Full state:', JSON.stringify(adBasedAccessState, null, 2));
```

### Console Logging

```typescript
// Enable detailed logging in development
if (__DEV__) {
  // AdMob service logs
  console.log('AdMob: Loading ad...');
  console.log('AdMob: Ad loaded successfully');

  // Session storage logs
  console.log('SessionStorage: Incrementing ads watched');
  console.log('SessionStorage: Current state:', state);

  // Context logs
  console.log('AdBasedAccess: State updated');
}
```

### Network Debugging

```typescript
// Test connectivity manually
import NetInfo from '@react-native-community/netinfo';

const testConnectivity = async () => {
  const networkState = await NetInfo.fetch();
  console.log('Network state:', networkState);
  console.log('Is connected:', networkState.isConnected);
  console.log('Connection type:', networkState.type);
};
```

## Performance Issues

### Memory Leaks

**Check Event Listeners**
```typescript
// Ensure proper cleanup
useEffect(() => {
  const subscription = AppState.addEventListener('change', handleAppStateChange);

  return () => {
    subscription.remove(); // Important: cleanup
  };
}, []);
```

**Ad Preloading**
```typescript
// Monitor preloaded ads
console.log('Preloaded ads count:', adMobService.preloadedAds.length);

// Clear when needed
adMobService.clearPreloadedAds();
```

### State Update Performance

```typescript
// Avoid unnecessary re-renders
const memoizedValue = useMemo(() => {
  return isPremiumUnlocked || hasTemporaryPremiumAccess;
}, [isPremiumUnlocked, hasTemporaryPremiumAccess]);
```

## Testing Checklist

### Development Testing

- [ ] Test ads load with test ad unit IDs
- [ ] Verify internet connectivity checks work
- [ ] Test offline behavior (ads disabled)
- [ ] Check state resets on app restart
- [ ] Verify question counting accuracy
- [ ] Test both iOS and Android platforms

### Production Testing

- [ ] Replace test ad unit IDs with production IDs
- [ ] Test with real ads
- [ ] Monitor ad fill rates
- [ ] Check revenue tracking
- [ ] Verify user experience flow
- [ ] Test edge cases (app backgrounding, network changes)

### User Acceptance Testing

- [ ] Free user can unlock premium by watching 4 ads
- [ ] Premium access works for current session only
- [ ] Ads required every 6 questions/dares during premium gameplay
- [ ] "Get Premium" button opens subscription paywall
- [ ] Offline users see appropriate messaging
- [ ] Success feedback after completing ads

## Support and Monitoring

### Error Reporting

```typescript
// Add error tracking
const handleAdError = (error: Error) => {
  console.error('Ad error:', error);

  // Report to crash analytics
  // crashlytics().recordError(error);

  // Show user-friendly message
  Alert.alert('Ad Error', 'Please try again later.');
};
```

### Analytics

```typescript
// Track ad events
const trackAdEvent = (event: string, data?: any) => {
  console.log(`Ad Event: ${event}`, data);

  // Send to analytics
  // analytics().logEvent(event, data);
};

// Usage
trackAdEvent('ad_requested');
trackAdEvent('ad_completed', { adIndex: 1 });
trackAdEvent('premium_unlocked_via_ads');
```
