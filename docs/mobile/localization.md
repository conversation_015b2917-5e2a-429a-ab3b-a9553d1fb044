# TapTrap Mobile App Localization

## Overview

TapTrap supports multiple languages, with English, Spanish, and Dominican Spanish implemented in the current version. The localization system handles both UI text and game content (questions and challenges).

## Localization Architecture

The app uses a custom localization system built around React Context, which provides:

1. Language detection based on device settings
2. Manual language switching
3. Persistent language preferences
4. Translation function for UI text
5. Localized content for game questions and challenges

## i18n Service

**File**: `services/i18nService.tsx`

### Language Context

The `LanguageContext` provides the current language, a function to change the language, and a translation function:

```typescript
interface LanguageContextProps {
  language: LanguageType;
  setAppLanguage: (lang: LanguageType) => void;
  t: (key: keyof typeof translations) => string;
}

export const LanguageContext = createContext<LanguageContextProps>({
  language: 'en',
  setAppLanguage: () => {},
  t: (key) => translations[key]?.en ?? key,
});
```

### Language Provider

The `LanguageProvider` component manages the language state and provides it to the app:

```typescript
export const LanguageProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<LanguageType>('en');

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const stored = await AsyncStorage.getItem('appLanguage');
        if (stored === 'en' || stored === 'es' || stored === 'dom') {
          setLanguage(stored as LanguageType);
        } else {
          // Use the getDeviceLanguage helper function
          const deviceLanguage = getDeviceLanguage();
          setLanguage(deviceLanguage);
        }
      } catch {
        setLanguage('en');
      }
    };
    loadLanguage();
  }, []);

  const setAppLanguage = async (lang: LanguageType) => {
    setLanguage(lang);
    try {
      await AsyncStorage.setItem('appLanguage', lang);
    } catch (err) {
      console.warn('Failed to store language', err);
    }
  };

  const t = (key: keyof typeof translations): string =>
    translations[key]?.[language] ?? translations[key]?.en ?? key;

  return (
    <LanguageContext.Provider value={{ language, setAppLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};
```

### Translation Dictionary

The app uses a simple key-value dictionary for translations:

```typescript
const translations = {
  pickTheMode: {
    en: 'Choose a Game Mode',
    es: '¿Qué vamos a jugar?',
    dom: '¿Qué vamo\' a jugal?'
  },
  questionsIndex: {
    en: 'Questions',
    es: 'Preguntas',
    dom: 'Pregunta\''
  },
  challengesIndex: {
    en: 'Dares',
    es: 'Retos',
    dom: 'Reto\''
  },
  pickTheCategory: {
    en: 'Pick the category',
    es: 'Elige la categoría',
    dom: 'Elige la categoría'
  },
  casual: {
    en: 'Casual',
    es: 'Casual',
    dom: 'Casual'
  },
  friends: {
    en: 'Daring',
    es: 'Atrevido',
    dom: 'Atrevido'
  },
  partners: {
    en: 'Spicy',
    es: 'Picante',
    dom: 'Picante'
  },
  adults: {
    en: 'Adults',
    es: 'Adultos',
    dom: 'Adulto\''
  },
  // Many more translations...
};
```

### Usage in Components

Components can access the language context using the `useLanguage` hook:

```typescript
import { useLanguage } from '@/services/i18nService';

function MyComponent() {
  const { language, setAppLanguage, t } = useLanguage();

  return (
    <View>
      <Text>{t('welcomeMessage')}</Text>
      <Button
        title={language === 'en' ? "Switch to Spanish" : "Switch to English"}
        onPress={() => setAppLanguage(language === 'en' ? 'es' : 'en')}
      />
    </View>
  );
}
```

## Game Content Localization

Game content (questions and challenges) is stored with translations for each supported language:

```typescript
interface Question {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
}

interface Challenge {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
}
```

### Getting Localized Content

The `contentService.ts` file provides a helper function to get the appropriate text based on the current language:

```typescript
export const getLocalizedText = (item: { text_en: string; text_es: string; text_dom: string }): string => {
  const language = getDeviceLanguage();
  if (language === 'es') return item.text_es;
  if (language === 'dom') return item.text_dom;
  return item.text_en; // Default to English
};
```

The `GameContext` also provides a similar function that uses the current language from the language context:

```typescript
const getLocalizedTextForItem = (item: Question | Challenge | null): string => {
  if (!item) return '';
  if (language === 'es') return item.text_es;
  if (language === 'dom') return item.text_dom;
  return item.text_en; // Default to English
};
```

## Language Selection UI

The app provides a language selection interface in the Settings screen:

```typescript
// In SettingsScreen.tsx
const handleSelectLanguage = async (lang: 'en' | 'es' | 'dom') => {
  await setAppLanguage(lang);
};

// Language selection modal
<Modal
  visible={selectedSetting === 'language'}
  animationType="slide"
  transparent={true}
>
  <View style={styles.modalContainer}>
    <View style={styles.modalContent}>
      <Text style={[typography.heading3, styles.modalTitle]}>{t('language')}</Text>

      <TouchableOpacity
        style={[styles.languageOption, language === 'en' && styles.languageOptionSelected]}
        onPress={() => handleSelectLanguage('en')}
      >
        <EnglishFlag width={32} height={32} />
        <Text style={styles.languageText}>{t('languageEnglish')}</Text>
        {language === 'en' && <CheckMarkIcon width={24} height={24} />}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.languageOption, language === 'es' && styles.languageOptionSelected]}
        onPress={() => handleSelectLanguage('es')}
      >
        <SpanishFlag width={32} height={32} />
        <Text style={styles.languageText}>{t('languageSpanish')}</Text>
        {language === 'es' && <CheckMarkIcon width={24} height={24} />}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.languageOption, language === 'dom' && styles.languageOptionSelected]}
        onPress={() => handleSelectLanguage('dom')}
      >
        <DomFlag width={32} height={32} />
        <Text style={styles.languageText}>{t('languageDominican')}</Text>
        {language === 'dom' && <CheckMarkIcon width={24} height={24} />}
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.closeButton}
        onPress={() => setSelectedSetting(null)}
      >
        <CloseIcon width={24} height={24} />
      </TouchableOpacity>
    </View>
  </View>
</Modal>
```

## Localized Assets

Some assets, like the mascot dialog, have language-specific versions:

```typescript
// In TouchGameScreen.tsx
const LocalizedMascotDialog = () => {
  const { language } = useLanguage();

  if (language === 'es') {
    return <MascotDialogEs width={300} height={120} style={styles.mascotDialog} />;
  } else if (language === 'dom') {
    return <MascotDialogDom width={300} height={120} style={styles.mascotDialog} />;
  } else {
    return <MascotDialogEn width={300} height={120} style={styles.mascotDialog} />;
  }
};
```

## Adding a New Language

To add support for a new language:

1. **Update Language Type**:
   ```typescript
   type LanguageType = 'en' | 'es' | 'fr'; // Add new language code
   ```

2. **Add Translations**:
   ```typescript
   const translations = {
     welcomeMessage: {
       en: 'Welcome',
       es: 'Bienvenido',
       fr: 'Bienvenue' // Add new translation
     },
     // Add translations for all keys
   };
   ```

3. **Update Content Model**:
   ```typescript
   interface Question {
     id: string;
     text_en: string;
     text_es: string;
     text_fr: string; // Add new language field
   }
   ```

4. **Update Backend**:
   - Add the new language field to the content model in the backend
   - Update the admin interface to support editing the new language

5. **Update Language Selection UI**:
   - Add the new language option to the language selection modal
   - Add a flag icon for the new language

6. **Create Localized Assets**:
   - Create language-specific versions of any assets that contain text

## Dominicano Language Implementation

The TapTrap app has been updated to support Dominican Spanish (Dominicano) as a language option. This implementation includes:

### Content Model Updates

The Question and Challenge interfaces have been updated to include the `text_dom` property:

```typescript
interface Question {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
}

interface Challenge {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
}
```

### Fallback Mechanism

In the TouchGameScreen.tsx file, a fallback mechanism has been implemented to handle cases where content might not have the `text_dom` property yet:

```typescript
// In createPoolFromContent function
questionsPool.push(
  ...content.questions[category].map((item: any) => ({
    ...item,
    // Add default text_dom if it doesn't exist in the item
    text_dom: item.text_dom || item.text_es || '',
    type: 'question',
    category: category
  }))
);
```

This ensures that even if the `text_dom` property is missing in some content items, the app will fall back to using the Spanish text (`text_es`) or an empty string as a last resort.

### Language Selection

The language selection in the app now includes Dominicano as an option, with a dedicated flag icon and localized text:

```typescript
<TouchableOpacity
  style={[styles.languageOption, language === 'dom' && styles.languageOptionSelected]}
  onPress={() => handleSelectLanguage('dom')}
>
  <DomFlag width={32} height={32} />
  <Text style={styles.languageText}>{t('languageDominican')}</Text>
  {language === 'dom' && <CheckMarkIcon width={24} height={24} />}
</TouchableOpacity>
```

### Localized Content Display

The app now checks for the 'dom' language setting when displaying content:

```typescript
const getLocalizedTextForItem = (item: Question | Challenge | null): string => {
  if (!item) return '';
  if (language === 'es') return item.text_es;
  if (language === 'dom') return item.text_dom;
  return item.text_en; // Default to English
};
```

## Best Practices

1. **Use Translation Keys**: Always use the `t()` function with keys instead of hardcoding text
2. **Keep Keys Organized**: Group related keys together in the translations object
3. **Provide Fallbacks**: Always include English translations as fallbacks
4. **Test All Languages**: Test the app thoroughly in all supported languages
5. **Consider Text Length**: Some languages may have longer text, so design UI with flexibility
6. **Use Semantic Keys**: Name keys based on their meaning, not their English text
7. **Document New Keys**: When adding new keys, document them for translators
8. **Implement Fallbacks**: When adding new language properties, implement fallback mechanisms to handle missing translations
