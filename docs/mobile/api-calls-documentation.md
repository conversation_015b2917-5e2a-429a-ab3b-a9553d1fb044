# Documentación de API Calls - TapTrap Mobile App

## Resumen Ejecutivo

Esta documentación detalla todos los API calls realizados en la aplicación móvil TapTrap, el proceso de desencriptación, y el manejo de errores que determina cuándo mostrar la pantalla "No Content".

## 🚨 PROBLEMA IDENTIFICADO

**Causa Raíz**: La función `createEmptyGameContent()` retorna estructuras válidas pero vacías en lugar de `null`, evitando que se muestre la pantalla NoInternetScreen cuando debería mostrarse.

**Ubicaciones Problemáticas**:
- `contentService.ts` líneas 655, 820, 364, 380, 396
- <PERSON><PERSON><PERSON>les fallbacks retornan contenido vacío en lugar de `null`

**Solución**: Cambiar `return createEmptyGameContent()` por `return null` en casos de error para forzar NoInternetScreen.

## 1. API Calls Principales

### 1.1 Content Service (`services/contentService.ts`)

#### **Fetch Game Content**
- **Endpoint**: `${API_URL}/content`
- **Método**: GET
- **Ubicación**: `fetchAndUpdateGameContent()`
- **Desencriptación**: ✅ Sí (usando `decryptApiData()`)
- **Caché**: ✅ Sí (AsyncStorage encriptado)
- **Throttling**: ✅ Sí (60 minutos entre llamadas)

```typescript
// Proceso de desencriptación
if (response.data.encrypted === true && response.data.data) {
  gameContent = decryptApiData(response.data.data);
}
```

#### **Llamadas desde**:
- `App.tsx` - Al iniciar la app (línea 99)
- `HomeScreen.tsx` - Al montar el componente por primera vez (línea 332)
- `NoInternetScreen.tsx` - Al hacer retry (línea 57, 96)

### 1.2 Penalty Service (`services/penaltyService.ts`)

#### **Fetch Penalties**
- **Endpoint**: `${API_URL}/penalties`
- **Método**: GET
- **Ubicación**: `fetchPenaltiesFromAPI()`
- **Desencriptación**: ✅ Sí (usando `decryptApiData()`)
- **Caché**: ✅ Sí (AsyncStorage encriptado)

#### **Llamadas desde**:
- `HomeScreen.tsx` - Al montar el componente (línea 275)

### 1.3 Ad Settings Service (`services/adSettingsService.ts`)

#### **Fetch Ad Settings**
- **Endpoint**: `${API_URL}/admin/ad-settings`
- **Método**: GET
- **Ubicación**: `fetchAndUpdateAdSettings()`
- **Desencriptación**: ❌ No
- **Caché**: ✅ Sí (AsyncStorage)
- **Throttling**: ✅ Sí (1 hora entre llamadas)

#### **Llamadas desde**:
- `App.tsx` - Al iniciar la app (línea 100)

### 1.4 What's New Service (`services/whatsNewService.ts`)

#### **Fetch What's New Logs**
- **Endpoint**: `${API_URL}/whatsnew`
- **Método**: GET
- **Ubicación**: `fetchLogsFromAPI()`
- **Desencriptación**: ❌ No
- **Caché**: ✅ Sí (AsyncStorage)

#### **Check for New Logs (Lightweight)**
- **Endpoint**: `${API_URL}/whatsnew/check-new`
- **Método**: GET
- **Ubicación**: `checkForNewLogs()`

#### **Llamadas desde**:
- `HomeScreen.tsx` - Al montar el componente (línea 333, 338)

### 1.5 Push Notification Service (`services/pushNotificationService.ts`)

#### **Upload Push Token**
- **Endpoint**: `${API_URL}/push-tokens`
- **Método**: POST
- **Ubicación**: `uploadTokenToBackend()`
- **Desencriptación**: ❌ No

#### **Update Notification Preference**
- **Endpoint**: `${API_URL}/push-tokens/preference`
- **Método**: PUT
- **Ubicación**: `updateNotificationPreferenceOnBackend()`
- **Desencriptación**: ❌ No

#### **Llamadas desde**:
- `App.tsx` - Al iniciar la app
- Varios componentes cuando el usuario interactúa con notificaciones

## 2. Proceso de Desencriptación

### 2.1 Función Principal: `decryptApiData()`

**Ubicación**: `services/contentService.ts` (línea 29)

```typescript
export const decryptApiData = (encryptedData: string): any => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, API_ENCRYPTION_KEY);
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedString);
  } catch (error) {
    console.error('Error decrypting API data:', error);
    throw new Error('Failed to decrypt data from API');
  }
};
```

### 2.2 Servicios que Usan Desencriptación

1. **Content Service** - Para contenido del juego (preguntas/dares)
2. **Penalty Service** - Para contenido de penalizaciones

### 2.3 Clave de Encriptación

- **Variable**: `API_ENCRYPTION_KEY`
- **Fuente**: Archivo `.env`
- **Valor**: `taptrap-encryption-secret-************************************`

## 3. Lógica de Detección de "No Content"

### 3.1 Condiciones para Mostrar NoInternetScreen

**Ubicación**: `App.tsx` (línea 104-139)

```typescript
// Detecta contenido vacío
const isEmpty = content === null || (
  content &&
  Object.values(content.questions).every(arr => arr.length === 0) &&
  Object.values(content.dares).every(arr => arr.length === 0)
);

// Navega a NoInternetScreen si está vacío
if (isEmpty && navigationRef.current) {
  const networkState = await NetInfo.fetch();
  const scenario = networkState.isConnected ? 'content-failed' : 'no-internet';
  navigationRef.current.navigate('NoInternetScreen', { scenario });
}
```

### 3.2 Escenarios de NoInternetScreen

1. **'no-internet'**: Sin conexión a internet
2. **'content-failed'**: Con internet pero fallo en descarga de contenido

## 4. Posibles Causas del Problema "Contenido No Se Muestra"

### 4.1 Problemas de Desencriptación

**Síntomas**: Usuario tiene internet, API responde, pero contenido no aparece

**Causas Potenciales**:
1. **Error en desencriptación** (línea 721-733 en contentService.ts)
2. **Clave de encriptación incorrecta**
3. **Formato de respuesta API inválido**

### 4.2 Problemas de Caché

**Síntomas**: Contenido no se actualiza o aparece vacío

**Causas Potenciales**:
1. **Caché corrupto** - Se limpia automáticamente cuando online
2. **Estructura de datos inválida** en caché
3. **Error en encoding/decoding** del caché

### 4.3 Problemas de Red/API

**Síntomas**: Timeouts o errores 404/500

**Causas Potenciales**:
1. **Timeout de 15 segundos** en requests
2. **API no disponible**
3. **Problemas de autenticación**

## 5. Flujo de Fallbacks

### 5.1 Orden de Fallbacks para Content

1. **API Call** → Si falla...
2. **Caché Local** → Si falla o está vacío...
3. **Estructura Vacía** (`createEmptyGameContent()`) → Si offline...
4. **NoInternetScreen** → Si online pero sin contenido

### 5.2 Comportamiento Offline vs Online

**Offline**:
- Usa caché aunque esté vacío
- Evita mostrar NoInternetScreen
- Retorna estructura vacía como último recurso

**Online**:
- Intenta API call
- Si falla, usa caché como fallback
- Si caché también falla, muestra NoInternetScreen

## 6. Logging y Debugging

### 6.1 Logs Importantes para Debugging

```typescript
// En contentService.ts
console.log('Received encrypted data from API');
console.log('Successfully decrypted data from API');
console.error('Failed to decrypt data from API:', decryptError);

// En App.tsx
console.warn('No valid content available, showing NoInternetScreen with scenario:', scenario);
```

### 6.2 Puntos de Monitoreo

1. **Respuesta de API** - Verificar si llega encriptada
2. **Proceso de desencriptación** - Verificar errores
3. **Validación de estructura** - Verificar que tenga questions/dares
4. **Estado de red** - Verificar conectividad

## 7. Análisis del Problema Específico

### 7.1 Situación Actual Reportada

**Problema**: A veces el contenido no se muestra a los usuarios, como que no se descarga, pero aún así no aparece la pantalla de "No content".

### 7.2 Posibles Escenarios Problemáticos

#### **Escenario A: Contenido Vacío Pero Válido**
```typescript
// En contentService.ts línea 645-649
if (totalQuestions > 0 || totalDares > 0) {
  return cachedContent;
} else {
  console.warn('⚠️ Cached content exists but contains no items while offline');
  return cachedContent; // Retorna estructura vacía pero válida
}
```

**Problema**: Si el caché tiene estructura válida pero sin contenido, la app no muestra NoInternetScreen.

#### **Escenario B: Error de Desencriptación Silencioso**
```typescript
// En contentService.ts línea 721-733
} catch (decryptError) {
  console.error('Failed to decrypt data from API:', decryptError);
  const cachedContent = await getCachedGameContent();
  if (cachedContent) {
    return cachedContent; // Puede retornar caché vacío
  }
  return null;
}
```

**Problema**: Si la desencriptación falla pero hay caché (aunque vacío), no se muestra error.

#### **Escenario C: Estructura Vacía Como Fallback**
```typescript
// En contentService.ts línea 818-820
console.warn('No content available from API or cache, using empty fallback');
return createEmptyGameContent();
```

**Problema**: `createEmptyGameContent()` retorna estructura válida pero vacía, evitando NoInternetScreen.

### 7.3 Gaps en la Lógica Actual

1. **No hay validación de contenido mínimo** después de desencriptación exitosa
2. **Fallbacks pueden retornar estructuras vacías** sin mostrar error al usuario
3. **No hay diferenciación** entre "sin internet" y "contenido vacío por error de API"

## 8. Soluciones Recomendadas

### 8.1 Mejorar Validación de Contenido

```typescript
// Agregar validación después de desencriptación
const validateContentHasItems = (content: GameContent): boolean => {
  const totalQuestions = Object.values(content.questions)
    .reduce((sum, arr) => sum + arr.length, 0);
  const totalDares = Object.values(content.dares)
    .reduce((sum, arr) => sum + arr.length, 0);

  return totalQuestions > 0 || totalDares > 0;
};
```

### 8.2 Mejorar Logging para Debugging

```typescript
// Agregar logs más específicos
console.log('📊 Content validation:', {
  hasQuestions: Object.keys(content.questions).length,
  hasDares: Object.keys(content.dares).length,
  totalItems: totalQuestions + totalDares,
  isValid: validateContentHasItems(content)
});
```

### 8.3 Revisar Lógica de Fallbacks

- **Cuando online**: Si API falla o retorna contenido vacío → Mostrar NoInternetScreen con scenario 'content-failed'
- **Cuando offline**: Solo usar caché si tiene contenido válido, sino mostrar NoInternetScreen

## 9. Puntos de Monitoreo Críticos

### 9.1 En App.tsx (Línea 104-139)
- **Verificar**: Si `isEmpty` se está calculando correctamente
- **Agregar**: Log del contenido antes de verificar si está vacío

### 9.2 En contentService.ts (Línea 714-738)
- **Verificar**: Si la desencriptación está funcionando
- **Agregar**: Validación de contenido después de desencriptar

### 9.3 En contentService.ts (Línea 640-656)
- **Verificar**: Si el comportamiento offline está causando el problema
- **Revisar**: Lógica de retorno de contenido vacío

## 10. Comandos de Debugging Recomendados

### 10.1 Para Verificar Estado Actual
```bash
# Revisar logs en tiempo real
npx react-native log-android  # Para Android
npx react-native log-ios      # Para iOS
```

### 10.2 Para Limpiar Caché
```typescript
// En la app, agregar función de debug
import AsyncStorage from '@react-native-async-storage/async-storage';

const clearAllCache = async () => {
  await AsyncStorage.removeItem('gameContent');
  await AsyncStorage.removeItem('penalties');
  await AsyncStorage.removeItem('adSettings');
  console.log('All cache cleared');
};
```

## 11. Análisis Definitivo del Problema

### 11.1 Causa Raíz Identificada

**El problema principal está en la función `createEmptyGameContent()`** (línea 246-261 en contentService.ts):

```typescript
const createEmptyGameContent = (): GameContent => ({
  questions: {
    casual_questions: [],
    mild_questions: [],
    spicy_questions: [],
    no_limits_questions: [],
    couple_questions: []
  },
  dares: {
    casual_dares: [],
    mild_dares: [],
    spicy_dares: [],
    no_limits_dares: [],
    couple_dares: []
  }
});
```

### 11.2 Lugares Donde Se Retorna Contenido Vacío Sin Mostrar Error

#### **1. Offline con Caché Vacío** (línea 648)
```typescript
console.warn('⚠️ Cached content exists but contains no items while offline');
return cachedContent; // Retorna estructura vacía pero válida
```

#### **2. Sin Internet y Sin Caché** (línea 655)
```typescript
console.log('No internet and no cached content available');
return createEmptyGameContent(); // Retorna estructura vacía
```

#### **3. Error Offline** (línea 364, 380, 396)
```typescript
console.warn('Offline: Using empty content structure as last resort');
return createEmptyGameContent();
```

#### **4. Fallback Final** (línea 820)
```typescript
console.warn('No content available from API or cache, using empty fallback');
return createEmptyGameContent();
```

### 11.3 Por Qué No Aparece NoInternetScreen

La lógica en `App.tsx` (línea 104-108) verifica si el contenido está vacío:

```typescript
const isEmpty = content === null || (
  content &&
  Object.values(content.questions).every(arr => arr.length === 0) &&
  Object.values(content.dares).every(arr => arr.length === 0)
);
```

**Pero** `createEmptyGameContent()` retorna una estructura válida con arrays vacíos, no `null`.

**El problema**: La verificación `content === null` falla porque `createEmptyGameContent()` retorna un objeto válido, y la segunda parte de la verificación (arrays vacíos) **SÍ detecta que está vacío**.

### 11.4 El Verdadero Problema

**La lógica DEBERÍA mostrar NoInternetScreen cuando el contenido está vacío, pero hay casos donde no lo hace.**

Revisando más profundamente, el problema puede estar en:

1. **Timing**: El contenido se carga después de que se evalúa la condición
2. **Estado de red inconsistente**: NetInfo puede reportar conectividad cuando no hay acceso real a la API
3. **Caché corrupto**: Estructura válida pero con datos inválidos que pasan la validación inicial

### 11.5 Casos Edge Identificados

#### **Caso A: API Responde Pero Con Datos Inválidos**
- API retorna 200 OK
- Datos se desencriptan "exitosamente"
- Pero el contenido desencriptado está vacío o corrupto
- Se guarda en caché y se usa

#### **Caso B: Desencriptación Parcial**
- API retorna datos encriptados
- Desencriptación falla parcialmente
- Se retorna estructura válida pero vacía
- No se detecta como error

#### **Caso C: Race Condition**
- App.tsx evalúa contenido antes de que termine de cargar
- `fetchAndUpdateGameContent()` aún está procesando
- Se evalúa como vacío prematuramente

## 12. Solución Definitiva Recomendada

### 12.1 Modificar Lógica de Fallbacks

```typescript
// En lugar de siempre retornar createEmptyGameContent()
// Retornar null cuando hay problemas para forzar NoInternetScreen

// Cambiar línea 655:
// return createEmptyGameContent();
return null; // Esto forzará NoInternetScreen

// Cambiar línea 820:
// return createEmptyGameContent();
return null; // Esto forzará NoInternetScreen
```

### 12.2 Agregar Validación Post-Desencriptación

```typescript
// Después de desencriptar (línea 714-738)
if (gameContent) {
  const totalItems = Object.values(gameContent.questions)
    .reduce((sum, arr) => sum + arr.length, 0) +
    Object.values(gameContent.dares)
    .reduce((sum, arr) => sum + arr.length, 0);

  if (totalItems === 0) {
    console.error('API returned empty content after decryption');
    throw new Error('Empty content from API');
  }
}
```

### 12.3 Mejorar Logging para Debugging

```typescript
// En App.tsx, antes de verificar isEmpty
console.log('🔍 Content validation details:', {
  contentIsNull: content === null,
  contentExists: !!content,
  questionsEmpty: content ? Object.values(content.questions).every(arr => arr.length === 0) : 'N/A',
  daresEmpty: content ? Object.values(content.dares).every(arr => arr.length === 0) : 'N/A',
  totalQuestions: content ? Object.values(content.questions).reduce((sum, arr) => sum + arr.length, 0) : 0,
  totalDares: content ? Object.values(content.dares).reduce((sum, arr) => sum + arr.length, 0) : 0
});
```

## 13. Implementación Inmediata

Para resolver el problema inmediatamente, se debe:

1. **Cambiar los `return createEmptyGameContent()`** por `return null` en casos de error
2. **Agregar validación** después de desencriptación exitosa
3. **Mejorar logging** para identificar el caso específico
4. **Testear** con diferentes escenarios de red y API

Esto asegurará que cuando realmente no hay contenido disponible, se muestre la pantalla NoInternetScreen apropiada.
