# Font Configuration in TapTrap

## Overview

TapTrap uses custom fonts to provide a consistent and branded user experience. This document outlines how fonts are configured and loaded in the application.

## Font Configuration Approach

### Previous Implementation (Runtime Loading)
Previously, fonts were loaded at runtime using the `useFonts` hook from `expo-font` in the `HomeScreen` component. This approach caused timing issues when the splash screen transitioned to the main app, leading to display problems.

### Current Implementation (Build-time Preloading)
Fonts are now preloaded at build time using the `expo-font` config plugin in `app.config.js`. This ensures fonts are available immediately when the app starts, preventing display issues.

## Configuration

### app.config.js
```javascript
plugins: [
  // ... other plugins
  [
    "expo-font",
    {
      "fonts": [
        "./assets/fonts/Melindya.ttf",
        "./assets/fonts/Nunito-Bold.ttf",
        "./assets/fonts/Nunito-SemiBold.ttf"
      ]
    }
  ],
  // ... other plugins
]
```

### Font Files
The following font files are included in the app:

- **Melindya.ttf**: Primary heading font used for all major headings and titles
- **Nunito-Bold.ttf**: Bold variant of body text font
- **Nunito-SemiBold.ttf**: Semi-bold variant of body text font

## Typography System

### ThemedText Component
The `components/ThemedText.tsx` file defines the typography system:

```typescript
export const typography = StyleSheet.create({
  // Font family styles
  nunitoBold: {
    fontFamily: 'Nunito-Bold',
  },
  nunitoSemiBold: {
    fontFamily: 'Nunito-SemiBold',
  },

  // Melindya heading styles
  headingJumbo: {
    fontFamily: 'Melindya',
    fontSize: 138,
    lineHeight: 160,
    textTransform: 'uppercase',
  },
  heading1: {
    fontFamily: 'Melindya',
    fontSize: 88,
    lineHeight: 104,
    textTransform: 'uppercase',
  },
  // ... other heading styles
});
```

## Usage

### In Components
```jsx
import { typography } from '@/components/ThemedText';

// Use predefined typography styles
<Text style={typography.heading1}>Main Title</Text>
<Text style={typography.nunitoBold}>Bold Text</Text>
```

### Font Family Names
When using fonts directly in styles, use these exact font family names:
- `'Melindya'` - For the Melindya font
- `'Nunito-Bold'` - For bold Nunito text
- `'Nunito-SemiBold'` - For semi-bold Nunito text

## Testing

### Font Configuration Test
A test script is available to verify font configuration:

```bash
node scripts/test-fonts.js
```

This script checks:
- ✅ app.config.js loads correctly
- ✅ expo-font plugin is configured
- ✅ All font files exist
- ✅ Expected fonts are configured
- ⚠️ Warns about unexpected fonts

## Development Workflow

### After Font Configuration Changes

1. **Clean and Prebuild**:
   ```bash
   npx expo prebuild --clean
   ```

2. **Build for Testing**:
   ```bash
   # For iOS development build
   eas build --local --platform ios --profile development
   
   # For Android development build
   eas build --local --platform android --profile development
   ```

3. **Verify Fonts Load**:
   - Install the build on a device
   - Check that fonts display correctly immediately after splash screen
   - Verify no font loading delays or fallbacks occur

## Troubleshooting

### Common Issues

1. **Fonts not loading**: 
   - Ensure `npx expo prebuild --clean` was run after configuration changes
   - Verify font files exist in the correct paths
   - Check that font family names match exactly

2. **Font fallbacks appearing**:
   - This indicates fonts aren't preloaded properly
   - Run the font configuration test script
   - Rebuild the app after fixing configuration

3. **Build errors**:
   - Check that all font file paths are correct and relative to project root
   - Ensure font files are valid TTF/OTF files

### Font Family Name Determination

To determine the correct font family name for a font file:

1. **iOS**: The font family name is taken directly from the font file metadata
2. **Android**: The file name (without extension) becomes the font family name
3. **Use the test script**: It will show which fonts are available after loading

## Migration Notes

### From Runtime to Build-time Loading

The migration from runtime font loading involved:

1. ✅ Added expo-font plugin configuration to `app.config.js`
2. ✅ Removed `useFonts` hook from `HomeScreen.tsx`
3. ✅ Removed font loading dependency from asset loading logic
4. ✅ Cleaned up unused imports

### Benefits of Build-time Loading

- **Faster startup**: Fonts are immediately available
- **No loading states**: Eliminates font loading delays
- **Better UX**: Prevents text flashing or layout shifts
- **Simpler code**: No need to manage font loading state

## Best Practices

1. **Always use the typography system** defined in `ThemedText.tsx`
2. **Test on real devices** after font configuration changes
3. **Run the test script** before committing font changes
4. **Keep font files optimized** for mobile use
5. **Document any new fonts** added to the system
