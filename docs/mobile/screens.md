# TapTrap Mobile App Screens

## Overview

The TapTrap mobile app consists of the following main screens:

1. [Home Screen](#home-screen)
2. [Select Categories Screen](#select-categories-screen)
3. [Touch Game Screen](#touch-game-screen)
4. [Settings Screen](#settings-screen)
5. [No Internet Screen](#no-internet-screen)

## Home Screen

**File**: `screens/HomeScreen.tsx`

### Purpose
The Home Screen is the main entry point of the app. It allows users to select which game mode(s) they want to play: Questions, Challenges, or both.

### Key Components
- Logo animation (using <PERSON><PERSON>)
- Game mode selection buttons (Questions and Challenges)
- Settings button
- Bottom sheet for navigation to the next screen

### State Management
- Tracks selected game modes
- Manages animations for UI elements
- Handles sound effects and haptic feedback

### Navigation
- Navigates to Select Categories Screen when game modes are selected
- Navigates to Settings Screen when settings button is pressed

### Visual Elements
- Animated logo
- Mode selection buttons with toggle state
- Bottom sheet that appears when modes are selected
- Background decorations

### Code Highlights
```jsx
<TouchableOpacity
  onPress={() => toggleModeSelection('questions')}
  style={[styles.button, selectedModes.includes('questions') && styles.buttonSelected]}
>
  <Text style={[typography.heading3, styles.buttonText, selectedModes.includes('questions') && styles.buttonTextSelected]}>
    {t('questionsIndex')}
  </Text>
  {selectedModes.includes('questions') ? (
    <SelectedIcon style={styles.buttomImageSelected}/>
  ) : (
    <QuestionsIcon style={styles.buttomImage}/>
  )}
</TouchableOpacity>
```

## Select Categories Screen

**File**: `screens/SelectCategoriesScreen.tsx`

### Purpose
Allows users to select which categories of questions or challenges they want to include in their game.

### Key Components
- Category selection buttons
- Premium content indicators (locked icons)
- Bottom sheet for navigation to the game screen
- Paywall modal for premium content

### State Management
- Tracks selected categories
- Manages premium content access
- Handles animations for UI elements

### Navigation
- Navigates to Touch Game Screen when categories are selected
- Can navigate back to Home Screen

### Visual Elements
- Category buttons with toggle state
- Lock icons for premium content
- Bottom sheet that appears when categories are selected

### Code Highlights
```jsx
<TouchableOpacity
  onPress={() => toggleCategory('spicy_questions')}
  style={[
    styles.categoryButton,
    selectedCategories.includes('spicy_questions') && styles.categoryButtonSelected
  ]}
>
  {selectedCategories.includes('spicy_questions') ? (
    <SelectedIcon style={styles.categoryIconSelected} />
  ) : !isPremiumUnlocked ? (
    <IconLocked style={styles.categoryIcon} />
  ) : (
    <IconSpicy style={styles.categoryIcon} />
  )}
  <Text style={[
    typography.heading3,
    styles.categoryText,
    selectedCategories.includes('spicy_questions') && styles.categoryTextSelected
  ]}>{t('partners')}</Text>
</TouchableOpacity>
```

## Touch Game Screen

**File**: `screens/TouchGameScreen.tsx`

### Purpose
The main game screen where players place their fingers on the screen and one is randomly selected to answer a question or perform a challenge.

### Key Components
- Multi-touch detection system
- Countdown animation
- Question/challenge display
- Navigation buttons (home, settings)
- Result modal

### State Management
- Tracks touch points (position, identifier)
- Manages game state (started, complete)
- Handles countdown timer
- Manages current question/challenge

### Game Logic
- Detects and tracks multiple fingers on the screen
- Starts countdown when at least two fingers are detected
- Randomly selects one finger after countdown
- Displays question or challenge based on selected categories
- Handles game reset and navigation

### Countdown Timing
- **Countdown Duration**: 500ms per step (0.5 seconds faster than original 1000ms)
- **Total Countdown Time**: ~2 seconds (3-2-1-0 sequence)
- **Lottie Animation Speed**: 2.5x speed (increased from 2x for synchronization)
- **Modal Delay**: 1 second delay before showing question dialog after selection
- **Sound Handling**: Countdown sound plays but doesn't block progression (no await on sound completion)

### Visual Elements
- Touch point indicators
- Countdown animation
- Mascot character with dialog
- Question/challenge modal
- Spark effects for selected finger

### Code Highlights
```jsx
const handleTouchStart = (event: GestureResponderEvent) => {
  const touches = event.nativeEvent.touches;

  setTouchPoints(prevPoints => {
    const newPoints = [...prevPoints];

    touches.forEach(touch => {
      const identifier = Number(touch.identifier);
      // Add new finger if it's not already tracked
      if (!newPoints.some(p => p.identifier === identifier)) {
        newPoints.push({
          identifier,
          pageX: touch.pageX,
          pageY: touch.pageY,
          isHighlighted: false,
          isSelected: false,
          seq: nextSeq.current++,
        });
      }
    });
    return newPoints;
  });

  if (touches.length >= 2 && !gameStarted && !gameComplete) {
    startGame(touches);
  }
};
```

## Settings Screen

**File**: `screens/SettingsScreen.tsx`

### Purpose
Allows users to configure app settings such as language, sound effects, haptic feedback, penalties, and push notifications.

### Key Components
- Settings options list with dynamic status display
- Language selection modal with flag icons
- Toggle switches for sound, haptic feedback, and notifications
- Penalty selection interface with premium indicators
- Support section with user ID display
- Navigation back button

### State Management
- Tracks current settings values (sound, haptics, language, notifications, penalties)
- Manages modal visibility for different settings
- Handles settings changes with immediate feedback
- Integrates with AppSettingsContext for persistent storage

### Settings Options

#### 1. Penalty Settings
- **Purpose**: Configure penalties for refusing questions/dares
- **Features**:
  - Individual penalty selection from all categories
  - Premium penalty indicators with unlock dialogs
  - Default penalty assignment based on user type (free/premium)
  - Dynamic display of selected penalty count

#### 2. Language Selection
- **Purpose**: Change app language (English, Spanish, Dominican Spanish)
- **Features**:
  - Flag icons for visual identification
  - Immediate language switching
  - Persistent language storage
  - Android-specific loading states for SVG flags

#### 3. Effects Settings
- **Purpose**: Control sound and haptic feedback
- **Features**:
  - Independent toggles for sound and haptics
  - Visual feedback with gradient switches
  - Immediate effect application
  - Status display (All On, Some On, None)

#### 4. Notification Settings
- **Purpose**: Manage push notification preferences
- **Features**:
  - Toggle for new content notifications
  - Permission status checking
  - Settings navigation for denied permissions
  - Backend synchronization of preferences
  - Visual status display (On/Off)

### Notification Toggle Implementation

The notification toggle integrates with the push notification system to provide user control over notification delivery:

#### Toggle Behavior
1. **When Enabling Notifications:**
   - Checks current notification permissions
   - If granted: Enables notifications and syncs to backend
   - If denied: Shows alert directing user to device settings

2. **When Disabling Notifications:**
   - Immediately disables notifications
   - Updates local preference storage
   - Syncs disabled state to backend

#### Backend Integration
- Uses `setNotificationPreference()` from AppSettingsContext
- Calls `pushNotificationService.updateNotificationPreferenceOnBackend()`
- Updates device record with `notificationEnabled` field
- Filters push notification recipients based on preference

#### Permission Handling
- Integrates with existing `pushNotificationService.showNotificationSettingsAlert()`
- Uses platform-specific settings navigation (iOS: `Linking.openSettings()`, Android: `IntentLauncher`)
- Provides localized alert messages for settings navigation

### Visual Elements
- Settings option cards with icons and dynamic status text
- Modal dialogs with smooth animations and delayed background overlay
- Toggle switches with gradient styling for active states
- Flag icons for language selection
- Premium badges and lock icons for penalty selection
- Loading indicators for Android SVG rendering

### State Synchronization
- **AppSettingsContext**: Central state management for all settings
- **AsyncStorage**: Persistent local storage for user preferences
- **Backend API**: Synchronization of notification preferences
- **Real-time Updates**: Immediate UI updates on setting changes

### Code Highlights

#### Notification Toggle Handler
```jsx
const handleNotificationToggle = async (enabled: boolean) => {
  try {
    if (enabled) {
      // Check if permissions are granted
      const { status } = await Notifications.getPermissionsAsync();

      if (status === 'granted') {
        // Permissions are granted, enable notifications
        await setNotificationPreference(true);
      } else {
        // Permissions denied, show settings alert
        await pushNotificationService.showNotificationSettingsAlert({
          title: t('pushNotificationDisabledTitle'),
          message: t('pushNotificationDisabledMessage'),
          openSettings: t('pushNotificationOpenSettings'),
          cancel: t('pushNotificationCancel'),
          error: t('pushNotificationSettingsError')
        });
      }
    } else {
      // When turning off, simply disable notifications
      await setNotificationPreference(false);
    }
  } catch (error) {
    console.error('Error handling notification toggle:', error);
  }
};
```

#### Dynamic Settings Data
```jsx
const settingsData = [
  {
    key: 'penalty',
    label: t('penaltyDialogTitle'),
    value: getPenaltyDisplayValue(),
    icon: PenaltyIcon,
  },
  {
    key: 'language',
    label: t('settingLanguage'),
    value: language === 'en' ? t('languageEnglish') :
           language === 'es' ? t('languageSpanish') :
           t('languageDominican'),
    icon: LanguageIcon,
  },
  {
    key: 'effects',
    label: t('settingEffects'),
    value: isSoundOn && isHapticsOn ? t('soundEffectsAllOn') :
           isSoundOn || isHapticsOn ? t('soundEffectsSomeOn') :
           t('soundEffectsAllOff'),
    icon: EffectsIcon,
  },
  {
    key: 'notifications',
    label: t('settingNotifications'),
    value: notificationPreference ? t('notificationsOn') : t('notificationsOff'),
    icon: NotificationsIcon,
  },
];
```

#### Notification Switch with Gradient
```jsx
<LinearGradient
  style={styles.switchContainer}
  colors={notificationPreference ? ['#42E0FF', '#42FFB7'] : ['transparent', 'transparent']}
>
  <Switch
    value={notificationPreference}
    onValueChange={handleNotificationToggle}
    // ... other switch props
  />
</LinearGradient>
```

## No Internet Screen

**File**: `screens/NoInternetScreen.tsx`

### Purpose
Displays an error message when the app launches with no internet connection and no cached content is available. This ensures users understand why content isn't loading and provides a retry option.

### Key Components
- Error message dialog
- Retry button
- Network connectivity checking
- Navigation handling

### State Management
- Manages dialog animation
- Handles network connectivity checks
- Controls navigation based on connectivity state

### Functionality
- Automatically shown when app starts with no internet and no content
- Detects when internet connectivity changes
- Provides visual feedback on retry attempts
- Returns to Home screen when connection is restored

### Visual Elements
- Gradient background (red/orange)
- Modal dialog with error message
- "Try Again" button with icon

### Network Handling
- Listens for network state changes in App.tsx
- Performs connectivity check on retry
- Attempts to load cached content
- Navigates back to Home when connection is restored

### Code Highlights
```jsx
// Retry button handler
const handleRetry = async () => {
  const networkState = await NetInfo.fetch();

  if (networkState.isConnected) {
    // We're back online, check if we have cached content
    const cachedContent = await getCachedGameContent();

    if (cachedContent || networkState.isConnected) {
      // Navigate back to Home if we either have content or an internet connection
      navigation.navigate('Home' as never);
    }
  } else {
    // Still offline, animate the dialog to give feedback
    dialogScale.value = withTiming(0.95, { duration: 100 }, () => {
      dialogScale.value = withTiming(1, { duration: 100 });
    });
  }
};
```
