# Ad Settings Implementation

## Overview

This document describes the implementation of two new admin panel controls for the TapTrap ad-based premium access system:

1. **Watch Ads Button Toggle Control**: Enable/disable the "watch ads" button per device
2. **Required Ads Count Control**: Configure how many ads users must watch to unlock premium access

## Features

### Admin Panel Controls

- **Watch Ads Button Toggle**: <PERSON><PERSON> can enable/disable the "Watch Ads" button in the UnlockDialog
- **Required Ads Count**: <PERSON><PERSON> can set the minimum number of ads (1-10) users must watch
- **Real-time Updates**: Settings are checked at app launch with 1-hour throttling
- **Offline Support**: Settings are cached locally for offline access
- **Fallback Defaults**: If no cached value and no internet, defaults to enabled button and 1 ad

### Mobile App Integration

- **Dynamic Ad Count**: WatchAdsScreen now uses the configurable required ads count
- **Conditional Button Display**: UnlockDialog only shows "Watch Ads" button when enabled
- **Session-based Access**: Ad-based premium access remains session-only
- **Proper Unlocking**: Fixed the premium category unlocking logic to use dynamic ad count

## Implementation Details

### Backend Components

#### 1. MongoDB Model (`backend/src/models/adSettings.js`)
```javascript
{
  settingsId: "default",           // Unique identifier
  watchAdsButtonEnabled: Boolean,  // Controls button visibility
  requiredAdsCount: Number        // Number of ads required (1-10)
}
```

#### 2. Admin Controller (`backend/src/controllers/adminController.js`)
- `getAdSettings()`: Public endpoint for mobile app
- `updateAdSettings()`: Protected endpoint for admin panel

#### 3. Admin Routes (`backend/src/routes/admin.js`)
- `GET /api/admin/ad-settings`: Public endpoint (no auth required)
- `PUT /api/admin/ad-settings`: Protected endpoint (requires admin auth)

### Frontend Admin Panel

#### 1. Ad Settings Page (`admin/src/pages/AdSettings.js`)
- Toggle control for watch ads button
- Number input for required ads count (1-10)
- Save functionality with validation
- Success/error feedback

#### 2. Navigation Integration
- Added "Ad Settings" menu item to admin navigation
- Added route to admin App.js

#### 3. Service Layer (`admin/src/services/adSettings.service.js`)
- API calls for getting and updating ad settings
- Input validation and error handling

### Mobile App Integration

#### 1. Ad Settings Service (`services/adSettingsService.ts`)
- Fetches ad settings from backend with time-based throttling (1 hour)
- Caches settings locally using AsyncStorage
- Provides fallback defaults when offline
- Follows the same pattern as content service

#### 2. Ad Settings Context (`context/AdSettingsContext.tsx`)
- Manages ad settings state throughout the app
- Provides hooks for accessing settings
- Updates session storage service when settings change
- Handles app state changes for refreshing settings

#### 3. Component Updates

**UnlockDialog (`components/ads/UnlockDialog.tsx`)**
- Conditionally shows "Watch Ads" button based on `isWatchAdsButtonEnabled`
- Uses `useAdSettings()` hook to access settings

**WatchAdsScreen (`screens/WatchAdsScreen.tsx`)**
- Uses dynamic `requiredAdsCount` instead of hardcoded 5
- Dynamically creates ad counter array based on required count
- Updates success logic to use dynamic count

**Session Storage Service (`services/sessionStorageService.ts`)**
- Added `setRequiredAdsCount()` and `getRequiredAdsCount()` methods
- Updated `hasTemporaryPremiumAccess()` to use dynamic count
- Fixed all hardcoded references to use `this.requiredAdsCount`

#### 4. App Launch Integration (`App.tsx`)
- Added AdSettingsProvider to context hierarchy
- Loads ad settings in parallel with content at app launch
- Proper error handling and logging

## Configuration

### Default Settings
- **Watch Ads Button Enabled**: `true`
- **Required Ads Count**: `1`

### Validation Rules
- **watchAdsButtonEnabled**: Must be boolean
- **requiredAdsCount**: Must be integer between 1 and 10

### Throttling
- Settings are fetched at app launch
- 1-hour throttling prevents excessive API calls
- Cached locally for offline access

## Testing

### Backend Testing
All endpoints have been tested and validated:
- ✅ GET /api/admin/ad-settings (public)
- ✅ PUT /api/admin/ad-settings (protected)
- ✅ Input validation
- ✅ Database persistence
- ✅ Error handling

### Admin Panel Testing
- ✅ Settings page loads correctly
- ✅ Toggle and number input work
- ✅ Save functionality with feedback
- ✅ Navigation integration

### Mobile Integration
- ✅ Ad settings context provides correct values
- ✅ UnlockDialog conditionally shows watch ads button
- ✅ WatchAdsScreen uses dynamic ad count
- ✅ Premium access unlocking works with dynamic count
- ✅ Session storage service updated correctly

## Usage

### Admin Panel
1. Navigate to "Ad Settings" in the admin menu
2. Toggle the "Watch Ads Button" on/off
3. Set the "Required Ads Count" (1-10)
4. Click "Save Settings"

### Mobile App
- Settings are automatically loaded at app launch
- Changes take effect within 1 hour (or immediately on app restart)
- Offline functionality with cached settings

## Notes

- Settings changes may take up to 1 hour to take effect on user devices due to throttling
- The GET endpoint is public to allow mobile apps to fetch settings without authentication
- The PUT endpoint requires admin authentication for security
- All settings are cached locally for offline functionality
- The implementation follows existing patterns in the codebase for consistency
