# TapTrap Mobile App Documentation

## Overview

TapTrap is a React Native mobile game where players use multi-touch input to engage in a selection process. The game supports multiple fingers on the screen and randomly selects one after a countdown. The selected finger is highlighted, and the player is presented with a question or challenge to complete.

## Table of Contents

1. [Project Structure](./structure.md)
2. [Screens](./screens.md)
3. [Components](./components.md)
4. [Game Mechanics](./game-mechanics.md)
5. [Penalty Feature](./penalty-feature.md)
6. [Premium Penalty System](./premium-penalty-system.md)
7. [State Management](./state-management.md)
8. [Services](./services.md)
9. [Localization](./localization.md)
10. [Assets](./assets.md)
11. [Android Optimizations](./android-optimizations.md)

## Core Features

- **Multi-touch Game**: Supports multiple fingers on the screen simultaneously
- **Random Selection**: Selects one finger after a 3-second countdown
- **Questions and Challenges**: Presents questions or challenges based on selected categories
- **Penalty System**: Alternative interaction when users refuse content
- **Premium Penalty System**: Premium-only penalties with automatic default assignment
- **Multilingual Support**: Available in English, Spanish, and Dominican Spanish
- **Content Management**: Backend integration for dynamic content updates
- **Offline Support**: Works offline with cached content
- **Sound Effects**: Interactive audio feedback
- **Haptic Feedback**: Tactile response on supported devices
- **Premium Content**: In-app purchases for premium content categories

## Technology Stack

- **Framework**: React Native with Expo
- **State Management**: React Context API
- **Navigation**: React Navigation
- **Animations**: React Native Reanimated
- **HTTP Client**: Axios
- **Storage**: Expo FileSystem
- **UI Components**: Custom components with SVG assets
- **Localization**: Custom i18n service

## Installation and Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```
3. Start the development server:
   ```bash
   npm start
   # or
   yarn start
   ```
4. Run on a device or emulator:
   - Press 'a' for Android
   - Press 'i' for iOS
   - Scan the QR code with the Expo Go app

## Quick Start Guide

For a quick overview of how to use the app, refer to the [User Guide](./user-guide.md).

For development information, check the [Development Guide](./development.md).
