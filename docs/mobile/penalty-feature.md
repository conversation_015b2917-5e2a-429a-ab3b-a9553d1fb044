# TapTrap Penalty Feature

## Overview

The Penalty feature provides an alternative interaction when users refuse to answer a question or perform a challenge. Instead of simply skipping content, users are presented with a penalty dialog that offers two choices: return to the content or reset the entire game. This feature enhances user engagement and maintains game flow continuity.

## Feature Components

### PenaltyDialog Component

**File**: `components/game/PenaltyDialog.tsx`

A modal dialog component that appears when users press the "Refuse" button in the question/dare dialog.

#### Features
- Displays penalty-themed UI with red color scheme
- Two action buttons: "Back to Content" and "Next Match"
- Smooth animations matching existing dialog patterns
- Haptic feedback and sound effects
- Fully localized with translation support

#### Props
```typescript
interface Props {
  onPress?: () => void;           // Legacy prop (unused)
  style?: ViewStyle;              // Custom styling (unused)
  isSequential?: boolean;         // Sequential mode flag (unused)
  onBackToContent?: () => void;   // Handler for back to content button
  onNextMatch?: () => void;       // Handler for next match button
}
```

#### Button Functionality
- **Back to Content Button** (Flip icon): Returns to the previous question without affecting content pools
- **Next Match Button**: Resets the entire game like the existing reset button

#### Usage
```jsx
import PenaltyDialog from '@/components/game/PenaltyDialog';

<PenaltyDialog
  onBackToContent={backToContentDialog}
  onNextMatch={handlePenaltyNextMatch}
/>
```

## Implementation Details

### TouchGameScreen Integration

**File**: `screens/TouchGameScreen.tsx`

The penalty feature is integrated into the main game screen with the following components:

#### State Management
```typescript
const [showPenalty, setShowPenalty] = useState(false);
const penaltyDialogScale = useSharedValue(0);
```

#### Animation System
```typescript
const penaltyDialogAnimatedStyle = useAnimatedStyle(() => {
  return {
    transform: [
      { scale: penaltyDialogScale.value },
      { translateY: withTiming(penaltyDialogScale.value === 0 ? 50 : 0, { duration: 300 }) }
    ],
    opacity: penaltyDialogScale.value,
  };
});
```

#### Transition Functions

##### Show Penalty Dialog
```typescript
const showPenaltyDialog = () => {
  const showPenalty = () => {
    setShowModal(false);
    setShowPenalty(true);

    // Show penalty dialog with animation
    penaltyDialogScale.value = withTiming(1, {
      duration: 400,
      easing: Easing.out(Easing.back(1.5))
    });
  };

  // Hide the main question dialog first
  dialogScale.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, (finished) => {
    if (finished) {
      runOnJS(showPenalty)();
    }
  });
};
```

##### Back to Content
```typescript
const backToContentDialog = () => {
  const showContent = () => {
    setShowPenalty(false);
    setShowModal(true);

    // Show main dialog with animation
    dialogScale.value = withTiming(1, {
      duration: 400,
      easing: Easing.out(Easing.back(1.5))
    });
  };

  // Hide penalty dialog first
  penaltyDialogScale.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, (finished) => {
    if (finished) {
      runOnJS(showContent)();
    }
  });
};
```

##### Next Match (Reset Game)
```typescript
const handlePenaltyNextMatch = () => {
  const resetGame = () => {
    setShowPenalty(false);
    handleResetTransition();
  };

  // Hide penalty dialog first
  penaltyDialogScale.value = withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) }, (finished) => {
    if (finished) {
      runOnJS(resetGame)();
    }
  });
};
```

#### Refuse Button Integration
The existing refuse button in the question dialog was modified to trigger the penalty dialog:

```typescript
// REFUSE Button
<TouchableOpacity
  style={styles.refuseButton}
  onPress={async () => {
    if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    await playSound('tapEffect1');
    showPenaltyDialog();
  }}
>
  <RefuseIcon style={styles.refuseButtonIcon} width={26} height={26} />
  <Text style={[typography.subtitle2, styles.refuseButtonLabel]}>
    {t('refurseButton')}
  </Text>
</TouchableOpacity>
```

#### Penalty Dialog Rendering
```typescript
{/* Penalty Dialog */}
{showPenalty && (
  <Animated.View style={[styles.questionOverlay, penaltyDialogAnimatedStyle]}>
    <PenaltyDialog
      onBackToContent={backToContentDialog}
      onNextMatch={handlePenaltyNextMatch}
    />
  </Animated.View>
)}
```

## Dynamic Background Gradient

### Implementation
The penalty feature includes dynamic background gradient changes that follow the same pattern as the existing premium content preview functionality.

#### Gradient Colors
```typescript
// Dynamic gradient colors for preview vs. penalty vs. default
const defaultGradientColors = ['#FF4EE2', '#C119FA'] as const;
const previewGradientColors = ['#FFB84D', '#FF4500'] as const;
const penaltyGradientColors = ['#FF4274', '#FF4E51'] as const; // red gradient for penalty theme

// Priority: penalty > premium preview > default
const containerGradientColors = showPenalty
  ? penaltyGradientColors
  : showingPremiumContent
    ? previewGradientColors
    : defaultGradientColors;
```

#### Behavior
- **When penalty dialog is shown**: Background changes to red gradient `['#FF4274', '#FF4E51']`
- **When penalty dialog is hidden**: Background reverts to previous state (premium preview or default)
- **Priority system**: Penalty takes precedence over premium preview when both are active
- **Smooth transitions**: Gradient changes are automatically synchronized with dialog state

## State Management Integration

### Penalty Consistency System
The penalty system maintains consistent penalties per content item to ensure users see the same penalty when repeatedly refusing the same question/dare:

```typescript
// State variables for penalty consistency
const [currentContentPenalty, setCurrentContentPenalty] = useState<string | null>(null);
const [penaltyContentId, setPenaltyContentId] = useState<string | null>(null);

// Penalty generation logic
const showPenaltyDialog = async () => {
  const currentQuestionId = question?.id;

  if (currentQuestionId && penaltyContentId === currentQuestionId && currentContentPenalty) {
    // Use existing penalty for this content item
    penaltyText = currentContentPenalty;
  } else {
    // Generate new penalty only for new content
    const newPenaltyText = await getRandomPenalty(languageKey);
    setCurrentContentPenalty(newPenaltyText);
    setPenaltyContentId(currentQuestionId);
  }
};
```

### Reset Game State
The penalty dialog state is properly integrated into the game reset functionality:

```typescript
const resetGameState = () => {
  // ... existing reset logic ...

  // Reset penalty dialog state
  setShowPenalty(false);
  penaltyDialogScale.value = 0;

  // Clear penalty content association
  setCurrentContentPenalty(null);
  setPenaltyContentId(null);

  // ... rest of reset logic ...
};
```

### Penalty Clearing on Content Change
Penalties are automatically cleared when moving to new content:

```typescript
// In proceedToNextQuestion and initial question setting
if (nextQuestion) {
  setQuestion(nextQuestion);

  // Clear penalty for the new content item
  setCurrentContentPenalty(null);
  setPenaltyContentId(null);
}

// In premium content display
setQuestion(premiumSample);

// Clear penalty for premium content (doesn't use penalties)
setCurrentContentPenalty(null);
setPenaltyContentId(null);
```

## Dialog Interaction Management

### InGameBanner Integration
The penalty dialog has special interaction logic with the InGameBanner component to prevent overlapping dialogs:

#### Preview Content State (isSequentialBanner = false)
When the penalty dialog is open and the user taps the InGameBanner in its preview content state:
1. The penalty dialog is automatically dismissed with animation
2. The main content dialog is restored
3. The premium content preview is then shown
4. This provides a smooth transition without overlapping dialogs

#### Payment Dialog State (isSequentialBanner = true)
When the penalty dialog is open and the user taps the InGameBanner in its payment dialog state:
1. The penalty dialog remains open
2. The RevenueCat paywall is opened as normal
3. This allows users to access premium features even when penalty dialog is active

#### Implementation
```typescript
const handleBannerPress = async () => {
  // Check if penalty dialog is open and handle accordingly
  if (showPenalty) {
    if (!isSequentialBanner) {
      // Dismiss penalty dialog first, then show premium content preview
      const showPreviewAfterPenaltyDismiss = () => {
        // Clear any existing banner timer
        if (bannerTimerRef.current) {
          clearTimeout(bannerTimerRef.current);
          bannerTimerRef.current = null;
        }

        // Select a random premium sample
        const randomIndex = Math.floor(Math.random() * PREMIUM_CONTENT_SAMPLES.length);
        const premiumSample = PREMIUM_CONTENT_SAMPLES[randomIndex];

        // Set up premium content state directly (no intermediate dialog)
        setShowPenalty(false);
        setShowingPremiumContent(true);
        setQuestion(premiumSample);
        setGameComplete(true);
        setShowModal(true);
        setIsSequentialBanner(true);

        // Animate dialog scale directly to show premium content
        dialogScale.value = withTiming(1, {
          duration: 400,
          easing: Easing.out(Easing.back(1.5))
        });

        // Restart the banner timer for the sequential banner
        startBannerTimer();
      };

      // Hide penalty dialog first
      penaltyDialogScale.value = withTiming(0, {
        duration: 300,
        easing: Easing.inOut(Easing.ease)
      }, (finished) => {
        if (finished) {
          runOnJS(showPreviewAfterPenaltyDismiss)();
        }
      });
      return;
    } else {
      // Proceed with paywall without dismissing penalty dialog
      console.log('Opening paywall while penalty dialog is open (sequential banner)');
    }
  }

  // ... rest of banner press logic ...
};
```

This ensures a smooth user experience by preventing dialog conflicts while maintaining the expected behavior for both banner states.

#### Visual Transition Optimization
The implementation includes a specific optimization to prevent visual glitches during the penalty-to-preview transition:

- **Direct State Setup**: Instead of first showing the default content dialog and then calling `showPremiumContentPreview()`, the transition directly sets up all premium content state (question content, banner mode, etc.) before animating the dialog
- **Single Animation**: Uses a single smooth animation from penalty dialog to premium preview dialog without intermediate steps
- **No Flash**: Eliminates the brief flash of original content that would occur with a two-step transition

This provides a seamless visual experience where users see a smooth transition directly from the penalty dialog to the premium content preview.

## Animation Patterns

### Consistency with Existing Dialogs
The penalty feature follows the exact same animation patterns as existing dialogs:

- **Show Animation**: `withTiming(1, { duration: 400, easing: Easing.out(Easing.back(1.5)) })`
- **Hide Animation**: `withTiming(0, { duration: 300, easing: Easing.inOut(Easing.ease) })`
- **Transform Effects**: Scale and translateY animations for smooth transitions
- **Positioning**: Uses `styles.questionOverlay` for proper full-screen positioning

### Animation Sequence
1. **Refuse Button Pressed**: Question dialog scales down and fades out
2. **Penalty Dialog Appears**: Scales up with bounce effect and fades in
3. **Back to Content**: Penalty dialog scales down, question dialog scales up
4. **Next Match**: Penalty dialog scales down, game resets

## User Experience Flow

### Typical User Journey
1. **Game starts**: User places fingers, random selection occurs
2. **Question appears**: User sees question/dare with action buttons
3. **User refuses**: Presses red "Refuse" button
4. **Penalty dialog**: Smooth transition to penalty dialog with red background
5. **User choice**:
   - **Back to Content**: Returns to same question (no pool impact)
   - **Next Match**: Resets entire game for new round

### Design Principles
- **Non-punitive**: Penalty dialog doesn't actually penalize, just provides alternatives
- **Smooth transitions**: All animations maintain visual continuity
- **Consistent patterns**: Follows existing UI/UX patterns throughout the app
- **Clear choices**: Two distinct options with clear visual and functional differences

## Penalty Walkthrough Tutorial System

### Overview
The penalty feature includes an intelligent tutorial system that guides users to the penalty settings when they first encounter the penalty dialog. This ensures users understand how to customize their penalty experience.

### Tutorial Components

#### Visual Components
- **PenaltyWalkthroughEN**: English tutorial graphic (255x110px)
- **PenaltyWalkthroughES**: Spanish/Dominican tutorial graphic (255x110px)
- **Positioning**: Positioned near settings button to guide user attention

#### State Management
```typescript
// Walkthrough state variables
const [showPenaltyWalkthrough, setShowPenaltyWalkthrough] = useState(false);
const [walkthroughCompleted, setWalkthroughCompleted] = useState(false);
const [userInteractedWithSettings, setUserInteractedWithSettings] = useState(false);

// Timer references for walkthrough lifecycle
const walkthroughDelayTimer = useRef<NodeJS.Timeout | null>(null);
const walkthroughAutoHideTimer = useRef<NodeJS.Timeout | null>(null);
const walkthroughReDisplayTimer = useRef<NodeJS.Timeout | null>(null);

// Animation value
const penaltyWalkthroughOpacity = useSharedValue(0);
```

### Tutorial Logic Flow

#### Display Trigger
1. **Penalty Dialog Opens**: User taps "Refuse" button and penalty dialog appears
2. **500ms Delay**: Walkthrough appears 500ms after penalty dialog is fully visible
3. **Language Detection**: Shows appropriate language version based on current app language
4. **Completion Check**: Only shows if tutorial hasn't been completed previously

#### Display Conditions
```typescript
const showWalkthrough = () => {
  if (walkthroughCompleted) return; // Don't show if already completed

  // Clear any existing timers
  clearWalkthroughTimers();

  // Show walkthrough after 500ms delay
  walkthroughDelayTimer.current = setTimeout(() => {
    setShowPenaltyWalkthrough(true);
    penaltyWalkthroughOpacity.value = withTiming(1, { duration: 300 });

    // Auto-hide after 10 seconds
    walkthroughAutoHideTimer.current = setTimeout(() => {
      hideWalkthrough(false); // false = not user-initiated
    }, 10000);
  }, 500);
};
```

#### Dismissal Behavior

##### User Interaction (Settings Button)
- **Immediate Dismissal**: Walkthrough hides instantly when user taps settings button
- **Completion Marking**: Tutorial marked as completed (never shows again)
- **State Persistence**: Completion status saved to AsyncStorage

```typescript
// Settings button integration
<TouchableOpacity
  onPress={async () => {
    // Hide walkthrough if visible (user interacted with settings)
    if (showPenaltyWalkthrough) {
      hideWalkthrough(true); // true = user-initiated
    }

    navigation.navigate('Settings');
  }}
>
  <SettingsIcon width={48} height={48} />
</TouchableOpacity>
```

##### Auto-Hide (No Interaction)
- **10-Second Timer**: Automatically hides after 10 seconds if no user interaction
- **Re-display Scheduling**: Schedules re-display after 8 hours
- **State Tracking**: Records last shown time for re-display logic

### Persistence System

#### AsyncStorage Keys
- `penaltyWalkthroughCompleted`: Boolean indicating permanent completion
- `penaltyWalkthroughLastShown`: Timestamp of last walkthrough display

#### State Management Functions
```typescript
const loadWalkthroughState = async () => {
  try {
    const completed = await AsyncStorage.getItem('penaltyWalkthroughCompleted');
    const lastShown = await AsyncStorage.getItem('penaltyWalkthroughLastShown');

    if (completed === 'true') {
      setWalkthroughCompleted(true);
      return;
    }

    // Check if 8 hours have passed since last shown
    if (lastShown) {
      const lastShownTime = parseInt(lastShown, 10);
      const now = Date.now();
      const eightHours = 8 * 60 * 60 * 1000;

      if (now - lastShownTime < eightHours) {
        return; // Not enough time has passed
      }
    }
  } catch (error) {
    console.error('Error loading walkthrough state:', error);
  }
};

const saveWalkthroughState = async (completed: boolean, userInteracted: boolean) => {
  try {
    if (completed && userInteracted) {
      await AsyncStorage.setItem('penaltyWalkthroughCompleted', 'true');
    } else {
      await AsyncStorage.setItem('penaltyWalkthroughLastShown', Date.now().toString());
    }
  } catch (error) {
    console.error('Error saving walkthrough state:', error);
  }
};
```

### Animation System

#### Smooth Transitions
```typescript
const penaltyWalkthroughAnimatedStyle = useAnimatedStyle(() => {
  return {
    opacity: penaltyWalkthroughOpacity.value,
    transform: [
      { scale: withTiming(penaltyWalkthroughOpacity.value === 0 ? 0.8 : 1, { duration: 300 }) }
    ],
  };
});
```

#### Safe Animation Callbacks
```typescript
const hideWalkthrough = (userInteracted: boolean = false) => {
  // Create a function that can be safely called from runOnJS
  const handleWalkthroughHidden = () => {
    setShowPenaltyWalkthrough(false);

    // Save state based on user interaction (async calls wrapped safely)
    if (userInteracted) {
      setWalkthroughCompleted(true);
      setTimeout(() => saveWalkthroughState(true, true), 0);
    } else {
      setTimeout(() => saveWalkthroughState(false, false), 0);

      // Schedule re-display after 8 hours
      walkthroughReDisplayTimer.current = setTimeout(() => {
        loadWalkthroughState();
      }, 8 * 60 * 60 * 1000);
    }
  };

  // Hide walkthrough with animation
  penaltyWalkthroughOpacity.value = withTiming(0, { duration: 300 }, (finished) => {
    if (finished) {
      runOnJS(handleWalkthroughHidden)();
    }
  });
};
```

### Localization Support

#### Language-Based Rendering
```typescript
{showPenaltyWalkthrough && (
  <Animated.View style={[styles.walkthroughOverlay, penaltyWalkthroughAnimatedStyle]}>
    {language === 'es' || language === 'dom' ? (
      <PenaltyWalkthroughES width={255} height={110} />
    ) : (
      <PenaltyWalkthroughEN width={255} height={110} />
    )}
  </Animated.View>
)}
```

#### Dynamic Language Changes
- **Real-time Updates**: Walkthrough updates immediately when language changes
- **Seamless Transitions**: No interruption to tutorial flow during language switches
- **Consistent Behavior**: Tutorial logic remains the same across all languages

### Lifecycle Management

#### Component Integration
```typescript
// Load walkthrough state on component mount
useEffect(() => {
  loadWalkthroughState();
}, []);

// Handle app state changes
useEffect(() => {
  const handleAppStateChange = (nextAppState: string) => {
    if (nextAppState === 'background' || nextAppState === 'inactive') {
      clearWalkthroughTimers(); // Clear timers when app goes to background
    }
  };

  const subscription = AppState.addEventListener('change', handleAppStateChange);

  return () => {
    subscription?.remove();
    clearWalkthroughTimers();
  };
}, []);

// Clean up walkthrough when penalty dialog is hidden
useEffect(() => {
  if (!showPenalty && showPenaltyWalkthrough) {
    hideWalkthrough(false);
  }
}, [showPenalty]);
```

#### Timer Cleanup
```typescript
const clearWalkthroughTimers = () => {
  if (walkthroughDelayTimer.current) {
    clearTimeout(walkthroughDelayTimer.current);
    walkthroughDelayTimer.current = null;
  }
  if (walkthroughAutoHideTimer.current) {
    clearTimeout(walkthroughAutoHideTimer.current);
    walkthroughAutoHideTimer.current = null;
  }
  if (walkthroughReDisplayTimer.current) {
    clearTimeout(walkthroughReDisplayTimer.current);
    walkthroughReDisplayTimer.current = null;
  }
};
```

### User Experience States

#### Tutorial States
1. **Never Shown**: First-time users see walkthrough on first penalty dialog
2. **Shown, No Interaction**: Users who saw walkthrough but didn't tap settings
3. **Completed**: Users who tapped settings while walkthrough was visible

#### Re-display Logic
```typescript
// State transitions
Never Shown → Show Walkthrough → User Taps Settings → Completed (never show again)
Never Shown → Show Walkthrough → Auto-Hide → Schedule Re-display (8 hours)
Shown, No Interaction → 8 Hours Pass → Show Walkthrough Again
```

#### Smart Scheduling
- **8-Hour Window**: Prevents tutorial spam while ensuring users eventually see guidance
- **User Respect**: Immediately stops showing tutorial once user demonstrates awareness
- **Persistent Memory**: Remembers user interaction across app sessions and updates

## Technical Considerations

### Performance
- Minimal performance impact due to reuse of existing animation patterns
- State management follows React best practices
- Proper cleanup in reset functions prevents memory leaks

### Accessibility
- Haptic feedback for button interactions
- Sound effects for audio feedback
- Consistent touch targets and visual hierarchy

### Localization
- All text content supports multiple languages
- Uses existing translation system (`useLanguage` hook)
- Maintains text consistency with existing UI elements

## Future Enhancements

### Potential Improvements
1. **Custom penalty content**: Display penalty-specific questions or challenges
2. **Penalty tracking**: Track how often users refuse content for analytics
3. **Adaptive difficulty**: Adjust content difficulty based on refusal patterns
4. **Social features**: Share penalty statistics with friends

### Extensibility
The penalty feature is designed to be easily extensible:
- Additional buttons can be added to the penalty dialog
- Custom penalty logic can be implemented
- Different penalty types can be supported
- Integration with analytics and user behavior tracking

## Testing Guidelines

### Manual Testing Checklist
1. **Basic Functionality**
   - [ ] Refuse button triggers penalty dialog (when penalties are enabled)
   - [ ] Refuse button is hidden when penalties are set to "none"
   - [ ] Penalty dialog appears with smooth animation
   - [ ] Background gradient changes to red theme
   - [ ] Back to content button returns to previous question
   - [ ] Next match button resets the entire game
   - [ ] Same penalty is shown consistently for the same content item

2. **Animation Testing**
   - [ ] Question dialog fades out smoothly when refuse is pressed
   - [ ] Penalty dialog scales in with bounce effect
   - [ ] Background gradient transitions smoothly
   - [ ] Back to content transition is smooth and consistent
   - [ ] Next match transition properly resets game state

3. **State Management Testing**
   - [ ] Penalty state is properly reset on game reset
   - [ ] Content pools are not affected by back to content action
   - [ ] Game state is completely reset by next match action
   - [ ] No memory leaks or state inconsistencies

4. **Edge Cases**
   - [ ] Rapid button presses don't cause crashes
   - [ ] Penalty dialog works correctly with premium content
   - [ ] Proper behavior when banner is visible
   - [ ] Correct gradient priority (penalty > premium > default)

5. **Refuse Button Visibility Testing**
   - [ ] Refuse button is visible when at least one penalty is selected
   - [ ] Refuse button is hidden when penalties are set to "none"
   - [ ] Layout adjusts properly when refuse button is hidden (Skip and Reset buttons fill space)
   - [ ] Button visibility updates immediately when penalty settings change
   - [ ] No visual glitches during button show/hide transitions

6. **Dialog Interaction Testing**
   - [ ] InGameBanner in preview state dismisses penalty dialog smoothly
   - [ ] InGameBanner in payment state keeps penalty dialog open
   - [ ] No overlapping dialogs when banner is tapped during penalty
   - [ ] Smooth animation transitions between penalty and premium preview
   - [ ] Paywall opens correctly when penalty dialog is active (sequential banner)
   - [ ] No visual flash of original content during penalty-to-preview transition
   - [ ] Direct transition from penalty dialog to premium content preview
   - [ ] Premium content appears immediately after penalty dialog dismisses

7. **Penalty Walkthrough Tutorial Testing**
   - [ ] Walkthrough appears 500ms after penalty dialog opens (first time)
   - [ ] Correct language version displays (EN/ES/DOM)
   - [ ] Walkthrough auto-hides after 10 seconds if no interaction
   - [ ] Settings button tap immediately hides walkthrough and marks as completed
   - [ ] Completed walkthrough never shows again
   - [ ] Non-completed walkthrough re-appears after 8 hours
   - [ ] Walkthrough state persists across app sessions
   - [ ] Smooth fade-in/out animations with scale effects
   - [ ] Walkthrough hides when penalty dialog is dismissed
   - [ ] Language changes update walkthrough component in real-time
   - [ ] App backgrounding clears walkthrough timers properly
   - [ ] Component unmounting cleans up all walkthrough timers
   - [ ] AsyncStorage errors don't crash the walkthrough system

### Automated Testing
```typescript
// Example test cases for penalty feature
describe('Penalty Feature', () => {
  test('should show penalty dialog when refuse button is pressed', () => {
    // Test implementation
  });

  test('should return to content without affecting pools', () => {
    // Test implementation
  });

  test('should reset game when next match is pressed', () => {
    // Test implementation
  });

  test('should change background gradient to penalty colors', () => {
    // Test implementation
  });
});

// Example test cases for penalty walkthrough tutorial
describe('Penalty Walkthrough Tutorial', () => {
  test('should show walkthrough 500ms after penalty dialog opens', () => {
    // Test implementation
  });

  test('should display correct language version based on app language', () => {
    // Test implementation
  });

  test('should auto-hide after 10 seconds if no user interaction', () => {
    // Test implementation
  });

  test('should hide immediately when settings button is tapped', () => {
    // Test implementation
  });

  test('should mark as completed when user taps settings', () => {
    // Test implementation
  });

  test('should not show again once marked as completed', () => {
    // Test implementation
  });

  test('should re-display after 8 hours if not completed', () => {
    // Test implementation
  });

  test('should persist state across app sessions', () => {
    // Test implementation
  });

  test('should clean up timers on component unmount', () => {
    // Test implementation
  });

  test('should handle AsyncStorage errors gracefully', () => {
    // Test implementation
  });
});
```

## Troubleshooting

### Common Issues

#### Penalty Dialog Not Appearing
- **Cause**: Animation state not properly initialized
- **Solution**: Ensure `penaltyDialogScale` is initialized to 0
- **Check**: Verify `showPenalty` state is being set correctly

#### Animation Glitches
- **Cause**: Incorrect `runOnJS` usage or timing issues
- **Solution**: Ensure proper function references in `runOnJS` calls
- **Check**: Verify animation completion callbacks are working

#### Background Gradient Not Changing
- **Cause**: State dependency issues or incorrect priority logic
- **Solution**: Check gradient logic order and state dependencies
- **Check**: Ensure `showPenalty` state is declared before gradient logic

#### Memory Leaks
- **Cause**: Improper cleanup in reset functions
- **Solution**: Ensure all penalty-related state is reset in `resetGameState`
- **Check**: Verify no lingering timers or animation values

#### Walkthrough Tutorial Not Appearing
- **Cause**: Tutorial already marked as completed or timing issues
- **Solution**: Check AsyncStorage for `penaltyWalkthroughCompleted` key
- **Debug**: Clear AsyncStorage or check `walkthroughCompleted` state
- **Check**: Verify 500ms delay timer is working correctly

#### Walkthrough Not Auto-Hiding
- **Cause**: Timer cleanup issues or animation callback problems
- **Solution**: Ensure `clearWalkthroughTimers()` is called properly
- **Check**: Verify 10-second auto-hide timer is set correctly

#### Walkthrough Showing Wrong Language
- **Cause**: Language detection logic or component import issues
- **Solution**: Check language state and conditional rendering logic
- **Check**: Verify both `PenaltyWalkthroughEN` and `PenaltyWalkthroughES` are imported

#### Walkthrough Not Persisting State
- **Cause**: AsyncStorage write/read errors or key naming issues
- **Solution**: Check AsyncStorage permissions and error handling
- **Check**: Verify `saveWalkthroughState()` and `loadWalkthroughState()` functions

#### App Crashes During Walkthrough Animation
- **Cause**: Improper `runOnJS` usage or async function calls in animation callbacks
- **Solution**: Ensure named functions are used with `runOnJS` and async calls are wrapped in `setTimeout`
- **Check**: Verify animation callback structure and error handling

### Debug Tips
1. **Console Logging**: Add logs to track state changes and function calls
2. **Animation Debugging**: Use React Native Debugger to inspect animation values
3. **State Inspection**: Use React DevTools to monitor state changes
4. **Performance Monitoring**: Check for unnecessary re-renders or memory usage

## Code Examples

### Adding Custom Penalty Logic
```typescript
// Example: Custom penalty with timer
const showPenaltyWithTimer = () => {
  showPenaltyDialog();

  // Auto-hide after 10 seconds
  setTimeout(() => {
    if (showPenalty) {
      backToContentDialog();
    }
  }, 10000);
};
```

### Extending Penalty Dialog
```typescript
// Example: Adding a third button to penalty dialog
const PenaltyDialogExtended = ({ onBackToContent, onNextMatch, onCustomAction }) => {
  return (
    <View style={styles.penaltyContainer}>
      {/* Existing buttons */}
      <TouchableOpacity onPress={onCustomAction}>
        <Text>Custom Action</Text>
      </TouchableOpacity>
    </View>
  );
};
```

### Analytics Integration
```typescript
// Example: Track penalty usage
const showPenaltyDialog = () => {
  // Analytics tracking
  analytics.track('penalty_dialog_shown', {
    question_id: question?.id,
    category: question?.category,
    timestamp: Date.now()
  });

  // Existing penalty logic
  // ...
};
```

## Bug Fixes and Improvements

### Penalty Consistency Fix ✅ RESOLVED (December 2024)

**Issue**: Each time a user tapped "Back to Content" and then "Refuse" again, a different penalty was displayed instead of showing the same penalty consistently for the same content item.

**Root Cause**: The `showPenaltyDialog` function was calling `getRandomPenalty()` every time it was invoked, generating a new random penalty on each penalty dialog opening rather than maintaining the same penalty for a given content item.

**Solution**: Implemented penalty-per-content tracking system:

```typescript
// New state variables for penalty consistency
const [currentContentPenalty, setCurrentContentPenalty] = useState<string | null>(null);
const [penaltyContentId, setPenaltyContentId] = useState<string | null>(null);

// Modified showPenaltyDialog function
const showPenaltyDialog = async () => {
  const currentQuestionId = question?.id;
  let penaltyText: string;

  if (currentQuestionId && penaltyContentId === currentQuestionId && currentContentPenalty) {
    // Use existing penalty for this content item
    penaltyText = currentContentPenalty;
    console.log('🎯 Using existing penalty for content:', currentQuestionId);
  } else {
    // Generate new penalty only for new content
    const languageKey = language === 'dom' ? 'dom' : language === 'es' ? 'es' : 'en';
    const newPenaltyText = await getRandomPenalty(languageKey);
    penaltyText = newPenaltyText || t('penaltyNotSelected');

    // Store penalty for this content item
    if (currentQuestionId) {
      setCurrentContentPenalty(penaltyText);
      setPenaltyContentId(currentQuestionId);
      console.log('🎯 Generated new penalty for content:', currentQuestionId);
    }
  }

  setCurrentPenaltyText(penaltyText);
  // ... rest of dialog logic
};
```

**Penalty Clearing Logic**: Penalties are automatically cleared when:
- Moving to new content (`proceedToNextQuestion`, initial question setting)
- Showing premium content (premium content doesn't use penalties)
- Resetting the game (`resetGameState`)

**Expected Behavior After Fix**:
1. User taps "Refuse" → Penalty generated and stored for that content item
2. User taps "Back to Content" → Returns to content dialog
3. User taps "Refuse" again → Shows the **same penalty** from step 1
4. User moves to next content → Penalty cleared, new one generated for new content

**Files Modified**: `screens/TouchGameScreen.tsx`

**User Experience Impact**: Users now see consistent penalties for each content item, making the penalty system more predictable and fair. This eliminates confusion and provides a better user experience.

### Refuse Button Visibility Control ✅ IMPLEMENTED (December 2024)

**Feature**: Automatically hide the "Refuse" button when penalty is set as "none" to provide a cleaner UI experience.

**Issue**: When users set penalties to "none" in settings, the refuse button was still visible but non-functional, creating confusion and unnecessary UI clutter.

**Solution**: Implemented conditional rendering based on penalty selection state:

```typescript
// Access penalty selection state
const { selectedIndividualPenalties } = useAppSettings();

// Check if penalties are enabled
const arePenaltiesEnabled = selectedIndividualPenalties.length > 0;

// Conditional rendering in TouchGameScreen
{arePenaltiesEnabled && (
  <TouchableOpacity
    style={styles.refuseButton}
    onPress={async () => {
      if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      await playSound('tapEffect1');
      showPenaltyDialog();
    }}
  >
    <RefuseIcon style={styles.refuseButtonIcon} width={26} height={26} />
    <Text style={[typography.subtitle2, styles.refuseButtonLabel]}>
      {t('refurseButton')}
    </Text>
  </TouchableOpacity>
)}
```

**Behavior**:
- **Penalties enabled** (user selected at least one penalty): Refuse button visible and functional
- **Penalties set to "none"** (no penalties selected): Refuse button completely hidden
- **Dynamic layout**: Skip and Reset buttons automatically adjust to fill available space using existing flex layout

**Technical Implementation**:
- Uses `selectedIndividualPenalties` from `AppSettingsContext`
- Checks array length to determine if penalties are enabled
- Conditional rendering with React's `&&` operator
- No additional styling needed due to existing flex layout

**User Experience Benefits**:
- **Cleaner interface**: Removes unnecessary UI elements when penalties are disabled
- **Reduced confusion**: Users don't see non-functional buttons
- **Consistent behavior**: UI reflects the actual penalty settings
- **Better accessibility**: Fewer interactive elements when not needed

**Files Modified**: `screens/TouchGameScreen.tsx`

### Penalty Walkthrough Tutorial System ✅ UPDATED (December 2024)

**Feature**: Intelligent tutorial system that guides users to penalty settings when they first encounter the penalty feature.

**Purpose**: Ensure users understand how to customize their penalty experience and discover the penalty settings functionality.

#### Updated Implementation (December 2024)

**Major Change**: Removed automatic 8-hour re-display timer and implemented user-type-based walkthrough logic.

**New Behavior**:
- **Free Users**: Always see penalty walkthrough every 8 hours when penalty dialog opens
- **Premium Users**: See walkthrough until they interact with settings, then permanently hidden

```typescript
// Core tutorial components (unchanged)
- PenaltyWalkthroughEN: English tutorial graphic (255x110px)
- PenaltyWalkthroughES: Spanish/Dominican tutorial graphic (255x110px)

// State management (simplified)
const [showPenaltyWalkthrough, setShowPenaltyWalkthrough] = useState(false);
const [walkthroughCompleted, setWalkthroughCompleted] = useState(false);
const penaltyWalkthroughOpacity = useSharedValue(0);

// Timer management (reduced)
const walkthroughDelayTimer = useRef<NodeJS.Timeout | null>(null);
const walkthroughAutoHideTimer = useRef<NodeJS.Timeout | null>(null);
// Removed: walkthroughReDisplayTimer (no longer used)
```

**Updated Tutorial Logic**:

1. **Display Trigger**: Appears 500ms after penalty dialog opens
2. **User Type Detection**: Different behavior for free vs premium users
3. **Free Users**: Always check 8-hour timing before showing
4. **Premium Users**: Check completion status before showing
5. **Language Detection**: Shows appropriate version based on app language
6. **Auto-Hide**: Disappears after 10 seconds if no interaction
7. **Persistence**: State saved using AsyncStorage across app sessions

**New Smart Scheduling Logic**:

```typescript
// Free Users (isPremiumUnlocked = false)
Never Shown → Show Walkthrough → Auto-Hide → Show Again After 8 Hours
Shown Previously → 8 Hours Pass → Show Walkthrough Again
Always Shows Every 8 Hours (regardless of interaction)

// Premium Users (isPremiumUnlocked = true)
Never Shown → Show Walkthrough → User Taps Settings → Completed (never show again)
Never Shown → Show Walkthrough → Auto-Hide → Show Again Next Time
Completed → Never Show Again
```

**Key Implementation Changes**:

```typescript
// Updated loadWalkthroughState function
const loadWalkthroughState = async () => {
  try {
    // For free users, always check if 8 hours have passed since last shown
    if (!isPremiumUnlocked) {
      const lastShown = await AsyncStorage.getItem('penaltyWalkthroughLastShown');

      if (lastShown) {
        const lastShownTime = parseInt(lastShown, 10);
        const now = Date.now();
        const eightHours = 8 * 60 * 60 * 1000;

        if (now - lastShownTime < eightHours) {
          return; // Not enough time has passed
        }
      }
      return; // Show walkthrough for free users
    }

    // For premium users, check if they've completed the walkthrough
    const completed = await AsyncStorage.getItem('penaltyWalkthroughCompleted');
    if (completed === 'true') {
      setWalkthroughCompleted(true);
      return;
    }
  } catch (error) {
    console.error('Error loading walkthrough state:', error);
  }
};

// Updated saveWalkthroughState function
const saveWalkthroughState = async (completed: boolean, userInteracted: boolean) => {
  try {
    if (completed && userInteracted && isPremiumUnlocked) {
      // Only save completion state for premium users
      await AsyncStorage.setItem('penaltyWalkthroughCompleted', 'true');
    } else {
      // Always save the last shown time for both free and premium users
      await AsyncStorage.setItem('penaltyWalkthroughLastShown', Date.now().toString());
    }
  } catch (error) {
    console.error('Error saving walkthrough state:', error);
  }
};

// Updated showWalkthrough function (now async)
const showWalkthrough = async () => {
  // For premium users, check if walkthrough is completed
  if (isPremiumUnlocked && walkthroughCompleted) return;

  // For free users, always check timing before showing
  if (!isPremiumUnlocked) {
    try {
      const lastShown = await AsyncStorage.getItem('penaltyWalkthroughLastShown');
      if (lastShown) {
        const lastShownTime = parseInt(lastShown, 10);
        const now = Date.now();
        const eightHours = 8 * 60 * 60 * 1000;

        if (now - lastShownTime < eightHours) {
          return; // Not enough time has passed for free users
        }
      }
    } catch (error) {
      console.error('Error checking walkthrough timing:', error);
      return;
    }
  }

  // Show walkthrough logic (unchanged)
  // ...
};

// Updated hideWalkthrough function (removed re-display timer)
const hideWalkthrough = (userInteracted: boolean = false) => {
  // ... existing logic ...

  const handleWalkthroughHidden = () => {
    setShowPenaltyWalkthrough(false);

    if (userInteracted && isPremiumUnlocked) {
      setWalkthroughCompleted(true);
      setTimeout(() => saveWalkthroughState(true, true), 0);
    } else {
      setTimeout(() => saveWalkthroughState(false, false), 0);
      // Note: Removed the 8-hour re-display timer as requested
      // Free users will see the walkthrough every 8 hours when penalty dialog opens
    }
  };

  // ... animation logic ...
};
```

**Technical Improvements**:
- **Removed Background Timers**: No more automatic 8-hour timers running in background
- **Event-Driven Display**: Walkthrough only appears when penalty dialog opens
- **User-Type Awareness**: Different logic for free vs premium users
- **Simplified Timer Management**: Removed unused `walkthroughReDisplayTimer`
- **Better Memory Management**: No long-running background timers
- **Persistent Timing**: Uses AsyncStorage for reliable 8-hour tracking

**AsyncStorage Keys** (unchanged):
- `penaltyWalkthroughCompleted`: Boolean indicating permanent completion (premium users only)
- `penaltyWalkthroughLastShown`: Timestamp of last walkthrough display (both user types)

**User Experience Benefits**:
- **Free Users**: Consistent reminders about penalty customization every 8 hours
- **Premium Users**: Respectful one-time tutorial that can be permanently dismissed
- **Better Performance**: No background timers consuming resources
- **Reliable Timing**: Event-driven display ensures consistent behavior
- **Memory Efficient**: Reduced timer usage and better cleanup

**Files Modified**:
- `screens/TouchGameScreen.tsx` (updated walkthrough logic)
- `assets/icons/penalty/penalty_walkthrough_en.svg` (unchanged)
- `assets/icons/penalty/penalty_walkthrough_es.svg` (unchanged)

### Code Refactoring: Centralized Walkthrough Logic ✅ COMPLETED (December 2024)

**Issue**: The walkthrough timing logic was duplicated across multiple functions (`loadWalkthroughState` and `showWalkthrough`), leading to potential inconsistencies and maintenance issues.

**Solution**: Refactored the code to use a centralized function for all walkthrough timing decisions.

#### New Centralized Architecture

```typescript
// Single source of truth for walkthrough display logic
const shouldShowWalkthrough = async (): Promise<boolean> => {
  try {
    // For premium users, check if they've completed the walkthrough
    if (isPremiumUnlocked) {
      const completed = await AsyncStorage.getItem('penaltyWalkthroughCompleted');
      return completed !== 'true';
    }

    // For free users, always check if enough time has passed since last shown
    const lastShown = await AsyncStorage.getItem('penaltyWalkthroughLastShown');
    if (lastShown) {
      const lastShownTime = parseInt(lastShown, 10);
      const now = Date.now();
      const eightHours = 8 * 60 * 60 * 1000; // 8 hours in milliseconds

      return now - lastShownTime >= eightHours;
    }

    // If no lastShown time exists, show walkthrough
    return true;
  } catch (error) {
    console.error('Error checking walkthrough conditions:', error);
    return false;
  }
};

// Simplified state loading (only for premium users)
const loadWalkthroughState = async () => {
  try {
    // For premium users, load completion state
    if (isPremiumUnlocked) {
      const completed = await AsyncStorage.getItem('penaltyWalkthroughCompleted');
      if (completed === 'true') {
        setWalkthroughCompleted(true);
      }
    }
    // For free users, no initial state loading needed - timing is checked on demand
  } catch (error) {
    console.error('Error loading walkthrough state:', error);
  }
};

// Simplified walkthrough display function
const showWalkthrough = async () => {
  // Check if walkthrough should be shown using centralized logic
  const shouldShow = await shouldShowWalkthrough();
  if (!shouldShow) {
    return;
  }

  // Clear any existing timers and show walkthrough
  clearWalkthroughTimers();

  walkthroughDelayTimer.current = setTimeout(() => {
    setShowPenaltyWalkthrough(true);
    penaltyWalkthroughOpacity.value = withTiming(1, { duration: 300 });

    walkthroughAutoHideTimer.current = setTimeout(() => {
      hideWalkthrough(false);
    }, 10000);
  }, 500);
};
```

**Key Improvements**:
- **Single Source of Truth**: All timing logic centralized in `shouldShowWalkthrough()`
- **Eliminated Duplication**: Removed duplicate timing calculations
- **Better Maintainability**: Changes to timing logic only need to be made in one place
- **Type Safety**: Added TypeScript return type annotations
- **Cleaner Code**: Simplified functions with clear responsibilities
- **Consistent Behavior**: No more potential for timing inconsistencies

**Technical Benefits**:
- **Reduced Code Complexity**: Fewer lines of code with clearer intent
- **Easier Testing**: Single function to test for timing logic
- **Better Error Handling**: Centralized error handling for timing checks
- **Performance**: Eliminated redundant AsyncStorage calls

### Default Penalty Selection Bug Fix ✅ RESOLVED (December 2024)

**Issue**: Users were not seeing the Refuse button on first app launch because penalty settings were defaulting to "None" instead of appropriate default penalties based on their subscription status.

**Root Cause**: Timing issue in the penalty initialization process:
1. `AppSettingsContext` loaded and tried to set default penalties immediately on app startup
2. `getDefaultPenaltyIds()` called `loadPenaltyContent()` which only loads from cache
3. On first app launch (or when cache is cleared), no cached penalty content existed
4. `loadPenaltyContent()` returned `null`, causing `getDefaultPenaltyIds()` to return empty array
5. Later, HomeScreen fetched penalty content from API, but default setting logic had already run

**Solution**: Implemented retry mechanism for default penalty setting after content is fetched:

#### AppSettingsContext Changes

```typescript
// Added new function to retry setting default penalties
interface AppSettingsContextProps {
  // ... existing props ...
  retrySetDefaultPenalties: () => Promise<void>;
}

// Implementation
const retrySetDefaultPenalties = async () => {
  try {
    console.log('🎯 Retrying to set default penalties...');
    await setDefaultPenaltiesIfNeeded(isPremiumUnlocked);
  } catch (error) {
    console.error('Error retrying to set default penalties:', error);
  }
};

// Added to context provider value
<AppSettingsContext.Provider
  value={{
    // ... existing values ...
    retrySetDefaultPenalties,
  }}
>
```

#### HomeScreen Integration

```typescript
// Import the retry function
const { isHapticsOn, retrySetDefaultPenalties } = useAppSettings();

// Call retry after penalty content is successfully fetched
const refreshGameContent = async () => {
  try {
    // ... existing game content refresh logic ...

    // Also refresh penalty content
    console.log('HOME: Refreshing penalty content...');
    try {
      const penaltyContent = await fetchAndUpdatePenalties();
      if (penaltyContent) {
        const totalPenalties = Object.values(penaltyContent).reduce((sum: number, arr) => sum + (arr as any[]).length, 0);
        console.log(`✅ Penalty content refreshed: ${totalPenalties} penalties loaded`);

        // Now that penalty content is available, retry setting default penalties
        console.log('🎯 Penalty content loaded, retrying default penalty setup...');
        await retrySetDefaultPenalties();
      } else {
        console.log('⚠️ No penalty content available');
      }
    } catch (penaltyError) {
      console.error('Error refreshing penalty content:', penaltyError);
    }

    // ... rest of function ...
  } catch (error) {
    console.error('HOME: Error updating content:', error);
  }
};
```

**Expected Behavior After Fix**:
1. **App Launch**: HomeScreen loads and fetches penalty content from API
2. **Content Available**: After penalty content is successfully fetched and cached
3. **Retry Default Setting**: `retrySetDefaultPenalties()` is called automatically
4. **Default Selection**: Appropriate default penalty is selected based on user's premium status:
   - **Free Users**: Get penalty marked with `isDefaultFree: true`
   - **Premium Users**: Get penalty marked with `isDefaultPremium: true`
5. **Refuse Button Visible**: Users immediately see the Refuse button in game interface

**Backend Verification**: Confirmed that backend has default penalties configured:
- Default free penalty: "Do 10 push-ups 💪" (`isDefaultFree: true`)
- Default premium penalty: "Take a shot 🥃" (`isDefaultPremium: true`)

**Technical Benefits**:
- **Reliable Initialization**: Default penalties are set regardless of cache state
- **Proper Timing**: Retry happens after content is actually available
- **User Experience**: Users see Refuse button immediately on first app launch
- **Subscription Awareness**: Correct defaults based on premium status
- **Backward Compatible**: Existing users' penalty selections remain unchanged

**Files Modified**:
- `context/AppSettingsContext.tsx` (added retry function)
- `screens/HomeScreen.tsx` (integrated retry call after penalty content fetch)

**User Experience Impact**:
- **First-Time Users**: Immediately see Refuse button with appropriate default penalty
- **Existing Users**: No impact on current penalty selections
- **Free Users**: Get free default penalty automatically
- **Premium Users**: Get premium default penalty automatically

## Related Documentation

- [Game Mechanics](./game-mechanics.md) - Core game flow and touch detection
- [Components](./components.md) - UI component architecture
- [State Management](./state-management.md) - App state patterns
- [Screens](./screens.md) - Screen navigation and structure

## Version History

### v1.0.0 (Initial Implementation)
- Basic penalty dialog functionality
- Refuse button integration
- Animation system implementation
- Dynamic background gradients
- State management integration

### Future Versions
- Enhanced penalty logic
- Analytics integration
- Custom penalty content
- Performance optimizations
