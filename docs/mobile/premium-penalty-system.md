# Premium Penalty System

This document describes the comprehensive premium penalty system implemented in TapTrap, which allows for premium-only penalties and automatic default penalty assignment based on user subscription status.

## Overview

The premium penalty system provides:

- **Premium Penalties**: Penalties that are only accessible to users with premium subscriptions
- **Default Penalty Assignment**: Automatic selection of appropriate default penalties based on user's premium status
- **Premium Badge Display**: Visual indicators for premium penalties in the settings interface
- **RevenueCat Integration**: Seamless paywall presentation when free users attempt to access premium penalties
- **Backward Compatibility**: Existing penalty data continues to work without modification

## Architecture

### Backend Components

1. **Penalty Model** (`backend/src/models/penalty.js`)
   - Added `isPremium`, `isDefaultFree`, and `isDefaultPremium` boolean fields
   - Maintains backward compatibility with existing penalties

2. **Penalty Controller** (`backend/src/controllers/penaltyController.js`)
   - Returns premium fields in API responses
   - Handles default penalty logic

3. **Admin Controller** (`backend/src/controllers/adminController.js`)
   - Enforces mutual exclusivity for default penalties
   - Validates premium penalty configurations

### Mobile Components

1. **Penalty Service** (`services/penaltyService.ts`)
   - Fetches penalty content with premium fields
   - Implements default penalty selection logic
   - Handles premium status-based filtering

2. **App Settings Context** (`context/AppSettingsContext.tsx`)
   - Manages penalty selection state
   - Integrates with premium status from RevenueCat
   - Handles default penalty assignment

3. **Settings Screen** (`screens/SettingsScreen.tsx`)
   - Displays premium badges for premium penalties
   - Shows paywall for premium penalty access
   - Provides visual feedback for disabled penalties

### Admin Panel Components

1. **Add/Edit Penalty Forms** (`admin/src/pages/AddPenalty.js`, `admin/src/pages/EditPenalty.js`)
   - Premium toggle switches
   - Default penalty assignment controls
   - Mutual exclusivity enforcement

2. **Penalty List** (`admin/src/pages/PenaltyList.js`)
   - Premium status indicators
   - Default status badges
   - Enhanced table view

## Data Flow

### Penalty Content Fetching

```
App Launch → penaltyService.fetchPenalties() → Backend API → MongoDB
                     ↓
Cache penalties with premium fields → Return to app
```

### Default Penalty Assignment

```
User opens Settings → Check premium status → Load penalty content
                              ↓
Premium User: Select default premium penalty (if available)
Free User: Select default free penalty (if available)
                              ↓
Update selectedIndividualPenalties in AppSettingsContext
```

### Premium Penalty Access

```
Free User taps Premium Penalty → handlePremiumPenaltyTap()
                                        ↓
iOS: Show RevenueCat Modal → Purchase → unlockPremium()
Android: presentPaywall() → Purchase → unlockPremium()
```

## Implementation Details

### Premium Fields

Each penalty document includes:

```typescript
interface Penalty {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
  category: string;
  isPremium: boolean;        // Premium-only access
  isDefaultFree: boolean;    // Default for free users
  isDefaultPremium: boolean; // Default for premium users
}
```

### Business Rules

1. **Mutual Exclusivity**: Premium penalties cannot be default for free users
2. **Single Default**: Only one penalty per user type can be marked as default
3. **Premium Access**: Free users cannot select premium penalties
4. **Fallback Logic**: If no default is set, no penalty is auto-selected

### Default Selection Logic

```typescript
const getDefaultPenalties = (penalties: PenaltyContent, isPremium: boolean): string[] => {
  const allPenalties = Object.values(penalties).flat();
  
  if (isPremium) {
    // Premium users: prefer premium default, fallback to free default
    const premiumDefault = allPenalties.find(p => p.isDefaultPremium);
    if (premiumDefault) return [premiumDefault.id];
    
    const freeDefault = allPenalties.find(p => p.isDefaultFree);
    if (freeDefault) return [freeDefault.id];
  } else {
    // Free users: only free defaults
    const freeDefault = allPenalties.find(p => p.isDefaultFree);
    if (freeDefault) return [freeDefault.id];
  }
  
  return []; // No default available
};
```

## User Experience

### Free Users

1. **Penalty Selection**: Can only select non-premium penalties
2. **Premium Penalties**: Displayed with reduced opacity and premium badge
3. **Premium Tap**: Shows RevenueCat paywall for upgrade
4. **Default Assignment**: Automatically gets free default penalty (if set)

### Premium Users

1. **Full Access**: Can select any penalty (premium or free)
2. **Premium Penalties**: Displayed normally without restrictions
3. **Default Assignment**: Gets premium default penalty, fallback to free default

### Visual Indicators

- **Premium Badge**: Purple badge on premium penalties
- **Disabled State**: 50% opacity for inaccessible premium penalties
- **Selection State**: Green checkmark for selected penalties

## Admin Management

### Creating Premium Penalties

1. Navigate to "Add New Penalty" in admin panel
2. Fill in penalty text for all languages
3. Select category
4. Toggle "Premium Only" to make it premium-exclusive
5. Optionally set as "Default for Premium Users"

### Setting Default Penalties

1. Edit existing penalty or create new one
2. Toggle "Default for Free Users" for non-premium penalties
3. Toggle "Default for Premium Users" for any penalty
4. Backend ensures only one default per user type

### Visual Management

- **Premium Column**: Shows "Premium" or "Free" badge
- **Default Column**: Shows "Free Default", "Premium Default", or "-"
- **Form Validation**: Prevents invalid combinations

## Migration and Deployment

### Database Migration

After deploying the premium penalty system:

```bash
cd backend
npm run migrate:penalties
```

This adds premium fields to existing penalties with default values.

### Deployment Checklist

1. ✅ Deploy backend with premium penalty model
2. ✅ Run database migration
3. ✅ Deploy admin panel with premium controls
4. ✅ Deploy mobile app with premium penalty support
5. ✅ Test premium penalty flow end-to-end
6. ✅ Verify RevenueCat integration works

## Testing

### Test Scenarios

1. **Free User Experience**
   - Verify premium penalties are disabled
   - Test paywall presentation on premium penalty tap
   - Confirm default free penalty assignment

2. **Premium User Experience**
   - Verify full access to all penalties
   - Test default premium penalty assignment
   - Confirm fallback to free default if no premium default

3. **Admin Panel**
   - Test premium toggle functionality
   - Verify default penalty mutual exclusivity
   - Test visual indicators and badges

4. **Migration**
   - Test migration on copy of production data
   - Verify all penalties have premium fields
   - Confirm no data loss or corruption

## Troubleshooting

### Common Issues

1. **Premium penalties not showing**: Check penalty content fetch and premium field inclusion
2. **Paywall not appearing**: Verify RevenueCat configuration and platform-specific implementation
3. **Default penalties not assigned**: Check penalty content structure and default selection logic
4. **Admin toggles not working**: Verify form state management and mutual exclusivity logic

### Debug Tools

- Console logs in penalty service for content fetching
- RevenueCat logs for premium status and paywall events
- Admin panel network tab for API requests
- MongoDB queries to verify penalty data structure

## Future Enhancements

Potential improvements to the premium penalty system:

1. **Penalty Categories**: Premium-only penalty categories
2. **Time-Limited Access**: Temporary premium penalty access
3. **Penalty Packs**: Bundled premium penalty collections
4. **Usage Analytics**: Track premium penalty engagement
5. **A/B Testing**: Test different premium penalty strategies
