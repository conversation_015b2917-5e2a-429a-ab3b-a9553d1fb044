# TapTrap Mobile App Structure

## Directory Structure

The TapTrap mobile app follows a standard React Native application structure:

```
TapTrap/
├── assets/                 # Images, sounds, and other static resources
│   ├── icons/              # UI icons
│   ├── images/             # Game images
│   ├── lottie/             # Lottie animation files
│   ├── redesign/           # SVG assets for UI
│   └── sounds/             # Game sound effects
├── components/             # Reusable UI components
│   ├── game/               # Game-specific components
│   │   ├── GameContext.tsx # Game state management
│   │   ├── SparkEffect.tsx # Visual effects for the game
│   │   └── playSound.tsx   # Sound management
│   ├── ui/                 # UI components
│   ├── ThemedText.tsx      # Text component with styling
│   └── ThemedView.tsx      # View component with styling
├── context/                # React context providers
│   └── AppSettingsContext.tsx # App settings state management
├── hooks/                  # Custom React hooks
│   └── useThemeColor.ts    # Theme color management
├── screens/                # Main application screens
│   ├── HomeScreen.tsx      # Home screen with game mode selection
│   ├── SelectCategoriesScreen.tsx # Category selection screen
│   ├── TouchGameScreen.tsx # Main game screen
│   └── SettingsScreen.tsx  # Settings screen
├── services/               # API and utility services
│   ├── contentService.ts   # Content fetching and caching
│   ├── i18nService.tsx     # Localization service
│   └── revenueCatService.ts # In-app purchase handling
├── App.tsx                 # Main application component
└── package.json            # Project dependencies and scripts
```

## Key Files and Their Purposes

### App.tsx

The main entry point for the application. It sets up:
- Navigation container and stack
- Context providers (AppSettings, Language, Game)
- Status bar configuration
- Initial content fetching

### Context Providers

#### AppSettingsContext.tsx
Manages app-wide settings including:
- Sound effects toggle
- Haptic feedback toggle
- Premium content unlock status

#### GameContext.tsx (in components/game/)
Manages all game-related state:
- Selected game modes
- Selected categories
- Current question/challenge
- Game content data
- Functions for getting next questions/challenges

### Navigation

The app uses React Navigation with a stack navigator defined in App.tsx:

```jsx
<Stack.Navigator screenOptions={{ headerShown: false }}>
  <Stack.Screen name="Home" component={HomeScreen} />
  <Stack.Screen name="SelectCategories" component={SelectCategoriesScreen} />
  <Stack.Screen name="TouchGame" component={TouchGameScreen} />
  <Stack.Screen name="Settings" component={SettingsScreen} />
</Stack.Navigator>
```

### Services

#### contentService.ts
Handles fetching and caching game content:
- Fetches content from backend API
- Caches content for offline use
- Provides localized text based on device language

#### i18nService.tsx
Manages language selection and text localization:
- Detects device language
- Provides translations for UI text
- Allows manual language switching

#### revenueCatService.ts
Handles in-app purchases for premium content:
- Initializes RevenueCat SDK
- Checks user subscription status
- Provides functions to unlock premium content
