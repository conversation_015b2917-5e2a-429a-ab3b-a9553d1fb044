# Android Content-Type Header Fix - TapTrap

## 🚨 Problema Identificado

**Síntoma**: API calls fallan en algunos dispositivos Android específicos con error "Network request failed"

**Causa Raíz**: Headers `Content-Type: application/json` explícitos causan problemas en ciertos dispositivos Android

## 📋 Solución Implementada

### **Basado en StackOverflow Solution**
Referencia: https://stackoverflow.com/questions/60987459/react-native-with-expo-fetch-with-formdata-network-error-on-android-only/64397804#64397804

**Principio**: Eliminar headers Content-Type explícitos para que el cliente (axios/fetch) los establezca automáticamente.

### **Archivos Modificados**

#### 1. `services/api.ts`
```typescript
// ANTES
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json', // ❌ Problemático en Android
  },
  timeout: 15000,
});

// DESPUÉS
const api = axios.create({
  baseURL: API_URL,
  // ✅ Content-Type omitido intencionalmente
  timeout: 15000,
});
```

#### 2. `services/pushNotificationService.ts`
```typescript
// ANTES - uploadTokenToBackend()
const response = await axios.post(`${API_URL}/push-tokens`, data, {
  headers: {
    'Content-Type': 'application/json', // ❌ Problemático
  },
  timeout: 15000,
});

// DESPUÉS
const response = await axios.post(`${API_URL}/push-tokens`, data, {
  // ✅ Content-Type omitido
  timeout: 15000,
});

// ANTES - updateNotificationPreferenceOnBackend()
const response = await axios.put(`${API_URL}/push-tokens/preference`, data, {
  headers: {
    'Content-Type': 'application/json', // ❌ Problemático
  },
  timeout: 15000,
});

// DESPUÉS
const response = await axios.put(`${API_URL}/push-tokens/preference`, data, {
  // ✅ Content-Type omitido
  timeout: 15000,
});
```

#### 3. `services/penaltyService.ts`
```typescript
// ANTES
const response = await fetch(penaltyUrl, {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json', // ❌ Problemático
  },
});

// DESPUÉS
const response = await fetch(penaltyUrl, {
  method: 'GET',
  // ✅ Content-Type omitido
});
```

## 🔬 Por Qué Funciona Esta Solución

### **Problema Técnico**
1. **Android Network Stack**: Algunos dispositivos Android tienen problemas con headers Content-Type explícitos
2. **Boundary Issues**: Aunque TapTrap no usa FormData, el problema es similar - headers explícitos interfieren
3. **Client Auto-Detection**: Axios y fetch pueden detectar automáticamente el Content-Type correcto

### **Dispositivos Afectados**
- **Samsung** con Knox Security
- **Huawei** con EMUI security policies  
- **Xiaomi** con MIUI security center
- **Android 7.0+** con validación SSL estricta

### **Comportamiento Esperado**
- **Axios**: Automáticamente establece `Content-Type: application/json` para objetos JSON
- **Fetch**: Automáticamente detecta el tipo de contenido basado en el body
- **Android**: Acepta headers establecidos automáticamente sin problemas

## ✅ Beneficios de la Solución

1. **Compatibilidad Mejorada**: Funciona en más dispositivos Android
2. **Menos Código**: Elimina headers redundantes
3. **Mejor Práctica**: Deja que el cliente maneje los headers automáticamente
4. **Sin Efectos Secundarios**: No afecta funcionalidad en dispositivos que funcionaban

## 🧪 Testing

### **Antes del Fix**
```bash
# Error típico en dispositivos problemáticos
TypeError: Network request failed
    at XMLHttpRequest.xhr.onerror
```

### **Después del Fix**
```bash
# Requests exitosos
API Request: GET /content
API Response: 200 /content
✅ API response received: 200
```

### **Dispositivos para Testear**
1. **Samsung Galaxy S21** (Android 11)
2. **Huawei P30 Pro** (EMUI 10)
3. **Xiaomi Mi 11** (MIUI 12)
4. **OnePlus 9** (OxygenOS 11)

## 📊 Impacto Esperado

### **API Calls Afectados**
- ✅ Content fetching (`/content`)
- ✅ Penalties fetching (`/penalties`)
- ✅ Push token upload (`/push-tokens`)
- ✅ Notification preferences (`/push-tokens/preference`)
- ✅ What's new logs (`/whatsnew`)

### **Funcionalidades Mejoradas**
- ✅ Descarga de contenido del juego
- ✅ Descarga de penalizaciones
- ✅ Registro de push notifications
- ✅ Actualización de preferencias
- ✅ Logs de "What's New"

## 🔄 Rollback Plan

Si la solución causa problemas, se puede revertir fácilmente:

```typescript
// Revertir agregando Content-Type de vuelta
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 15000,
});
```

## 📝 Notas Importantes

1. **No afecta backend**: El backend sigue recibiendo `application/json` correctamente
2. **Mantiene funcionalidad**: Todos los API calls siguen funcionando igual
3. **Mejora compatibilidad**: Soluciona problemas específicos de Android
4. **Práctica estándar**: Muchas librerías recomiendan omitir Content-Type para JSON

## 🎯 Próximos Pasos

1. **Build de prueba** con los cambios
2. **Testing en dispositivos problemáticos**
3. **Monitoreo de logs** para verificar mejoras
4. **Feedback de usuarios** afectados
