# TapTrap Mobile App Development Guide

## Development Environment Setup

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Expo CLI (`npm install -g expo-cli`)
- Xcode (for iOS development)
- Android Studio (for Android development)
- Git

### Getting Started

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/taptrap.git
   cd taptrap
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your API encryption key and other required variables
   ```
   See [Environment Variables](./environment-variables.md) for detailed configuration.

3. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

4. Start the development server:
   ```bash
   npm start
   # or
   yarn start
   ```

5. Run on a device or emulator:
   - Press 'a' for Android
   - Press 'i' for iOS
   - Scan the QR code with the Expo Go app

## Project Structure

See the [Structure Documentation](./structure.md) for a detailed overview of the project structure.

## Development Workflow

### Adding a New Screen

1. Create a new file in the `screens/` directory:
   ```typescript
   // screens/NewScreen.tsx
   import React from 'react';
   import { View, Text } from 'react-native';
   import { typography } from '@/components/ThemedText';

   export default function NewScreen() {
     return (
       <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
         <Text style={typography.heading2}>New Screen</Text>
       </View>
     );
   }
   ```

2. Add the screen to the navigation stack in `App.tsx`:
   ```typescript
   <Stack.Navigator screenOptions={{ headerShown: false }}>
     <Stack.Screen name="Home" component={HomeScreen} />
     <Stack.Screen name="SelectCategories" component={SelectCategoriesScreen} />
     <Stack.Screen name="TouchGame" component={TouchGameScreen} />
     <Stack.Screen name="Settings" component={SettingsScreen} />
     <Stack.Screen name="NewScreen" component={NewScreen} />
   </Stack.Navigator>
   ```

3. Add navigation to the new screen:
   ```typescript
   import { useNavigation } from '@react-navigation/native';

   function SomeComponent() {
     const navigation = useNavigation();

     const handlePress = () => {
       navigation.navigate('NewScreen' as never);
     };

     return (
       <Button title="Go to New Screen" onPress={handlePress} />
     );
   }
   ```

### Adding a New Component

1. Create a new file in the `components/` directory:
   ```typescript
   // components/MyComponent.tsx
   import React from 'react';
   import { View, Text, StyleSheet } from 'react-native';

   interface MyComponentProps {
     title: string;
   }

   export default function MyComponent({ title }: MyComponentProps) {
     return (
       <View style={styles.container}>
         <Text style={styles.title}>{title}</Text>
       </View>
     );
   }

   const styles = StyleSheet.create({
     container: {
       padding: 16,
       backgroundColor: '#f0f0f0',
       borderRadius: 8,
     },
     title: {
       fontSize: 18,
       fontWeight: 'bold',
     },
   });
   ```

2. Import and use the component:
   ```typescript
   import MyComponent from '@/components/MyComponent';

   function SomeScreen() {
     return (
       <View>
         <MyComponent title="Hello World" />
       </View>
     );
   }
   ```

### Adding New Translations

1. Add new keys to the translations object in `services/i18nService.tsx`:
   ```typescript
   const translations = {
     // Existing translations...

     newFeature: {
       en: 'New Feature',
       es: 'Nueva Función'
     },
     newDescription: {
       en: 'This is a new feature',
       es: 'Esta es una nueva función'
     },
   };
   ```

2. Use the new translations:
   ```typescript
   import { useLanguage } from '@/services/i18nService';

   function MyComponent() {
     const { t } = useLanguage();

     return (
       <View>
         <Text>{t('newFeature')}</Text>
         <Text>{t('newDescription')}</Text>
       </View>
     );
   }
   ```

### Adding New Game Content

Game content is fetched from the backend API, but you can add new content to the bundled content for testing:

1. Edit the `assets/game_content.json` file:
   ```json
   {
     "questions": {
       "casual_questions": [
         {
           "id": "q1",
           "text_en": "What's your favorite color?",
           "text_es": "¿Cuál es tu color favorito?"
         },
         // Add new question
         {
           "id": "q_new",
           "text_en": "What's your favorite movie?",
           "text_es": "¿Cuál es tu película favorita?"
         }
       ],
       // Other categories...
     },
     // Challenges...
   }
   ```

2. Test the new content by running the app in offline mode.

## Working with the Backend

The mobile app connects to the backend API to fetch game content. See the [Backend Integration Documentation](../API.md) for details on the API endpoints.

### Testing with the Backend

1. Make sure the backend server is running:
   ```bash
   cd backend
   npm run dev
   ```

2. Update the API URL in `services/contentService.ts` if needed:
   ```typescript
   const API_URL = 'http://localhost:3000/api';
   ```

3. Run the app and check the console for API request logs.

## Building for Production

### Expo Build

To create a production build using Expo:

```bash
expo build:android  # For Android
expo build:ios      # For iOS
```

### EAS Build

For more advanced builds with Expo Application Services:

1. Install EAS CLI:
   ```bash
   npm install -g eas-cli
   ```

2. Configure EAS:
   ```bash
   eas build:configure
   ```

3. Set up environment variables in EAS:
   ```bash
   eas secret:create --scope project --name API_ENCRYPTION_KEY --value "your-encryption-key"
   ```
   See [Environment Variables](./environment-variables.md) for more details.

4. Build for production:
   ```bash
   eas build --platform android
   eas build --platform ios
   ```

## Testing

### Manual Testing

1. Test on multiple devices (phones and tablets)
2. Test on both Android and iOS
3. Test with different language settings
4. Test offline functionality
5. Test with different content categories

### Automated Testing

To run tests:

```bash
npm test
# or
yarn test
```

To add a new test:

1. Create a test file in the `__tests__` directory:
   ```typescript
   // __tests__/MyComponent-test.tsx
   import * as React from 'react';
   import renderer from 'react-test-renderer';
   import MyComponent from '../components/MyComponent';

   it('renders correctly', () => {
     const tree = renderer.create(<MyComponent title="Test" />).toJSON();
     expect(tree).toMatchSnapshot();
   });
   ```

2. Run the test:
   ```bash
   npm test
   ```

## Common Development Tasks

### Updating Dependencies

```bash
npm outdated  # Check for outdated packages
npm update    # Update packages to latest compatible versions
```

For major updates, update packages individually:

```bash
npm install package-name@latest
```

### Troubleshooting

#### Metro Bundler Issues

```bash
expo start --clear
# or
rm -rf node_modules && npm install && expo start
```

#### iOS Build Issues

```bash
cd ios && pod install && cd ..
```

#### Android Build Issues

```bash
cd android && ./gradlew clean && cd ..
```

#### Expo Errors

```bash
expo doctor
```

## Best Practices

1. **Follow the Existing Pattern**: Maintain consistency with the existing codebase
2. **Use TypeScript**: Define types for all props and state
3. **Component Organization**: Keep components small and focused
4. **State Management**: Use context for global state, local state for component-specific state
5. **Error Handling**: Implement proper error boundaries and fallbacks
6. **Performance**: Optimize renders with memoization and useCallback
7. **Accessibility**: Ensure the app is accessible to all users
8. **Testing**: Write tests for critical functionality
9. **Documentation**: Document complex logic and component APIs
10. **Code Reviews**: Have your code reviewed by peers
