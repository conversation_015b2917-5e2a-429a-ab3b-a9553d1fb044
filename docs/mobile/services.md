# TapTrap Mobile App Services

## Overview

TapTrap uses several service modules to handle specific functionality across the app. These services encapsulate related logic and provide clean interfaces for the rest of the application to use.

## Content Service

**File**: `services/contentService.ts`

### Purpose
Handles fetching, caching, and managing game content (questions and challenges).

### Key Features
- Fetches content from the backend API
- Caches content for offline use
- Provides localized text based on device language
- Handles error cases and fallbacks

### Data Structure
```typescript
interface GameContent {
  questions: {
    casual_questions: Array<{ id: string; text_en: string; text_es: string }>;
    mild_questions: Array<{ id: string; text_en: string; text_es: string }>;
    spicy_questions: Array<{ id: string; text_en: string; text_es: string }>;
    no_limits_questions: Array<{ id: string; text_en: string; text_es: string }>;
  };
  dares: {
    casual_dares: Array<{ id: string; text_en: string; text_es: string }>;
    mild_dares: Array<{ id: string; text_en: string; text_es: string }>;
    spicy_dares: Array<{ id: string; text_en: string; text_es: string }>;
    no_limits_dares: Array<{ id: string; text_en: string; text_es: string }>;
  };
}
```

### Key Functions

#### getCachedGameContent
Retrieves cached game content from the device's file system.

```typescript
export const getCachedGameContent = async (): Promise<GameContent | null> => {
  try {
    // Only try to read from saved cache in document directory
    if (Platform.OS !== 'web') {
      try {
        // Check if the file exists in the document directory
        const fileInfo = await FileSystem.getInfoAsync(gameContentPath);

        if (fileInfo.exists) {
          const content = await FileSystem.readAsStringAsync(gameContentPath);
          const parsedContent = JSON.parse(content);
          console.log('Loaded cached content from storage');
          return parsedContent;
        }
      } catch (error) {
        console.warn('Error accessing cached content:', error);
      }
    }

    // If no cached content or error, return null
    return null;
  } catch (error) {
    console.error('Error in getCachedGameContent:', error);
    return null;
  }
};
```

#### fetchAndUpdateGameContent
Fetches game content from the backend API and updates the local cache.

```typescript
export const fetchAndUpdateGameContent = async (): Promise<GameContent | null> => {
  try {
    console.log('Initiating game content update from backend API');

    // Check internet connection
    const networkState = await NetInfo.fetch();
    if (!networkState.isConnected) {
      console.log('No internet connection detected, checking for cached content');

      // Try to load cached content if available
      const cachedContent = await getCachedGameContent();
      if (cachedContent) {
        console.log('Using cached content while offline');
        return cachedContent;
      }

      // No cached content and no internet
      console.log('No internet and no cached content available');
      return null;
    }

    // With internet connection, try to fetch data from our API
    try {
      console.log(`Fetching game content from API: ${API_URL}/content`);

      const response = await axios.get(`${API_URL}/content`, {
        timeout: 15000 // 15 second timeout for slower connections
      });

      console.log('API response received:', response.status);

      if (response.data) {
        const gameContent = response.data;

        // Validate the data structure
        if (!gameContent.questions || !gameContent.dares) {
          console.warn('API returned invalid data structure');
          return null;
        }

        // Cache the content for offline use
        if (Platform.OS !== 'web') {
          try {
            await FileSystem.writeAsStringAsync(
              gameContentPath,
              JSON.stringify(gameContent)
            );
            console.log('Game content cached successfully');
          } catch (error) {
            console.warn('Failed to cache game content:', error);
          }
        }

        return gameContent;
      }
    } catch (error) {
      console.warn('API fetch error:', error);

      // Try to load cached content as fallback
      const cachedContent = await getCachedGameContent();
      if (cachedContent) {
        console.log('Using cached content after API error');
        return cachedContent;
      }
    }

    return null;
  } catch (error) {
    console.error('Error in fetchAndUpdateGameContent:', error);
    return null;
  }
};
```

#### getLocalizedText
Returns the appropriate text based on the device language.

```typescript
export const getLocalizedText = (item: { text_en: string; text_es: string }): string => {
  const language = getDeviceLanguage();
  return language === 'es' ? item.text_es : item.text_en;
};
```

### Usage
```typescript
import { fetchAndUpdateGameContent, getCachedGameContent, getLocalizedText } from '@/services/contentService';

// In a component or context provider
useEffect(() => {
  const loadContent = async () => {
    // Try to get cached content first
    let content = await getCachedGameContent();

    // If no cached content, fetch from API
    if (!content) {
      content = await fetchAndUpdateGameContent();
    }

    // Use the content
    if (content) {
      // Do something with the content
    }
  };

  loadContent();
}, []);
```

## Localization Service (i18n)

**File**: `services/i18nService.tsx`

### Purpose
Manages language selection and provides translations for the app's text.

### Key Features
- Detects device language
- Provides translations for UI text
- Allows manual language switching
- Persists language preference

### Translation Dictionary
```typescript
const translations = {
  pickTheMode: {
    en: 'Choose a Game Mode',
    es: '¿Qué vamos a jugar?'
  },
  questionsIndex: {
    en: 'Questions',
    es: 'Preguntas'
  },
  challengesIndex: {
    en: 'Dares',
    es: 'Retos'
  },
  // Many more translations...
};
```

### Key Functions

#### setAppLanguage
Sets the app language and persists it to storage.

```typescript
const setAppLanguage = async (lang: LanguageType) => {
  setLanguage(lang);
  try {
    await AsyncStorage.setItem('appLanguage', lang);
  } catch (err) {
    console.warn('Failed to store language', err);
  }
};
```

#### t (translate)
Returns the translated text for a given key.

```typescript
const t = (key: keyof typeof translations): string =>
  translations[key]?.[language] ?? translations[key]?.en ?? key;
```

### Usage
```typescript
import { useLanguage } from '@/services/i18nService';

function MyComponent() {
  const { language, setAppLanguage, t } = useLanguage();

  return (
    <View>
      <Text>{t('welcomeMessage')}</Text>
      <Button
        title={t('changeLanguage')}
        onPress={() => setAppLanguage(language === 'en' ? 'es' : 'en')}
      />
    </View>
  );
}
```

## RevenueCat Service

**File**: `services/revenueCatService.ts`

### Purpose
Handles in-app purchases and premium content access.

### Key Features
- Initializes RevenueCat SDK
- Checks user subscription status
- Provides functions to unlock premium content

### Key Functions

#### initRevenueCat
Initializes the RevenueCat SDK with the appropriate API key.

```typescript
export const initRevenueCat = async () => {
  try {
    // Initialize RevenueCat with your API key
    await Purchases.configure({
      apiKey: Platform.OS === 'ios'
        ? 'ios_api_key_here'
        : 'android_api_key_here',
    });

    console.log('RevenueCat initialized successfully');
  } catch (error) {
    console.error('Error initializing RevenueCat:', error);
  }
};
```

#### checkIfUserHasAccess
Checks if the user has access to premium content.

```typescript
export const checkIfUserHasAccess = async (): Promise<boolean> => {
  try {
    const customerInfo = await Purchases.getCustomerInfo();

    // Check if user has active entitlement for premium content
    return customerInfo.entitlements.active['premium_access'] !== undefined;
  } catch (error) {
    console.error('Error checking premium access:', error);
    return false;
  }
};
```

#### purchasePremium
Initiates the purchase flow for premium content.

```typescript
export const purchasePremium = async (): Promise<boolean> => {
  try {
    // Get available packages
    const offerings = await Purchases.getOfferings();

    if (offerings.current && offerings.current.availablePackages.length > 0) {
      // Get the first package
      const package = offerings.current.availablePackages[0];

      // Make the purchase
      const { customerInfo } = await Purchases.purchasePackage(package);

      // Check if the purchase was successful
      return customerInfo.entitlements.active['premium_access'] !== undefined;
    }

    return false;
  } catch (error) {
    console.error('Error purchasing premium:', error);
    return false;
  }
};
```

### Usage
```typescript
import { initRevenueCat, checkIfUserHasAccess, purchasePremium } from '@/services/revenueCatService';

// Initialize on app start
useEffect(() => {
  initRevenueCat();
}, []);

// Check premium status
const checkPremium = async () => {
  const hasPremium = await checkIfUserHasAccess();
  setPremiumStatus(hasPremium);
};

// Purchase premium
const handlePurchase = async () => {
  const success = await purchasePremium();
  if (success) {
    // Update UI to reflect premium status
    setPremiumStatus(true);
  }
};
```

## Sound Service

**File**: `components/game/playSound.tsx`

### Purpose
Provides a way to play sound effects throughout the app.

### Key Features
- Loads and caches sound effects
- Provides functions to play sounds with volume control
- Integrates with app settings for sound toggle

### Usage
```typescript
import { useSound } from '@/components/game/playSound';

function MyComponent() {
  const { playSound } = useSound();

  const handlePress = async () => {
    await playSound('tapEffect1');
    // Do something after sound plays
  };

  return (
    <Button
      title="Press Me"
      onPress={handlePress}
    />
  );
}
```

## Penalty Service

**File**: `services/penaltyService.ts`

### Purpose
Manages penalty content fetching, caching, and formatting for the penalty feature, including premium penalty support and default penalty assignment.

### Key Features
- Fetches penalty content from backend API with premium fields
- Caches penalty data locally using FileSystem
- Formats penalty data for app consumption
- Handles offline scenarios with cached data
- Supports multi-language penalty content
- **Premium penalty filtering and access control**
- **Automatic default penalty assignment based on user premium status**
- **RevenueCat integration for premium status checking**

### API Integration
```typescript
// Fetch penalties from backend with premium fields
const response = await fetch(`${API_URL}/penalties`);
const data = await response.json();
```

### Data Structure
```typescript
interface PenaltyContent {
  drinking: Penalty[];
  physical: Penalty[];
  social: Penalty[];
  silly: Penalty[];
  creative: Penalty[];
}

interface Penalty {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
  isPremium: boolean;        // Premium-only access
  isDefaultFree: boolean;    // Default for free users
  isDefaultPremium: boolean; // Default for premium users
}
```

### Premium Penalty Features

#### Default Penalty Selection
```typescript
const getDefaultPenalties = (penalties: PenaltyContent, isPremium: boolean): string[] => {
  const allPenalties = Object.values(penalties).flat();

  if (isPremium) {
    // Premium users: prefer premium default, fallback to free default
    const premiumDefault = allPenalties.find(p => p.isDefaultPremium);
    if (premiumDefault) return [premiumDefault.id];

    const freeDefault = allPenalties.find(p => p.isDefaultFree);
    if (freeDefault) return [freeDefault.id];
  } else {
    // Free users: only free defaults
    const freeDefault = allPenalties.find(p => p.isDefaultFree);
    if (freeDefault) return [freeDefault.id];
  }

  return []; // No default available
};
```

#### Premium Access Control
- Free users cannot select premium penalties
- Premium penalties are visually indicated with badges
- Tapping premium penalties shows RevenueCat paywall for free users

### Usage
```typescript
import { fetchPenalties, getCachedPenalties, getDefaultPenalties } from '@/services/penaltyService';

// Fetch and cache penalties
const loadPenalties = async () => {
  try {
    const penalties = await fetchPenalties();
    // Penalties are automatically cached
    return penalties;
  } catch (error) {
    // Fallback to cached penalties
    return await getCachedPenalties();
  }
};

// Get default penalties for user
const assignDefaultPenalties = async (isPremium: boolean) => {
  const penalties = await getCachedPenalties();
  if (penalties) {
    const defaults = getDefaultPenalties(penalties, isPremium);
    // Update user's penalty selection
    updateIndividualPenaltySelections(defaults);
  }
};
```

## Network Connectivity Service

The app handles network connectivity using the NetInfo package and custom logic in App.tsx and the contentService.

### Purpose
Ensures the app works properly in both online and offline scenarios, providing appropriate feedback to users when no internet connection is available.

### Key Features
- Detects network state changes
- Handles offline gameplay with cached content
- Shows NoInternetScreen when no content is available offline
- Provides retry mechanism when connection is restored

### Implementation in App.tsx
```typescript
// In App.tsx
useEffect(() => {
  const loadContent = async () => {
    try {
      // Check network connection
      const networkState = await NetInfo.fetch();

      // Fetch content (will attempt to load from cache if offline)
      const content = await fetchAndUpdateGameContent();

      if (!content) {
        // Navigate to NoInternetScreen if there's no content and no internet
        if (!networkState.isConnected && navigationRef.current) {
          navigationRef.current.navigate('NoInternetScreen');
        }
      }
    } catch (error) {
      console.error('Error initiating content loading:', error);
    }
  };

  // Setup network connectivity listener
  const unsubscribe = NetInfo.addEventListener(async state => {
    if (state.isConnected === false) {
      // Check if we have cached content
      const content = await fetchAndUpdateGameContent();
      if (!content && navigationRef.current) {
        navigationRef.current.navigate('NoInternetScreen');
      }
    }
  });

  // Clean up listener on unmount
  return () => {
    unsubscribe();
  };
}, []);
```

### Usage in NoInternetScreen
```typescript
// In NoInternetScreen.tsx
const handleRetry = async () => {
  const networkState = await NetInfo.fetch();

  if (networkState.isConnected) {
    // We're back online, check if we have cached content
    const cachedContent = await getCachedGameContent();

    if (cachedContent || networkState.isConnected) {
      // Navigate back to Home if we either have content or an internet connection
      navigation.navigate('Home' as never);
    }
  } else {
    // Still offline, animate the dialog to give feedback
    dialogScale.value = withTiming(0.95, { duration: 100 }, () => {
      dialogScale.value = withTiming(1, { duration: 100 });
    });
  }
};
```

## Service Integration

These services work together to provide a seamless experience:

1. **App Initialization**:
   - `contentService` fetches and caches game content
   - `penaltyService` fetches and caches penalty content with premium fields
   - `i18nService` detects and sets the appropriate language
   - `revenueCatService` initializes the in-app purchase system
   - Network connectivity service checks for internet access

2. **Game Flow**:
   - `contentService` provides questions and challenges
   - `penaltyService` provides penalty content and handles premium access
   - `i18nService` translates UI and content
   - Sound service provides audio feedback
   - `revenueCatService` controls access to premium content and penalties
   - Network connectivity service manages offline gameplay

3. **Premium Features**:
   - `penaltyService` assigns default penalties based on premium status
   - `revenueCatService` manages premium subscription state
   - Premium penalties are filtered and controlled by user subscription status
   - Paywall integration for premium penalty access

4. **Error Handling**:
   - Network connectivity service shows NoInternetScreen when needed
   - `contentService` and `penaltyService` fall back to cached content when offline
   - NoInternetScreen provides retry functionality

This modular approach keeps the codebase organized and maintainable while providing clear separation of concerns.
