# TapTrap State Management

## Overview

TapTrap uses React Context API for state management across the application. This approach provides a clean way to share state between components without prop drilling, while maintaining good performance for the app's needs.

## Context Providers Hierarchy

The app uses a nested context provider structure to ensure all components have access to the necessary state:

```jsx
// App.tsx
<AppSettingsProvider>
  <LanguageProvider>
    <GameProvider>
      <NavigationContainer>
        {/* App screens */}
      </NavigationContainer>
    </GameProvider>
  </LanguageProvider>
</AppSettingsProvider>
```

This hierarchy ensures that:
1. App settings are available to all components
2. Language settings can access app settings
3. Game state can access both app settings and language settings
4. All screens have access to all context providers

## AppSettings Context

**File**: `context/AppSettingsContext.tsx`

### Purpose
Manages application-wide settings that affect various aspects of the app.

### State
```typescript
interface AppSettingsContextProps {
  isSoundOn: boolean;
  isHapticsOn: boolean;
  isPremiumUnlocked: boolean;
  toggleSound: () => void;
  toggleHaptics: () => void;
  unlockPremium: () => void;
}
```

### Implementation
```typescript
export const AppSettingsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isSoundOn, setIsSoundOn] = useState(true);
  const [isHapticsOn, setIsHapticsOn] = useState(true);
  const [isPremiumUnlocked, setIsPremiumUnlocked] = useState(false);

  // Load settings from AsyncStorage on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const soundSetting = await AsyncStorage.getItem('soundEnabled');
        if (soundSetting !== null) {
          setIsSoundOn(soundSetting === 'true');
        }

        const hapticsSetting = await AsyncStorage.getItem('hapticsEnabled');
        if (hapticsSetting !== null) {
          setIsHapticsOn(hapticsSetting === 'true');
        }
        
        // Check premium status from RevenueCat
        const hasPremium = await checkIfUserHasAccess();
        setIsPremiumUnlocked(hasPremium);
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    };

    loadSettings();
    
    // Initialize RevenueCat
    initRevenueCat();
  }, []);

  // Toggle sound and save to AsyncStorage
  const toggleSound = async () => {
    const newValue = !isSoundOn;
    setIsSoundOn(newValue);
    try {
      await AsyncStorage.setItem('soundEnabled', String(newValue));
    } catch (error) {
      console.error('Error saving sound setting:', error);
    }
  };

  // Toggle haptics and save to AsyncStorage
  const toggleHaptics = async () => {
    const newValue = !isHapticsOn;
    setIsHapticsOn(newValue);
    try {
      await AsyncStorage.setItem('hapticsEnabled', String(newValue));
    } catch (error) {
      console.error('Error saving haptics setting:', error);
    }
  };

  // Unlock premium content
  const unlockPremium = () => {
    setIsPremiumUnlocked(true);
  };

  const value = {
    isSoundOn,
    isHapticsOn,
    isPremiumUnlocked,
    toggleSound,
    toggleHaptics,
    unlockPremium
  };

  return <AppSettingsContext.Provider value={value}>{children}</AppSettingsContext.Provider>;
};
```

### Usage
```typescript
import { useAppSettings } from '@/context/AppSettingsContext';

function MyComponent() {
  const { isSoundOn, toggleSound } = useAppSettings();
  
  return (
    <Button 
      title={isSoundOn ? "Sound On" : "Sound Off"} 
      onPress={toggleSound} 
    />
  );
}
```

## Language Context

**File**: `services/i18nService.tsx`

### Purpose
Manages language selection and provides translations for the app's text.

### State
```typescript
interface LanguageContextProps {
  language: 'en' | 'es';
  setAppLanguage: (lang: 'en' | 'es') => void;
  t: (key: string) => string;
}
```

### Implementation
```typescript
export const LanguageProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<LanguageType>('en');

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const stored = await AsyncStorage.getItem('appLanguage');
        if (stored === 'en' || stored === 'es') {
          setLanguage(stored);
        } else {
          const locale = Localization.locale.split('-')[0];
          const fallback = ['en', 'es'].includes(locale) ? (locale as LanguageType) : 'en';
          setLanguage(fallback);
        }
      } catch {
        setLanguage('en');
      }
    };
    loadLanguage();
  }, []);

  const setAppLanguage = async (lang: LanguageType) => {
    setLanguage(lang);
    try {
      await AsyncStorage.setItem('appLanguage', lang);
    } catch (err) {
      console.warn('Failed to store language', err);
    }
  };

  const t = (key: keyof typeof translations): string =>
    translations[key]?.[language] ?? translations[key]?.en ?? key;

  return (
    <LanguageContext.Provider value={{ language, setAppLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};
```

### Usage
```typescript
import { useLanguage } from '@/services/i18nService';

function MyComponent() {
  const { language, setAppLanguage, t } = useLanguage();
  
  return (
    <View>
      <Text>{t('welcomeMessage')}</Text>
      <Button 
        title={language === 'en' ? "Switch to Spanish" : "Switch to English"} 
        onPress={() => setAppLanguage(language === 'en' ? 'es' : 'en')} 
      />
    </View>
  );
}
```

## Game Context

**File**: `components/game/GameContext.tsx`

### Purpose
Manages the game state, including selected modes, categories, and content.

### State
```typescript
interface GameContextType {
  selectedModes: ('questions' | 'challenges')[];
  selectedQuestionCategory: QuestionCategory;
  challengeDifficulty: ChallengeDifficulty;
  currentQuestion: Question | null;
  currentChallenge: Challenge | null;
  isLoading: boolean;
  selectedCategories: QuestionCategory[];
  setGameMode: (modes: ('questions' | 'challenges')[]) => void;
  setSelectedQuestionCategory: (category: QuestionCategory) => void;
  setChallengeDifficulty: (difficulty: ChallengeDifficulty) => void;
  setSelectedCategories: (categories: QuestionCategory[]) => void;
  getNextQuestion: () => Promise<Question | null>;
  getNextChallenge: () => Promise<Challenge | null>;
  resetGame: () => void;
  getLocalizedTextForItem: (item: Question | Challenge | null) => string;
}
```

### Implementation
```typescript
export const GameProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [selectedModes, setGameMode] = useState<('questions' | 'challenges')[]>([]);
  const [selectedQuestionCategory, setSelectedQuestionCategory] = useState<QuestionCategory>('casual_questions');
  const [challengeDifficulty, setChallengeDifficulty] = useState<ChallengeDifficulty>('casual_dares');
  const [gameContent, setGameContent] = useState<any>(null);
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [currentChallenge, setCurrentChallenge] = useState<Challenge | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [usedQuestionIds, setUsedQuestionIds] = useState<Set<string>>(new Set());
  const [usedChallengeIds, setUsedChallengeIds] = useState<Set<string>>(new Set());
  const [selectedCategories, setSelectedCategories] = useState<QuestionCategory[]>([]);
  const { language } = useLanguage();
  const { isPremiumUnlocked } = useAppSettings();

  // Load game content on mount
  useEffect(() => {
    const loadContent = async () => {
      try {
        setIsLoading(true);
        const content = await getCachedGameContent();
        if (content) {
          setGameContent(content);
        } else {
          const updatedContent = await fetchAndUpdateGameContent();
          setGameContent(updatedContent);
        }
      } catch (error) {
        console.error('Error loading game content:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadContent();
  }, []);

  // Get next question
  const getNextQuestion = async (): Promise<Question | null> => {
    if (!gameContent || !gameContent.questions) return null;
    
    // Get questions from selected category
    const questions = gameContent.questions[selectedQuestionCategory] || [];
    
    // Filter out used questions
    const unusedQuestions = questions.filter(q => !usedQuestionIds.has(q.id));
    
    // If all used, reset
    if (unusedQuestions.length === 0) {
      setUsedQuestionIds(new Set());
      const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
      setCurrentQuestion(randomQuestion);
      return randomQuestion;
    }
    
    // Get random unused question
    const randomQuestion = unusedQuestions[Math.floor(Math.random() * unusedQuestions.length)];
    setUsedQuestionIds(prev => new Set([...prev, randomQuestion.id]));
    setCurrentQuestion(randomQuestion);
    return randomQuestion;
  };

  // Reset game state
  const resetGame = () => {
    setSelectedModes([]);
    setCurrentQuestion(null);
    setCurrentChallenge(null);
  };

  // Get localized text based on current language
  const getLocalizedTextForItem = (item: Question | Challenge | null): string => {
    if (!item) return '';
    return language === 'es' ? item.text_es : item.text_en;
  };

  const value = {
    selectedModes,
    selectedQuestionCategory,
    challengeDifficulty,
    currentQuestion,
    currentChallenge,
    isLoading,
    selectedCategories,
    setGameMode,
    setSelectedQuestionCategory,
    setChallengeDifficulty,
    setSelectedCategories,
    getNextQuestion,
    getNextChallenge,
    resetGame,
    getLocalizedTextForItem
  };

  return <GameContext.Provider value={value}>{children}</GameContext.Provider>;
};
```

### Usage
```typescript
import { useGameContext } from '@/components/game/GameContext';

function GameScreen() {
  const { 
    currentQuestion, 
    getNextQuestion,
    getLocalizedTextForItem
  } = useGameContext();
  
  const handleNext = async () => {
    const nextQuestion = await getNextQuestion();
    // Handle next question
  };
  
  return (
    <View>
      <Text>{getLocalizedTextForItem(currentQuestion)}</Text>
      <Button title="Next Question" onPress={handleNext} />
    </View>
  );
}
```

## Local Component State

In addition to the global context state, each screen manages its own local state for UI-specific concerns:

- **HomeScreen**: Manages animation states and selected modes
- **SelectCategoriesScreen**: Manages selected categories and paywall visibility
- **TouchGameScreen**: Manages touch points, game state, and animations
- **SettingsScreen**: Manages selected settings and modal visibility

This combination of global context and local component state provides a clean and maintainable state management approach for the app.
