# Push Notifications Implementation

## Overview

The TapTrap push notification system allows users to receive notifications about new content and features. The implementation follows Expo's best practices and integrates seamlessly with the existing banner system.

## Features

### Mobile App Features

1. **Banner Display Logic**
   - Shows on the first question/dare if conditions are met
   - Hides when other banners (InGameBanner, StoreReviewBanner) are displayed
   - Respects user preferences and permission states

2. **Permission Management**
   - Native permission dialog with clear messaging
   - Handles permission denial gracefully
   - Stores permission state to avoid repeated prompts

3. **User Preference Control**
   - Settings page toggle for notification preferences
   - Independent control from system permissions
   - Backend synchronization of user preferences
   - Visual status display (On/Off) in settings

4. **8-Hour Cooldown**
   - When user dismisses banner with X button, it won't show again for 8 hours
   - Cooldown resets after 8 hours automatically

5. **Token Management**
   - Automatically generates and uploads Expo push tokens
   - Handles token refresh and device changes
   - Secure token storage and transmission
   - Includes notification preference in token data

### Admin Panel Features

1. **Device Management**
   - View all registered devices
   - See platform (iOS/Android) breakdown
   - Track registration and last usage dates
   - Monitor notification preference status per device

2. **Notification Sending**
   - Send notifications only to devices with notifications enabled
   - Automatic filtering based on user preferences
   - Simple form with title and message fields
   - Real-time feedback on send status

3. **Statistics Dashboard**
   - Total device count
   - Platform-specific breakdowns
   - Registration trends
   - Notification preference statistics

## Implementation Details

### Mobile App Components

#### PushBanner Component
**File**: `components/notifications/PushBanner.tsx`

A banner component that displays a call-to-action for push notification permissions.

**Props:**
- `onPress?: () => void` - Callback when banner is pressed
- `style?: ViewStyle` - Optional custom styling

#### PushNotificationService
**File**: `services/pushNotificationService.ts`

Handles all push notification logic including:
- Permission requests
- Token generation and management
- Backend communication
- Banner display logic

**Key Methods:**
- `requestPermissions()` - Request push notification permissions
- `getExpoPushToken()` - Get or generate Expo push token
- `uploadTokenToBackend()` - Upload token to backend (uses deviceIdService for unique device identification)
- `shouldShowBanner()` - Determine if banner should be displayed
- `getNotificationPreference()` - Get current user notification preference
- `setNotificationPreference()` - Set user notification preference locally
- `updateNotificationPreferenceOnBackend()` - Sync preference to backend
- `areNotificationsEnabled()` - Check both permission and preference status

**Related Documentation:**
- [Device Identification System](./device-identification.md) - Details on unique device ID generation
- [Settings Screen](./screens.md#settings-screen) - Notification toggle implementation

### Backend Components

#### API Endpoints

**POST /api/push-tokens**
- Register a new push notification token
- Accepts `notificationEnabled` parameter for user preference
- Public endpoint (no authentication required)

**PUT /api/push-tokens/preference**
- Update notification preference for a specific device
- Requires `deviceId` and `notificationEnabled` parameters
- Public endpoint (no authentication required)

**GET /api/admin/push-tokens**
- Get all registered push tokens (Admin only)
- Includes notification preference status
- Requires authentication

**POST /api/admin/push-notifications/send**
- Send push notifications to multiple devices (broadcast)
- Automatic filtering based on `notificationEnabled` field (unless `forcePush: true`)
- **Automatic batch processing** for >100 devices (respects Expo limits)
- Requires authentication

**POST /api/admin/push-notifications/send-specific**
- Send push notification to a specific device by Device ID
- Bypasses notification preference filtering
- Requires authentication

#### Database Model
**File**: `backend/src/models/PushToken.js`

Stores push notification tokens with:
- Token string (unique)
- Platform (iOS/Android)
- Device ID (unique UUID generated via deviceIdService)
- Language preference (en/es/dom)
- **Notification preference** (`notificationEnabled` boolean, default: true)
- Active status
- Creation and last used timestamps

### Batch Processing System

**Problem Solved**: Expo Push Service has a limit of 100 notifications per request. When sending to >100 devices, the system automatically:

1. **Divides messages into batches** of 100 notifications each
2. **Sends batches sequentially** with 100ms delay between them
3. **Provides detailed feedback** on each batch's success/failure rate
4. **Shows progress indicators** in the admin panel

See detailed documentation: [Push Notifications Batch System](../admin/push-notifications-batch-system.md)

### Admin Panel Components

#### PushNotifications Page
**File**: `admin/src/pages/PushNotifications.js`

Complete admin interface for:
- Viewing registered devices
- Sending notifications
- Monitoring statistics

## Configuration

### Mobile App Configuration

1. **Dependencies**
   ```json
   {
     "expo-notifications": "~0.29.11",
     "expo-device": "~6.0.2",
     "expo-secure-store": "~13.0.2",
     "uuid": "^9.0.1",
     "react-native-get-random-values": "^1.9.0"
   }
   ```

2. **App Config**
   ```javascript
   // app.config.js
   plugins: [
     [
       "expo-notifications",
       {
         "icon": "./assets/images/notification-icon.png",
         "color": "#ffffff",
         "sounds": ["./assets/sounds/notification.wav"]
       }
     ]
   ]
   ```

### Backend Configuration

1. **Environment Variables**
   - No additional environment variables required
   - Uses existing MongoDB connection

2. **Routes**
   ```javascript
   // backend/src/index.js
   app.use('/api', pushNotificationRoutes);
   ```

## Notification Preference System

### Overview

The notification preference system provides users with granular control over push notifications, independent of system permissions. This two-layer approach ensures users can:

1. **System Level**: Grant/deny notification permissions via iOS/Android settings
2. **App Level**: Enable/disable notifications via TapTrap settings toggle

### Implementation Flow

#### Settings Toggle Interaction
1. **User taps notification toggle in Settings**
2. **System checks current notification permissions**
3. **If permissions granted**: Toggle updates preference and syncs to backend
4. **If permissions denied**: Shows alert directing user to device settings

#### Backend Filtering
1. **Admin sends notification via admin panel**
2. **Backend queries devices with `isActive: true` AND `notificationEnabled: true`**
3. **Only matching devices receive the notification**

#### State Synchronization
- **Local Storage**: User preference stored in AsyncStorage
- **Backend Database**: Preference synced to PushToken model
- **Real-time Updates**: Settings UI reflects current state immediately

### User Experience

#### Settings Page Toggle
- **Visual Status**: Shows "On" or "Off" based on current preference
- **Gradient Styling**: Active toggle displays gradient background
- **Permission Handling**: Automatically detects and handles permission states

#### Permission Denied Flow
1. User attempts to enable notifications
2. System detects permissions are denied
3. Alert appears with localized message
4. User can tap "Open Settings" to navigate to device settings
5. Platform-specific navigation (iOS: `Linking.openSettings()`, Android: `IntentLauncher`)

## Usage

### For Users

1. **First Time Experience**
   - Banner appears on first question/dare
   - Tap banner to see permission dialog
   - Grant or deny permissions

2. **Settings Control**
   - Navigate to Settings → Notifications
   - Toggle notifications on/off independently of system permissions
   - Visual feedback shows current status

3. **Banner Dismissal**
   - Tap X button to dismiss for 8 hours
   - Banner won't show again if permission already granted/denied

### For Administrators

1. **Viewing Devices**
   - Navigate to "Push Notifications" in admin panel
   - View all registered devices with platform and timestamps
   - Monitor notification preference status per device

2. **Sending Notifications**
   - Fill in title and message
   - Click "Send to All Devices"
   - System automatically filters to only devices with notifications enabled
   - Receive confirmation of send status with recipient count

## Technical Notes

### Permission States

- **Not Requested**: Banner shows, can request permissions
- **Granted**: Banner hidden, token uploaded to backend
- **Denied**: Banner hidden permanently
- **Dismissed**: Banner hidden for 8 hours

### Notification Preference States

- **Enabled + Permissions Granted**: User receives notifications
- **Enabled + Permissions Denied**: Settings toggle shows alert to enable permissions
- **Disabled + Permissions Granted**: User does not receive notifications (filtered by backend)
- **Disabled + Permissions Denied**: User does not receive notifications

### Banner Priority

1. PushBanner (first question/dare)
2. InGameBanner (second question/dare, casual/mild only)
3. StoreReviewBanner (varies by user type)

Only one banner shows at a time, with higher priority banners hiding lower priority ones.

### Error Handling

- Network errors are logged and don't crash the app
- Permission errors are handled gracefully
- Backend errors provide meaningful feedback to admin users

## Testing

### Mobile App Testing

1. **Banner Display**
   - Start new game
   - Verify banner shows on first question/dare
   - Test banner hiding when other banners appear

2. **Permission Flow**
   - Tap banner to trigger permission dialog
   - Test both grant and deny scenarios
   - Verify banner behavior after permission decision

3. **Cooldown Testing**
   - Dismiss banner with X button
   - Verify 8-hour cooldown works correctly

### Backend Testing

1. **Token Registration**
   - Test POST /api/push-tokens endpoint
   - Verify tokens are stored correctly
   - Test duplicate token handling

2. **Notification Sending**
   - Test admin notification sending
   - Verify notifications reach devices
   - Test error handling for invalid tokens

## Troubleshooting

### Common Issues

1. **Banner Not Showing**
   - Check if permissions already granted/denied
   - Verify banner cooldown hasn't been triggered
   - Check if other banners are currently displayed

2. **Permissions Not Working**
   - Ensure app is running on physical device (not simulator)
   - Check Expo project ID is configured correctly
   - Verify network connectivity

3. **Notifications Not Received**
   - Check token was uploaded to backend successfully
   - Verify notification was sent from admin panel
   - Check device notification settings

### Debug Information

Enable debug logging by checking console output for:
- `Push notification banner` - Banner display logic
- `Push notification permission` - Permission flow
- `Push token` - Token generation and upload
