# RevenueCat Integration in TapTrap

This document outlines the RevenueCat integration in the TapTrap app, including configuration, implementation details, and platform-specific considerations.

## Overview

TapTrap uses RevenueCat to handle in-app purchases for premium content categories. The integration allows users to unlock premium question categories through a paywall interface.

## Configuration

### Environment Variables

RevenueCat API keys are stored in environment variables and accessed through Expo's configuration system:

```
# .env file
REVENUECAT_IOS_KEY=your_ios_key_here
REVENUECAT_ANDROID_KEY=your_android_key_here
```

These keys are accessed in `app.config.js`:

```javascript
// app.config.js
extra: {
  revenueCatIosKey: process.env.REVENUECAT_IOS_KEY,
  revenueCatAndroidKey: process.env.REVENUECAT_ANDROID_KEY,
}
```

### Android-Specific Configuration

Android requires specific configuration to work properly:

1. **BILLING Permission**: Added to `app.config.js` in the Android section:
   ```javascript
   android: {
     // ...other config
     permissions: ["BILLING"],
   }
   ```

2. **Launch Mode**: The main activity should use `standard` or `singleTop` launch mode to handle the purchase flow correctly, especially when the user needs to verify a purchase in another app.

## Implementation

### RevenueCat Service

The core functionality is implemented in `services/revenueCatService.ts`:

1. **Initialization**: The SDK is initialized with the appropriate API key based on the platform.
2. **Offerings**: Functions to fetch available offerings from RevenueCat.
3. **Purchase Flow**: Functions to handle the purchase process.
4. **Entitlement Checking**: Functions to check if the user has premium access.

### Premium Content Access

Premium content access is managed through:

1. **AppSettingsContext**: Maintains the premium status state and provides functions to unlock premium content.
2. **RevenueCat SDK**: Verifies entitlements with the RevenueCat backend and provides built-in caching.

The RevenueCat SDK automatically caches subscription information for up to 5 minutes, which reduces network requests and provides offline access to entitlements. We rely on this built-in caching mechanism rather than implementing our own.

## Platform-Specific Considerations

### iOS vs Android Differences

1. **Purchase Flow**:
   - iOS handles purchases within the app
   - Android may require the user to leave the app to verify payment

2. **UI Implementation**:
   - iOS uses the RevenueCatUI.Paywall component with animated transitions
   - Android uses the RevenueCatUI.Paywall component with a simpler container to minimize lifecycle issues

### Android-Specific Issues

Android may experience crashes when:

1. The app is backgrounded during the purchase process
2. The activity's `launchMode` is not set to `standard` or `singleTop`
3. The BILLING permission is missing
4. Complex animations are used during the purchase flow
5. The RevenueCatUI.Paywall component is used without a proper lifecycle owner

The RevenueCatUI.Paywall component uses Jetpack Compose internally. When this component is rendered inside a React Native Modal with complex animations, it may encounter lifecycle issues, resulting in errors like:

```
java.lang.IllegalStateException: ViewTreeLifecycleOwner not found from android.widget.FrameLayout
```

To minimize these issues, we use a simpler container for the paywall on Android, avoiding complex animations that might interfere with the Compose lifecycle.

## Troubleshooting

### Common Issues

1. **Crashes on Android when tapping locked categories**:
   - Ensure BILLING permission is added
   - Check launchMode configuration
   - Simplify UI animations during purchase flow
   - Add proper error handling

2. **Purchase completed but content still locked**:
   - Check if the entitlement ID matches what's configured in RevenueCat dashboard
   - Verify that the correct API keys are being used
   - Check network connectivity

3. **RevenueCat dashboard not showing purchases**:
   - Verify that the correct API keys are being used
   - Check if the app is in sandbox/testing mode

4. **Sandbox subscriptions reactivating after deletion**:
   - This is a known issue in the RevenueCat sandbox environment
   - When a subscription is deleted in the sandbox environment, the app may still show the user as subscribed after restarting
   - This happens because of caching mechanisms in both the RevenueCat SDK and our app

## Debugging

Enable debug logs in development:

```javascript
if (__DEV__) {
  // Check if LOG_LEVEL is available in this version
  if (Purchases.LOG_LEVEL && Purchases.LOG_LEVEL.DEBUG) {
    Purchases.setLogLevel(Purchases.LOG_LEVEL.DEBUG);
  } else {
    console.log('Debug logging not available in this version of RevenueCat SDK');
  }
}

// Configure with just the API key
Purchases.configure({
  apiKey
});
```

## Sandbox Environment Behavior

### Subscription Caching and Persistence

The RevenueCat SDK caches subscription information to reduce network requests and provide offline access to entitlements. In the sandbox environment, this caching behavior can sometimes lead to unexpected results:

1. **Accelerated Renewal Rates**: In the sandbox environment, subscription renewals happen at an accelerated rate:
   - 1-month subscriptions renew every 5 minutes
   - 3-month subscriptions renew every 15 minutes
   - 6-month subscriptions renew every 30 minutes
   - 1-year subscriptions renew every hour

2. **Subscription Deletion Persistence**: When a subscription is deleted in the RevenueCat sandbox environment, the app may still show the user as subscribed after restarting. This happens because:
   - The RevenueCat SDK caches subscription information for up to 5 minutes
   - Our app also caches subscription status in AsyncStorage for offline access

### Our Robust Solution

To ensure reliable subscription handling and offline access, we use a hybrid approach that combines RevenueCat's SDK caching with our own AsyncStorage caching:

1. **Dual Caching Strategy**:
   - Use RevenueCat SDK's built-in caching (which lasts for 5 minutes)
   - Always cache subscription status in AsyncStorage for reliable offline access
   - This ensures users maintain premium access when offline

2. **Network-Aware Status Checking**:
   - Check network connectivity before attempting to refresh from RevenueCat
   - Use cached AsyncStorage values when offline
   - Always update AsyncStorage when online checks succeed

3. **Consistent Refresh Mechanism**:
   - Refresh subscription status when the app becomes active
   - Always invalidate RevenueCat cache when checking status online
   - Update AsyncStorage with the latest status after each check

4. **Robust Error Handling**:
   - Implement fallback mechanisms for network failures
   - Use cached values when RevenueCat checks fail
   - Provide detailed logging for troubleshooting

5. **Testing Recommendations**:
   - After deleting a subscription in the sandbox environment, force refresh by closing and reopening the app
   - Test offline access by enabling airplane mode after confirming subscription status
   - If issues persist, clear both RevenueCat cache and AsyncStorage cache

### Implementation Examples

#### Network-Aware Subscription Status Checking

```typescript
// In revenueCatService.ts
export const checkIfUserHasAccess = async (): Promise<boolean> => {
  try {
    // First check network connectivity
    const networkState = await NetInfo.fetch();

    // If we're offline, use the cached value from AsyncStorage
    if (!networkState.isConnected) {
      console.log('No internet connection, using cached premium status');
      const cachedStatus = await AsyncStorage.getItem('premiumUnlocked');
      return cachedStatus === 'true';
    }

    // We're online, so check with RevenueCat
    try {
      // always fetch fresh entitlements
      await Purchases.invalidateCustomerInfoCache();
      const customerInfo = await Purchases.getCustomerInfo();
      const entitlement = customerInfo.entitlements.active[ENTITLEMENT_ID];
      const isPremium = entitlement?.isActive ?? false;

      // Cache the result for offline use
      await AsyncStorage.setItem('premiumUnlocked', isPremium ? 'true' : 'false');

      return isPremium;
    } catch (e) {
      console.error('Error checking premium access with RevenueCat', e);

      // If RevenueCat check fails, fall back to cached value
      const cachedStatus = await AsyncStorage.getItem('premiumUnlocked');
      return cachedStatus === 'true';
    }
  } catch (e) {
    console.error('Error in checkIfUserHasAccess', e);

    // Last resort - try to read from AsyncStorage
    try {
      const cachedStatus = await AsyncStorage.getItem('premiumUnlocked');
      return cachedStatus === 'true';
    } catch {
      // If everything fails, default to not premium
      return false;
    }
  }
};
```

#### App State Change Subscription Status Refresh

```typescript
// In AppSettingsContext.tsx
useEffect(() => {
  const subscription = AppState.addEventListener('change', async nextState => {
    if (nextState === 'active') {
      // Check subscription status when app becomes active
      const hasAccess = await checkIfUserHasAccess();
      setIsPremiumUnlocked(hasAccess);
      // Ensure the cached value is updated
      await AsyncStorage.setItem('premiumUnlocked', hasAccess ? 'true' : 'false');
    }
  });
  return () => subscription.remove();
}, []);
```

#### Initial Premium Status Loading

```typescript
// In AppSettingsContext.tsx
useEffect(() => {
  const loadPremiumStatus = async () => {
    // Check subscription status on app launch
    const hasAccess = await checkIfUserHasAccess();
    setIsPremiumUnlocked(hasAccess);
    // Ensure the cached value is updated
    await AsyncStorage.setItem('premiumUnlocked', hasAccess ? 'true' : 'false');
  };
  loadPremiumStatus();
}, []);
```

#### Unlocking Premium Content

```typescript
// In AppSettingsContext.tsx
const unlockPremium = async () => {
  // Update the UI state
  setIsPremiumUnlocked(true);
  // Ensure the cached value is updated
  await AsyncStorage.setItem('premiumUnlocked', 'true');
};
```

## Common Subscription Issues and Solutions

### 1. Inconsistent Subscription Status Detection

**Problem**:
- When a subscription is canceled in the RevenueCat dashboard, the app correctly revokes premium access initially.
- However, when reopening the app in a subsequent session, premium access is incorrectly restored even though the subscription remains canceled.

**Causes**:
- RevenueCat SDK's internal caching mechanism (5 minutes) may not be properly invalidated between app sessions
- Relying solely on RevenueCat's cache without proper invalidation
- Inconsistent AsyncStorage caching

**Solution**:
- Always invalidate the RevenueCat cache when checking subscription status online
- Implement a consistent AsyncStorage caching strategy
- Refresh subscription status when the app becomes active
- Use the implementation pattern shown in the examples above

### 2. Offline Access Issues

**Problem**:
- Premium content becomes inaccessible when there's no internet connection
- Users lose access to premium content they've paid for when offline

**Causes**:
- Not properly caching subscription status for offline use
- Not checking network connectivity before attempting to refresh from RevenueCat
- Not implementing proper fallback mechanisms

**Solution**:
- Always cache subscription status in AsyncStorage after successful online checks
- Check network connectivity before attempting to refresh from RevenueCat
- Use cached AsyncStorage values when offline
- Implement robust error handling with fallbacks to cached values

### 3. Sandbox Testing Issues

**Problem**:
- Deleted subscriptions in the sandbox environment sometimes reactivate after app restart
- Inconsistent behavior between sandbox and production environments

**Causes**:
- RevenueCat sandbox environment has accelerated renewal rates
- Caching mechanisms may not properly reflect sandbox subscription changes

**Solution**:
- Always invalidate the cache when testing in sandbox
- Wait at least 5 minutes after deleting a subscription before testing
- Implement proper cache invalidation when the app becomes active
- Use the network-aware implementation pattern shown in the examples

## Recent Fixes and Improvements

The following improvements have been implemented to address various issues:

1. **Robust Offline Support**:
   - Added network connectivity checking before RevenueCat API calls
   - Implemented consistent AsyncStorage caching for offline access
   - Added multiple fallback mechanisms for error scenarios

2. **Consistent Subscription Detection**:
   - Always invalidate RevenueCat cache when checking subscription status online
   - Refresh subscription status when the app becomes active
   - Ensure AsyncStorage is always updated with the latest status

3. **Platform-Specific Optimizations**:
   - Added BILLING permission to Android configuration
   - Simplified the container for RevenueCatUI.Paywall on Android
   - Minimized lifecycle issues with Jetpack Compose

4. **Developer Experience**:
   - Added more detailed logging for debugging
   - Enhanced error handling in the purchase flow
   - Updated RevenueCat SDK to the latest version (8.10.0)

## Best Practices for RevenueCat Integration

To ensure reliable subscription handling and prevent issues from recurring, follow these best practices:

### 1. Always Use Network-Aware Status Checking

```typescript
// First check network connectivity
const networkState = await NetInfo.fetch();
if (!networkState.isConnected) {
  // Use cached value from AsyncStorage
  const cachedStatus = await AsyncStorage.getItem('premiumUnlocked');
  return cachedStatus === 'true';
}
```

### 2. Implement Consistent AsyncStorage Caching

```typescript
// Always cache the result after successful online checks
await AsyncStorage.setItem('premiumUnlocked', isPremium ? 'true' : 'false');
```

### 3. Always Invalidate Cache When Online

```typescript
// Always fetch fresh entitlements when online
await Purchases.invalidateCustomerInfoCache();
const customerInfo = await Purchases.getCustomerInfo();
```

### 4. Implement Robust Error Handling

```typescript
try {
  // RevenueCat operations
} catch (e) {
  // Log the error
  console.error('Error checking premium access with RevenueCat', e);

  // Fall back to cached value
  const cachedStatus = await AsyncStorage.getItem('premiumUnlocked');
  return cachedStatus === 'true';
}
```

### 5. Refresh Status on App Resume

```typescript
// In AppSettingsContext.tsx
useEffect(() => {
  const subscription = AppState.addEventListener('change', async nextState => {
    if (nextState === 'active') {
      // Check subscription status when app becomes active
      const hasAccess = await checkIfUserHasAccess();
      setIsPremiumUnlocked(hasAccess);
    }
  });
  return () => subscription.remove();
}, []);
```

### 6. Test Thoroughly in Different Network Conditions

- Test with internet connection
- Test in airplane mode
- Test with intermittent connectivity
- Test subscription cancellation and restoration

## References

- [RevenueCat Android Documentation](https://www.revenuecat.com/docs/getting-started/installation/android)
- [RevenueCat React Native Documentation](https://www.revenuecat.com/docs/getting-started/installation/reactnative)
- [RevenueCat Subscription Status Documentation](https://www.revenuecat.com/docs/customers/customer-info)
- [RevenueCat Sandbox Testing Documentation](https://www.revenuecat.com/docs/test-and-launch/sandbox)
- [RevenueCat Apple Sandbox Documentation](https://www.revenuecat.com/docs/test-and-launch/sandbox/apple-app-store)
- [React Native NetInfo Documentation](https://github.com/react-native-netinfo/react-native-netinfo)
