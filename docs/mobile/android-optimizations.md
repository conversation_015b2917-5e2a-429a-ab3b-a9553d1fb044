# Android Optimizations

## Overview

This document outlines specific optimizations implemented for the Android version of the TapTrap app to address platform-specific performance issues and improve user experience.

## SVG Loading in Language Modal

### Issue

On Android devices, the language selection modal in the Settings screen experienced performance issues:
- The modal appeared to open slowly
- The UI felt laggy and buggy when interacting with language options
- SVG rendering was causing noticeable delays

### Solution

We implemented a progressive loading approach for the language selection modal:

1. **Immediate Modal Display**: The modal now appears immediately without waiting for SVG assets to load
2. **Loading Indicator**: A spinner is displayed while SVG assets (language flags) are loading
3. **Progressive Content Reveal**: Once SVGs are loaded, the language options are displayed

### Implementation Details

The solution uses React's state management to control the loading state of SVG assets:

```typescript
// In SettingsScreen.tsx
const [flagsLoaded, setFlagsLoaded] = useState(Platform.OS !== 'android'); // Initially loaded for iOS

// Effect to simulate SVG loading in Android
useEffect(() => {
  if (Platform.OS === 'android' && selectedSetting === 'language') {
    // Simulate loading time for SVGs on Android
    const timer = setTimeout(() => {
      setFlagsLoaded(true);
    }, 300); // 300ms is enough for the modal to appear before loading SVGs
    
    return () => {
      clearTimeout(timer);
    };
  }
}, [selectedSetting]);

// Reset loading state when modal closes
useEffect(() => {
  if (!selectedSetting && Platform.OS === 'android') {
    setFlagsLoaded(false);
  }
}, [selectedSetting]);
```

In the render function, we conditionally display a loading spinner or the language options:

```typescript
{selectedSetting === 'language' && (
  <View>
    <Text style={[typography.heading4, styles.bottomTitle]}>
      {t('settingLanguage')}
    </Text>

    {Platform.OS === 'android' && !flagsLoaded ? (
      // Show loading spinner only on Android while SVGs are loading
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#42FFB7" />
      </View>
    ) : (
      // Show language options once SVGs are loaded
      <>
        {/* Language options with SVG flags */}
        <TouchableOpacity
          style={language === 'en' ? styles.settingControlSeleted : styles.settingControl}
          onPress={async () => {
            setSelectedSetting(null);
            handleSelectLanguage('en');
            await playSound('tapEffect1');
            if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
        >
          <EnglishFlag width={56} height={56} />
          <Text style={[typography.subtitle2, styles.settingControlTitle]}>{t('languageEnglish')}</Text>
          {language === 'en' && <CheckMarkIcon width={48} height={48} />}
        </TouchableOpacity>
        
        {/* Other language options... */}
      </>
    )}
  </View>
)}
```

### Benefits

This optimization provides several benefits:

1. **Improved Perceived Performance**: The modal appears instantly, giving users immediate feedback
2. **Better User Experience**: The loading spinner communicates that content is loading
3. **Reduced Jank**: By delaying SVG rendering until after the modal animation completes, we avoid UI stuttering
4. **Platform-Specific Solution**: The optimization only applies to Android where the issue occurs

## Future Android Optimizations

### SVG Preloading

For future improvements, consider implementing a more robust SVG preloading mechanism:

```typescript
// Example of potential future implementation
const preloadSVGs = async () => {
  if (Platform.OS === 'android') {
    // Preload SVGs in the background when app starts
    try {
      // Implementation would depend on how SVGs are loaded in the app
      await Promise.all([
        // Preload methods for each SVG
      ]);
      setSVGsPreloaded(true);
    } catch (error) {
      console.error('Error preloading SVGs:', error);
    }
  }
};
```

### Other Android-Specific Optimizations

Other areas to consider for Android-specific optimizations:

1. **Touch Feedback**: Optimize touch feedback animations for Android
2. **Layout Performance**: Use `LayoutAnimation` instead of Animated for certain transitions
3. **Image Caching**: Implement more aggressive image caching for Android
4. **Memory Management**: Implement more frequent garbage collection triggers
5. **Hardware Acceleration**: Ensure hardware acceleration is enabled for complex animations

## Testing Android Optimizations

When implementing Android-specific optimizations:

1. **Test on Multiple Devices**: Test on various Android devices with different specs
2. **Profile Performance**: Use React Native performance monitors to measure improvements
3. **A/B Testing**: Consider A/B testing optimizations with real users
4. **Gather Metrics**: Collect metrics on app performance before and after optimizations

## References

- [React Native Performance Guide](https://reactnative.dev/docs/performance)
- [Android Platform Specific Code](https://reactnative.dev/docs/platform-specific-code)
- [SVG Performance in React Native](https://github.com/react-native-svg/react-native-svg#performance)
