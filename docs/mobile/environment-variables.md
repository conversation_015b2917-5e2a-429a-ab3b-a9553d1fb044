# Environment Variables in TapTrap Mobile App

This document explains how environment variables are configured and used in the TapTrap mobile application.

## Overview

TapTrap uses environment variables to manage configuration that may change between environments (development, staging, production) or that contains sensitive information like API keys. The app uses the `dotenv` package to load environment variables from a `.env` file and makes them available to the app through Expo's configuration system.

## Setup

### 1. Environment Files

The following files are used for environment variables:

- `.env`: Main environment file for local development
- `.env.example`: Template file with example values (no real secrets)

The `.env` file should never be committed to the repository. It is listed in `.gitignore`.

### 2. Required Variables

The TapTrap mobile app requires the following environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `API_ENCRYPTION_KEY` | Key used to decrypt encrypted API responses | `your-strong-encryption-key-at-least-32-chars` |

### 3. Configuration in app.config.js

The `app.config.js` file loads environment variables and makes them available to the app:

```javascript
require('dotenv').config();

module.exports = {
  expo: {
    // ... other configuration
    owner: "eliezerpujols",
    slug: "tap-trap",
    extra: {
      apiEncryptionKey: process.env.API_ENCRYPTION_KEY,
      eas: {
        projectId: "c488beec-b0b8-4ad1-b82a-4e646c9103a4",
      },
    },
  },
};
```

## Usage in Code

### Accessing Environment Variables

Environment variables are accessed in the app through Expo's Constants:

```typescript
import Constants from 'expo-constants';

// Access the API encryption key
const API_ENCRYPTION_KEY = Constants.expoConfig?.extra?.apiEncryptionKey || '';
```

Example from `services/contentService.ts`:

```typescript
// Access the encryption key from EAS environment variables
const API_ENCRYPTION_KEY = Constants.expoConfig?.extra?.apiEncryptionKey || '';

// Add validation to ensure the key is available
if (!API_ENCRYPTION_KEY) {
  console.error('API encryption key not found in EAS configuration');
}
```

## Local Development vs. EAS Builds

### Local Development

For local development:

1. Create a `.env` file in the project root with your environment variables:
   ```
   API_ENCRYPTION_KEY=your-encryption-key
   ```

2. The `dotenv` package will load these variables when you run the app locally.

### EAS Builds (Production)

For EAS (Expo Application Services) builds:

1. Set up environment variables in EAS using the CLI:
   ```bash
   eas secret:create --scope project --name API_ENCRYPTION_KEY --value "your-encryption-key"
   ```

2. Or configure them in the EAS dashboard for your project.

This ensures your app will use the local `.env` file during development and the EAS secrets during production builds.

## Security Considerations

- Never commit `.env` files to your repository
- Use different encryption keys for development and production
- Regularly rotate encryption keys for security
- Validate that required environment variables are present before using them

## Troubleshooting

### Environment Variables Not Loading

If environment variables aren't loading properly:

1. Verify that the `.env` file exists in the project root
2. Check that the variable names match exactly (they are case-sensitive)
3. Restart the development server with `expo start --clear`
4. Check the console for any error messages

### Testing Environment Variables

You can create a simple test script to verify that environment variables are loading correctly:

```javascript
// test-env.js
require('dotenv').config();

console.log('Testing environment variables loading:');
console.log('API_ENCRYPTION_KEY from process.env:',
  process.env.API_ENCRYPTION_KEY ? 'DEFINED' : 'UNDEFINED');

// Show the first few characters of the key for verification
if (process.env.API_ENCRYPTION_KEY) {
  console.log('First 10 characters of API_ENCRYPTION_KEY:',
    process.env.API_ENCRYPTION_KEY.substring(0, 10) + '...');
}
```

Run with:
```bash
node test-env.js
```
