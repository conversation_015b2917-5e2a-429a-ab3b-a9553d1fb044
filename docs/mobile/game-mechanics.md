# TapTrap Game Mechanics

## Overview

TapTrap's core gameplay revolves around a multi-touch selection system where players place their fingers on the screen and one is randomly selected to answer a question or perform a challenge. This document explains the technical implementation of these game mechanics.

## Touch Detection System

### Implementation

The touch detection system is implemented in `TouchGameScreen.tsx` and handles:

1. **Touch Tracking**: Records position and identifier of each finger
2. **Touch Movement**: Updates position as fingers move
3. **Touch Removal**: Detects when fingers are removed
4. **Selection Logic**: Implements the random selection algorithm

### Touch Point Data Structure

Each touch point is represented by an object with the following properties:

```typescript
interface TouchPoint {
  identifier: number;  // Unique identifier for the touch
  pageX: number;       // X-coordinate on the screen
  pageY: number;       // Y-coordinate on the screen
  isHighlighted: boolean; // Whether the touch is highlighted
  isSelected: boolean;    // Whether the touch is selected
  seq: number;         // Sequence number for tracking order
}
```

### Touch Event Handlers

#### Touch Start

```typescript
const handleTouchStart = (event: GestureResponderEvent) => {
  const touches = event.nativeEvent.touches;

  setTouchPoints(prevPoints => {
    const newPoints = [...prevPoints];

    touches.forEach(touch => {
      const identifier = Number(touch.identifier);
      // Add new finger if it's not already tracked
      if (!newPoints.some(p => p.identifier === identifier)) {
        newPoints.push({
          identifier,
          pageX: touch.pageX,
          pageY: touch.pageY,
          isHighlighted: false,
          isSelected: false,
          seq: nextSeq.current++,
        });
      }
    });
    return newPoints;
  });

  if (touches.length >= 2 && !gameStarted && !gameComplete) {
    startGame(touches);
  }
};
```

#### Touch Move

```typescript
const handleTouchMove = (event: GestureResponderEvent) => {
  const touches = event.nativeEvent.touches;

  setTouchPoints(prevPoints => {
    const updatedPoints = [...prevPoints];

    touches.forEach(touch => {
      const identifier = Number(touch.identifier);
      const index = updatedPoints.findIndex(p => p.identifier === identifier);

      if (index !== -1) {
        updatedPoints[index] = {
          ...updatedPoints[index],
          pageX: touch.pageX,
          pageY: touch.pageY,
        };
      }
    });

    return updatedPoints;
  });
};
```

#### Touch End

```typescript
const handleTouchEnd = (event: GestureResponderEvent) => {
  const touches = event.nativeEvent.touches;
  const activeIdentifiers = touches.map(t => Number(t.identifier));

  setTouchPoints(prevPoints => {
    // Keep only the touch points that are still active
    return prevPoints.filter(p => activeIdentifiers.includes(p.identifier));
  });

  // Game logic for handling touch removal
  // ...
};
```

## Game Flow

### Starting the Game

The game starts when at least two fingers are detected on the screen:

```typescript
const startGame = (touches: GestureResponderEvent['nativeEvent']['touches']) => {
  const initialPoints: TouchPoint[] = touches.map(touch => ({
    identifier: Number(touch.identifier),
    pageX: touch.pageX,
    pageY: touch.pageY,
    isHighlighted: false,
    isSelected: false,
    seq: nextSeq.current++,
  }));

  setTouchPoints(initialPoints);
  setGameStarted(true);
  selectRandomFinger(initialPoints);
};
```

### Countdown and Selection

When the game starts, a countdown begins and then a random finger is selected:

```typescript
const selectRandomFinger = (points: TouchPoint[]) => {
  // Start countdown
  setCountdown(3);

  // Play countdown sound
  startCountdownSound();

  // Start countdown timer
  countdownTimerRef.current = setInterval(() => {
    setCountdown(prev => {
      if (prev === 1) {
        // Last count, clear interval
        if (countdownTimerRef.current) {
          clearInterval(countdownTimerRef.current);
          countdownTimerRef.current = null;
        }

        // Select random finger after countdown
        setTimeout(() => {
          selectWinner(points);
        }, 500);

        return null;
      }
      return prev - 1;
    });
  }, 1000);
};
```

### Winner Selection

After the countdown, a random finger is selected as the winner:

```typescript
const selectWinner = (points: TouchPoint[]) => {
  // If no points, reset game
  if (points.length === 0) {
    handleResetTransition();
    return;
  }

  // Select random index
  const randomIndex = Math.floor(Math.random() * points.length);

  // Update touch points to highlight winner
  setTouchPoints(prevPoints => {
    return prevPoints.map((point, index) => {
      if (index === randomIndex) {
        return { ...point, isSelected: true };
      }
      return point;
    });
  });

  // Play selection sound and haptic feedback
  playSelectionSound();
  if (isHapticsOn) Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

  // Show question or challenge after selection
  setTimeout(() => {
    getRandomQuestion().then(question => {
      setQuestion(question);
      setGameComplete(true);
    });
  }, 1000);
};
```

## Question and Challenge System

### Content Structure

Questions and challenges are organized by categories and difficulties:

```typescript
interface GameContent {
  questions: {
    casual_questions: Question[];
    mild_questions: Question[];
    spicy_questions: Question[];
    no_limits_questions: Question[];
  };
  dares: {
    casual_dares: Challenge[];
    mild_dares: Challenge[];
    spicy_dares: Challenge[];
    no_limits_dares: Challenge[];
  };
}

interface Question {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
}

interface Challenge {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
}
```

### Getting Random Questions/Challenges

The game retrieves random questions or challenges based on the selected categories:

```typescript
const getRandomQuestion = async (): Promise<Question | null> => {
  // Get available questions from selected category
  const availableQuestions = gameContent?.questions[selectedQuestionCategory] || [];

  // Filter out already used questions
  const unusedQuestions = availableQuestions.filter(q => !usedQuestionIds.has(q.id));

  // If all questions used, reset used IDs
  if (unusedQuestions.length === 0) {
    setUsedQuestionIds(new Set());
    return availableQuestions[Math.floor(Math.random() * availableQuestions.length)] || null;
  }

  // Select random question
  const randomQuestion = unusedQuestions[Math.floor(Math.random() * unusedQuestions.length)];

  // Mark as used
  setUsedQuestionIds(prev => new Set([...prev, randomQuestion.id]));

  return randomQuestion;
};
```

## Sound and Haptic Feedback

The game uses sound effects and haptic feedback to enhance the user experience:

```typescript
// Play sound effect
await playSound('tapEffect1');

// Trigger haptic feedback
if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
```

## Game Reset

The game can be reset to start a new round:

```typescript
const handleResetTransition = () => {
  // Reset game state
  setGameStarted(false);
  setGameComplete(false);
  setTouchPoints([]);
  setQuestion(null);

  // Reset animations
  dialogScale.value = 0;

  // Clear any active timers
  if (countdownTimerRef.current) {
    clearInterval(countdownTimerRef.current);
    countdownTimerRef.current = null;
  }
};
```

## Premium Content

Some categories are locked behind premium access:

```typescript
const toggleCategory = async (category: 'casual_questions' | 'mild_questions' | 'spicy_questions' | 'no_limits_questions') => {
  await playSound('tapEffect2');
  if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

  const isPremium = category === 'no_limits_questions' || category === 'spicy_questions';
  if (isPremium && !isPremiumUnlocked) {
    setShowPaywall(true);
    return;
  }

  setSelectedCategories(prev =>
    prev.includes(category) ? prev.filter(c => c !== category) : [...prev, category]
  );
};
```
