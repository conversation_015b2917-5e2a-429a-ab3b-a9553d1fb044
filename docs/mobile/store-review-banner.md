# Store Review Banner Feature

## Overview

The Store Review Banner feature prompts premium iOS users to rate the TapTrap app on the App Store. This feature follows the same implementation patterns as the existing InGameBanner component and integrates seamlessly with the game experience.

## Implementation

### Components

#### StoreReviewBanner Component
**File**: `@components/game/StoreReviewBanner.tsx`

A banner component that displays a call-to-action for users to rate the app on the App Store.

**Props:**
- `onPress?: () => void` - Callback function when banner is pressed
- `style?: ViewStyle` - Optional custom styling

**Features:**
- Uses localized text via `useLanguage()` hook
- Displays star icon and "Rate Us" call-to-action
- Consistent styling with other banner components

#### Service Integration
**File**: `@services/storeReviewService.ts`

Handles the store review logic, state persistence, and retry mechanisms.

**Key Functions:**
- `shouldShowStoreReviewBanner(isPremiumUnlocked: boolean): Promise<boolean>` - Checks all conditions
- `handleStoreReviewRequest(): Promise<boolean>` - Requests App Store review
- `getStoreReviewState(): Promise<StoreReviewState>` - Gets current review state
- `resetStoreReviewState(): Promise<void>` - Resets state (for testing)

### Display Conditions

The store review banner is shown only when ALL of the following conditions are met:

1. **Question Count**:
   - **Premium users**: 2nd question/content
   - **Free users**: 6th question/content
2. **Internet Connection**: User must have an active internet connection
3. **iOS Platform**: Only displayed on iOS devices (uses `Platform.OS === 'ios'`)
4. **Review State**: User hasn't successfully reviewed the app before
5. **Retry Logic**: If previous attempts failed, at least 5 days must have passed

### Display Timing

The store review banner shows for **both free and premium users** with different timing:

**Premium Users:**
- Shows for **any game category** (casual, mild, spicy, no limits)
- Appears on the **2nd question/content**
- Earlier timing to encourage reviews from engaged premium users

**Free Users:**
- Shows for **any game category** (casual, mild, spicy, no limits)
- Appears on the **6th question/content**
- Later timing to avoid interrupting the initial experience

### Banner Interaction

**Priority System:**
- Only one banner is visible at a time
- When store review banner appears, it automatically hides the InGameBanner
- Store review banner takes priority over InGameBanner when both conditions are met

**Auto-Hide Timer:**
- Store review banner automatically hides after **2 minutes** if not interacted with
- Same timing as InGameBanner for consistent user experience
- Timer is cleared when banner is manually hidden or user navigates away

### State Management

#### AsyncStorage Keys
- `storeReviewSuccess` - Boolean indicating successful review completion
- `storeReviewLastAttempt` - Timestamp of last review attempt
- `storeReviewFailedCount` - Number of failed attempts

#### Banner State
The banner uses the same animation patterns as InGameBanner:
- Fade-in animation when shown (400ms duration)
- Fade-out animation when hidden (300ms duration)
- Positioned above the InGameBanner when both are visible

### Integration in TouchGameScreen

#### State Variables
```typescript
const [showStoreReviewBanner, setShowStoreReviewBanner] = useState(false);
const storeReviewBannerOpacity = useSharedValue(0);
```

#### Question Count Logic
The banner is shown in the same question count increment logic as InGameBanner:
```typescript
// Show StoreReviewBanner - premium users on 2nd question, free users on 6th question
const shouldCheckStoreReview =
  (newCount === 4 && isPremiumUnlocked) ||
  (newCount === 12 && !isPremiumUnlocked);

if (shouldCheckStoreReview && !showStoreReviewBanner) {
  console.log(`Checking store review banner conditions (${isPremiumUnlocked ? 'premium' : 'free'} user, question ${newCount})...`);
  // Check store review conditions asynchronously
  shouldShowStoreReviewBanner(isPremiumUnlocked).then(shouldShow => {
    if (shouldShow) {
      console.log('Showing store review banner - conditions met!');

      // Hide InGameBanner if it's currently visible before showing store review banner
      if (showBanner) {
        console.log('Hiding InGameBanner to show store review banner');
        hideBanner();
      }

      setShowStoreReviewBanner(true);
      storeReviewBannerOpacity.value = withTiming(1, { duration: 400 });

      // Start the auto-hide timer for store review banner
      startStoreReviewBannerTimer();
    }
  }).catch(error => {
    console.error('Error checking store review banner conditions:', error);
  });
}
```

#### Timer Functions
Auto-hide functionality with dedicated timer management:
```typescript
// Function to hide the store review banner with a smooth fade-out animation
const hideStoreReviewBanner = () => {
  if (showStoreReviewBanner && storeReviewBannerOpacity.value !== 0) {
    console.log('Hiding store review banner with fade-out animation');

    // Clear any existing timer
    if (storeReviewBannerTimerRef.current) {
      clearTimeout(storeReviewBannerTimerRef.current);
      storeReviewBannerTimerRef.current = null;
    }

    // Fade out the banner smoothly
    storeReviewBannerOpacity.value = withTiming(0, { duration: 400 });
    setTimeout(() => setShowStoreReviewBanner(false), 400);
  }
};

// Function to start the store review banner auto-hide timer
const startStoreReviewBannerTimer = () => {
  // Clear any existing timer first
  if (storeReviewBannerTimerRef.current) {
    clearTimeout(storeReviewBannerTimerRef.current);
    storeReviewBannerTimerRef.current = null;
  }

  // Set a new timer to hide the banner after exactly 2 minutes
  storeReviewBannerTimerRef.current = setTimeout(() => {
    console.log('Store review banner timer expired - hiding banner now');
    hideStoreReviewBanner();
  }, 120 * 1000); // 120 seconds = 2 minutes
};
```

#### Banner Rendering
```typescript
{/* Store Review Banner - Show for both free and premium iOS users with internet */}
{showStoreReviewBanner && (
  <View style={styles.storeReviewBanner}>
    <Animated.View style={[storeReviewBannerAnimatedStyle]}>
      <StoreReviewBanner onPress={handleStoreReviewBannerPress} />
    </Animated.View>
  </View>
)}
```

### Error Handling and Retry Logic

#### Success Case
- When `StoreReview.requestReview()` succeeds, the banner is permanently hidden
- Success state is saved to AsyncStorage
- Banner will never show again for that user

#### Failure Cases
- **ERR_STORE_REVIEW_FAILED**: Retryable error - banner will show again after 5 days
- **NOT_AVAILABLE**: Store review not available on device - treated as permanent failure
- **Other errors**: Treated as permanent failures

#### Retry Mechanism
- Failed attempts are tracked with timestamps
- 5-day delay before showing banner again after failure
- Failed count is incremented for analytics purposes

### Localization

The banner uses the following translation keys:

```typescript
storeReviewBannerTitle: {
  en: 'Love the game?',
  es: '¿Te diviertes?',
  dom: '¿Te diviertes?'
},
storeReviewBannerButton: {
  en: 'Rate Us',
  es: 'Califícanos',
  dom: 'Califícanos'
}
```

### Dependencies

#### Required Packages
- `expo-store-review@~8.1.5` - For App Store review functionality
- `@react-native-async-storage/async-storage` - For state persistence
- `@react-native-community/netinfo` - For connectivity checking
- `react-native` - For platform detection

#### Existing Dependencies
- `react-native-reanimated` - For animations
- Custom i18n service for localization

### Styling

The banner follows the same visual patterns as other banners:
- Positioned absolutely at bottom of screen
- Centered horizontally
- Dark background with rounded corners
- Consistent typography and spacing
- Z-index slightly lower than InGameBanner (998 vs 999)

### Testing Considerations

#### Manual Testing
1. **Premium Users**: Test on iOS device with premium subscription
   - Banner should appear on 2nd question/content
2. **Free Users**: Test on iOS device without premium subscription
   - Banner should appear on 6th question/content
3. Verify banner appears for new users (both free and premium)
4. Test banner press triggers App Store review
5. Verify banner doesn't reappear after successful review
6. Test retry logic after failed attempts

#### Reset Function
Use `resetStoreReviewState()` to clear all stored state for testing:
```typescript
import { resetStoreReviewState } from '@/services/storeReviewService';
await resetStoreReviewState();
```

#### Development Testing Button
A red "CLEAR REVIEW" button appears in development builds (`__DEV__`) in TouchGameScreen:
- **Location**: Top-right corner, left of the Settings button
- **Function**: Clears all store review AsyncStorage state
- **Behavior**: Hides current banner and resets state for fresh testing
- **Note**: Banner will appear after the second question, not immediately after clearing

**Usage:**
1. Press "CLEAR REVIEW" to reset state
2. Play the game and receive questions/content:
   - **Premium users**: Banner appears on 2nd question
   - **Free users**: Banner appears on 6th question
3. Store review banner should appear (if all conditions are met)
4. Test the review flow by pressing the banner

### Performance Considerations

- Banner condition checking is only triggered when premium status changes
- AsyncStorage operations are batched where possible
- Network connectivity is checked efficiently using NetInfo
- Banner animations use native driver for smooth performance

### Future Enhancements

Potential improvements for future versions:
- Analytics tracking for banner impressions and interactions
- A/B testing for different banner designs or messaging
- Configurable retry delays via remote config
- Support for Android app rating (Google Play In-App Review)
