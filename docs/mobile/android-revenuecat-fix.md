# Android RevenueCat Paywall Fix

## Issue

The TapTrap app was experiencing crashes on Android when attempting to open the RevenueCat paywall for premium categories. The specific error was:

```
java.lang.IllegalStateException: ViewTreeLifecycleOwner not found from android.widget.FrameLayout{d0b4aa4 V.E...... ......I. 0,0-0,0}
```

This error occurs because the RevenueCatUI.Paywall component uses Jetpack Compose internally, and when rendered inside a React Native Modal with complex animations, it encounters lifecycle issues.

## Solution

The solution implemented involves:

1. Using `RevenueCatUI.presentPaywall()` method for Android instead of the Modal component
2. Keeping the Modal approach for iOS for consistent UX
3. Adding proper error handling to prevent app crashes

### Implementation Details

#### 1. SelectCategoriesScreen.tsx

In the `toggleCategory` function, we now use different approaches for Android and iOS:

```javascript
if (Platform.OS === 'android') {
  // For Android, use the presentPaywall method instead of the Modal component
  try {
    const result = await RevenueCatUI.presentPaywall({
      fontFamily: "<PERSON>indya",
    });
    
    // Check if purchase was successful
    if (result === 'PURCHASED' || result === 'RESTORED') {
      unlockPremium();
    }
  } catch (paywallError) {
    console.error('Error presenting paywall on Android:', paywallError);
  }
} else {
  // For iOS, continue using the Modal approach
  setShowPaywall(true);
}
```

The Modal component is now only rendered for iOS:

```javascript
{/* Show paywall modal for iOS only - Android uses presentPaywall */}
{showPaywall && Platform.OS === 'ios' && (
  <Modal
    transparent
    visible
    animationType="none"
    onRequestClose={() => {
      setShowPaywall(false);
    }}
  >
    <RNAnimated.View style={{ flex: 1, opacity: fadeAnim }}>
      <RevenueCatUI.Paywall
        options={{
          fontFamily: "Melindya",
          displayCloseButton: true,
        }}
        onDismiss={() => setShowPaywall(false)}
        onPurchaseCompleted={() => {
          unlockPremium();
          setShowPaywall(false);
        }}
        onPurchaseError={(error) => {
          console.error('Purchase error:', error);
          setShowPaywall(false);
        }}
      />
    </RNAnimated.View>
  </Modal>
)}
```

#### 2. TouchGameScreen.tsx

Similar changes were made in the `handleBannerPress` function:

```javascript
if (Platform.OS === 'android') {
  try {
    const result = await RevenueCatUI.presentPaywall({
      fontFamily: "Melindya",
    });
    
    if (result === 'PURCHASED' || result === 'RESTORED') {
      unlockPremium();
    }
  } catch (paywallError) {
    console.error('Error presenting paywall on Android:', paywallError);
  }
} else {
  setShowPaywall(true);
}
```

And the Modal component is also iOS-only in this screen.

## Technical Background

### Why This Issue Occurs

The RevenueCatUI.Paywall component uses Jetpack Compose internally. When this component is rendered inside a React Native Modal with complex animations, it encounters lifecycle issues because:

1. The React Native Modal creates a new Android Activity or Fragment
2. The Jetpack Compose components expect to be attached to a proper lifecycle owner
3. When rendered in a Modal with complex animations, the lifecycle owner may not be properly established

### Why presentPaywall() Works

The `presentPaywall()` method works because:

1. It handles the lifecycle management internally
2. It creates a proper Android Activity with the correct lifecycle configuration
3. It avoids the complexity of nesting Compose components in React Native views

## Best Practices for RevenueCat Integration

1. **For Android:**
   - Use `RevenueCatUI.presentPaywall()` or `RevenueCatUI.presentPaywallIfNeeded()` methods
   - Avoid using the `<RevenueCatUI.Paywall>` component inside React Native Modal components
   - Handle the result of the presentPaywall call to update the premium status

2. **For iOS:**
   - Either approach works well (Modal or presentPaywall)
   - If using Modal, avoid complex animations that might interfere with the component lifecycle

3. **Error Handling:**
   - Always wrap RevenueCat calls in try/catch blocks
   - Log detailed error information
   - Provide fallback behavior when errors occur

## Testing

After implementing these changes, test the following scenarios:

1. Tapping on locked premium categories (spicy_questions, no_limits_questions)
2. Tapping on the in-game banner when it appears
3. Completing a purchase through the paywall
4. Canceling a purchase
5. Testing on multiple Android devices with different OS versions

## References

- [RevenueCat Documentation - Displaying Paywalls](https://www.revenuecat.com/docs/tools/paywalls/displaying-paywalls)
- [RevenueCat Community - Android Crashes](https://community.revenuecat.com/sdks-51/app-crashes-when-i-try-to-show-a-pay-wall-in-react-native-android-4384)
