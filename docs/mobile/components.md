# TapTrap Mobile App Components

## Overview

The TapTrap mobile app uses a variety of reusable components to maintain consistency and reduce code duplication. This document outlines the key components used throughout the application.

## Core Components

### ThemedText

**File**: `components/ThemedText.tsx`

A customized Text component that applies consistent typography and theming across the app.

#### Features
- Supports different text styles (default, title, subtitle, etc.)
- Adapts to light/dark theme
- Provides typography constants for consistent text styling

#### Usage
```jsx
import { ThemedText, typography } from '@/components/ThemedText';

// Basic usage
<ThemedText>Regular text</ThemedText>

// With typography styles
<ThemedText style={typography.heading1}>Large Heading</ThemedText>

// With type prop
<ThemedText type="title">Title Text</ThemedText>
```

#### Typography Styles
The component exports a `typography` object with predefined styles:
- `headingJumbo`: Largest heading (138px)
- `heading1`: Primary heading (88px)
- `heading2`: Secondary heading (45px)
- `heading3`: Tertiary heading (35px)
- `heading4`: Quaternary heading (28px)
- `subtitle1`: Primary subtitle (24px)
- `subtitle2`: Secondary subtitle (20px)
- `subtitle3`: Tertiary subtitle (16px)

### ThemedView

**File**: `components/ThemedView.tsx`

A customized View component that applies consistent theming across the app.

#### Features
- Adapts to light/dark theme
- Applies appropriate background colors

#### Usage
```jsx
import { ThemedView } from '@/components/ThemedView';

<ThemedView style={styles.container}>
  {/* Content */}
</ThemedView>
```

## Game Components

### GameContext

**File**: `components/game/GameContext.tsx`

A React Context provider that manages the game state and logic.

#### Features
- Manages selected game modes and categories
- Handles question and challenge selection
- Provides functions for game flow control
- Integrates with content service for data

#### Usage
```jsx
import { useGameContext } from '@/components/game/GameContext';

function MyComponent() {
  const {
    selectedModes,
    getNextQuestion,
    currentQuestion
  } = useGameContext();

  // Use game context values and functions
}
```

#### Key Functions
- `setGameMode`: Sets the active game modes (questions, challenges)
- `setSelectedQuestionCategory`: Sets the active question category
- `setChallengeDifficulty`: Sets the active challenge difficulty
- `getNextQuestion`: Retrieves the next question from the selected category
- `getNextChallenge`: Retrieves the next challenge from the selected difficulty
- `resetGame`: Resets the game state

### PenaltyDialog

**File**: `components/game/PenaltyDialog.tsx`

A modal dialog component that appears when users press the "Refuse" button in the question/dare dialog. Provides alternative actions instead of simply skipping content.

#### Features
- Displays penalty-themed UI with red color scheme
- Two action buttons: "Back to Content" and "Next Match"
- Smooth animations matching existing dialog patterns
- Haptic feedback and sound effects
- Fully localized with translation support

#### Props
- `onBackToContent`: Function called when user wants to return to the previous question
- `onNextMatch`: Function called when user wants to reset the entire game
- `onPress`: Legacy prop (unused)
- `style`: Custom styling prop (unused)
- `isSequential`: Sequential mode flag (unused)

#### Usage
```jsx
import PenaltyDialog from '@/components/game/PenaltyDialog';

<PenaltyDialog
  onBackToContent={backToContentDialog}
  onNextMatch={handlePenaltyNextMatch}
/>
```

#### Button Functionality
- **Back to Content Button** (Flip icon): Returns to the previous question without affecting content pools
- **Next Match Button**: Resets the entire game like the existing reset button

### InGameBanner

**File**: `components/game/InGameBanner.tsx`

A customizable banner component that encourages users to upgrade to the premium plan. The banner displays different text based on the game state.

#### Features
- Displays a spicy icon with promotional text
- Supports two different text modes via the `isSequential` parameter
- Fully localized with translations for all supported languages
- Customizable styling via the `style` prop
- Handles touch events via the `onPress` prop
- Automatically hides after exactly 2 minutes (120 seconds) with a smooth fade-out animation
- Hides immediately when user presses "Skip" or "Done" buttons in the question modal

#### Props
- `onPress`: Function to call when the banner is pressed
- `style`: Custom styles to apply to the banner (ViewStyle)
- `isSequential`: Boolean flag that changes the displayed text (default: false)
  - When `false`: Shows "Want smth spicier?" / "Subscribe"
  - When `true`: Shows "Want smth spicier?" / "Try 4 Free"

#### Behavior
- Appears the second time a user receives a question/dare in Casual or Mild categories
- Fades in with the question
- Automatically fades out after exactly 2 minutes (120 seconds) even if users don't interact with it
- When user presses "Skip" or "Done" buttons, the banner fades out smoothly
- Does not appear for premium subscribers
- When tapped, displays premium content preview in the question overlay, even if the user has previously dismissed the dialog
- Changes to sequential mode after showing premium content preview

#### Usage
```jsx
import InGameBanner from '@/components/game/InGameBanner';

// Basic usage
<InGameBanner
  onPress={() => navigation.navigate('Settings')}
/>

// With sequential mode (different text)
<InGameBanner
  isSequential={true}
  onPress={() => navigation.navigate('Settings')}
/>

// With custom styling
<InGameBanner
  style={styles.customBanner}
  onPress={() => navigation.navigate('Settings')}
/>
```

### PremiumBadge

**File**: `assets/icons/premiumBadge.svg`

A visual indicator component for premium-only content throughout the app.

#### Features
- SVG-based premium badge icon
- Used in penalty settings to indicate premium penalties
- Consistent styling and positioning across the app
- Customizable size and styling

#### Props
- `style`: Custom styling for positioning (ViewStyle)
- `width`: Badge width (default: 79)
- `height`: Badge height (default: 28)

#### Usage
```jsx
import PremiumBadge from '@/assets/icons/premiumBadge.svg';

// Basic usage in penalty settings
<PremiumBadge
  style={styles.premiumBadge}
  width={79}
  height={28}
/>

// Custom positioning
<PremiumBadge
  style={{
    position: 'absolute',
    right: 16,
    top: -18,
  }}
  width={60}
  height={20}
/>
```

#### Styling Guidelines
- Position absolutely within parent container
- Typically placed in top-right corner of content items
- Use consistent sizing across similar UI elements
- Ensure sufficient contrast against background

### Sound Player

**File**: `components/game/playSound.tsx`

A custom hook for playing sound effects throughout the app.

#### Features
- Loads and caches sound effects
- Provides functions to play sounds with volume control
- Integrates with app settings for sound toggle

#### Usage
```jsx
import { useSound } from '@/components/game/playSound';

function MyComponent() {
  const { playSound } = useSound();

  const handlePress = async () => {
    await playSound('tapEffect1');
    // Do something after sound plays
  };
}
```

## UI Components

### TabBarBackground

**File**: `components/ui/TabBarBackground.ios.tsx`

A component that provides a blurred background for the tab bar on iOS.

#### Features
- Uses BlurView for native iOS blur effect
- Adapts to system theme
- Handles safe area insets

#### Usage
```jsx
import BlurTabBarBackground from '@/components/ui/TabBarBackground.ios';

<Tab.Navigator
  tabBar={(props) => (
    <View>
      <BlurTabBarBackground />
      <BottomTabBar {...props} />
    </View>
  )}
>
  {/* Tab screens */}
</Tab.Navigator>
```

## Custom Hooks

### useThemeColor

**File**: `hooks/useThemeColor.ts`

A custom hook that provides theme-aware colors.

#### Features
- Returns appropriate colors based on current theme
- Supports custom light/dark color overrides

#### Usage
```jsx
import { useThemeColor } from '@/hooks/useThemeColor';

function MyComponent() {
  const backgroundColor = useThemeColor(
    { light: '#fff', dark: '#000' },
    'background'
  );

  return (
    <View style={{ backgroundColor }}>
      {/* Content */}
    </View>
  );
}
```

## Component Hierarchy

The component hierarchy in the TapTrap app follows this general structure:

```
App
├── AppSettingsProvider
│   └── LanguageProvider
│       └── GameProvider
│           └── Navigation
│               ├── HomeScreen
│               │   ├── ThemedText
│               │   └── ThemedView
│               ├── SelectCategoriesScreen
│               │   ├── ThemedText
│               │   └── ThemedView
│               ├── TouchGameScreen
│               │   ├── ThemedText
│               │   ├── ThemedView
│               │   └── SparkEffect
│               └── SettingsScreen
│                   ├── ThemedText
│                   └── ThemedView
```

This hierarchy ensures that all components have access to the necessary context providers and maintains a clean, organized structure.
