# Push Notification Settings Navigation Implementation

## Overview

This document describes the implementation of a feature that guides users to their device's notification settings when push notifications are disabled in the TapTrap app.

## Problem Statement

When users deny push notification permissions, the existing banner would continue to appear but clicking it would not provide a clear path to enable notifications. Users needed guidance to manually enable notifications through their device settings.

## Solution Implementation

### 1. Platform-Specific Settings Navigation

**iOS Implementation:**
- Uses `Linking.openSettings()` to open the app's settings page
- Direct access to app-specific notification settings

**Android Implementation:**
- Uses `expo-intent-launcher` with `ActivityAction.APP_NOTIFICATION_SETTINGS`
- Opens app-specific notification settings using the app package name
- Fallback error handling for unsupported devices

### 2. Unified Settings Navigation Function

**Function**: `openNotificationSettings()`
- Automatically detects platform (iOS/Android)
- Calls appropriate platform-specific function
- Returns boolean indicating success/failure

### 3. User-Friendly Alert System

**Function**: `showNotificationSettingsAlert()`
- Checks current permission status before showing alert
- Only displays when permissions are actually denied
- Uses localized text from i18n service
- Provides "Open Settings" and "Cancel" options
- Shows error alert if settings cannot be opened

### 4. Enhanced Banner Logic

**Updated `handlePushBannerPress()` in TouchGameScreen:**
- Checks permission status before taking action
- If `status === 'denied'`: Shows settings navigation alert
- If other status: Uses normal permission request flow
- Maintains existing banner dismissal logic

## New Localized Strings

Added to `services/i18nService.tsx`:

```typescript
// English
pushNotificationDisabledTitle: 'Notifications Disabled'
pushNotificationDisabledMessage: 'To receive notifications about new content and features, please enable notifications in your device settings.'
pushNotificationOpenSettings: 'Open Settings'
pushNotificationCancel: 'Cancel'
pushNotificationSettingsError: 'Unable to open settings. Please enable notifications manually in your device settings.'

// Spanish
pushNotificationDisabledTitle: 'Notificaciones Desactivadas'
pushNotificationDisabledMessage: 'Para recibir notificaciones sobre nuevo contenido y funciones, activa las notificaciones en la configuración de tu dispositivo.'
pushNotificationOpenSettings: 'Abrir Configuración'
pushNotificationCancel: 'Cancelar'
pushNotificationSettingsError: 'No se pudo abrir la configuración. Activa las notificaciones manualmente en la configuración de tu dispositivo.'

// Dominican Spanish
pushNotificationDisabledTitle: 'Notificaciones Desactivadas'
pushNotificationDisabledMessage: 'Pa\' recibir notificaciones sobre contenido nuevo y funciones, activa las notificaciones en la configuración de tu dispositivo.'
pushNotificationOpenSettings: 'Abrir Configuración'
pushNotificationCancel: 'Cancelar'
pushNotificationSettingsError: 'No se pudo abrir la configuración. Activa las notificaciones manualmente en la configuración de tu dispositivo.'
```

## Dependencies Added

```json
{
  "expo-intent-launcher": "~13.0.0",
  "expo-application": "~6.0.0"
}
```

## Updated Banner Logic

**Important Change**: The banner logic has been updated to support the settings navigation feature.

**Previous Logic**:
- Banner would never show again if permissions were previously denied

**New Logic**:
- Banner shows for users with `undetermined` permissions (first-time users)
- Banner shows for users with `denied` permissions (so they can access settings navigation)
- Banner respects 8-hour dismissal cooldown for both scenarios
- Banner never shows if permissions are `granted`

## User Flow

### Scenario 1: First-time User
1. User sees push notification banner on first question/dare
2. User taps banner → Normal permission request dialog appears
3. User grants/denies permissions → Banner disappears

### Scenario 2: Previously Denied Permissions
1. User sees push notification banner (respects 8-hour cooldown)
2. User taps banner → Settings navigation alert appears
3. User chooses "Open Settings" → Device settings open
4. User enables notifications → Returns to app
5. App state change detection registers token automatically

### Scenario 3: Settings Navigation Failure
1. User taps banner with denied permissions
2. Settings navigation alert appears
3. User chooses "Open Settings" → Settings fail to open
4. Error alert appears with manual instructions

## Testing Instructions

### Test Case 1: iOS Settings Navigation
1. Deny notifications when first prompted
2. Wait for banner to appear again (or clear cache)
3. Tap banner → Verify settings alert appears
4. Tap "Open Settings" → Verify iOS Settings app opens to TapTrap
5. Enable notifications → Return to app
6. Verify token gets registered (check logs)

### Test Case 2: Android Settings Navigation
1. Install on Android device
2. Deny notifications when prompted
3. Tap banner → Verify settings alert appears
4. Tap "Open Settings" → Verify Android notification settings open
5. Enable notifications → Return to app
6. Verify token gets registered

### Test Case 3: Error Handling
1. Test on device/emulator where settings navigation might fail
2. Verify error alert appears with manual instructions
3. Verify app doesn't crash

### Test Case 4: Normal Permission Flow
1. Fresh install or clear cache
2. Tap banner → Verify normal permission dialog appears
3. Grant permissions → Verify token registration

## Debug Commands

```typescript
// Test settings navigation directly
await openPushNotificationSettings();

// Test settings alert with custom translations
await showPushNotificationSettingsAlert({
  title: 'Test Title',
  message: 'Test Message',
  openSettings: 'Open Settings',
  cancel: 'Cancel',
  error: 'Error Message'
});

// Clear cache to reset banner state
await clearPushNotificationCache();

// Debug banner conditions
await debugPushNotificationBanner();
```

## Log Messages to Monitor

### Settings Navigation:
- `🔔 [Settings] Opening notification settings for platform: ios/android`
- `🔔 [Settings] Opening iOS app settings...`
- `🔔 [Settings] Opening Android notification settings...`
- `🔔 [Settings] Package name: com.example.app`

### Banner Press Handling:
- `🔔 [TouchGameScreen] Current permission status: denied/granted`
- `🔔 [TouchGameScreen] Permissions denied, showing settings alert`
- `🔔 [TouchGameScreen] Attempting normal permission flow`

### User Actions:
- `🔔 [Settings] User cancelled settings navigation`
- `🔔 [Settings] User chose to open settings`

## Integration with Existing Features

### Banner Display Logic
- Maintains existing 8-hour dismissal cooldown
- Respects existing banner priority (doesn't show with other banners)
- Works with existing permission denied tracking

### App State Change Detection
- Integrates with recently implemented app state change detection
- Automatically registers tokens when users enable notifications via settings
- No additional user interaction required after enabling notifications

### Backward Compatibility
- All existing functionality preserved
- No breaking changes to existing code
- Service interface maintains backward compatibility

## Benefits

1. **Improved User Experience**: Clear path to enable notifications after denial
2. **Higher Notification Opt-in Rate**: Easier for users to enable notifications
3. **Platform-Specific Optimization**: Uses native settings navigation for each platform
4. **Robust Error Handling**: Graceful fallback when settings cannot be opened
5. **Seamless Integration**: Works with existing banner logic and app state detection
6. **Localized Experience**: Supports all app languages (English, Spanish, Dominican Spanish)
