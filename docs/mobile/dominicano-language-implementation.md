# Dominicano Language Implementation in TouchGameScreen

## Overview

This document details the implementation of the Dominicano language support in the TouchGameScreen component of the TapTrap mobile app. The implementation addresses TypeScript errors related to missing the `text_dom` property in question and challenge objects.

## Background

The TapTrap app has been updated to support Dominican Spanish (Dominicano) as a language option. This required adding a `text_dom` property to the Question and Challenge interfaces in the GameContext. However, the TouchGameScreen component was still using objects without this property, causing TypeScript errors.

## Error Details

The following TypeScript error was occurring:

```
Argument of type '{ id: string; text_en: string; text_es: string; type: "question" | "challenge"; category: string; }' is not assignable to parameter of type 'Question | Challenge | null'.
  Property 'text_dom' is missing in type '{ id: string; text_en: string; text_es: string; type: "question" | "challenge"; category: string; }' but required in type 'Challenge'.ts(2345)
GameContext.tsx(20, 3): 'text_dom' is declared here.
```

## Implementation Changes

### 1. Updated State Types

The question and availableQuestions state types were updated to include the `text_dom` property:

```typescript
const [question, setQuestion] = useState<{ 
  id: string; 
  text_en: string; 
  text_es: string; 
  text_dom: string; 
  type: 'question' | 'challenge'; 
  category: string 
} | null>(null);

const [availableQuestions, setAvailableQuestions] = useState<{ 
  id: string; 
  text_en: string; 
  text_es: string; 
  text_dom: string; 
  type: 'question' | 'challenge'; 
  category: string 
}[]>([]);
```

### 2. Updated Content Pool Type

The fullContentPool state type was also updated:

```typescript
const [fullContentPool, setFullContentPool] = useState<{ 
  id: string; 
  text_en: string; 
  text_es: string; 
  text_dom: string; 
  type: 'question' | 'challenge'; 
  category: string 
}[]>([]);
```

### 3. Added Fallback Mechanism

A fallback mechanism was implemented in the `createPoolFromContent` function to handle cases where content might not have the `text_dom` property yet:

```typescript
// For questions
questionsPool.push(
  ...content.questions[category].map((item: any) => ({
    ...item,
    // Add default text_dom if it doesn't exist in the item
    text_dom: item.text_dom || item.text_es || '',
    type: 'question',
    category: category
  }))
);

// For challenges
questionsPool.push(
  ...content.dares[mappedChallengeCategory].map((item: any) => ({
    ...item,
    // Add default text_dom if it doesn't exist in the item
    text_dom: item.text_dom || item.text_es || '',
    type: 'challenge',
    category: mappedChallengeCategory
  }))
);
```

This ensures that even if the `text_dom` property is missing in some content items, the app will fall back to using the Spanish text (`text_es`) or an empty string as a last resort.

### 4. Removed Unused Code

Some unused gradient-related code was removed to clean up warnings:

```typescript
// Note: Gradient determination logic is currently not being used
// but kept for future reference
```

## Testing

After implementing these changes, the TypeScript errors were resolved, and the app can now properly handle content with or without the `text_dom` property.

## Best Practices

1. **Always Include Required Properties**: When defining state types, ensure all required properties from interfaces are included.
2. **Implement Fallbacks**: When adding new language properties, implement fallback mechanisms to handle missing translations.
3. **Clean Up Unused Code**: Remove or comment out unused code to avoid confusion and improve maintainability.
4. **Document Changes**: Document any significant changes to the codebase, especially those related to data models.

## Related Files

- `screens/TouchGameScreen.tsx`: The main file that was modified
- `components/game/GameContext.tsx`: Contains the Question and Challenge interfaces with the `text_dom` property
- `docs/mobile/localization.md`: Documentation on the app's localization system
- `docs/mobile/game-mechanics.md`: Documentation on the game's content structure
