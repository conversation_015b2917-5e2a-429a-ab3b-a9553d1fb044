# Ad-Based Premium Access - API Reference

## Services

### AdMobService

**Location**: `services/adMobService.ts`

#### Methods

##### `isInternetAvailable(): Promise<boolean>`
Checks if internet connection is available.

```typescript
const isOnline = await adMobService.isInternetAvailable();
```

**Returns**: Promise resolving to boolean indicating connectivity status.

##### `loadInterstitialAd(): Promise<InterstitialAd>`
Loads a single interstitial ad.

```typescript
try {
  const ad = await adMobService.loadInterstitialAd();
  // Ad loaded successfully
} catch (error) {
  // Handle loading error
}
```

**Returns**: Promise resolving to InterstitialAd instance.
**Throws**: Error if ad fails to load.

##### `showInterstitialAd(ad: InterstitialAd): Promise<boolean>`
Shows an interstitial ad and returns whether it was successfully watched.

```typescript
const wasWatched = await adMobService.showInterstitialAd(ad);
if (wasWatched) {
  // User watched the ad completely
}
```

**Parameters**:
- `ad`: InterstitialAd instance to show

**Returns**: Promise resolving to boolean indicating if ad was watched.

##### `preloadAds(): Promise<InterstitialAd[]>`
Preloads multiple ads for better user experience.

```typescript
const preloadedAds = await adMobService.preloadAds();
console.log(`Preloaded ${preloadedAds.length} ads`);
```

**Returns**: Promise resolving to array of successfully preloaded ads.

##### `getAd(): Promise<InterstitialAd | null>`
Gets a preloaded ad or loads a new one if none available.

```typescript
const ad = await adMobService.getAd();
if (ad) {
  // Ad is ready to show
} else {
  // No ad available (likely offline)
}
```

**Returns**: Promise resolving to InterstitialAd or null if unavailable.

##### `clearPreloadedAds(): void`
Clears all preloaded ads (useful when app goes to background).

```typescript
adMobService.clearPreloadedAds();
```

### PersistentAdStorageService

**Location**: `services/sessionStorageService.ts`

#### Interfaces

```typescript
interface AdBasedAccessState {
  hasWatchedAds: boolean;
  adsWatchedCount: number;
  questionsAnsweredSinceAds: number;
  lastAdWatchTime: number | null;
  sessionStartTime: number | null;
}

interface FreeContentCounters {
  premiumContentRemaining: number; // Shared counter for all premium categories
  lastResetTime: number | null;
}
```

#### Methods

##### `getAdBasedAccessState(): AdBasedAccessState`
Gets the current ad-based access state.

```typescript
const state = sessionStorageService.getAdBasedAccessState();
console.log('Current state:', state);
```

##### `hasTemporaryPremiumAccess(): boolean`
Checks if user has temporary premium access through ads.

```typescript
const hasAccess = sessionStorageService.hasTemporaryPremiumAccess();
```

##### `incrementAdsWatched(): void`
Increments the count of ads watched.

```typescript
sessionStorageService.incrementAdsWatched();
// Automatically grants access when reaching 5 ads
```

##### `resetAdsWatchedCount(): void`
Resets ads watched count (used when user needs to watch ads again).

```typescript
sessionStorageService.resetAdsWatchedCount();
```

##### `initialize(): Promise<void>`
Initializes the service by loading persisted data from AsyncStorage.

```typescript
await sessionStorageService.initialize();
```

##### `getFreeContentCounters(): FreeContentCounters`
Gets the current free content counters.

```typescript
const counters = sessionStorageService.getFreeContentCounters();
console.log('Premium content remaining:', counters.premiumContentRemaining);
```

##### `decrementFreeContent(category): void`
Decrements the shared free content counter for premium categories. All premium categories share the same counter.

```typescript
sessionStorageService.decrementFreeContent('spicy_questions');
sessionStorageService.decrementFreeContent('no_limits_questions');
// Both calls decrement the same shared counter
```

##### `getRemainingFreeContent(category): number`
Gets the remaining free content count for premium categories. Returns the same shared counter value regardless of category.

```typescript
const spicyRemaining = sessionStorageService.getRemainingFreeContent('spicy_questions');
const noLimitsRemaining = sessionStorageService.getRemainingFreeContent('no_limits_questions');
// spicyRemaining === noLimitsRemaining (both return shared counter)
console.log(`${spicyRemaining} premium content items remaining`);
```

##### `incrementQuestionsAnswered(): void`
Increments the count of questions/dares answered since last ad session.

```typescript
sessionStorageService.incrementQuestionsAnswered();
```

##### `resetQuestionsAnsweredCount(): void`
Resets questions answered count (used after watching ads again).

```typescript
sessionStorageService.resetQuestionsAnsweredCount();
```

##### `needsToWatchAdsAgain(): boolean`
Checks if user needs to watch ads again (every 6 questions/dares).

```typescript
const needsAds = sessionStorageService.needsToWatchAdsAgain();
```

##### `completeAdWatchingCycle(): void`
Completes ad watching cycle (reset questions count, keep access).

```typescript
sessionStorageService.completeAdWatchingCycle();
```

##### `resetAllSessionData(): void`
Resets all session data (called when app starts or user loses access).

```typescript
sessionStorageService.resetAllSessionData();
```

##### `getDebugInfo(): string`
Gets debug information about current state.

```typescript
console.log(sessionStorageService.getDebugInfo());
```

## Context

### AdBasedAccessContext

**Location**: `context/AdBasedAccessContext.tsx`

#### Hook: `useAdBasedAccess()`

```typescript
const {
  // State
  adBasedAccessState,
  hasTemporaryPremiumAccess,
  needsToWatchAdsAgain,
  isInternetAvailable,

  // Actions
  incrementAdsWatched,
  resetAdsWatchedCount,
  incrementQuestionsAnswered,
  resetQuestionsAnsweredCount,
  completeAdWatchingCycle,
  checkInternetConnectivity,

  // Debug
  getDebugInfo,
} = useAdBasedAccess();
```

#### Properties

##### State Properties

**`adBasedAccessState: AdBasedAccessState`**
Current ad-based access state object.

**`hasTemporaryPremiumAccess: boolean`**
Whether user currently has temporary premium access.

**`needsToWatchAdsAgain: boolean`**
Whether user needs to watch ads again (every 6 questions).

**`isInternetAvailable: boolean`**
Current internet connectivity status.

##### Action Methods

**`incrementAdsWatched(): void`**
Increments ads watched count and updates state.

**`resetAdsWatchedCount(): void`**
Resets ads watched count and updates state.

**`incrementQuestionsAnswered(): void`**
Increments questions answered count and updates state.

**`resetQuestionsAnsweredCount(): void`**
Resets questions answered count and updates state.

**`completeAdWatchingCycle(): void`**
Completes ad watching cycle and updates state.

**`checkInternetConnectivity(): Promise<void>`**
Checks and updates internet connectivity status.

**`getDebugInfo(): string`**
Returns debug information string.

## Components

### UnlockDialog

**Location**: `components/ads/UnlockDialog.tsx`

#### Props

```typescript
interface Props {
  isSequential?: boolean;
  onClose?: () => void;
  isInsideModal?: boolean;
}
```

**`isSequential`**: Whether this is a sequential ad requirement (during gameplay).
**`onClose`**: Callback function when dialog is closed.
**`isInsideModal`**: Whether the dialog is rendered inside a React Native Modal (fixes iOS navigation issues).

#### Usage

```tsx
// Normal usage (not inside a modal)
<UnlockDialog
  isSequential={false}
  onClose={() => setShowUnlockDialog(false)}
/>

// When rendered inside a Modal component
<UnlockDialog
  isSequential={false}
  isInsideModal={true}
  onClose={() => setShowUnlockDialog(false)}
/>
```

### AdsCounter

**Location**: `components/ads/AdsCounter.tsx`

#### Props

```typescript
interface AdsCounterProps {
  watchedCount: number;
  totalCount: number;
  style?: any;
}
```

**`watchedCount`**: Number of ads watched.
**`totalCount`**: Total number of ads required.
**`style`**: Optional style overrides.

#### Usage

```tsx
<AdsCounter
  watchedCount={2}
  totalCount={5}
  style={{ marginBottom: 20 }}
/>
```

## Navigation

### WatchAdsScreen

**Location**: `screens/WatchAdsScreen.tsx`

#### Navigation

```typescript
// Navigate to WatchAdsScreen
navigation.navigate('WatchAdsScreen' as never);

// The screen handles its own navigation back
// after all ads are completed
```

## Configuration

### Environment Variables

```env
ADMOB_REWARDED_AD_UNIT_ID_ANDROID=ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX
ADMOB_REWARDED_AD_UNIT_ID_IOS=ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX
```

### App Config

```javascript
// app.config.js
export default {
  expo: {
    extra: {
      admobRewardedAdUnitIdAndroid: process.env.ADMOB_REWARDED_AD_UNIT_ID_ANDROID,
      admobRewardedAdUnitIdIos: process.env.ADMOB_REWARDED_AD_UNIT_ID_IOS,
    }
  }
}
```

## Constants

### Ad Requirements

```typescript
// These values are now configurable through admin settings
// Default values:
const AD_REQUIREMENT_COUNT = 1; // Number of ads to watch for access (configurable 1-10)
const QUESTIONS_BEFORE_AD_REQUIREMENT = 6; // Questions before requiring ads again (configurable 1-20)
```

### Test Ad Unit IDs

```typescript
// Development test IDs (automatically used in __DEV__ mode)
const TEST_AD_UNIT_ID = TestIds.INTERSTITIAL;
```

## Error Types

### AdMob Errors

```typescript
// Ad loading errors
interface AdLoadError {
  message: string;
  code?: number;
}

// Ad showing errors
interface AdShowError {
  message: string;
  code?: number;
}
```

### Connectivity Errors

```typescript
// Network connectivity errors
interface ConnectivityError {
  message: string;
  isConnected: boolean;
}
```

## Events

### Ad Events

```typescript
// Ad event types
type AdEventType =
  | 'ad_requested'
  | 'ad_loaded'
  | 'ad_shown'
  | 'ad_completed'
  | 'ad_failed'
  | 'ad_closed';
```

### State Change Events

```typescript
// State change event types
type StateChangeEvent =
  | 'ads_watched_incremented'
  | 'questions_answered_incremented'
  | 'access_granted'
  | 'access_reset'
  | 'connectivity_changed';
```

## Utility Functions

### Premium Access Check

```typescript
// Helper function to check any premium access
const hasAnyPremiumAccess = (
  isPremiumUnlocked: boolean,
  hasTemporaryPremiumAccess: boolean
): boolean => {
  return isPremiumUnlocked || hasTemporaryPremiumAccess;
};
```

### Category Check

```typescript
// Helper function to check if category is premium
const isPremiumCategory = (category: string): boolean => {
  return category === 'spicy_questions' ||
         category === 'no_limits_questions' ||
         category === 'spicy_dares' ||
         category === 'no_limits_dares';
};
```

## Type Definitions

```typescript
// Main types used throughout the system
export interface AdBasedAccessState {
  hasWatchedAds: boolean;
  adsWatchedCount: number;
  questionsAnsweredSinceAds: number;
  lastAdWatchTime: number | null;
}

export interface AdMobServiceInterface {
  isInternetAvailable(): Promise<boolean>;
  loadInterstitialAd(): Promise<InterstitialAd>;
  showInterstitialAd(ad: InterstitialAd): Promise<boolean>;
  preloadAds(): Promise<InterstitialAd[]>;
}

export interface AdBasedAccessContextProps {
  adBasedAccessState: AdBasedAccessState;
  hasTemporaryPremiumAccess: boolean;
  needsToWatchAdsAgain: boolean;
  isInternetAvailable: boolean;
  incrementAdsWatched: () => void;
  resetAdsWatchedCount: () => void;
  incrementQuestionsAnswered: () => void;
  resetQuestionsAnsweredCount: () => void;
  completeAdWatchingCycle: () => void;
  checkInternetConnectivity: () => Promise<void>;
  getDebugInfo: () => string;
}
```
