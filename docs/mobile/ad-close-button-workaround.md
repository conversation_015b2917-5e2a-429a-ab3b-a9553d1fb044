# Ad Close Button Workaround System

## Overview

This document explains the dual-timeout system implemented in `adMobService.ts` to handle iOS (and Android) rewarded ad close button failures.

## The Problem

### Issue Description

iOS rewarded ads sometimes have unresponsive close buttons, causing two critical problems:

1. **Ad Modal Stuck**: The ad modal stays open indefinitely, preventing user interaction
2. **Promise Never Resolves**: The `AdEventType.CLOSED` event never fires, causing the Promise to hang forever

### Impact

- **User Experience**: Users can't continue playing, app appears frozen
- **App Stability**: JavaScript execution hangs waiting for Promise resolution
- **Revenue Loss**: Users don't get credit for watching ads

## The Solution: Dual-Timeout System

### Architecture

We implement a two-tier timeout system that addresses both problems independently:

```
Ad Shows → Tier 1 (2s) → Tier 2 (15s) → Promise Resolves
           ↓             ↓
           Mark Watched  Force Resolve
```

### Tier 1: Ad Marking Timeout (2 seconds)

**Purpose**: Ensure user gets credit even if close button fails

```typescript
setTimeout(() => {
  console.log('AdMob: Auto-marking ad as watched after 2 seconds');
  adWatched = true;
}, 2000);
```

**Why 2 seconds?**
- Long enough for legitimate ad viewing
- Short enough to help stuck ads quickly
- Based on typical rewarded ad viewing patterns

**Platform Coverage**: Originally iOS-only, now applied to both platforms for consistency

### Tier 2: Promise Resolution Timeout (15 seconds)

**Purpose**: Prevent app from hanging indefinitely

```typescript
setTimeout(() => {
  console.log(`AdMob: Force resolving after ${AD_TIMEOUT_MS}ms timeout`);
  forceResolve(adWatched); // Uses value set by Tier 1
}, 15000);
```

**Why 15 seconds?**
- Allows time for normal ad completion
- Reasonable maximum wait time for users
- Balances UX with technical reliability

## Why Both Tiers Are Essential

### Without Tier 1 (Ad Marking):
```typescript
let adWatched = false; // Starts false
// ... 15 seconds later
forceResolve(adWatched); // Resolves with false
// Result: User gets no credit
```

### Without Tier 2 (Promise Resolution):
```typescript
// Promise waits forever for CLOSED event
// Result: App hangs indefinitely
```

### With Both Tiers:
```typescript
let adWatched = false;
// After 2 seconds: adWatched = true
// After 15 seconds: forceResolve(true)
// Result: User gets credit AND app doesn't hang
```

## Implementation Details

### Event Flow

1. **Ad Starts**: `ad.show()` called
2. **Event Listeners**: Set up for CLOSED, ERROR, EARNED_REWARD
3. **Tier 1 Timer**: Starts 2-second countdown
4. **Tier 2 Timer**: Starts 15-second countdown
5. **Normal Case**: CLOSED event fires, timers cleared
6. **Stuck Case**: Timers fire in sequence, Promise resolves

### Code Structure

```typescript
async showInterstitialAd(ad: RewardedAd): Promise<boolean> {
  return new Promise((resolve) => {
    let adWatched = false;
    let resolved = false;

    // Event listeners...
    
    // Tier 1: Mark as watched
    setTimeout(() => adWatched = true, 2000);
    
    // Tier 2: Force resolve
    setTimeout(() => forceResolve(adWatched), 15000);
    
    ad.show();
  });
}
```

### Cleanup Logic

```typescript
const forceResolve = (result: boolean) => {
  if (!resolved) {
    resolved = true;
    cleanup(); // Remove all event listeners
    resolve(result);
  }
};
```

## Platform Considerations

### iOS
- **Primary Target**: Known close button issues
- **Test Ads Affected**: Even Google's test ads have problems
- **Safe Area Issues**: Close button can be covered by status bar

### Android
- **Defensive Programming**: Applied same logic for consistency
- **Potential Issues**: May have similar problems in some cases
- **Unified Behavior**: Same experience across platforms

## User Experience Enhancements

### WatchAdsScreen Integration

The `WatchAdsScreen` component includes additional UX improvements:

- **20-second detection**: Shows user guidance modal
- **Manual instructions**: Tells user how to close stuck ads
- **Continue option**: Allows manual progression

### Modal Guidance

When ads are stuck for 20+ seconds:
```
"The ad seems to be stuck. Try closing it by:
• Look for an X or close button
• Try tapping the top corners
• If nothing works, force close the app and restart

Would you like to continue anyway?"
```

## Monitoring and Debugging

### Console Logs

```
AdMob: Auto-marking ad as watched after 2 seconds (close button workaround)
AdMob: Force resolving after 15000ms timeout - ad may be stuck
AdMob: Resolving with adWatched = true
```

### Key Metrics to Track

- Frequency of Tier 1 timeouts (close button failures)
- Frequency of Tier 2 timeouts (complete hangs)
- User progression through stuck ad modals
- Platform-specific failure rates

## Configuration

### Timeout Values

```typescript
class AdMobService {
  private readonly AD_TIMEOUT_MS = 15000; // Tier 2: Promise resolution
  // Tier 1 is hardcoded to 2000ms in the setTimeout
}
```

### Customization Options

- **Tier 1 Duration**: Adjust 2-second marking timeout
- **Tier 2 Duration**: Adjust 15-second resolution timeout
- **Platform Targeting**: Re-enable iOS-only if needed

## Testing

### Test Scenarios

1. **Normal Ads**: Verify timeouts don't interfere
2. **Stuck Ads**: Confirm both tiers fire correctly
3. **Platform Differences**: Test iOS vs Android behavior
4. **Edge Cases**: App backgrounding during ads

### Verification Steps

1. Watch console logs during ad playback
2. Confirm user gets credit for stuck ads
3. Verify app doesn't hang on problematic ads
4. Test modal guidance system

## Future Considerations

### Potential Improvements

- **Dynamic Timeouts**: Adjust based on ad performance
- **Analytics Integration**: Track timeout frequency
- **A/B Testing**: Optimize timeout durations
- **Native Solutions**: Explore SDK-level fixes

### Alternative Approaches

- **Different Ad Networks**: Reduce dependency on Google Ads
- **Ad Format Changes**: Use different ad types
- **Native Modules**: Custom close button implementation

## Related Files

- `services/adMobService.ts` - Main implementation
- `screens/WatchAdsScreen.tsx` - User guidance system
- `docs/mobile/ad-based-premium-access.md` - Overall ad system
- `docs/mobile/ad-based-premium-troubleshooting.md` - Debugging guide
