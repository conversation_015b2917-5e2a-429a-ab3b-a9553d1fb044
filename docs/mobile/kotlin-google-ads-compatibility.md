# Kotlin and Google Mobile Ads Compatibility Issue

## Overview

This document explains a critical compatibility issue between Kotlin versions and the `react-native-google-mobile-ads` library that affects Android builds in the TapTrap project.

## The Problem

### Issue Description

When using `react-native-google-mobile-ads` version 15.0.0 or higher, the Android build fails with Kotlin compatibility errors:

```
Could not find org.jetbrains.kotlin:kotlin-compose-compiler-plugin-embeddable:1.9.25.
```

Or:

```
Module was compiled with an incompatible version of Kotlin. The binary version of its metadata is 2.1.0, expected version is 1.9.0.
```

### Root Cause

- **react-native-google-mobile-ads version 15.0.0+** requires **Kotlin 2.0.0** as a breaking change
- The Expo SDK and expo-modules-core are currently hardcoded to use **Kotlin 1.9.25**
- This creates a version conflict where different parts of the build system expect different Kotlin versions

## The Solution

### Downgrade Google Mobile Ads Library

The recommended solution is to downgrade `react-native-google-mobile-ads` to version **14.7.0**, which is compatible with Kotlin 1.9.25.

#### Step 1: Update package.json

```bash
npm install react-native-google-mobile-ads@14.7.0
```

#### Step 2: Configure Kotlin Version

Update your Android configuration files:

**android/gradle.properties:**
```properties
# Kotlin and Compose Compiler versions
android.kotlinVersion=1.9.25
android.composeCompilerVersion=1.5.15
```

**android/build.gradle:**
```gradle
buildscript {
    ext {
        buildToolsVersion = findProperty('android.buildToolsVersion') ?: '35.0.0'
        minSdkVersion = Integer.parseInt(findProperty('android.minSdkVersion') ?: '24')
        compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '35')
        targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '34')
        kotlinVersion = findProperty('android.kotlinVersion') ?: '1.9.25'
        composeCompilerVersion = findProperty('android.composeCompilerVersion') ?: '1.5.15'
        
        ndkVersion = "26.1.10909125"
    }
}
```

**android/app/build.gradle:**
```gradle
android {
    // ... other configuration
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = '17'
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion rootProject.ext.composeCompilerVersion
    }
}
```

#### Step 3: Clean and Rebuild

```bash
# Clean Gradle cache
rm -rf ~/.gradle/caches

# Clean local build directories
cd android
rm -rf .gradle build app/build
./gradlew clean

# Clean and reinstall node modules
cd ..
rm -rf node_modules
npm install

# Build the app
npx expo run:android --device
```

## Version Compatibility Matrix

| react-native-google-mobile-ads | Required Kotlin Version | Expo SDK Compatibility |
|--------------------------------|------------------------|------------------------|
| 14.7.0 and below             | 1.9.25                 | ✅ Compatible          |
| 15.0.0 and above             | 2.0.0                  | ❌ Incompatible        |

## Alternative Solutions (Not Recommended)

### Option 1: Upgrade Kotlin to 2.0.0

While technically possible, this approach is **not recommended** because:
- Expo modules are hardcoded to use Kotlin 1.9.25
- May cause compatibility issues with other dependencies
- Requires extensive testing across all Expo modules

### Option 2: Wait for Expo SDK Update

Wait for a future Expo SDK version that supports Kotlin 2.0.0. Monitor:
- [Expo SDK releases](https://github.com/expo/expo/releases)
- [React Native Google Mobile Ads releases](https://github.com/invertase/react-native-google-mobile-ads/releases)

## Troubleshooting

### Common Error Messages

1. **"Could not find org.jetbrains.kotlin:kotlin-compose-compiler-plugin-embeddable"**
   - Solution: Ensure Kotlin version is set to 1.9.25 in gradle.properties

2. **"Module was compiled with an incompatible version of Kotlin"**
   - Solution: Downgrade react-native-google-mobile-ads to 14.7.0

3. **"For input string: 'findProperty('android'"**
   - Solution: Use direct property assignment in gradle.properties, not Gradle functions

### Verification Steps

After applying the fix, verify the configuration:

1. Check that Google Mobile Ads version is 14.7.0:
   ```bash
   npm list react-native-google-mobile-ads
   ```

2. Verify Kotlin version in build logs:
   ```
   :react-native-google-mobile-ads:version set from package.json: 14.7.0
   ```

3. Ensure clean build completes without Kotlin errors

## References

- [React Native Google Mobile Ads v15.0.0 Release Notes](https://github.com/invertase/react-native-google-mobile-ads/releases/tag/v15.0.0)
- [Kotlin Compatibility Guide](https://kotlinlang.org/docs/compatibility-guide.html)
- [Expo Android Build Configuration](https://docs.expo.dev/build-reference/android-builds/)

## Related Issues

- GitHub Issue: [Kotlin 2.0 requirement breaking change](https://github.com/invertase/react-native-google-mobile-ads/issues/xyz)
- Stack Overflow: [Kotlin version compatibility with React Native](https://stackoverflow.com/questions/75128041/this-version-compose-compiler-requires-kotlin-version-1-7-20-but-you-appear-to-b)
