# Best Practices for Using `withTiming` in Reanimated 2/3
## How to Avoid Common Bugs in Android

When using React Native Reanimated, especially on Android, **incorrect usage of `withTiming` inside `useAnimatedStyle` can lead to animation glitches**:

- Animations not visible even though the element is tapable.
- Animations stuck at `opacity: 0` or `scale: 0`.
- Elements flickering or not appearing on first render.

These issues happen **more frequently on Android**, due to subtle differences in the rendering pipeline compared to iOS.

---

## Golden Rule

👉 **Always animate your `SharedValue` with `withTiming` OUTSIDE of `useAnimatedStyle`.**  
👉 Inside `useAnimatedStyle`, only READ the `.value` of your `SharedValue`.  
👉 Do NOT call `withTiming` inside the `useAnimatedStyle` function.

---

## Why?

- `withTiming` returns an `AnimatedNode`, which must be connected to the animation tree at the right time.
- If you call `withTiming` INSIDE `useAnimatedStyle`, it will be re-created on every frame → this breaks the expected behavior of the animation.
- Android is more sensitive to this due to how the view tree is re-evaluated when SharedValues are updated.

---

## Bad Example ❌ (Do not do this)

```tsx
const animatedStyle = useAnimatedStyle(() => {
  return {
    transform: [
      { scale: withTiming(isVisible ? 1 : 0, { duration: 300 }) } // BAD: causes bugs
    ],
    opacity: withTiming(isVisible ? 1 : 0, { duration: 300 }), // BAD: causes bugs
  };
});


⸻

Correct Example ✅ (Best Practice)

const scale = useSharedValue(0);
const opacity = useSharedValue(0);

// Trigger animation externally:
useEffect(() => {
  if (isVisible) {
    scale.value = withTiming(1, { duration: 300 });
    opacity.value = withTiming(1, { duration: 300 });
  } else {
    scale.value = withTiming(0, { duration: 300 });
    opacity.value = withTiming(0, { duration: 300 });
  }
}, [isVisible]);

// Then use .value safely in your AnimatedStyle:
const animatedStyle = useAnimatedStyle(() => {
  return {
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  };
});


⸻

Special Case: Transform Arrays

👉 Do NOT do this:

transform: [
  { translateY: withTiming(condition ? 50 : 0, { duration: 300 }) } // ❌ BAD
]

👉 Instead, compute a value based on an already-animated SharedValue:

transform: [
  { translateY: scale.value === 0 ? 50 : 0 } // ✅ Correct
]

Or even better, animate a translateYValue SharedValue:

const translateYValue = useSharedValue(50);

useEffect(() => {
  translateYValue.value = withTiming(isVisible ? 0 : 50, { duration: 300 });
}, [isVisible]);

const animatedStyle = useAnimatedStyle(() => {
  return {
    transform: [
      { translateY: translateYValue.value }
    ],
  };
});


⸻

When to use pointerEvents + opacity

If you are showing/hiding components based on visibility, do this:

<Animated.View
  style={[
    myAnimatedStyle,
    {
      opacity: isVisible ? opacity.value : 0,
      pointerEvents: isVisible ? 'auto' : 'none',
    },
  ]}
>
  {/* Content */}
</Animated.View>

👉 Why?
	•	Keeps the Animated.View always in the tree → prevents glitches when animating scale/opacity.
	•	Avoids Android bugs where the view doesn’t appear if it’s conditionally mounted with {isVisible && ( ... )}.

⸻

Final Checklist ✅

Do	Don’t
Animate .value of SharedValues using withTiming OUTSIDE of useAnimatedStyle	Do not call withTiming INSIDE useAnimatedStyle
Read .value safely in useAnimatedStyle	Do not mix withTiming with transform arrays inside style
Use opacity + pointerEvents to control visibility	Do not conditionally mount Animated.View in your JSX
Keep Animated.View always in the tree if it needs animations	Do not rely on {isVisible && (Animated.View)} for animated components


⸻

Summary

✅ Always trigger animations externally → inside useEffect or on events.
✅ Use SharedValues → .value → cleanly read them inside useAnimatedStyle.
✅ Never put withTiming(...) inside transform or opacity in the AnimatedStyle.
✅ Use opacity + pointerEvents for visibility instead of mounting/unmounting the Animated.View.

⸻

Reusable pattern for dialogs / overlays

<Animated.View
  style={[
    styles.overlay,
    {
      opacity: isVisible ? opacity.value : 0,
      pointerEvents: isVisible ? 'auto' : 'none',
    },
  ]}
>
  <Animated.View
    style={[
      styles.dialog,
      {
        transform: [{ scale: scale.value }],
      },
    ]}
  >
    {/* Dialog content */}
  </Animated.View>
</Animated.View>

→ This is the pattern you can safely reuse for PenaltyDialog, UnlockDialog, PenaltyWalkthrough, etc.