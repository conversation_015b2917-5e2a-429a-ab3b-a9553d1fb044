# Settings Page Notification Toggle Implementation

## Overview

The Settings page notification toggle provides users with granular control over push notifications, allowing them to enable or disable notifications independently of system permissions. This feature integrates seamlessly with the existing push notification system and backend infrastructure.

## Features

### User Interface
- **Toggle Switch**: Visual on/off control in Settings → Notifications
- **Status Display**: Shows "On" or "Off" based on current preference
- **Gradient Styling**: Active toggle displays gradient background
- **Permission Integration**: Automatically handles permission states

### Functionality
- **Permission Checking**: Validates system notification permissions before enabling
- **Settings Navigation**: Directs users to device settings when permissions are denied
- **Backend Synchronization**: Syncs user preferences to backend for filtering
- **Local Storage**: Persists preferences locally for immediate UI updates

## Implementation Details

### Components Involved

#### 1. Settings Screen (`screens/SettingsScreen.tsx`)
- **Notification Toggle**: Switch component with gradient styling
- **Permission Handling**: Checks and responds to notification permission states
- **Settings Navigation**: Shows alerts directing users to device settings

#### 2. App Settings Context (`context/AppSettingsContext.tsx`)
- **State Management**: Manages notification preference state
- **Persistence**: Handles local storage and backend synchronization
- **Integration**: Connects with push notification service

#### 3. Push Notification Service (`services/pushNotificationService.ts`)
- **Preference Management**: Functions for getting/setting notification preferences
- **Backend Communication**: API calls to update preferences on server
- **Permission Utilities**: Helper functions for checking notification status

### Backend Integration

#### Database Schema
```javascript
// PushToken model includes:
{
  token: String,
  platform: String,
  deviceId: String,
  language: String,
  notificationEnabled: Boolean, // New field for user preference
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### API Endpoints
- **PUT /api/push-tokens/preference**: Update notification preference by device ID
- **POST /api/push-tokens**: Include notification preference in token registration
- **Notification Sending**: Automatically filters to only enabled devices

### Toggle Behavior

#### Enabling Notifications
1. User taps toggle to "On" position
2. System checks current notification permissions
3. **If permissions granted**: 
   - Updates local preference to enabled
   - Syncs preference to backend
   - Toggle remains in "On" position
4. **If permissions denied**:
   - Shows alert with localized message
   - Provides "Open Settings" button
   - Toggle automatically returns to "Off" position

#### Disabling Notifications
1. User taps toggle to "Off" position
2. Updates local preference to disabled
3. Syncs preference to backend
4. Toggle remains in "Off" position

### Permission Handling

#### Settings Navigation Alert
When permissions are denied, the system shows a localized alert with:
- **Title**: "Notifications Disabled"
- **Message**: Instructions to enable notifications in device settings
- **Actions**: "Open Settings" button and "Cancel" option
- **Platform-specific navigation**: iOS uses `Linking.openSettings()`, Android uses `IntentLauncher`

#### Permission States
- **Granted**: Toggle works normally, preferences sync to backend
- **Denied**: Toggle shows settings navigation alert
- **Not Determined**: Toggle requests permissions first

## Code Examples

### Toggle Handler
```jsx
const handleNotificationToggle = async (enabled: boolean) => {
  try {
    if (enabled) {
      // Check if permissions are granted
      const { status } = await Notifications.getPermissionsAsync();
      
      if (status === 'granted') {
        // Permissions are granted, enable notifications
        await setNotificationPreference(true);
      } else {
        // Permissions denied, show settings alert
        await pushNotificationService.showNotificationSettingsAlert({
          title: t('pushNotificationDisabledTitle'),
          message: t('pushNotificationDisabledMessage'),
          openSettings: t('pushNotificationOpenSettings'),
          cancel: t('pushNotificationCancel'),
          error: t('pushNotificationSettingsError')
        });
      }
    } else {
      // When turning off, simply disable notifications
      await setNotificationPreference(false);
    }
  } catch (error) {
    console.error('Error handling notification toggle:', error);
  }
};
```

### Switch Component
```jsx
<LinearGradient 
  style={styles.switchContainer} 
  colors={notificationPreference ? ['#42E0FF', '#42FFB7'] : ['transparent', 'transparent']}
>
  <Switch
    value={notificationPreference}
    onValueChange={handleNotificationToggle}
    disabled={false}
    circleSize={28}
    barHeight={38}
    circleBorderWidth={0}
    backgroundActive={'transparent'}
    backgroundInactive={'#2E2F31'}
    circleActiveColor={'#FFF'}
    circleInActiveColor={'#FFF'}
    changeValueImmediately={true}
    renderActiveText={false}
    renderInActiveText={false}
    switchLeftPx={3}
    switchRightPx={3}
    switchWidthMultiplier={2}
    switchBorderRadius={30}
  />
</LinearGradient>
```

### Settings Data Integration
```jsx
const settingsData = [
  // ... other settings
  {
    key: 'notifications',
    label: t('settingNotifications'),
    value: notificationPreference ? t('notificationsOn') : t('notificationsOff'),
    icon: NotificationsIcon,
  },
];
```

## State Management

### Local Storage
- **Key**: `taptrap_notification_preference`
- **Value**: `'true'` or `'false'` string
- **Persistence**: Maintained across app sessions

### Backend Synchronization
- **Endpoint**: `PUT /api/push-tokens/preference`
- **Payload**: `{ deviceId, notificationEnabled }`
- **Filtering**: Backend only sends notifications to devices with `notificationEnabled: true`

### Context Integration
```jsx
// AppSettingsContext provides:
const {
  notificationPreference,      // Current preference state
  setNotificationPreference,   // Function to update preference
} = useAppSettings();
```

## User Experience

### Visual Feedback
- **Status Text**: "On" or "Off" displayed in settings list
- **Gradient Background**: Active toggle shows blue-green gradient
- **Immediate Updates**: UI reflects changes instantly

### Error Handling
- **Permission Denied**: Clear alert with actionable steps
- **Network Errors**: Graceful degradation, local state maintained
- **Settings Navigation**: Platform-specific deep linking to notification settings

## Testing

### Manual Testing
1. **Enable Toggle**: Verify permissions are checked and preference is synced
2. **Disable Toggle**: Confirm preference is updated and synced to backend
3. **Permission Denied**: Test alert display and settings navigation
4. **Backend Filtering**: Verify notifications only reach enabled devices

### Edge Cases
- **No Internet**: Local preference should still update
- **Backend Error**: User should see current state, retry on next sync
- **Permission Changes**: Toggle should reflect actual permission status

## Related Documentation

- [Push Notifications](./push-notifications.md) - Complete push notification system
- [Settings Screen](./screens.md#settings-screen) - Full settings page documentation
- [App Settings Context](./state-management.md) - State management details
