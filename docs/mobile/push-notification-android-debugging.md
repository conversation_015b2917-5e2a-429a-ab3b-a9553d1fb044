# Push Notification Banner Android Debugging Guide

## Issue
The push notification banner doesn't appear on Android devices, while it works correctly on iOS.

## Root Cause Identified

**Android notifications are enabled by default**, unlike iOS which requires explicit user permission. This means:
- `Notifications.getPermissionsAsync()` returns `'granted'` immediately on Android
- The banner logic skips showing the banner because permissions are already "granted"
- **No push token is ever generated or uploaded** because the user never interacts with the banner

## Recent Changes Made

### 1. Added Android Permissions
Updated `app.config.js` to include required Android notification permissions:
```javascript
permissions: [
  "BILLING",
  "android.permission.POST_NOTIFICATIONS",
  "android.permission.RECEIVE_BOOT_COMPLETED",
  "android.permission.VIBRATE"
]
```

### 2. **Removed Firebase Integration (No Longer Needed)**
Firebase integration has been removed as TapTrap now uses Expo's native push notification system which doesn't require Firebase for Android. The push notification service has been simplified to use only `expo-notifications`.

### 3. **Fixed Android Auto-Registration at App Launch**
Added automatic token registration that runs once at app startup:
```javascript
// In App.tsx useEffect
await initializePushNotificationsAtLaunch();
```

The `initializeAtAppLaunch()` method:
- Runs once when the app starts
- Initializes Firebase for Android (required for push tokens)
- Checks if token already exists (avoids duplicates)
- For Android with granted permissions, automatically gets and uploads token
- For iOS, waits for user interaction with banner
- Only runs on physical devices
- Comprehensive logging for debugging

### 4. Enhanced Debugging
Added comprehensive logging to track banner display logic:
- `shouldShowBanner()` now logs each condition check
- Added `debugBannerState()` method to inspect current state
- Added `clearAllCachedStates()` for testing

### 5. Debug Helper Functions
Added exportable functions for console debugging:
```javascript
import { debugPushNotificationBanner, clearPushNotificationCache, testBannerConditions } from '@/services/pushNotificationService';

// Debug current state
await debugPushNotificationBanner();

// Clear cache and test again
await clearPushNotificationCache();

// Test all conditions
await testBannerConditions();
```

## Debugging Steps

### Step 1: Check Console Logs
Look for logs with `🔔 [PushBanner]` prefix when starting the first question/dare:
- Platform detection
- Device.isDevice status
- Permission states
- Banner display decision

### Step 2: Manual Testing
In the app console or dev tools, run:
```javascript
// Check current state
await debugPushNotificationBanner();

// Clear all cached states
await clearPushNotificationCache();

// Test conditions
await testBannerConditions();
```

### Step 3: Common Android Issues

#### Device Detection
- `Device.isDevice` might return false on Android emulators
- Test on physical Android device

#### Permission Status
- Android 13+ requires explicit POST_NOTIFICATIONS permission
- Check if permission status is correctly detected

#### AsyncStorage Issues
- Previous permission denials might be cached
- Use `clearPushNotificationCache()` to reset

#### Platform-Specific Behavior
- Android notification channels must be set up correctly
- Check if notification channel creation is working

## Expected Behavior

### **Android (Permissions Granted by Default)**
1. **At App Launch**: Auto-registration runs and uploads token to backend
2. **During Gameplay**: Banner never shows (permissions already granted)
3. **Console Logs**: Look for `🔔 [AppLaunch]` and `🔔 [AutoReg]` messages

### **iOS (Explicit Permission Required)**
1. **At App Launch**: No auto-registration (waits for user interaction)
2. **During Gameplay**: Banner shows on first question/dare
3. **User Interaction**: Tapping banner requests permission and uploads token

### **Banner Display Conditions (iOS only)**
The banner should appear on the first question/dare if:
1. Running on physical device (`Device.isDevice === true`)
2. Permission not previously denied
3. Banner not dismissed in last 8 hours
4. Notification permission not already granted
5. No other banners currently showing

### **Expected Console Logs**
**Android App Launch:**
```
🔔 [AppLaunch] Initializing push notifications...
🔔 [AppLaunch] Platform: android
🔔 [AppLaunch] Current permission status: granted
🔔 [AppLaunch] Android with granted permissions - registering token
🔔 [AutoReg] Getting push token for auto-registration...
🔔 [AutoReg] Uploading token to backend...
🔔 [AutoReg] ✅ Auto-registration completed successfully
```

**iOS App Launch:**
```
🔔 [AppLaunch] Initializing push notifications...
🔔 [AppLaunch] Platform: ios
🔔 [AppLaunch] Current permission status: undetermined
🔔 [AppLaunch] Will wait for user interaction (iOS or Android without permissions)
```

## Testing Checklist

- [ ] Test on physical Android device (not emulator)
- [ ] Clear app data/cache before testing
- [ ] Check console logs for detailed debugging info
- [ ] Verify Android permissions are properly configured
- [ ] Test with fresh app install
- [ ] Compare behavior with iOS device

## Next Steps

If the issue persists after these changes:
1. Test on multiple Android devices/versions
2. Check if expo-notifications works correctly on Android
3. Verify Android notification channel setup
4. Consider Android-specific permission request timing
