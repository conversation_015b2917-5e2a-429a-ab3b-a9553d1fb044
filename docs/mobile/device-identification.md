# Device Identification System

## Overview

TapTrap uses a unique device identification system to track devices for push notifications and analytics. This system has been updated to use a more reliable approach with `expo-secure-store` and `uuid` instead of the unreliable `Device.osInternalBuildId`.

## Why the Change?

### Problems with Device.osInternalBuildId

The previous implementation used `Device.osInternalBuildId` from expo-device, which had several issues:

- **Not unique per device**: `osInternalBuildId` represents the internal build version of the operating system, not a unique device identifier
- **Collision issues**: Multiple users with the same device model and OS version could have identical `osInternalBuildId` values
- **Data integrity problems**: This caused incorrect data in the backend when trying to identify unique users/devices

### New Solution

The new implementation uses:
- **expo-secure-store**: For secure, persistent storage across app sessions
- **uuid**: For generating truly unique identifiers
- **react-native-get-random-values**: For proper UUID generation in React Native

## Implementation

### Device ID Service

**Location**: `services/deviceIdService.ts`

The service provides three main functions:

#### `getUniqueDeviceId(): Promise<string>`

Generates or retrieves a unique device identifier:

```typescript
import { getUniqueDeviceId } from './deviceIdService';

const deviceId = await getUniqueDeviceId();
console.log('Device ID:', deviceId); // e.g., "550e8400-e29b-41d4-a716-************"
```

**Behavior:**
1. Checks if a unique ID already exists in SecureStore
2. If it exists, returns the stored ID
3. If it doesn't exist, generates a new UUID and stores it
4. Returns the unique ID
5. On error, generates a temporary UUID as fallback

#### `clearDeviceId(): Promise<boolean>`

Clears the stored device ID (useful for testing):

```typescript
import { clearDeviceId } from './deviceIdService';

const success = await clearDeviceId();
if (success) {
  console.log('Device ID cleared successfully');
}
```

#### `hasDeviceId(): Promise<boolean>`

Checks if a device ID exists in storage:

```typescript
import { hasDeviceId } from './deviceIdService';

const exists = await hasDeviceId();
console.log('Device ID exists:', exists);
```

### Storage Details

- **Storage Key**: `taptrap_device_unique_id`
- **Storage Method**: Expo SecureStore (encrypted, persistent)
- **ID Format**: UUID v4 (e.g., `550e8400-e29b-41d4-a716-************`)

## Usage in Push Notifications

The device ID service is integrated into the push notification system:

```typescript
// services/pushNotificationService.ts
import { getUniqueDeviceId } from './deviceIdService';

async function uploadTokenToBackend(token: string): Promise<boolean> {
  const currentLanguage = await getCurrentLanguage();
  const deviceId = await getUniqueDeviceId(); // New implementation

  const response = await axios.post(`${API_URL}/push-tokens`, {
    token,
    platform: Platform.OS,
    deviceId, // Now uses unique UUID instead of osInternalBuildId
    language: currentLanguage,
  });

  return response.status === 200 || response.status === 201;
}
```

## Dependencies

### Required Packages

```json
{
  "expo-secure-store": "~13.0.2",
  "uuid": "^9.0.1",
  "react-native-get-random-values": "^1.9.0"
}
```

### TypeScript Types

```json
{
  "@types/uuid": "^9.0.7"
}
```

## Error Handling

The service includes robust error handling:

1. **SecureStore errors**: Falls back to temporary UUID generation
2. **UUID generation errors**: Logs error and attempts fallback
3. **Storage access errors**: Gracefully degrades functionality

## Security Considerations

- **Encrypted Storage**: Uses Expo SecureStore for encrypted storage
- **No Personal Data**: UUID contains no personal information
- **Local Only**: Device ID is generated and stored locally
- **Persistent**: Survives app uninstalls/reinstalls on the same device

## Testing

### Development Testing

```typescript
import { getUniqueDeviceId, clearDeviceId, hasDeviceId } from './deviceIdService';

// Test ID generation
const id1 = await getUniqueDeviceId();
const id2 = await getUniqueDeviceId();
console.log('IDs should be the same:', id1 === id2); // true

// Test clearing
await clearDeviceId();
const exists = await hasDeviceId();
console.log('Should be false:', exists); // false

// Test regeneration
const newId = await getUniqueDeviceId();
console.log('New ID should be different:', id1 !== newId); // true
```

### Production Considerations

- Device IDs persist across app updates
- Device IDs are unique per app installation
- Uninstalling and reinstalling the app will generate a new device ID

## Migration Notes

### Automatic Migration

The system automatically migrates from the old `Device.osInternalBuildId` approach:

1. Existing users will get a new unique device ID on their next app launch
2. The backend will update their device record with the new ID
3. No user action required

### Backend Compatibility

The backend continues to accept the `deviceId` field in the same format, ensuring backward compatibility during the transition period.

## Troubleshooting

### Common Issues

**Device ID not persisting:**
- Check if SecureStore is properly configured
- Verify app has necessary permissions
- Check device storage availability

**UUID generation errors:**
- Ensure `react-native-get-random-values` is properly installed
- Check if polyfill is imported before UUID usage

**Backend not receiving device ID:**
- Verify network connectivity
- Check API endpoint configuration
- Ensure proper error handling in upload function

### Debug Information

Enable debug logging to troubleshoot issues:

```typescript
// The service automatically logs device ID operations
// Check console for messages like:
// "Generated new unique device ID: ..."
// "Using existing unique device ID: ..."
// "Error getting/generating unique device ID: ..."
```

### Pit-falls
Although SecureStore is slightly more persistent than AsyncStorage (e.g., survives app restarts), it does not survive uninstalls. On iOS and Android, uninstalling the app deletes all secure storage for it.