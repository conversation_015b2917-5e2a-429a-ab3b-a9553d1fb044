# OS-Specific Ad Entry Point Control

## Overview

The ad entry point system has been enhanced to support OS-specific control over the "Watch Ads" button visibility. Administrators can now independently control whether iOS and Android users see the "Watch Ads" button in the UnlockDialog.

## Key Features

- **Separate iOS/Android Toggles**: Independent control for each platform
- **Backward Compatibility**: Legacy `watchAdsButtonEnabled` field maintained for older clients
- **Automatic Fallback**: New fields fall back to legacy field if not present
- **Platform Detection**: Mobile app automatically uses the correct setting based on `Platform.OS`

## Implementation Details

### Backend Changes

#### 1. Database Model (`backend/src/models/adSettings.js`)

**New Fields Added:**
```javascript
{
  // Legacy field (maintained for backward compatibility)
  watchAdsButtonEnabled: Boolean, // Optional, defaults to true
  
  // New OS-specific fields
  watchAdsButtonEnabledIOS: Boolean,     // Required, defaults to true
  watchAdsButtonEnabledAndroid: Boolean, // Required, defaults to true
}
```

#### 2. API Controller (`backend/src/controllers/adminController.js`)

**Enhanced Update Logic:**
- Accepts both legacy and new format requests
- When new format is used: Updates both OS-specific fields + legacy field (for compatibility)
- When legacy format is used: Updates legacy field + both OS-specific fields (same value)
- Maintains full backward compatibility

**Default Settings Creation:**
- Creates both legacy and OS-specific fields with `true` values
- Ensures all existing functionality continues to work

### Frontend Admin Panel Changes

#### 1. Admin Interface (`admin/src/pages/AdSettings.js`)

**New UI Elements:**
- Separate toggle for "Watch Ads Button - iOS"
- Separate toggle for "Watch Ads Button - Android"
- Removed single "Watch Ads Button" toggle

**State Management:**
- Handles both legacy and new field formats
- Provides fallback logic for missing fields
- Sends new format to API (both OS-specific fields)

### Mobile App Changes

#### 1. Service Layer (`services/adSettingsService.ts`)

**Updated Interface:**
```typescript
export interface AdSettings {
  watchAdsButtonEnabled: boolean;        // Legacy field
  watchAdsButtonEnabledIOS: boolean;     // New iOS field
  watchAdsButtonEnabledAndroid: boolean; // New Android field
  requiredAdsCount: number;
  freeContentLimit: number;
}
```

**API Response Handling:**
- Parses new OS-specific fields from API response
- Falls back to legacy field if new fields are missing
- Provides default `true` values if all fields are missing

#### 2. Context Layer (`context/AdSettingsContext.tsx`)

**OS-Specific Logic:**
```typescript
const isWatchAdsButtonEnabled = Platform.OS === 'ios' 
  ? (adSettings?.watchAdsButtonEnabledIOS ?? adSettings?.watchAdsButtonEnabled ?? false)
  : (adSettings?.watchAdsButtonEnabledAndroid ?? adSettings?.watchAdsButtonEnabled ?? false);
```

**Fallback Chain:**
1. Use OS-specific field if available
2. Fall back to legacy field if OS-specific field is missing
3. Default to `false` if both are missing

#### 3. Component Integration (`components/ads/UnlockDialog.tsx`)

**No Changes Required:**
- Component continues to use `isWatchAdsButtonEnabled` from `useAdSettings()`
- Automatically benefits from new OS-specific logic
- Maintains existing functionality and behavior

## Backward Compatibility

### Legacy Client Support

**Old Mobile Apps:**
- Continue to work with `watchAdsButtonEnabled` field
- Receive appropriate value based on admin settings
- No breaking changes or required updates

**Legacy API Requests:**
- Admin panel can still send legacy format
- API automatically applies setting to both platforms
- Maintains existing behavior

### Migration Strategy

**Existing Installations:**
- Database migration not required
- New fields are created with default values on first API call
- Legacy field continues to function normally

**Gradual Rollout:**
- Admin panel immediately shows new toggles
- Mobile apps use OS-specific logic when available
- Seamless transition without service interruption

## Usage Examples

### Admin Panel Usage

1. **Platform-Specific Control:**
   - Enable ads for iOS users only: iOS=ON, Android=OFF
   - Enable ads for Android users only: iOS=OFF, Android=ON
   - Disable ads for all users: iOS=OFF, Android=OFF
   - Enable ads for all users: iOS=ON, Android=ON

2. **Testing Scenarios:**
   - Test iOS ad integration: Enable iOS only
   - Test Android ad integration: Enable Android only
   - A/B testing: Different settings per platform

### API Examples

**New Format Request:**
```json
{
  "watchAdsButtonEnabledIOS": true,
  "watchAdsButtonEnabledAndroid": false,
  "requiredAdsCount": 5,
  "freeContentLimit": 6
}
```

**Legacy Format Request (still supported):**
```json
{
  "watchAdsButtonEnabled": true,
  "requiredAdsCount": 5,
  "freeContentLimit": 6
}
```

## Testing

### Verification Steps

1. **Admin Panel:**
   - Verify separate iOS/Android toggles appear
   - Test saving different combinations
   - Confirm API requests include new fields

2. **Mobile App:**
   - Test iOS app with iOS=ON, Android=OFF
   - Test Android app with iOS=OFF, Android=ON
   - Verify fallback behavior with legacy-only data

3. **Backward Compatibility:**
   - Test with old admin panel (if applicable)
   - Verify legacy API requests still work
   - Confirm existing mobile apps continue functioning

## Benefits

1. **Granular Control:** Platform-specific ad management
2. **Testing Flexibility:** Independent platform testing
3. **Gradual Rollouts:** Platform-specific feature releases
4. **Backward Compatibility:** No breaking changes
5. **Future-Proof:** Extensible for additional platforms
