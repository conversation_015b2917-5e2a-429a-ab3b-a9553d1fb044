# Persistent Ad-Based Premium Access Storage

## Overview

The TapTrap app implements a persistent storage system for ad-based premium access with 15-minute expiration that maintains user state across app restarts and handles stuck ad recovery. This system tracks individual ad viewing progress, premium access status, and free content consumption counters.

## Key Features

- **Persistent Ad Watch Tracking**: Individual ad watches are stored with 15-minute expiration timestamps
- **Stuck Ad Recovery**: Users can force-close stuck ad modals and return to find their progress maintained
- **Automatic Expiration**: Persistent ad states automatically expire after 15 minutes to prevent indefinite access
- **Session Management**: Each app session gets a unique ID for tracking and debugging
- **Free Content Tracking**: Tracks remaining free premium content with shared counter
- **AsyncStorage Integration**: Uses React Native AsyncStorage for reliable persistence
- **Automatic Initialization**: Service initializes automatically with recovery logic when the app starts
- **Cross-Platform Support**: Works on iOS, Android, and gracefully degrades on web

## Storage Keys

The service uses the following AsyncStorage keys:

- `taptrap_ad_based_access_state`: Stores ad viewing progress and premium access state
- `taptrap_free_content_counters`: Stores remaining free content counts per category
- `taptrap_persistent_ad_watches`: Stores individual ad watch states with 15-minute expiration

## Data Structures

### AdBasedAccessState

```typescript
interface AdBasedAccessState {
  hasWatchedAds: boolean;           // Whether user has completed required ads
  adsWatchedCount: number;          // Number of ads watched (0-required count)
  questionsAnsweredSinceAds: number; // Questions answered since last ad cycle
  lastAdWatchTime: number | null;   // Timestamp of last ad watched
  sessionStartTime: number | null;  // When current session started
}
```

### FreeContentCounters

```typescript
interface FreeContentCounters {
  premiumContentRemaining: number; // Shared counter for all premium categories
  lastResetTime: number | null;    // When counter was last reset
}
```

### PersistentAdWatchState

```typescript
interface PersistentAdWatchState {
  adIndex: number;      // Index of the ad that was watched
  watchedAt: number;    // Timestamp when ad was watched
  expiresAt: number;    // Timestamp when this watch expires (15 minutes later)
  sessionId: string;    // Unique session ID for tracking
}
```

**Important**: All premium categories (spicy_questions, no_limits_questions, spicy_dares, no_limits_dares) share the same counter. When one category runs out of free content, all premium categories become locked.

## Implementation Details

### Service Initialization

The service automatically initializes when the AdBasedAccessContext loads with enhanced recovery logic:

```typescript
// In AdBasedAccessContext.tsx
useEffect(() => {
  const initializeService = async () => {
    try {
      await sessionStorageService.initialize();
      // Update state after initialization (includes recovery from persistent watches)
      setAdBasedAccessState(sessionStorageService.getAdBasedAccessState());
      setFreeContentCounters(sessionStorageService.getFreeContentCounters());
      setIsInitialized(true);
    } catch (error) {
      console.error('Error initializing service:', error);
      setIsInitialized(true); // Continue with default values
    }
  };

  initializeService();
}, []);
```

### Initialization Process

The service initialization now includes:

1. **Session ID Generation**: Creates unique session ID for tracking
2. **Data Loading**: Loads all persistent data from AsyncStorage
3. **Expired Cleanup**: Automatically removes expired ad watches (older than 15 minutes)
4. **Progress Recovery**: Restores valid ad watch progress from persistent storage
5. **State Reset**: If no valid ad watches remain, automatically resets premium access state
6. **State Synchronization**: Updates in-memory state with recovered data and saves changes

### Persistent Ad Watch Tracking

Each ad watch is now tracked individually with 15-minute expiration:

```typescript
// When ads are watched
incrementAdsWatched(): void {
  const now = Date.now();

  // Create persistent ad watch entry with 15-minute expiration
  const persistentWatch: PersistentAdWatchState = {
    adIndex: this.adBasedAccessState.adsWatchedCount,
    watchedAt: now,
    expiresAt: now + (15 * 60 * 1000), // 15 minutes
    sessionId: this.currentSessionId,
  };

  // Add to persistent watches and save immediately
  this.persistentAdWatches.push(persistentWatch);
  this.savePersistentAdWatches();

  // Update regular state
  this.adBasedAccessState.adsWatchedCount += 1;
  this.adBasedAccessState.lastAdWatchTime = now;

  // Grant access if user has watched all required ads
  if (this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount) {
    this.adBasedAccessState.hasWatchedAds = true;
    this.resetFreeContentCounters();
  }

  // Save updated state
  this.saveAdBasedAccessState();
}
```

### Free Content Management

The system tracks free content consumption using a shared counter for all premium categories with automatic ad state reset when content is exhausted:

```typescript
// When premium content is consumed
decrementFreeContent(category: 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares'): void {
  // All premium categories share the same counter
  if (this.freeContentCounters.premiumContentRemaining > 0) {
    this.freeContentCounters.premiumContentRemaining--;
  }

  // CRITICAL FIX: Reset ad watch state when free content is exhausted
  if (this.freeContentCounters.premiumContentRemaining === 0) {
    console.log('PersistentAdStorage: Free content exhausted, resetting ad watch state');
    this.adBasedAccessState.hasWatchedAds = false;
    this.adBasedAccessState.adsWatchedCount = 0;
    this.adBasedAccessState.lastAdWatchTime = null;
    this.adBasedAccessState.sessionStartTime = null;
    this.adBasedAccessState.questionsAnsweredSinceAds = 0;

    // Clear persistent ad watches
    this.persistentAdWatches = [];
    this.savePersistentAdWatches();
    this.saveAdBasedAccessState();
  }

  this.saveFreeContentCounters();
  console.log(`Decremented premium content for ${category}, remaining: ${this.freeContentCounters.premiumContentRemaining}`);
}
```

**Key Points**:
- All premium categories decrement the same shared counter
- **When the counter reaches 0, ad watch state is automatically reset**
- **Persistent ad watches are cleared when content is exhausted**
- The counter resets to the configured limit when users gain premium access through ads
- This ensures consistent state between `hasTemporaryPremiumAccess` and remaining content

## Integration Points

### TouchGameScreen

Premium content consumption and access control is handled with improved logic for content exhaustion:

```typescript
// Updated logic for both skip and done button flows
if (isPremiumCategory && !isPremiumUnlocked) {
  // If user has temporary premium access, consume content
  if (hasTemporaryPremiumAccess) {
    // Decrement free content counter for this category
    decrementFreeContent(nextQuestion.category as 'spicy_questions' | 'no_limits_questions' | 'spicy_dares' | 'no_limits_dares' | 'couple_questions' | 'couple_dares');

    // Increment questions answered for ad-based access tracking
    incrementQuestionsAnswered();

    // Check if user needs to watch ads again (every 6 questions/dares)
    if (needsToWatchAdsAgain) {
      // Show UnlockDialog when ad cycle limit reached
      setShowUnlockDialog(true);
    }
  } else {
    // User doesn't have premium access, show unlock dialog immediately
    console.log('TouchGameScreen: User tried to access premium content without access, showing unlock dialog');
    setShowUnlockDialog(true);
    return; // Don't proceed to next question, show unlock dialog instead
  }
}
```

**Key Improvements:**
- **Immediate UnlockDialog**: When `hasTemporaryPremiumAccess` is `false` (due to content exhaustion), UnlockDialog shows immediately
- **Consistent Logic**: Same logic applies to both skip and done button flows
- **No Lost State**: Users can't accidentally consume premium content without proper access

### SelectCategoriesScreen

Free content counts are displayed to users using the shared counter:

```typescript
const { getRemainingFreeContent } = useAdBasedAccess();

// Display remaining count (both categories show the same value)
{hasTemporaryPremiumAccess && (
  <Text style={styles.categoryFreeAmount}>
    {getRemainingFreeContent('spicy_questions')} free
  </Text>
)}

{hasTemporaryPremiumAccess && (
  <Text style={styles.categoryFreeAmount}>
    {getRemainingFreeContent('no_limits_questions')} free
  </Text>
)}
// Both display the same count since they share the counter
```

**Real-time Updates**: The counter updates automatically when users return from TouchGameScreen due to the `useFocusEffect` hook.

## Stuck Ad Recovery System

### The Problem

iOS ad modals sometimes get stuck and don't respond to close button taps, forcing users to force-close the app. Previously, this would cause users to lose their ad watching progress.

### The Solution

The persistent ad watch tracking system solves this by:

1. **Immediate Persistence**: Each ad watch is saved to AsyncStorage immediately when marked as watched (after 2 seconds on iOS)
2. **15-Minute Expiration**: Persistent watches automatically expire after 15 minutes to prevent indefinite access
3. **Automatic Recovery**: When the app restarts, valid persistent watches are recovered and progress is restored
4. **Seamless Integration**: Works with existing 2-second iOS auto-marking and 10-second timeout systems

### Recovery Flow

```typescript
// When app restarts and service initializes
private recoverAdWatchProgress(): void {
  const validWatches = this.persistentAdWatches.filter(watch => watch.expiresAt > Date.now());

  if (validWatches.length > 0) {
    // Restore ads watched count from valid persistent watches
    const recoveredCount = validWatches.length;
    this.adBasedAccessState.adsWatchedCount = Math.max(
      this.adBasedAccessState.adsWatchedCount,
      recoveredCount
    );

    // Grant access if user has watched enough ads
    if (this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount) {
      this.adBasedAccessState.hasWatchedAds = true;
      this.resetFreeContentCounters();
    }
  } else {
    // No valid watches found - reset ad access state
    console.log('PersistentAdStorage: No valid ad watches found, resetting ad access state');
    this.adBasedAccessState.hasWatchedAds = false;
    this.adBasedAccessState.adsWatchedCount = 0;
    this.adBasedAccessState.lastAdWatchTime = null;
    this.adBasedAccessState.sessionStartTime = null;
  }

  // Save the updated state
  this.saveAdBasedAccessState();
}
```

### Platform-Specific Behavior

**iOS**:
- Ads are auto-marked as watched after 2 seconds to handle close button issues
- Persistent tracking ensures progress is maintained even if modal gets stuck
- Users can force-close the app and return to find their progress intact

**Android**:
- More reliable close button behavior but still benefits from persistent tracking
- Consistent experience across platforms

## Premium Access Expiration System

The system handles two types of premium access expiration to ensure consistent state management:

### 1. Content-Based Expiration (Immediate)

When users exhaust their free content allocation, premium access is immediately revoked:

```typescript
// In hasTemporaryPremiumAccess() method
hasTemporaryPremiumAccess(): boolean {
  // User must have watched enough ads AND still have remaining free content
  const hasWatchedEnoughAds = this.adBasedAccessState.hasWatchedAds && this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount;
  const hasRemainingContent = this.freeContentCounters.premiumContentRemaining > 0;
  const hasAccess = hasWatchedEnoughAds && hasRemainingContent;

  return hasAccess;
}
```

**Flow:**
1. User consumes last free premium content → `remainingFreeContent` becomes 0
2. `decrementFreeContent()` automatically resets ad watch state
3. `hasTemporaryPremiumAccess()` returns `false` (no remaining content)
4. Next premium content access → UnlockDialog appears immediately

### 2. Time-Based Expiration (15 Minutes)

When 15 minutes pass since the last ad watch, premium access expires regardless of remaining content:

```typescript
// In checkPremiumAccessExpiration() method
if (timeSinceLastAd > this.PERSISTENT_AD_EXPIRATION_MS) {
  // Reset premium access state
  this.adBasedAccessState.hasWatchedAds = false;
  this.adBasedAccessState.adsWatchedCount = 0;
  // ... reset other fields

  // Reset free content counters to API default
  this.resetFreeContentCounters(); // ← Restores full content allocation
}
```

**Flow:**
1. 15 minutes pass since last ad watch
2. System automatically resets both ad state AND content counters
3. User gets fresh content allocation when they watch ads again
4. `hasTemporaryPremiumAccess()` returns `false` (no valid ad watches)

### Expiration Scenarios

| Scenario | Content Exhausted First | Time Expired First |
|----------|------------------------|-------------------|
| **Trigger** | `remainingContent = 0` | `15 minutes passed` |
| **Ad State** | ✅ Reset | ✅ Reset |
| **Content Counter** | ❌ Stays at 0 | ✅ Reset to limit |
| **User Benefit** | Must watch ads for new content | Gets fresh content allocation |

## Automatic Cleanup and State Management

### Expiration-Based Cleanup

The system automatically manages ad watch expiration and state cleanup during app initialization:

#### Cleanup Process
1. **Load Persistent Data**: All stored ad watches are loaded from AsyncStorage
2. **Filter Expired Watches**: Ad watches older than 15 minutes (or 1 second in testing) are identified
3. **Remove Expired Data**: Expired watches are removed from storage
4. **Update State**: If no valid watches remain, premium access state is reset
5. **Save Changes**: Updated state is persisted to AsyncStorage

#### State Reset Logic
When no valid ad watches are found during recovery:
```typescript
// Automatic state reset when all ads have expired
if (validWatches.length === 0) {
  this.adBasedAccessState.hasWatchedAds = false;
  this.adBasedAccessState.adsWatchedCount = 0;
  this.adBasedAccessState.lastAdWatchTime = null;
  this.adBasedAccessState.sessionStartTime = null;
  this.saveAdBasedAccessState(); // Persist the reset state
}
```

#### Testing Configuration
For development and testing purposes, the expiration time can be configured:
```typescript
// In sessionStorageService.ts
private readonly PERSISTENT_AD_EXPIRATION_MS = 1000; // 1 second for testing
// private readonly PERSISTENT_AD_EXPIRATION_MS = 15 * 60 * 1000; // 15 minutes for production
```

#### Cleanup Logging
The system provides detailed logging for cleanup operations:
- `"Cleaned up X expired ad watches"` - Shows number of expired watches removed
- `"No valid ad watches found, resetting ad access state"` - Indicates state reset
- `"Recovered X valid ad watches"` - Shows successful recovery count

### Benefits of Automatic Cleanup

1. **Prevents Indefinite Access**: Users can't maintain premium access beyond the intended duration
2. **Maintains Data Integrity**: Expired data is automatically removed to prevent storage bloat
3. **Seamless User Experience**: State transitions happen transparently during app launch
4. **Debugging Support**: Clear logging helps identify cleanup behavior during development

## Error Handling

The service includes robust error handling:

- **Initialization Errors**: Service continues with default values if AsyncStorage fails
- **Storage Errors**: Individual save operations are wrapped in try-catch blocks
- **Platform Support**: Gracefully degrades on web platform where AsyncStorage may not be available

## Migration from Session Storage

The persistent storage system maintains backward compatibility with the previous session-based implementation:

1. **Same API**: All existing methods work the same way
2. **Automatic Migration**: Old session data is preserved during the transition
3. **Fallback Support**: If persistent storage fails, the system falls back to memory-only storage

## Performance Considerations

- **Lazy Loading**: Data is only loaded when the service initializes
- **Efficient Updates**: Only changed data is written to storage
- **Memory Management**: Service maintains minimal memory footprint
- **Background Operations**: Storage operations don't block the UI thread

## Bug Fixes: Content Exhaustion State Management

### Issues Resolved

**Bug 1: Ad watches not being cleared when remainingFreeContent reaches 0**
- **Problem**: When free content was exhausted, ad watch state remained active, causing inconsistent state
- **Solution**: Added automatic ad state reset in `decrementFreeContent()` when counter reaches 0
- **Result**: Clean state transition when content is exhausted

**Bug 2: UnlockDialog not showing when remainingFreeContent reaches 0**
- **Problem**: TouchGameScreen only checked `hasTemporaryPremiumAccess && needsToWatchAdsAgain`, missing the case where content was exhausted
- **Solution**: Modified logic to check `!isPremiumUnlocked` first, then handle both access and no-access cases separately
- **Result**: UnlockDialog appears immediately when user tries to access premium content without valid access

### State Consistency Fix

The core issue was that `hasTemporaryPremiumAccess()` was only checking ad watch status, not remaining content:

```typescript
// BEFORE (inconsistent state possible)
hasTemporaryPremiumAccess(): boolean {
  return this.adBasedAccessState.hasWatchedAds && this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount;
}

// AFTER (consistent state guaranteed)
hasTemporaryPremiumAccess(): boolean {
  const hasWatchedEnoughAds = this.adBasedAccessState.hasWatchedAds && this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount;
  const hasRemainingContent = this.freeContentCounters.premiumContentRemaining > 0;
  return hasWatchedEnoughAds && hasRemainingContent;
}
```

This ensures that when `remainingFreeContent = 0`, `hasTemporaryPremiumAccess` always returns `false`.

## Testing

To test the persistent ad watch tracking system and content exhaustion fixes:

### Basic Functionality
1. **Watch Ads**: Complete the required ad cycle to gain premium access
2. **Check Shared Counter**: Verify both spicy and no_limits categories show "6 free"
3. **Consume Content**: Use any premium category to decrement the shared counter
4. **Verify Counter Updates**: Return to SelectCategoriesScreen and confirm both categories show the same decremented count

### Content Exhaustion Testing
5. **Exhaust Free Content**: Continue consuming premium content until counter reaches 0
6. **Verify State Reset**: Check that `hasTemporaryPremiumAccess` becomes `false` when content = 0
7. **Test UnlockDialog**: Try to access premium content and verify UnlockDialog appears immediately
8. **Verify Ad State Cleared**: Confirm that ad watch progress is reset (adsWatchedCount = 0)

### Stuck Ad Recovery Testing
5. **Simulate Stuck Ad**: Watch an ad and force-close the app during/after the ad (before returning to WatchAdsScreen)
6. **Restart App**: Completely close and restart the app
7. **Verify Recovery**: Check that the ad watch progress is maintained and recovered
8. **Check Expiration**: Wait 15+ minutes and restart app to verify expired watches are cleaned up

### Persistence Testing
9. **Multiple Sessions**: Watch ads across multiple app sessions to verify persistent tracking
10. **Debug Information**: Use debug method to inspect persistent watch states and expiration times

### Expected Behavior

- **Stuck Ad Recovery**: Users don't lose progress when force-closing stuck ad modals
- **15-Minute Expiration**: Persistent watches automatically expire after 15 minutes
- **Automatic Cleanup**: Expired watches are removed during app initialization
- **Automatic State Reset**: When all ads expire, premium access is automatically revoked
- **Content Exhaustion Handling**: When free content reaches 0, ad state is immediately reset
- **Consistent State**: `hasTemporaryPremiumAccess` always reflects both ad status AND remaining content
- **Immediate UnlockDialog**: UnlockDialog appears immediately when accessing premium content without valid access
- **Shared Counter**: Both spicy and no_limits categories always show the same count
- **Real-time Updates**: Counter updates immediately when returning from TouchGameScreen
- **Session Tracking**: Each app session gets a unique ID for debugging
- **Seamless Integration**: Works transparently with existing ad watching flow
- **Dual Expiration System**: Handles both content-based and time-based access expiration

## Debugging

Use the debug method to inspect current state including persistent watch information:

```typescript
const { getDebugInfo } = useAdBasedAccess();
console.log(getDebugInfo());
```

This will output detailed information including:
- Ad progress and premium access status
- Content counters and session state
- **Persistent watch counts** (total and valid)
- **Session ID** for tracking
- **Expiration status** of persistent watches

### Additional Debug Methods

```typescript
// Get valid (non-expired) persistent ad watches
const validWatches = sessionStorageService.getValidPersistentAdWatches();

// Check if there are any valid persistent watches
const hasValidWatches = sessionStorageService.hasValidPersistentAdWatches();

// Manually cleanup expired watches and get count removed
const removedCount = await sessionStorageService.cleanupExpiredAdWatchesManual();
```
