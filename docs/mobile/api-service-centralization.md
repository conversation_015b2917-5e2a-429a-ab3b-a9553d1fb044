# API Service Centralization - TapTrap Mobile

## 📋 Resumen de Cambios

Se ha centralizado el uso del API service en todos los servicios de la aplicación móvil, siguiendo el patrón correcto donde `services/api.ts` es el punto central para todas las llamadas HTTP.

## ✅ Cambios Implementados

### **1. Centralización de API_URL**

**Antes**: `API_URL` estaba definido en `contentService.ts`
**Después**: `API_URL` está definido en `api.ts` y se exporta desde ahí

```typescript
// services/api.ts
export const API_URL = 'https://taptrap-backend.vercel.app/api';
```

### **2. Servicios Actualizados**

#### **services/contentService.ts**
```typescript
// ANTES
import axios from 'axios';
export const API_URL = 'https://taptrap-backend.vercel.app/api';

// DESPUÉS  
import api, { API_URL } from './api';

// Cambio en API call
// ANTES
const response = await axios.get(`${API_URL}/content`, { timeout: 15000 });

// DESPUÉS
const response = await api.get('/content', { timeout: 15000 });
```

#### **services/pushNotificationService.ts**
```typescript
// ANTES
import { API_URL } from './contentService';

// DESPUÉS
import { API_URL } from './api';
```

#### **services/penaltyService.ts**
```typescript
// ANTES
import { decryptFromStorage, encryptForStorage, API_URL, decryptApiData } from './contentService';

// DESPUÉS
import { decryptFromStorage, encryptForStorage, decryptApiData } from './contentService';
import { API_URL } from './api';
```

#### **services/adSettingsService.ts**
```typescript
// ANTES
import axios from 'axios';
import { API_URL } from './contentService';

// DESPUÉS
import api, { API_URL } from './api';

// Cambio en API call
// ANTES
const response = await axios.get(`${API_URL}/admin/ad-settings`, { timeout: 15000 });

// DESPUÉS
const response = await api.get('/admin/ad-settings', { timeout: 15000 });
```

#### **services/whatsNewService.ts**
```typescript
// ✅ YA ESTABA CORRECTO
import api from './api';
```

### **3. Beneficios de la Centralización**

#### **Consistencia**
- Todos los servicios usan la misma instancia de axios
- Headers y configuraciones centralizadas
- Manejo de errores consistente

#### **Mantenibilidad**
- Un solo lugar para cambiar configuración de API
- Fácil agregar interceptors globales
- Debugging centralizado

#### **Solución Android Content-Type**
- El fix de Content-Type se aplica automáticamente a todos los servicios
- No hay headers duplicados o inconsistentes

## 🔧 Configuración Actual del API Service

### **services/api.ts**
```typescript
import axios from 'axios';
import { Platform } from 'react-native';

// API configuration
export const API_URL = 'https://taptrap-backend.vercel.app/api';

// Create axios instance with base configuration
// Note: Content-Type header is intentionally omitted to let the client set it automatically
// This fixes Android-specific network issues where explicit Content-Type headers cause problems
const api = axios.create({
  baseURL: API_URL,
  timeout: 15000, // 15 second timeout for slower connections
});

// Add request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for enhanced error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    // Enhanced logging for SSL and network errors
    if (error.code === 'NETWORK_ERROR' || 
        error.message.includes('SSL') || 
        error.message.includes('certificate') ||
        error.message.includes('Network request failed')) {
      console.error('🚨 SSL/Network Error Details:', {
        message: error.message,
        code: error.code,
        url: error.config?.url,
        method: error.config?.method,
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version
        },
        timestamp: new Date().toISOString()
      });
    } else {
      console.error('API Response Error:', error.response?.status, error.config?.url);
    }
    return Promise.reject(error);
  }
);

export default api;
```

## 📊 Estado de Servicios

| Servicio | Usa api.ts | Usa API_URL centralizado | Content-Type Fix |
|----------|------------|-------------------------|------------------|
| contentService.ts | ✅ | ✅ | ✅ |
| pushNotificationService.ts | ❌ (usa axios directo) | ✅ | ✅ |
| penaltyService.ts | ❌ (usa fetch) | ✅ | ✅ |
| adSettingsService.ts | ✅ | ✅ | ✅ |
| whatsNewService.ts | ✅ | ✅ | ✅ |

## 🎯 Próximos Pasos Opcionales

### **1. Migrar pushNotificationService.ts a api.ts**
```typescript
// Cambiar de:
const response = await axios.post(`${API_URL}/push-tokens`, data, { timeout: 15000 });

// A:
const response = await api.post('/push-tokens', data, { timeout: 15000 });
```

### **2. Migrar penaltyService.ts de fetch a api.ts**
```typescript
// Cambiar de:
const response = await fetch(penaltyUrl, { method: 'GET' });

// A:
const response = await api.get('/penalties');
```

### **3. Agregar Retry Logic Global**
```typescript
// En api.ts
export const retryApiCall = async <T>(
  apiCall: () => Promise<T>, 
  maxRetries: number = 3
): Promise<T> => {
  // Implementación de retry con backoff exponencial
};
```

## ✅ Resultado Final

- **API_URL centralizado** en `services/api.ts`
- **contentService.ts y adSettingsService.ts** usan la instancia centralizada
- **Todos los servicios** importan API_URL desde el lugar correcto
- **Content-Type fix** aplicado automáticamente a todos los servicios
- **Logging consistente** en todas las llamadas API
- **Manejo de errores mejorado** para problemas de red/SSL

La arquitectura ahora sigue el patrón correcto donde `api.ts` es el servicio central y todos los demás servicios lo utilizan.
