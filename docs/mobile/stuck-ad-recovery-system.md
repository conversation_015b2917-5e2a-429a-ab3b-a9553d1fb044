# Stuck Ad Recovery System

## Overview

The TapTrap app implements a robust stuck ad recovery system that prevents users from losing their ad watching progress when iOS ad modals get stuck and require force-closing the app. This system uses persistent storage with 15-minute expiration to maintain ad watch states across app sessions.

## The Problem

### iOS Ad Modal Issues

iOS ad modals sometimes exhibit the following problematic behaviors:
- Close button becomes unresponsive
- <PERSON><PERSON> gets stuck on screen and can't be dismissed
- Users are forced to force-close the entire app
- Previously, this would cause users to lose all ad watching progress

### Impact on User Experience

- Users would lose progress after watching multiple ads
- Frustration when having to restart the ad watching process
- Inconsistent behavior between iOS and Android platforms
- Poor user retention due to technical issues

## The Solution

### Persistent Ad Watch Tracking

The system now tracks each individual ad watch with the following approach:

1. **Immediate Persistence**: When an ad is marked as watched (after 2-second iOS auto-marking), it's immediately saved to AsyncStorage
2. **15-Minute Expiration**: Each ad watch has an expiration timestamp to prevent indefinite access
3. **Automatic Recovery**: When the app restarts, valid ad watches are recovered and progress is restored
4. **Session Management**: Each app session gets a unique ID for tracking and debugging

### Technical Implementation

```typescript
interface PersistentAdWatchState {
  adIndex: number;      // Index of the ad that was watched (0-4 for 5-ad requirement)
  watchedAt: number;    // Timestamp when ad was watched
  expiresAt: number;    // Timestamp when this watch expires (15 minutes later)
  sessionId: string;    // Unique session ID for tracking
}
```

## How It Works

### 1. Ad Watch Persistence

When a user watches an ad:

```typescript
incrementAdsWatched(): void {
  const now = Date.now();
  
  // Create persistent ad watch entry
  const persistentWatch: PersistentAdWatchState = {
    adIndex: this.adBasedAccessState.adsWatchedCount,
    watchedAt: now,
    expiresAt: now + (15 * 60 * 1000), // 15 minutes from now
    sessionId: this.currentSessionId,
  };

  // Save immediately to AsyncStorage
  this.persistentAdWatches.push(persistentWatch);
  this.savePersistentAdWatches();
  
  // Continue with regular ad counting...
}
```

### 2. Recovery Process

When the app initializes:

```typescript
async initialize(): Promise<void> {
  // 1. Generate new session ID
  this.currentSessionId = this.generateSessionId();
  
  // 2. Load all persistent data
  await this.loadPersistentAdWatches();
  
  // 3. Clean up expired watches
  await this.cleanupExpiredAdWatches();
  
  // 4. Recover valid progress
  this.recoverAdWatchProgress();
}
```

### 3. Progress Recovery Logic

```typescript
private recoverAdWatchProgress(): void {
  const validWatches = this.persistentAdWatches.filter(watch => watch.expiresAt > Date.now());
  
  if (validWatches.length > 0) {
    // Restore ads watched count from valid persistent watches
    const recoveredCount = validWatches.length;
    this.adBasedAccessState.adsWatchedCount = Math.max(
      this.adBasedAccessState.adsWatchedCount, 
      recoveredCount
    );
    
    // Grant access if user has watched enough ads
    if (this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount) {
      this.adBasedAccessState.hasWatchedAds = true;
      this.resetFreeContentCounters();
    }
    
    console.log(`Recovered ${recoveredCount} valid ad watches`);
  }
}
```

## User Experience Flow

### Normal Flow (No Issues)
1. User opens WatchAdsScreen
2. User taps ad → Ad plays → Ad closes normally
3. Progress is saved both in memory and persistently
4. User continues to next ad or returns to game

### Stuck Ad Recovery Flow
1. User opens WatchAdsScreen
2. User taps ad → Ad plays → **Ad modal gets stuck**
3. User force-closes the app (progress is already saved persistently)
4. User reopens the app
5. **System automatically recovers ad watch progress**
6. User can continue from where they left off

## Configuration

### Expiration Time

The 15-minute expiration is configurable:

```typescript
private readonly PERSISTENT_AD_EXPIRATION_MS = 15 * 60 * 1000; // 15 minutes
```

### Storage Keys

The system uses dedicated AsyncStorage key:

```typescript
private readonly STORAGE_KEY_PERSISTENT_AD_WATCHES = 'taptrap_persistent_ad_watches';
```

## Benefits

### For Users
- **No Lost Progress**: Never lose ad watching progress due to stuck modals
- **Seamless Recovery**: Progress is restored automatically when app restarts
- **Time-Limited**: Access doesn't last indefinitely (15-minute expiration)
- **Cross-Session Continuity**: Can continue ad watching across app sessions

### For Developers
- **Debugging Support**: Session IDs help track user sessions
- **Automatic Cleanup**: Expired watches are cleaned up automatically
- **Backward Compatibility**: Existing code continues to work unchanged
- **Platform Consistency**: Same behavior across iOS and Android

## Testing

### Manual Testing Steps

1. **Basic Recovery Test**:
   - Watch 2-3 ads in WatchAdsScreen
   - Force-close the app during/after an ad
   - Restart the app
   - Verify progress is maintained

2. **Expiration Test**:
   - Watch some ads and force-close app
   - Wait 15+ minutes
   - Restart app
   - Verify expired watches are cleaned up

3. **Full Cycle Test**:
   - Complete full ad watching cycle with force-closes
   - Verify premium access is granted correctly

### Debug Methods

```typescript
// Get current persistent watch state
const validWatches = sessionStorageService.getValidPersistentAdWatches();
console.log(`Valid watches: ${validWatches.length}`);

// Get debug information
console.log(sessionStorageService.getDebugInfo());

// Manual cleanup
const removedCount = await sessionStorageService.cleanupExpiredAdWatchesManual();
console.log(`Removed ${removedCount} expired watches`);
```

## Integration with Existing Systems

### AdMob Service Integration
- Works seamlessly with existing 2-second iOS auto-marking
- Compatible with 10-second stuck ad timeout
- No changes required to ad loading/showing logic

### Context Integration
- AdBasedAccessContext automatically handles recovery
- No changes required to existing component usage
- Enhanced debug information available

### Storage Integration
- Uses existing AsyncStorage patterns
- Graceful degradation on web platform
- Robust error handling for storage failures

## Conclusion

The stuck ad recovery system provides a robust solution to iOS ad modal issues while maintaining backward compatibility and adding valuable debugging capabilities. Users can now confidently watch ads knowing their progress will be maintained even if technical issues force them to restart the app.
