# Multi-Language Push Notification System

## Overview

The TapTrap push notification system has been enhanced to support multi-language messaging, allowing administrators to send targeted notifications to users based on their language preferences.

## Features

### 1. Language Capture and Storage
- **Mobile App**: Automatically captures the user's current language preference when registering for push notifications
- **Database**: Stores language preference alongside push tokens in MongoDB
- **Supported Languages**: English (en), Spanish (es), Dominican Spanish (dom)

### 2. Language-Targeted Notifications
- **Admin Panel**: Provides language dropdown selector for targeting specific language groups
- **Backend API**: Filters recipients based on selected language preference
- **Fallback**: If no language is selected, notifications are sent to all users

### 3. Language Statistics
- **Admin Dashboard**: Shows device count breakdown by language
- **Real-time Updates**: Language statistics update automatically when devices register

## Implementation Details

### Database Schema Changes

The `PushToken` model has been updated to include a `language` field:

```javascript
{
  token: String,           // Expo push token
  platform: String,       // 'ios' or 'android'
  deviceId: String,        // Device identifier
  language: String,        // 'en', 'es', or 'dom' (default: 'en')
  isActive: Boolean,       // Token status
  lastUsed: Date,         // Last usage timestamp
  createdAt: Date,        // Creation timestamp
  updatedAt: Date         // Last update timestamp
}
```

### Mobile App Changes

#### Push Notification Service
**File**: `services/pushNotificationService.ts`

- Added `getCurrentLanguage()` method to retrieve user's language preference
- Updated `uploadTokenToBackend()` to include language data in registration
- Language is determined from AsyncStorage or device settings

```typescript
async getCurrentLanguage(): Promise<string> {
  try {
    // First try to get stored language preference
    const storedLanguage = await AsyncStorage.getItem('appLanguage');
    if (storedLanguage && ['en', 'es', 'dom'].includes(storedLanguage)) {
      return storedLanguage;
    }
    
    // Fall back to device language
    return getDeviceLanguage();
  } catch (error) {
    console.error('Error getting current language:', error);
    return 'en'; // Default to English
  }
}
```

### Backend API Changes

#### Push Token Registration
**Endpoint**: `POST /api/push-tokens`

Updated to accept and store language preference:

```javascript
{
  "token": "ExponentPushToken[...]",
  "platform": "ios",
  "deviceId": "device-id",
  "language": "es"  // New field
}
```

#### Push Notification Sending
**Endpoint**: `POST /api/admin/push-notifications/send`

Updated to support language filtering:

```javascript
{
  "title": "Notification Title",
  "body": "Notification Message",
  "language": "es",  // Optional: target specific language
  "data": {}
}
```

### Admin Panel Changes

#### Language Statistics
**File**: `admin/src/pages/PushNotifications.js`

Added language distribution dashboard showing:
- English users count
- Spanish users count  
- Dominican users count

#### Language Selector
Added dropdown in notification form with options:
- All Languages (default)
- English (en)
- Spanish (es)
- Dominican (dom)

#### Enhanced Device List
Updated device table to include language column with color-coded badges:
- Blue: English (en)
- Green: Spanish (es)
- Purple: Dominican (dom)

## Usage Examples

### Sending Language-Targeted Notifications

1. **All Users**:
   - Leave language dropdown empty
   - Sends to all registered devices

2. **Spanish Users Only**:
   - Select "Spanish (es)" from dropdown
   - Only sends to users with language preference set to "es"

3. **Dominican Users Only**:
   - Select "Dominican (dom)" from dropdown
   - Only sends to users with language preference set to "dom"

### API Usage

```javascript
// Send to all users
await api.post('/admin/push-notifications/send', {
  title: 'New Content Available',
  body: 'Check out our latest updates!'
});

// Send to Spanish users only
await api.post('/admin/push-notifications/send', {
  title: 'Nuevo Contenido Disponible',
  body: '¡Revisa nuestras últimas actualizaciones!',
  language: 'es'
});
```

## Migration Notes

### Existing Tokens
- Existing push tokens without language field will default to 'en' (English)
- Users will be prompted to update their language preference on next app launch
- Language preference is automatically updated when users change app language

### Database Indexes
New indexes have been added for efficient language-based queries:
- `{ language: 1, isActive: 1 }`
- `{ platform: 1, language: 1, isActive: 1 }`

## Testing

### Manual Testing Steps

1. **Language Registration**:
   - Change app language in settings
   - Register for push notifications
   - Verify language is stored correctly in admin panel

2. **Targeted Notifications**:
   - Send notification to specific language group
   - Verify only users with that language receive notification

3. **Language Statistics**:
   - Register devices with different languages
   - Verify statistics update correctly in admin panel

### API Testing

```bash
# Register token with language
curl -X POST http://localhost:5002/api/push-tokens \
  -H "Content-Type: application/json" \
  -d '{
    "token": "ExponentPushToken[test]",
    "platform": "ios",
    "language": "es"
  }'

# Send targeted notification
curl -X POST http://localhost:5002/api/admin/push-notifications/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "title": "Test",
    "body": "Spanish users only",
    "language": "es"
  }'
```

## Future Enhancements

1. **Template System**: Pre-defined notification templates for each language
2. **Auto-Translation**: Automatic translation of notifications to all supported languages
3. **A/B Testing**: Send different messages to different language groups
4. **Analytics**: Track notification engagement by language
5. **Scheduling**: Schedule notifications for optimal times per language/region
