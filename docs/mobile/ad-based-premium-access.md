# Ad-Based Temporary Premium Access System

## Overview

The ad-based temporary premium access system allows free users to unlock premium content (Spicy and No Limits categories) by watching video advertisements. This provides an alternative to the subscription-based premium access while maintaining monetization through ad revenue.

## Key Features

- **Persistent Ad Watch Tracking**: Individual ad watches persist with 15-minute expiration for stuck ad recovery
- **Stuck Ad Recovery**: Users can force-close stuck ad modals without losing progress
- **Configurable Ad Requirements**: Configurable number of ads required (default varies by admin settings)
- **Shared Free Content Counter**: All premium categories (spicy/no_limits) share the same 6-item free counter
- **Recurring Ad Requirements**: During premium gameplay, users must watch ads again every 6 questions/dares
- **Ad Reliability**: Ads automatically marked as watched when they open successfully on both platforms
- **Real-Time Counter Updates**: Free content counter updates immediately when content is consumed
- **Internet Connectivity**: All ad functionality is disabled when there's no internet connection
- **Dual Access Options**: Users can choose between "Get Premium" (subscription) or "Watch Ads" (temporary)
- **Session Tracking**: Each app session gets unique ID for debugging and tracking

## Architecture

### Core Components

#### 1. Services

**`services/adMobService.ts`**
- Manages AdMob rewarded interstitial ad integration
- Handles ad preloading for better user experience
- Provides internet connectivity checks
- Uses test ad unit IDs in development, production IDs in release

**`services/sessionStorageService.ts`** (PersistentAdStorageService)
- Manages persistent storage for ad access state using AsyncStorage
- **Tracks individual ad watches with 15-minute expiration for stuck ad recovery**
- Provides automatic recovery logic when app restarts
- **Handles session management with unique session IDs**
- Data persists across app restarts and sessions
- Includes free content tracking for premium categories
- **Automatic cleanup of expired ad watch states**

#### 2. Context

**`context/AdBasedAccessContext.tsx`**
- React context for managing ad-based premium access state
- Provides hooks for components to access and modify state
- Handles app state changes and connectivity monitoring
- Preloads ads when user has temporary access

#### 3. UI Components

**`components/ads/AdsCounter.tsx`**
- Displays progress counter (e.g., "2/5 Watched")
- Supports localization
- Consistent styling with app theme

**Enhanced `components/ads/UnlockDialog.tsx`**
- Modal dialog with two options: "Get Premium" and "Watch Ads"
- Internet connectivity awareness
- Platform-specific RevenueCat integration
- Smooth animations and haptic feedback

**Enhanced `screens/WatchAdsScreen.tsx`**
- Complete ad watching experience
- Visual feedback for watched/unwatched ads
- Success callout when all ads are completed
- Error handling and user feedback

## User Flow

### Initial Premium Access

1. **User taps premium category** (Spicy/No Limits) without subscription
2. **UnlockDialog appears** with two options:
   - "Get Premium": Opens RevenueCat paywall
   - "Watch Ads": Navigates to WatchAdsScreen
3. **User selects "Watch Ads"**
4. **WatchAdsScreen displays** 5 ad items to watch
5. **User taps ad item** → Rewarded interstitial ad plays
6. **Ad completion** → Item marked as "Watched"
7. **All 5 ads watched** → Success callout → Navigate back
8. **Premium content unlocked** for current session

### Recurring Ad Requirements

1. **User plays premium content** with temporary access
2. **Every configurable number of questions/dares** → UnlockDialog appears (sequential mode)
3. **User must watch configurable number of ads again** to continue playing
4. **Cycle repeats** every configurable number of questions/dares indefinitely

## Technical Implementation

### State Management

```typescript
interface AdBasedAccessState {
  hasWatchedAds: boolean;
  adsWatchedCount: number;
  questionsAnsweredSinceAds: number;
  lastAdWatchTime: number | null;
  sessionStartTime: number | null;
}

interface FreeContentCounters {
  premiumContentRemaining: number; // Shared counter for all premium categories
  lastResetTime: number | null;
}

// NEW: Individual ad watch tracking with expiration
interface PersistentAdWatchState {
  adIndex: number;      // Index of the ad that was watched
  watchedAt: number;    // Timestamp when ad was watched
  expiresAt: number;    // Timestamp when this watch expires (15 minutes later)
  sessionId: string;    // Unique session ID for tracking
}
```

### Key Methods

- `initialize()`: Loads persisted data from AsyncStorage with recovery logic on app startup
- `incrementAdsWatched()`: **Creates persistent ad watch entry with 15-minute expiration** and increments count
- `decrementFreeContent()`: **Decrements shared premium content counter and resets ad state when exhausted**
- `getRemainingFreeContent()`: Returns remaining free content count (shared across all premium categories)
- `incrementQuestionsAnswered()`: Tracks questions during premium gameplay
- `needsToWatchAdsAgain()`: Returns true every configurable number of questions/dares
- `hasTemporaryPremiumAccess()`: **Checks if user has current ad-based access AND remaining content**
- `hasFreeContentExhausted()`: **NEW** - Checks if user has exhausted their free content allocation
- `getValidPersistentAdWatches()`: Returns non-expired persistent ad watches
- `hasValidPersistentAdWatches()`: Checks if there are any valid persistent watches
- `cleanupExpiredAdWatchesManual()`: Manual cleanup of expired watches with count

### Integration Points

**SelectCategoriesScreen**
- Shows UnlockDialog instead of direct RevenueCat for premium categories
- Checks `hasAnyPremiumAccess` (subscription OR ad-based)
- Displays real-time free content counter for premium categories
- Both spicy and no_limits categories show the same shared counter

**TouchGameScreen**
- Tracks question/dare count during premium gameplay
- Decrements free content counter when premium content is consumed
- **Shows UnlockDialog immediately when user lacks premium access**
- Shows UnlockDialog every 6 questions with `isSequential: true` for users with access
- Resets question count when user completes ad cycle
- **Handles both content exhaustion and recurring ad requirements**

## Platform-Specific Behavior

### Stuck Ad Recovery System

**The Problem**: iOS ad modals sometimes get stuck and don't respond to close button taps, forcing users to force-close the app and lose their ad watching progress.

**The Solution**: Persistent ad watch tracking with 15-minute expiration:

```typescript
// When ad is marked as watched (after 2 seconds on iOS)
incrementAdsWatched(): void {
  const now = Date.now();

  // Create persistent ad watch entry with 15-minute expiration
  const persistentWatch: PersistentAdWatchState = {
    adIndex: this.adBasedAccessState.adsWatchedCount,
    watchedAt: now,
    expiresAt: now + (15 * 60 * 1000), // 15 minutes
    sessionId: this.currentSessionId,
  };

  // Save immediately to AsyncStorage
  this.persistentAdWatches.push(persistentWatch);
  this.savePersistentAdWatches();

  // Continue with regular ad counting logic...
}
```

### Ad Marking Reliability

Due to close button reliability issues on both platforms, ads are automatically marked as watched when they open successfully:

```typescript
// iOS: Auto-mark after 2 seconds
if (Platform.OS === 'ios') {
  setTimeout(() => {
    console.log('AdMob: iOS auto-marking ad as watched after 2 seconds');
    adWatched = true;
  }, 2000);
}

// Show the ad
ad.show();
```

### Shared Counter Logic

All premium categories (spicy_questions, no_limits_questions, spicy_dares, no_limits_dares) share the same counter:

```typescript
// Both categories show the same count
{hasTemporaryPremiumAccess && (
  <Text>{getRemainingFreeContent('spicy_questions')} free</Text>  // Shows: "6 free"
)}

{hasTemporaryPremiumAccess && (
  <Text>{getRemainingFreeContent('no_limits_questions')} free</Text>  // Shows: "6 free" (same)
)}
```

When one category runs out of free content, both categories become locked.

## Content Exhaustion and State Management

### Dual Expiration System

The system handles two types of premium access expiration:

#### 1. Content-Based Expiration (Immediate)

When users consume all their free premium content:

```typescript
// Enhanced hasTemporaryPremiumAccess() logic
hasTemporaryPremiumAccess(): boolean {
  const hasWatchedEnoughAds = this.adBasedAccessState.hasWatchedAds && this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount;
  const hasRemainingContent = this.freeContentCounters.premiumContentRemaining > 0;
  return hasWatchedEnoughAds && hasRemainingContent; // Both conditions required
}

// Automatic state reset in decrementFreeContent()
if (this.freeContentCounters.premiumContentRemaining === 0) {
  // Reset ad watch state immediately
  this.adBasedAccessState.hasWatchedAds = false;
  this.adBasedAccessState.adsWatchedCount = 0;
  // ... clear all ad progress
}
```

**Flow:**
1. User consumes last free content → Counter reaches 0
2. Ad watch state is automatically reset
3. `hasTemporaryPremiumAccess()` returns `false`
4. Next premium access attempt → UnlockDialog appears immediately

#### 2. Time-Based Expiration (15 Minutes)

When 15 minutes pass since last ad watch:

```typescript
// Existing time-based expiration logic
if (timeSinceLastAd > 15_MINUTES) {
  // Reset both ad state AND content counter
  this.resetAdState();
  this.resetFreeContentCounters(); // User gets fresh content allocation
}
```

**Flow:**
1. 15 minutes pass → System resets everything
2. User gets fresh content allocation when they watch ads again
3. Benefits users who didn't exhaust their content

### TouchGameScreen Integration

Enhanced logic handles both access scenarios:

```typescript
// Updated premium content access logic
if (isPremiumCategory && !isPremiumUnlocked) {
  if (hasTemporaryPremiumAccess) {
    // User has access - consume content
    decrementFreeContent(category);
    incrementQuestionsAnswered();

    // Check recurring ad requirements
    if (needsToWatchAdsAgain) {
      setShowUnlockDialog(true);
    }
  } else {
    // User lacks access - show unlock dialog immediately
    setShowUnlockDialog(true);
    return; // Don't proceed to content
  }
}
```

**Benefits:**
- **Immediate Feedback**: Users see UnlockDialog as soon as access is lost
- **Consistent State**: No more inconsistent `hasTemporaryPremiumAccess: true, remainingContent: 0`
- **Clean Transitions**: Automatic state cleanup prevents edge cases

## Configuration

### OS-Specific Ad Entry Point Control

The system now supports OS-specific control over the "Watch Ads" button visibility:

- **iOS Toggle**: Controls whether iOS users see the "Watch Ads" button
- **Android Toggle**: Controls whether Android users see the "Watch Ads" button
- **Backward Compatibility**: Legacy `watchAdsButtonEnabled` field is maintained for older clients

### Environment Variables

```env
# AdMob Configuration
ADMOB_REWARDED_AD_UNIT_ID_ANDROID=ca-app-pub-3940256099942544/**********
ADMOB_REWARDED_AD_UNIT_ID_IOS=ca-app-pub-8401892082498077~**********
```

### App Configuration

```javascript
// app.config.js
extra: {
  admobRewardedAdUnitIdAndroid: process.env.ADMOB_REWARDED_AD_UNIT_ID_ANDROID,
  admobRewardedAdUnitIdIos: process.env.ADMOB_REWARDED_AD_UNIT_ID_IOS,
}
```

## Dependencies

### Required Packages

```json
{
  "react-native-google-mobile-ads": "^13.x.x",
  "@react-native-community/netinfo": "^11.x.x"
}
```

### Context Hierarchy

```tsx
<AppSettingsProvider>
  <AdBasedAccessProvider>
    <LanguageProvider>
      <GameProvider>
        {/* App components */}
      </GameProvider>
    </LanguageProvider>
  </AdBasedAccessProvider>
</AppSettingsProvider>
```

## Error Handling

### Internet Connectivity
- Ads are disabled when offline
- "Watch Ads" button becomes disabled and grayed out
- User receives alert if trying to watch ads without internet

### Ad Loading Failures
- Graceful error messages to user
- Fallback to loading ads on-demand if preloading fails
- Retry mechanisms for failed ad requests

### Edge Cases
- App backgrounding during ad playback
- Ad completion tracking
- State consistency across app lifecycle

## Localization

### Translation Keys

```typescript
watchAdsTitle: "Watch Ads" | "Ver Anuncios"
watchAdsCallout: "To unlock access to premium, watch these ads."
watchAdsAdText: "Ad" | "Anuncio"
watchAdsPending: "Pending" | "Pendiente"
watchAdsWatched: "Watched" | "Visto"
getPremium: "GET PREMIUM" | "OBTENER PREMIUM"
```

## Bug Fixes: Content Exhaustion State Management

### Critical Issues Resolved

#### Issue 1: Inconsistent State When Content Exhausted
**Problem**: When `remainingFreeContent` reached 0, `hasTemporaryPremiumAccess` remained `true`, causing state inconsistency.

**Root Cause**: `hasTemporaryPremiumAccess()` only checked ad watch status, not remaining content.

**Solution**: Enhanced the method to require both conditions:
```typescript
// BEFORE: Only checked ad status
return this.adBasedAccessState.hasWatchedAds && this.adBasedAccessState.adsWatchedCount >= this.requiredAdsCount;

// AFTER: Checks both ad status AND remaining content
return hasWatchedEnoughAds && hasRemainingContent;
```

#### Issue 2: Ad State Not Cleared on Content Exhaustion
**Problem**: When free content was exhausted, ad watch progress remained, requiring manual reset.

**Solution**: Added automatic ad state reset in `decrementFreeContent()`:
```typescript
if (this.freeContentCounters.premiumContentRemaining === 0) {
  // Automatically reset all ad watch state
  this.adBasedAccessState.hasWatchedAds = false;
  this.adBasedAccessState.adsWatchedCount = 0;
  // ... clear persistent watches
}
```

#### Issue 3: UnlockDialog Not Appearing on Content Exhaustion
**Problem**: TouchGameScreen logic only showed UnlockDialog when `hasTemporaryPremiumAccess && needsToWatchAdsAgain`, missing the content exhaustion case.

**Solution**: Restructured logic to handle both access and no-access scenarios:
```typescript
// BEFORE: Missed content exhaustion case
if (hasTemporaryPremiumAccess && needsToWatchAdsAgain) {
  setShowUnlockDialog(true);
}

// AFTER: Handles all access scenarios
if (hasTemporaryPremiumAccess) {
  // User has access - consume content and check limits
} else {
  // User lacks access - show dialog immediately
  setShowUnlockDialog(true);
}
```

### State Consistency Guarantee

The fixes ensure that when `remainingFreeContent = 0`:
- ✅ `hasTemporaryPremiumAccess` returns `false`
- ✅ Ad watch state is cleared (`adsWatchedCount = 0`)
- ✅ UnlockDialog appears on next premium access attempt
- ✅ No inconsistent state combinations possible

## Testing

### Content Exhaustion Testing
1. **Watch Required Ads**: Complete ad cycle to gain premium access
2. **Verify Initial State**: Check `hasTemporaryPremiumAccess: true, remainingContent: 6`
3. **Consume All Content**: Use premium content until counter reaches 0
4. **Verify State Reset**: Confirm `hasTemporaryPremiumAccess: false, adsWatchedCount: 0`
5. **Test UnlockDialog**: Try accessing premium content → UnlockDialog should appear immediately
6. **Verify Clean State**: No inconsistent state combinations should occur

### Time-Based Expiration Testing
7. **Partial Content Use**: Watch ads, consume only some content (e.g., 3/6)
8. **Wait 15+ Minutes**: Let time-based expiration trigger
9. **Verify Full Reset**: Both ad state and content counter should reset
10. **Fresh Allocation**: User should get full content allocation on next ad cycle

### Development Mode
- Uses AdMob test ad unit IDs
- Console logging for debugging state transitions
- State inspection through context debug methods
- Enhanced logging for content exhaustion events

### Production Considerations
- Replace test ad unit IDs with production IDs
- Monitor ad fill rates and revenue
- Track user engagement and conversion metrics
- Monitor content exhaustion patterns and user behavior

## Performance Considerations

### Ad Preloading
- Preloads up to 5 ads when user has temporary access
- Replaces used ads with new preloaded ads
- Clears preloaded ads when app goes to background

### Memory Management
- Session-only storage prevents memory leaks
- Proper cleanup of ad event listeners
- Context state resets on app restart

## Future Enhancements

### Potential Improvements
- Configurable ad count requirements
- Different ad types (rewarded video, banner ads)
- Analytics integration for ad performance
- A/B testing for ad placement and frequency
- Progressive ad requirements (fewer ads for returning users)

### Metrics to Track
- Ad completion rates
- Premium content engagement after ad viewing
- Conversion from ad-based to subscription access
- User retention with ad-based access model
