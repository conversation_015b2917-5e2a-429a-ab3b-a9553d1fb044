# TapTrap Persistent Question Pool

## Overview

The TapTrap game implements a persistent question pool system that maintains the state of questions and challenges across app sessions. This ensures that users don't see repetitive content even if they:

1. Navigate away from the game screen
2. Close and reopen the app
3. Change categories or game modes

The persistent pool tracks which questions have been shown to the user and ensures a good distribution of content over time. It also automatically adds new content to the existing pool when it becomes available, ensuring users get access to new questions without losing their progress.

## Implementation Details

### Storage Mechanism

The persistent pool uses AsyncStorage with encryption to securely store:

1. The complete pool of available questions and challenges for each category combination
2. The set of question IDs that have already been shown to the user for each category combination
3. A global set of question IDs that have been shown across all game modes and categories

This dual-storage approach ensures:
- Each combination of categories and modes has its own isolated pool (mode-specific storage)
- Content viewed in any mode is tracked globally to prevent repetition across mode changes (global storage)

### Key Files

- **poolStorageService.ts**: Handles saving and loading the pool state
- **TouchGameScreen.tsx**: Uses the pool storage service to maintain state
- **contentService.ts**: Provides encryption/decryption functions for secure storage

### Data Flow

1. **Initial Load**:
   - App checks for a saved question pool in AsyncStorage for the current categories
   - If found, it loads the pool and the set of used question IDs
   - It also checks for new content from the backend
   - If new content is found:
     - Load the latest cached content and build a fresh pool of items.
     - Compare the fresh pool against the saved pool’s item IDs.
     - Identify new items (those with IDs not present in the saved pool).
     - Append new items to the saved pool as unused, preserving any IDs already marked as used.
     - Persist the merged pool back to storage.
     - Log each step (e.g. “Merging X new items into saved pool” and “Saved updated pool with Y items”).
   - If no saved pool is found, it creates a new pool from the game content

2. **During Gameplay**:
   - When a question is shown, its ID is added to the used set
   - The updated set is saved to AsyncStorage with the current categories
   - When all questions have been shown, the used set is reset

3. **App Restart**:
   - The saved pool and used IDs are loaded from AsyncStorage for the current categories
   - New content is checked and merged if available
   - Gameplay continues from where the user left off

4. **Category Change**:
   - When the user changes categories, a different pool is loaded
   - Each category combination has its own isolated pool and used IDs
   - This prevents content mixing between categories

5. **Home Screen Focus**:
   - When the user returns to the Home screen, a forced refresh from the API is triggered
   - The app fetches the latest content directly from the backend API
   - This refresh happens whenever the Home screen receives focus
   - This ensures users always have access to the latest content
   - When the user starts a new game, the pool will be updated with:
     - New content added to the existing pool
     - Inactive content removed from the pool
     - Used question IDs updated to remove references to inactive content
   - This approach maintains user progress while keeping content up-to-date

## Pool Management

### Question Selection Logic

The question selection follows these rules:

1. First, try to select from questions that haven't been shown yet in either:
   - The current mode-specific pool (tracked by `usedQuestionIds`)
   - Any mode (tracked by `globalUsedIds`)
2. If all questions have been shown in the current mode:
   - Reset the mode-specific used IDs and start fresh
   - Keep the global used IDs intact to maintain cross-mode persistence
3. Always save both the mode-specific and global state after each selection or reset

The key filtering logic:
```typescript
// Filter out questions that have already been used (either in current mode or globally)
const unusedQuestions = currentPool.filter(q =>
  !usedQuestionIds.has(q.id) && !globalUsedIds.has(q.id)
);
```

### Pool Persistence

The pool persists across:

- Navigation changes within the app
- App restarts
- Device reboots
- Game mode changes (questions/dares)
- Category changes

The dual storage system ensures:
- Mode-specific pools track content usage within a specific mode
- Global tracking ensures content viewed in any mode is remembered across all modes

## Technical Implementation

### PoolItem Interface

```typescript
export interface PoolItem {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
  type: 'question' | 'challenge';
  category: string;
}
```

### Storage Functions

```typescript
// Save the current question pool with categories
export const saveQuestionPool = async (pool: PoolItem[], categories: string[] = []): Promise<boolean>

// Load the question pool for specific categories
export const loadQuestionPool = async (categories: string[] = []): Promise<PoolItem[] | null>

// Save the set of used question IDs with categories (mode-specific)
export const saveUsedQuestionIds = async (usedIds: Set<string>, categories: string[] = []): Promise<boolean>

// Load the set of used question IDs for specific categories (mode-specific)
export const loadUsedQuestionIds = async (categories: string[] = []): Promise<Set<string> | null>

// Save the set of globally used question IDs (persistent across all modes)
export const saveGlobalUsedQuestionIds = async (usedIds: Set<string>): Promise<boolean>

// Load the set of globally used question IDs (persistent across all modes)
export const loadGlobalUsedQuestionIds = async (): Promise<Set<string> | null>

// Clear pool-related data for specific categories or all pools
export const clearPoolStorage = async (categories?: string[]): Promise<boolean>
```

### Security

The pool data is encrypted before being stored in AsyncStorage using the same encryption mechanism as the game content:

```typescript
// Generate a unique storage key based on categories
const storageKey = getPoolKey(categories);

// Encrypt data for storage
const encryptedPool = encryptForStorage(pool);
await AsyncStorage.setItem(storageKey, encryptedPool);

// Decrypt data from storage
const storedPool = await AsyncStorage.getItem(storageKey);
const decryptedPool = decryptFromStorage(storedPool);
```

The storage keys are generated based on the categories to ensure isolation between different category combinations:

```typescript
// Helper to generate storage keys based on categories
const getPoolKey = (categories: string[]): string => {
  // Sort categories to ensure consistent key generation
  const sortedCategories = [...categories].sort().join('_');
  return `${POOL_STORAGE_KEY_PREFIX}${sortedCategories || 'all'}`;
};
```

## Benefits

1. **Improved User Experience**: Users don't see the same questions repeatedly
2. **Efficient Content Usage**: All content is shown before repeating
3. **Seamless Gameplay**: Users can exit and return without losing their place
4. **Memory Efficiency**: Only stores the necessary state information
5. **Content Updates**: New content is automatically merged into existing pools
6. **Content Maintenance**: Inactive content is automatically removed from pools
7. **Category Isolation**: Each category combination has its own isolated pool
8. **Persistent Progress**: User progress is maintained across app sessions
9. **Cross-Mode Persistence**: Content viewed in any mode is remembered across all modes
10. **Flexible Game Experience**: Users can freely switch between game modes without seeing repeated content

## Future Improvements

Potential enhancements to the persistent pool system:

1. **Content Versioning**: Add version tracking to handle content schema changes
2. **User Preferences**: Store user preferences for question types
3. **Favorites System**: Allow users to mark and revisit favorite questions
4. **Analytics Integration**: Track which questions are most frequently shown/skipped
5. **Content Refresh**: Add option to manually refresh the content pool
6. **Weighted Selection**: Implement weighted selection based on user feedback
7. **Content Synchronization**: Sync pool state across multiple devices
