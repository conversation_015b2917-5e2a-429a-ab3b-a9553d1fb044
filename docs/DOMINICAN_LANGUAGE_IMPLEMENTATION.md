# Dominican Spanish Language Implementation

## Overview

This document describes the implementation of Dominican Spanish ("Dominicano") as a new language option in the TapTrap app. The implementation includes changes to the mobile app, backend, and admin panel.

## Database Schema Changes

The content model has been updated to include a new `text_dom` field for Dominican Spanish text:

```javascript
// In backend/src/models/content.js
const contentSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  text_en: {
    type: String,
    required: true
  },
  text_es: {
    type: String,
    required: true
  },
  text_dom: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true
  },
  gameMode: {
    type: String,
    required: true
  },
  active: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});
```

## Migration Scripts

Two scripts have been created to help with the migration:

1. **migrate-add-text-dom.js**: Updates existing content to include the `text_dom` field by copying the value from `text_es` as a starting point.
2. **verify-text-dom.js**: Verifies that all content has the `text_dom` field.

To run the migration:

```bash
cd backend/src/scripts
node migrate-add-text-dom.js
node verify-text-dom.js
```

## Mobile App Changes

### Language Type

The language type has been updated to include 'dom' as a valid option:

```typescript
// In services/i18nService.tsx
type LanguageType = 'en' | 'es' | 'dom';
```

### Translation Dictionary

The translation dictionary has been updated to include translations for all keys in Dominican Spanish:

```typescript
const translations = {
  pickTheMode: {
    en: 'Choose a Game Mode',
    es: '¿Qué vamos a jugar?',
    dom: '¿Qué vamo\' a jugal?'
  },
  // More translations...
};
```

### Device Language Detection

The device language detection has been updated to recognize Dominican Spanish:

```typescript
export const getDeviceLanguage = (): LanguageType => {
  try {
    // Use getLocales() instead of deprecated locale property
    const locales = Localization.getLocales();
    const languageCode = locales.length > 0 ? locales[0].languageCode || 'en' : 'en';
    
    // Check if the language code is one of our supported languages
    if (languageCode && ['en', 'es', 'dom'].includes(languageCode)) {
      return languageCode as LanguageType;
    }
    
    // Special case for Dominican Republic
    if (languageCode === 'es' && locales.length > 0 && locales[0].regionCode === 'DO') {
      return 'dom';
    }
    
    return 'en'; // Default to English
  } catch {
    return 'en'; // Default to English if there's an error
  }
};
```

### Settings Screen

The Settings screen has been updated to include a new language option for Dominican Spanish:

```tsx
<TouchableOpacity
  style={language === 'dom' ? styles.settingControlSeleted : styles.settingControl}
  onPress={async () => {
    setSelectedSetting(null);
    handleSelectLanguage('dom');
    await playSound('tapEffect1');
    if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }}
>
  <DominicanFlag width={56} height={56} />
  <Text style={[typography.subtitle2, styles.settingControlTitle]}>{t('languageDominican')}</Text>
  {language === 'dom' && <CheckMarkIcon width={48} height={48} />}
</TouchableOpacity>
```

### Content Service

The content service has been updated to handle the new language field:

```typescript
// In services/contentService.ts
interface GameContent {
  questions: {
    casual_questions: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    // More categories...
  };
  dares: {
    casual_dares: Array<{ id: string; text_en: string; text_es: string; text_dom: string }>;
    // More categories...
  };
}

export const getLocalizedText = (item: { text_en: string; text_es: string; text_dom: string }): string => {
  const language = getDeviceLanguage();
  if (language === 'es') return item.text_es;
  if (language === 'dom') return item.text_dom;
  return item.text_en; // Default to English
};
```

## Backend Changes

### Content Controller

The content controller has been updated to include the new language field in the formatted content:

```javascript
// In backend/src/controllers/contentController.js
items.forEach(item => {
  const { id, text_en, text_es, text_dom, category, gameMode } = item;

  // Skip inactive items
  if (!item.active) return;

  const contentItem = { 
    id, 
    text_en, 
    text_es, 
    text_dom: text_dom || text_es // Fallback to Spanish if Dominican not available
  };

  // Rest of the code...
});
```

### Admin Controller

The admin controller has been updated to handle the new language field:

```javascript
// In backend/src/controllers/adminController.js
// Handle search term for text_en, text_es, and text_dom
if (search) {
  filter.$or = [
    { text_en: { $regex: search, $options: 'i' } },
    { text_es: { $regex: search, $options: 'i' } },
    { text_dom: { $regex: search, $options: 'i' } }
  ];
}
```

## Admin Panel Changes

### Content Forms

The content forms have been updated to include a field for Dominican Spanish text:

```jsx
// In admin/src/pages/AddContent.js and admin/src/pages/EditContent.js
<FormControl isInvalid={errors.text_dom} isRequired>
  <FormLabel>Text (Dominican Spanish)</FormLabel>
  <Textarea
    {...register('text_dom', {
      required: 'Dominican Spanish text is required',
      minLength: { value: 3, message: 'Minimum length should be 3 characters' },
    })}
    placeholder="Enter content text in Dominican Spanish"
    rows={4}
  />
  <FormErrorMessage>{errors.text_dom?.message}</FormErrorMessage>
</FormControl>
```

### Content Service

The admin content service has been updated to handle the new language field:

```javascript
// In admin/src/services/content.service.js
export const transformContentForUI = (content) => {
  return {
    id: content.id,
    text_en: content.text_en,
    text_es: content.text_es,
    text_dom: content.text_dom || content.text_es, // Fallback to Spanish if Dominican not available
    category: content.category,
    gameMode: content.gameMode,
    active: content.active,
    createdAt: content.createdAt ? new Date(content.createdAt) : null,
    updatedAt: content.updatedAt ? new Date(content.updatedAt) : null
  };
};
```

## Testing

To test the implementation:

1. Run the migration scripts to update existing content
2. Restart the backend server
3. Test the admin panel by adding and editing content with the new language field
4. Test the mobile app by selecting Dominican Spanish in the settings
5. Verify that the content is displayed correctly in all languages
