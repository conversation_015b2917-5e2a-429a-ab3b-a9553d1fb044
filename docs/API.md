# TapTrap API Documentation

This document provides detailed information about the TapTrap backend API, including endpoints, request/response formats, and authentication.

## API Overview

The TapTrap API is a RESTful service that provides game content to the mobile application and allows administrators to manage that content.

### Base URL

- Development: `http://localhost:5002/api`
- Production: `https://taptrap-backend.vercel.app/api`

### Swagger Documentation

The API includes interactive documentation using Swagger UI:

- Development: `http://localhost:5002/api/docs`
- Production: `https://taptrap-backend.vercel.app/api/docs`

Swagger UI provides:
- Interactive documentation for all endpoints
- Testing interface for API calls
- Detailed schema information
- Authentication support

### Authentication

Admin endpoints require authentication using JSON Web Tokens (JWT).

- **Login**: Send credentials to `/api/admin/login` to receive a token
- **Authorization**: You can use either of these methods:
  - Include the token in the `Authorization` header as `Bearer <token>` (standard JWT method)
  - Include the token in the `x-auth-token` header (alternative method)

## Public Endpoints

These endpoints are accessible without authentication and are used by the mobile app.

### Get All Raw Content

Retrieves all content items (including inactive) without formatting.

- **URL**: `/api/content/raw`
- **Method**: `GET`
- **Authentication**: None

#### Response

```json
[
  {
    "id": "q1",
    "text_en": "What's your favorite color?",
    "text_es": "¿Cuál es tu color favorito?",
    "text_dom": "¿Cuál e' tu color favorito?",
    "category": "casual_questions",
    "gameMode": "questions",
    "active": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  // More items...
]
```

### Get All Game Content

Retrieves all active game content formatted for the mobile app.

- **URL**: `/api/content`
- **Method**: `GET`
- **Authentication**: None

#### Response

When encryption is disabled (ENCRYPTION_KEY not set):

```json
{
  "questions": {
    "casual_questions": [
      {
        "id": "q1",
        "text_en": "What's your favorite color?",
        "text_es": "¿Cuál es tu color favorito?",
        "text_dom": "¿Cuál e' tu color favorito?"
      },
      // More questions...
    ],
    "mild_questions": [...],
    "spicy_questions": [...],
    "no_limits_questions": [...]
  },
  "dares": {
    "casual_dares": [...],
    "mild_dares": [...],
    "spicy_dares": [...],
    "no_limits_dares": [...]
  }
}
```

When encryption is enabled (ENCRYPTION_KEY set):

```json
{
  "encrypted": true,
  "data": "U2FsdGVkX1+A2KKGrSWUVXDFRNrTsXYeT9Lx3VQ6jWYn9..."
}
```

#### Decryption Example (Client-side)

If you receive encrypted data, you'll need to decrypt it using the same encryption key:

```javascript
import CryptoJS from 'crypto-js';

// Function to decrypt data
const decryptData = (encryptedData, secretKey) => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
  const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
  return JSON.parse(decryptedString);
};

// Example usage
const fetchGameContent = async () => {
  const response = await fetch('/api/content');
  const data = await response.json();

  if (data.encrypted) {
    // Data is encrypted, decrypt it
    const decryptedData = decryptData(data.data, 'your-encryption-key');
    return decryptedData;
  } else {
    // Data is not encrypted
    return data;
  }
};
```

### Get Specific Content

Retrieves content for a specific game mode and category.

- **URL**: `/api/content/:gameMode/:category`
- **Method**: `GET`
- **Authentication**: None
- **URL Parameters**:
  - `gameMode`: `questions` or `dares`
  - `category`: `casual_questions`, `mild_questions`, `spicy_questions`, `no_limits_questions`, `casual_dares`, `mild_dares`, `spicy_dares`, or `no_limits_dares`

#### Response

```json
[
  {
    "id": "q1",
    "text_en": "What's your favorite color?",
    "text_es": "¿Cuál es tu color favorito?",
    "text_dom": "¿Cuál e' tu color favorito?"
  },
  // More items...
]
```

## Admin Endpoints

These endpoints require authentication and are used by the admin dashboard.

### Admin Login

Authenticates an administrator and returns a JWT token.

- **URL**: `/api/admin/login`
- **Method**: `POST`
- **Authentication**: None

#### Request

```json
{
  "username": "admin",
  "password": "your_password"
}
```

#### Response

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 86400
}
```

### Get All Content (Admin)

Retrieves all content, including inactive items.

- **URL**: `/api/admin/content`
- **Method**: `GET`
- **Authentication**: Required

#### Query Parameters

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 50)
- `gameMode`: Filter by game mode (`questions` or `dares`)
- `category`: Filter by category
- `search`: Search term for text_en or text_es
- `active`: Filter by active status (`true` or `false`)

#### Response

```json
{
  "items": [
    {
      "id": "q1",
      "text_en": "What's your favorite color?",
      "text_es": "¿Cuál es tu color favorito?",
      "category": "casual_questions",
      "gameMode": "questions",
      "active": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    // More items...
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 50,
    "pages": 2
  }
}
```

### Create Content

Creates a new content item.

- **URL**: `/api/admin/content`
- **Method**: `POST`
- **Authentication**: Required

#### Request

```json
{
  "text_en": "What's your favorite movie?",
  "text_es": "¿Cuál es tu película favorita?",
  "text_dom": "¿Cuál e' tu película favorita?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true
}
```

#### Response

```json
{
  "id": "q123",
  "text_en": "What's your favorite movie?",
  "text_es": "¿Cuál es tu película favorita?",
  "text_dom": "¿Cuál e' tu película favorita?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true,
  "createdAt": "2023-06-01T00:00:00.000Z",
  "updatedAt": "2023-06-01T00:00:00.000Z"
}
```

### Get Content by ID

Retrieves a specific content item by ID.

- **URL**: `/api/admin/content/:id`
- **Method**: `GET`
- **Authentication**: Required
- **URL Parameters**:
  - `id`: Content item ID

#### Response

```json
{
  "id": "q1",
  "text_en": "What's your favorite color?",
  "text_es": "¿Cuál es tu color favorito?",
  "text_dom": "¿Cuál e' tu color favorito?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

### Update Content

Updates an existing content item.

- **URL**: `/api/admin/content/:id`
- **Method**: `PUT`
- **Authentication**: Required
- **URL Parameters**:
  - `id`: Content item ID

#### Request

```json
{
  "text_en": "What's your favorite movie of all time?",
  "text_es": "¿Cuál es tu película favorita de todos los tiempos?",
  "text_dom": "¿Cuál e' tu película favorita de todo' lo' tiempo'?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true
}
```

#### Response

```json
{
  "id": "q1",
  "text_en": "What's your favorite movie of all time?",
  "text_es": "¿Cuál es tu película favorita de todos los tiempos?",
  "text_dom": "¿Cuál e' tu película favorita de todo' lo' tiempo'?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-06-01T00:00:00.000Z"
}
```

### Delete Content

Deletes a content item.

- **URL**: `/api/admin/content/:id`
- **Method**: `DELETE`
- **Authentication**: Required
- **URL Parameters**:
  - `id`: Content item ID

#### Response

```json
{
  "message": "Content deleted successfully"
}
```

## Data Models

### Content Item

```json
{
  "id": "string",            // Unique identifier
  "text_en": "string",       // English text
  "text_es": "string",       // Spanish text
  "text_dom": "string",      // Dominican Spanish text
  "category": "string",      // e.g., "casual_questions", "mild_dares", etc.
  "gameMode": "string",      // "questions" or "dares"
  "active": "boolean",       // Whether to include in app content
  "createdAt": "date",       // Creation timestamp
  "updatedAt": "date"        // Last update timestamp
}
```

### Game Modes

- `questions`: Question-based content
- `dares`: Challenge/dare-based content

### Categories

#### Question Categories
- `casual_questions`: Casual, friendly questions
- `mild_questions`: Slightly more personal questions
- `spicy_questions`: More intimate questions
- `no_limits_questions`: Very personal questions

#### Dare Categories
- `casual_dares`: Casual, friendly challenges
- `mild_dares`: Slightly more adventurous challenges
- `spicy_dares`: More daring challenges
- `no_limits_dares`: Very adventurous challenges

## Error Handling

The API returns standard HTTP status codes:

- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required or failed
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

Error responses include a message:

```json
{
  "error": "Error message describing the issue"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- Public endpoints: 100 requests per minute
- Admin endpoints: 300 requests per minute

When rate limited, the API returns a `429 Too Many Requests` status code.

## CORS

The API supports Cross-Origin Resource Sharing (CORS) for the following origins:

- `http://localhost:3000` (development)
- `https://taptrap.app` (production)
- Mobile app origins

## API Versioning

The current API version is v1, which is implicit in the base URL. Future versions will be explicitly versioned (e.g., `/api/v2/content`).
