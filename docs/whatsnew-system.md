# What's New System Documentation

## Overview

The What's New system allows TapTrap administrators to create, manage, and display announcements about new features, updates, and improvements to users. The system includes backend API endpoints, admin panel management interface, and mobile app integration with smart caching and version comparison.

## Architecture

### Backend Components

#### 1. MongoDB Model (`backend/src/models/whatsNew.js`)
- **Collection**: `whatsNew`
- **Schema**: Stores What's New logs with type, date, title, description, video URL, and app version requirements
- **Indexes**: Optimized for creation date and app version queries

#### 2. API Routes (`backend/src/routes/whatsNew.js`)
- **Public Routes**:
  - `GET /api/whatsnew` - Fetch all logs for mobile app
  - `GET /api/whatsnew/check-new` - Check for new logs since last visit
- **Admin Routes** (Protected):
  - `POST /api/whatsnew/admin` - Create new log
  - `PUT /api/whatsnew/admin/:id` - Update existing log
  - `DELETE /api/whatsnew/admin/:id` - Delete log
  - `GET /api/whatsnew/admin` - Get all logs for admin panel

#### 3. Controller (`backend/src/controllers/whatsNewController.js`)
- Handles all CRUD operations
- Implements lightweight check for new logs
- Provides proper error handling and validation

### Mobile App Components

#### 1. API Service (`services/api.ts`)
- Centralized axios instance with proper configuration
- Follows existing patterns from contentService.ts
- Includes request/response interceptors for logging

#### 2. What's New Service (`services/whatsNewService.ts`)
- **Key Features**:
  - Smart caching with 1-hour expiration
  - Version comparison logic for update indicators
  - Offline support with cached data
  - Lightweight API calls for home screen dot indicator

#### 3. WhatsNewScreen (`screens/WhatsNewScreen.tsx`)
- **Features**:
  - Real-time data fetching with fallback to cache
  - Loading states and error handling
  - Offline detection with user alerts
  - Session-based caching for performance

#### 4. Home Screen Integration (`screens/HomeScreen.tsx`)
- **Red Dot Indicator**: Shows when new logs are available
- **Smart Checking**: Checks for new logs on app launch and screen focus
- **Performance**: Lightweight API calls that don't impact app startup

### Admin Panel Components

#### 1. What's New Management (`admin/src/components/WhatsNewManagement.jsx`)
- **Built with**: Chakra UI components
- **Features**:
  - Full CRUD interface for What's New logs
  - Form validation and error handling
  - Visual type indicators with color coding
  - Video URL support with external link buttons

#### 2. Navigation Integration
- Added to admin sidebar menu with gift icon
- Route: `/whatsnew`
- Protected by authentication middleware

## Data Structure

### WhatsNewLog Interface
```typescript
interface WhatsNewLog {
  id: string;
  type: 'dark' | 'red' | 'cyan' | 'purple';
  date: string;                    // Display date (e.g., "JUN 13, 2023")
  title_en: string;                // Log title in English
  title_es: string;                // Log title in Spanish
  title_dom: string;               // Log title in Dominican Spanish
  description_en: string;          // Feature description in English
  description_es: string;          // Feature description in Spanish
  description_dom: string;         // Feature description in Dominican Spanish
  videoUrl?: string;               // Optional demo video URL
  appVersion: string;              // Minimum required app version
  platform: 'ios' | 'android' | 'both'; // Platform compatibility
  createdAt: string;               // Creation timestamp
  updatedAt: string;               // Last update timestamp
  updateApp?: boolean;             // Calculated field for mobile app
  // Computed fields for mobile app
  title?: string;                  // Localized title based on user's language
  description?: string;            // Localized description based on user's language
}
```

## Key Features

### 1. Version Comparison Logic
- **Purpose**: Determines if a feature requires an app update
- **Implementation**: Semantic version comparison (e.g., 1.0.14 vs 1.0.10)
- **Result**: Shows "Update App" indicator when user's version is lower

### 2. Smart Caching Strategy
- **Cache Duration**: 1 hour (same as content service)
- **Storage**: AsyncStorage with encrypted data
- **Fallback**: Always provides cached data when API fails
- **Performance**: Immediate display of cached data while fetching fresh content

### 3. Red Dot Indicator System
- **Trigger**: Shows when new logs exist since last visit
- **Storage**: Last visit timestamp in AsyncStorage
- **API**: Lightweight `/check-new` endpoint for minimal data transfer
- **Reset**: Automatically removed when user visits What's New screen

### 4. Offline Support
- **Detection**: Uses NetInfo to check connectivity
- **Behavior**: Shows alert when offline, displays cached content
- **Graceful Degradation**: Never blocks user from viewing existing logs

## Usage Guide

### For Administrators

#### Creating a New Log
1. Navigate to "What's New" in admin panel
2. Click "Add New Log"
3. Fill in required fields:
   - **Type**: Visual theme (dark, red, cyan, purple)
   - **Date**: Display date as shown in app
   - **Title**: Feature name (uppercase recommended)
   - **Description**: Brief feature description
   - **App Version**: Minimum required version
   - **Video URL**: Optional demo video

#### Managing Existing Logs
- **Edit**: Click edit icon to modify any field
- **Delete**: Click delete icon with confirmation dialog
- **View Video**: Click external link icon to open video

### For Developers

#### Adding New Log Types
1. Update enum in `whatsNew.js` model
2. Add color mapping in admin component
3. Update mobile app type definitions

#### Modifying Cache Duration
```typescript
// In whatsNewService.ts
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour
```

#### Customizing Version Logic
```typescript
// In whatsNewService.ts
const isVersionLower = (currentVersion: string, requiredVersion: string): boolean => {
  // Custom version comparison logic
}
```

## API Reference

### Public Endpoints

#### GET /api/whatsnew
Returns all What's New logs for mobile app consumption.

**Response:**
```json
[
  {
    "id": "log_id",
    "type": "purple",
    "date": "DEC 15, 2024",
    "title": "NEW FEATURE",
    "description": "Feature description",
    "videoUrl": "https://youtube.com/watch?v=...",
    "appVersion": "1.0.10",
    "createdAt": "2024-12-15T10:00:00Z",
    "updatedAt": "2024-12-15T10:00:00Z"
  }
]
```

#### GET /api/whatsnew/check-new
Checks for new logs since last visit.

**Query Parameters:**
- `lastVisit` (optional): ISO timestamp of last visit

**Response:**
```json
{
  "hasNewLogs": true,
  "newLogsCount": 2
}
```

### Admin Endpoints (Protected)

#### POST /api/whatsnew/admin
Creates a new What's New log.

**Request Body:**
```json
{
  "type": "purple",
  "date": "DEC 15, 2024",
  "title": "NEW FEATURE",
  "description": "Feature description",
  "videoUrl": "https://youtube.com/watch?v=...",
  "appVersion": "1.0.10"
}
```

#### PUT /api/whatsnew/admin/:id
Updates an existing log.

#### DELETE /api/whatsnew/admin/:id
Deletes a log.

## Testing

### Backend Testing
```bash
# Start backend server
cd backend
npm run dev

# Test endpoints
curl http://localhost:5002/api/whatsnew
curl "http://localhost:5002/api/whatsnew/check-new?lastVisit=2024-12-01T00:00:00Z"
```

### Mobile App Testing
1. **Cache Testing**: Clear app data and verify offline behavior
2. **Version Testing**: Modify app version in `app.config.js`
3. **Network Testing**: Toggle airplane mode to test offline alerts

### Admin Panel Testing
1. **CRUD Operations**: Create, edit, and delete logs
2. **Form Validation**: Test required field validation
3. **Video Links**: Verify external video links open correctly

## Troubleshooting

### Common Issues

#### Mobile App Not Showing Logs
1. Check API URL in `services/contentService.ts`
2. Verify backend server is running
3. Check console logs for API errors
4. Clear AsyncStorage cache

#### Admin Panel Authentication Errors
1. Verify JWT token in localStorage
2. Check admin credentials in environment variables
3. Ensure backend auth middleware is working

#### Red Dot Not Appearing
1. Check `checkForNewLogs` function calls
2. Verify last visit timestamp storage
3. Test with fresh app installation

### Debug Logging
The system includes comprehensive logging:
- **Mobile**: Console logs prefixed with `📱 WhatsNewScreen:`
- **Backend**: Controller logs for all operations
- **Admin**: Toast notifications for user feedback

## Future Enhancements

### Potential Improvements
1. **Rich Text Support**: Markdown or HTML in descriptions
2. **Image Support**: Feature screenshots alongside videos
3. **Localization**: Multi-language support for logs
4. **Push Notifications**: Notify users of new features
5. **Analytics**: Track which logs users view most
6. **Scheduling**: Publish logs at specific times

### Performance Optimizations
1. **Pagination**: For large numbers of logs
2. **CDN Integration**: For video and image assets
3. **Background Sync**: Update cache in background
4. **Compression**: Reduce API response sizes
