# Couple Category Implementation

This document outlines the implementation of the new "couple/parejas" premium content category in TapTrap.

## Overview

The couple category is a new premium content category specifically designed for romantic partners and couples. It includes both questions and dares that focus on romantic relationships, dating, and couple dynamics.

## Implementation Details

### Category Names
- **Questions**: `couple_questions`
- **Dares**: `couple_dares`
- **Premium Status**: Yes (requires subscription or ad-based access)
- **Icon**: `assets/redesign/icon_couple.svg`

### Backend Changes

#### Database Model (`backend/src/models/content.js`)
- Added `couple_questions` and `couple_dares` to the category enum
- Maintains backward compatibility with existing categories

#### Content Controller (`backend/src/controllers/contentController.js`)
- Updated `formatGameContent` function to include couple categories
- Added couple categories to the response structure

#### Content Service (`backend/src/services/content.service.js`)
- Updated `getFormattedContent` to include couple categories in the response structure

#### Content DTO (`backend/src/dtos/content.dto.js`)
- Added couple categories to the validation logic

#### AI Content Generation
- **Prompt Files**: 
  - `backend/src/prompts/categories/couple_questions.js`
  - `backend/src/prompts/categories/couple_dares.js`
- **Prompt Manager**: Updated to include couple category prompts
- **Content Focus**: Romantic relationships, dating, couple dynamics

### Mobile App Changes

#### Content Service (`services/contentService.ts`)
- Updated `GameContent` interface to include couple categories
- Added couple categories to `createEmptyGameContent` function
- Updated fallback content handling for couple categories

#### Category Selection (`screens/SelectCategoriesScreen.tsx`)
- Added couple category button with proper icon and translations
- Updated premium category logic to include couple categories
- Added couple category to cleanup logic for premium access

#### Game Context (`components/game/GameContext.tsx`)
- Updated type definitions to include couple categories
- Added couple categories to premium category checks

#### Ad-Based Access (`context/AdBasedAccessContext.tsx`)
- Updated free content methods to support couple categories
- Maintains shared counter across all premium categories

#### Touch Game Screen (`screens/TouchGameScreen.tsx`)
- Updated premium category checks to include couple categories
- Added couple categories to ad-based access tracking

#### Translations (`services/i18nService.tsx`)
- Added translations for the couple category:
  - English: "Couple"
  - Spanish: "Parejas"
  - Dominican: "Parejas"

### Database Seeding

#### Seed File (`backend/seeds/couple-category-seed.json`)
Contains example content for the couple category:
- 2 example questions
- 2 example dares
- All content includes English, Spanish, and Dominican translations

## Backward Compatibility

### Graceful Fallback
- Older app versions that don't recognize the couple category will simply ignore it
- Content filtering continues to work for existing categories
- No breaking changes to existing API responses

### Content Structure
- The couple category follows the same structure as existing categories
- Uses the same multi-language format (text_en, text_es, text_dom)
- Maintains platform compatibility fields

## Usage

### For Developers

#### Adding Couple Content via Admin Panel
1. Select "Couple" from the category dropdown
2. Choose "Questions" or "Dares" for game mode
3. Content will be automatically categorized as premium

#### API Response Structure
```json
{
  "questions": {
    "casual_questions": [...],
    "mild_questions": [...],
    "spicy_questions": [...],
    "no_limits_questions": [...],
    "couple_questions": [...]
  },
  "dares": {
    "casual_dares": [...],
    "mild_dares": [...],
    "spicy_dares": [...],
    "no_limits_dares": [...],
    "couple_dares": [...]
  }
}
```

### For Users

#### Access Requirements
- Premium subscription OR
- Ad-based temporary access (watch ads to unlock)

#### Category Features
- Romantic and relationship-focused content
- Designed specifically for couples
- Intimate but not explicitly sexual content
- Available in English, Spanish, and Dominican Spanish

## Testing

### Recommended Test Cases
1. **Category Selection**: Verify couple category appears and functions correctly
2. **Premium Access**: Test both subscription and ad-based access
3. **Content Loading**: Ensure couple content loads properly from API
4. **Backward Compatibility**: Test with older app versions
5. **Translations**: Verify all language variants work correctly
6. **Ad-Based Access**: Test free content counter with couple categories

### Database Population
Use the seed file to populate the database with example content:
```bash
# Import the seed data into MongoDB
mongoimport --db taptrap --collection content --file backend/seeds/couple-category-seed.json --jsonArray
```

## Future Considerations

### Content Expansion
- More couple-specific questions and dares can be added
- Consider seasonal or themed couple content
- Potential for couple challenges or mini-games

### Feature Enhancements
- Couple-specific game modes
- Relationship milestone tracking
- Partner preference settings

## Troubleshooting

### Common Issues
1. **Category Not Appearing**: Check premium access status
2. **Content Not Loading**: Verify API response includes couple categories
3. **Translation Issues**: Ensure i18n service includes couple translations
4. **Premium Access**: Verify ad-based access includes couple categories

### Debug Information
- Check console logs for category loading
- Verify API response structure
- Test premium access logic
- Validate content filtering
