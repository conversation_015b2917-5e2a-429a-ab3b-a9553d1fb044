# Analytics Chart Implementation

## Overview

This document describes the implementation of the analytics chart component in the TapTrap admin dashboard. The chart displays content analytics data with time-based filtering capabilities.

## Features

### 1. **Chart Data Display**
- Shows the number of questions and dares added per category
- Separate counts for questions vs dares
- Breaks down data by game categories (casual, mild, spicy, no_limits)
- Visual bar chart representation using Recharts library

### 2. **Time Period Filter**
- This week
- Last week  
- This month
- Last month
- Last 3 months
- All time

### 3. **Summary Statistics**
- Total content count for selected period
- Total questions count with percentage
- Total dares count with percentage

## Implementation Details

### Backend Components

#### 1. **Analytics API Endpoint**
- **Route**: `GET /admin/analytics?timePeriod={period}`
- **Controller**: `adminController.getAnalytics`
- **File**: `backend/src/controllers/adminController.js`

#### 2. **MongoDB Aggregation**
The analytics endpoint uses MongoDB aggregation pipeline to:
- Filter content by time period and active status
- Group by category and game mode
- Calculate counts for questions and dares
- Transform data for chart consumption

```javascript
const pipeline = [
  { $match: { active: true, ...dateFilter } },
  {
    $group: {
      _id: { category: '$category', gameMode: '$gameMode' },
      count: { $sum: 1 },
      createdDates: { $push: '$createdAt' }
    }
  },
  {
    $group: {
      _id: '$_id.category',
      questions: {
        $sum: { $cond: [{ $eq: ['$_id.gameMode', 'questions'] }, '$count', 0] }
      },
      dares: {
        $sum: { $cond: [{ $eq: ['$_id.gameMode', 'dares'] }, '$count', 0] }
      },
      total: { $sum: '$count' }
    }
  },
  { $sort: { _id: 1 } }
];
```

### Frontend Components

#### 1. **AnalyticsChart Component**
- **File**: `admin/src/components/AnalyticsChart.jsx`
- **Features**:
  - Responsive bar chart using Recharts
  - Loading and error states
  - Custom tooltip
  - Summary statistics cards
  - Time period filter integration

#### 2. **TimePeriodFilter Component**
- **File**: `admin/src/components/TimePeriodFilter.jsx`
- **Features**:
  - Dropdown select for time periods
  - Chakra UI styling
  - Loading state support

#### 3. **Analytics Service**
- **File**: `admin/src/services/analytics.service.js`
- **Features**:
  - API communication
  - Time period options configuration
  - Error handling

### Dashboard Integration

The analytics chart is integrated into the main dashboard page (`admin/src/pages/Dashboard.js`) and appears above the existing legacy category view.

## Data Structure

### API Response Format
```json
{
  "timePeriod": "thisMonth",
  "chartData": [
    {
      "category": "Casual",
      "questions": 45,
      "dares": 32,
      "total": 77
    },
    {
      "category": "Mild",
      "questions": 38,
      "dares": 28,
      "total": 66
    }
  ],
  "summary": {
    "totalContent": 143,
    "totalQuestions": 83,
    "totalDares": 60
  }
}
```

## Dependencies

### New Dependencies Added
- **recharts**: React charting library for data visualization
  - Installed via: `npm install recharts`
  - Used for: Bar charts, responsive containers, tooltips, legends

### Existing Dependencies Used
- **@chakra-ui/react**: UI components and styling
- **axios**: HTTP client for API requests
- **react**: Core React functionality

## Usage

1. **Access the Dashboard**: Navigate to the admin dashboard home page
2. **View Analytics**: The analytics chart appears automatically below the summary statistics
3. **Filter by Time**: Use the time period dropdown to filter data by different time ranges
4. **Interpret Data**: 
   - Blue bars represent questions
   - Green bars represent dares
   - Hover over bars for detailed tooltips
   - Summary cards show totals and percentages

## Responsive Design

The chart is fully responsive and adapts to different screen sizes:
- **Desktop**: Full-width chart with all features visible
- **Tablet**: Responsive grid layout for summary cards
- **Mobile**: Stacked layout with rotated category labels

## Error Handling

- **Loading States**: Spinner displayed while fetching data
- **Error States**: Alert component with error message
- **No Data**: Informational alert when no data is available
- **Network Errors**: Graceful error handling with retry capability

## Future Enhancements

Potential improvements for the analytics feature:
1. **Additional Chart Types**: Line charts, pie charts for different data views
2. **Export Functionality**: Download charts as images or data as CSV
3. **Real-time Updates**: WebSocket integration for live data updates
4. **Advanced Filters**: Filter by specific categories, languages, or content status
5. **Comparison Views**: Side-by-side comparison of different time periods
