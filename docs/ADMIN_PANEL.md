# TapTrap Admin Panel Documentation

## Overview

The TapTrap Admin Panel is a React-based web application that provides an interface for managing game content. It allows administrators to create, read, update, and delete questions and challenges used in the TapTrap mobile app.

## Table of Contents

1. [Features](#features)
2. [Architecture](#architecture)
3. [Installation and Setup](#installation-and-setup)
4. [Usage Guide](#usage-guide)
5. [Deployment](#deployment)
6. [Troubleshooting](#troubleshooting)

## Features

- **Authentication**: Secure login system for administrators
- **Dashboard**: Overview of content statistics
- **Content Management**: CRUD operations for game content
  - List all content with filtering and search
  - Add new questions and challenges
  - Edit existing content
  - Delete content
- **Responsive Design**: Works on desktop and mobile devices
- **API Integration**: Communicates with the TapTrap backend API

## Architecture

### Technology Stack

- **Frontend Framework**: React
- **UI Library**: Chakra UI
- **State Management**: React Context API
- **Routing**: React Router
- **HTTP Client**: Axios
- **Authentication**: JWT (JSON Web Tokens)

### Component Structure

```
admin/
├── src/
│   ├── components/       # Reusable UI components
│   │   ├── Layout.js     # Main layout wrapper
│   │   └── PrivateRoute.js # Authentication wrapper
│   ├── context/          # React context providers
│   │   └── AuthContext.js # Authentication state management
│   ├── hooks/            # Custom React hooks
│   │   └── useAuth.js    # Authentication hook
│   ├── pages/            # Application pages
│   │   ├── AddContent.js # Add new content form
│   │   ├── ContentList.js # Content listing page
│   │   ├── Dashboard.js  # Main dashboard
│   │   ├── EditContent.js # Edit content form
│   │   └── Login.js      # Login page
│   ├── services/         # API and utility services
│   │   └── api.js        # API communication service
│   ├── App.js            # Main application component
│   └── index.js          # Application entry point
└── public/               # Static assets
```

### Authentication Flow

1. User enters credentials on the login page
2. Credentials are sent to the backend API
3. If valid, the API returns a JWT token
4. The token is stored in localStorage
5. Protected routes check for the token's presence
6. API requests include the token in headers
7. If the token expires, the user is redirected to login

## Installation and Setup

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- TapTrap backend API running

### Local Development Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/TapTrap.git
   cd TapTrap/admin
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Configure environment variables:
   - Create a `.env.development.local` file with:
     ```
     REACT_APP_API_URL=http://localhost:5001/api
     ```

4. Start the development server:
   ```bash
   npm start
   # or
   yarn start
   ```

5. Access the admin panel at `http://localhost:3000`

## Usage Guide

### Logging In

1. Navigate to the login page
2. Enter your administrator credentials
3. Click "Login"

### Dashboard

The dashboard provides an overview of:
- Total number of questions and challenges
- Distribution by category
- Recent activity

### Managing Content

#### Viewing Content

1. Navigate to the "Content" section
2. Use filters to narrow down the list:
   - Game mode (Questions/Challenges)
   - Category (Casual, Mild, Spicy, No Limits)
   - Active status
3. Use the search box to find specific content

#### Adding Content

1. Click "Add New Content"
2. Fill in the form:
   - English text
   - Spanish text
   - Game mode (Questions/Challenges)
   - Category
   - Active status
3. Click "Save"

#### Editing Content

1. Find the content item in the list
2. Click the "Edit" button
3. Modify the form fields
4. Click "Save"

#### Deleting Content

1. Find the content item in the list
2. Click the "Delete" button
3. Confirm the deletion

## Deployment

The TapTrap Admin Panel is designed to be deployed to Vercel, with API requests proxied to the backend.

### Vercel Deployment

1. Configure environment variables in Vercel:
   - `REACT_APP_API_URL`: Set to `/api` for production

2. Set up the Vercel project:
   - Framework preset: Create React App
   - Build command: `npm run vercel-build`
   - Output directory: `build`

3. Configure API rewrites in `vercel.json`:
   ```json
   {
     "rewrites": [
       {
         "source": "/api/:path*",
         "destination": "https://your-backend-url.com/api/:path*"
       }
     ]
   }
   ```

For detailed deployment instructions, see [VERCEL_DEPLOYMENT.md](../admin/VERCEL_DEPLOYMENT.md).

## Troubleshooting

### Common Issues

#### Authentication Problems

- **"Invalid credentials"**: Verify username and password
- **"Server error during login"**: Check backend API logs
- **Automatic logout**: JWT token may have expired

#### API Connection Issues

- **"Network Error"**: Check if the backend API is running
- **CORS Errors**: Ensure the backend allows requests from the admin panel's origin
- **404 Errors**: Verify API endpoint paths

#### Content Management Issues

- **Content not loading**: Check authentication and API connection
- **Cannot save changes**: Verify form data and API responses
- **Changes not reflected in app**: Content may need to be marked as active

For more detailed troubleshooting, see [TROUBLESHOOTING.md](../admin/TROUBLESHOOTING.md).

## Security Considerations

- The admin panel uses JWT for authentication
- All API requests to protected endpoints require a valid token
- Passwords are never stored in the frontend
- API requests are made over HTTPS in production
- Environment variables are used for sensitive configuration

## Development Guidelines

### Adding New Features

1. Create a new branch for your feature
2. Implement the feature using the existing patterns
3. Test thoroughly
4. Submit a pull request

### Code Style

- Follow React best practices
- Use functional components with hooks
- Maintain consistent error handling
- Document complex logic with comments

## API Integration

The admin panel communicates with the backend API using the service defined in `services/api.js`.

### Key API Endpoints

- `POST /api/admin/login`: Authentication
- `GET /api/admin/content`: List all content
- `GET /api/admin/content/:id`: Get content by ID
- `POST /api/admin/content`: Create new content
- `PUT /api/admin/content/:id`: Update content
- `DELETE /api/admin/content/:id`: Delete content

For full API documentation, see [API.md](API.md).
