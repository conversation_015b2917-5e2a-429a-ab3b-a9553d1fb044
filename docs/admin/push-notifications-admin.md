# Panel de Administración - Push Notifications TapTrap

## Descripción General

El panel de administración de push notifications permite a los administradores gestionar dispositivos registrados, enviar notificaciones dirigidas y monitorear estadísticas del sistema. Incluye soporte completo para filtrado por idioma y análisis detallado.

## Interfaz de Usuario

### Página Principal: Push Notifications

**Archivo**: `admin/src/pages/PushNotifications.js`

La página principal del panel de administración incluye:

1. **Dashboard de Estadísticas**
2. **Distribución por Idiomas**
3. **Formulario de Envío**
4. **Lista de Dispositivos Registrados**

## Componentes del Dashboard

### 1. Estadísticas Generales

```jsx
<SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
  <Stat>
    <StatLabel>Total Devices</StatLabel>
    <StatNumber>{tokens.length}</StatNumber>
    <StatHelpText>Registered for notifications</StatHelpText>
  </Stat>
  
  <Stat>
    <StatLabel>iOS Devices</StatLabel>
    <StatNumber>{iosTokens.length}</StatNumber>
    <StatHelpText>Apple devices</StatHelpText>
  </Stat>
  
  <Stat>
    <StatLabel>Android Devices</StatLabel>
    <StatNumber>{androidTokens.length}</StatNumber>
    <StatHelpText>Android devices</StatHelpText>
  </Stat>
</SimpleGrid>
```

### 2. Distribución por Idiomas

```jsx
<Card>
  <CardHeader>
    <Heading size="md">Language Distribution</Heading>
  </CardHeader>
  <CardBody>
    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
      <Stat>
        <StatLabel>English</StatLabel>
        <StatNumber>{englishTokens.length}</StatNumber>
        <StatHelpText>EN users</StatHelpText>
      </Stat>
      
      <Stat>
        <StatLabel>Spanish</StatLabel>
        <StatNumber>{spanishTokens.length}</StatNumber>
        <StatHelpText>ES users</StatHelpText>
      </Stat>
      
      <Stat>
        <StatLabel>Dominican</StatLabel>
        <StatNumber>{dominicanTokens.length}</StatNumber>
        <StatHelpText>DOM users</StatHelpText>
      </Stat>
    </SimpleGrid>
  </CardBody>
</Card>
```

## Formulario de Envío de Notificaciones

### Campos del Formulario

| Campo | Tipo | Requerido | Descripción |
|-------|------|-----------|-------------|
| **Título** | Input | ✅ | Título de la notificación |
| **Mensaje** | Textarea | ✅ | Cuerpo del mensaje |
| **Idioma Objetivo** | Select | ❌ | Filtro por idioma |

### Selector de Idioma

```jsx
<FormControl>
  <FormLabel>Target Language</FormLabel>
  <Select
    placeholder="All Languages"
    value={notification.language}
    onChange={(e) => setNotification(prev => ({ ...prev, language: e.target.value }))}
  >
    <option value="en">English ({englishTokens.length} devices)</option>
    <option value="es">Spanish ({spanishTokens.length} devices)</option>
    <option value="dom">Dominican ({dominicanTokens.length} devices)</option>
  </Select>
</FormControl>
```

### Botón de Envío Dinámico

```jsx
<Button
  colorScheme="blue"
  onClick={handleSendNotification}
  isLoading={sending}
  loadingText="Sending..."
  leftIcon={<FiSend />}
  isDisabled={!notification.title.trim() || !notification.body.trim()}
>
  {notification.language 
    ? `Send to ${notification.language.toUpperCase()} Users (${getTargetCount()})`
    : `Send to All Devices (${getTargetCount()})`
  }
</Button>
```

## Lista de Dispositivos

### Tabla de Dispositivos Registrados

| Columna | Descripción | Formato |
|---------|-------------|---------|
| **Platform** | iOS/Android | Badge con color |
| **Language** | Idioma del usuario | Badge con color |
| **Device ID** | ID único del dispositivo | Texto monospace |
| **Token Preview** | Primeros 20 caracteres | Texto truncado |
| **Registered** | Fecha de registro | Formato localizado |
| **Last Used** | Última actividad | Formato localizado |

### Códigos de Color

```jsx
// Colores por plataforma
const getPlatformColor = (platform) => {
  return platform === 'ios' ? 'blue' : 'green';
};

// Colores por idioma
const getLanguageColor = (language) => {
  switch(language) {
    case 'en': return 'blue';
    case 'es': return 'green';
    case 'dom': return 'purple';
    default: return 'gray';
  }
};
```

## Lógica de Negocio

### Estado del Componente

```jsx
const [tokens, setTokens] = useState([]);
const [loading, setLoading] = useState(true);
const [sending, setSending] = useState(false);
const [notification, setNotification] = useState({
  title: '',
  body: '',
  data: {},
  language: '' // Vacío = todos los idiomas
});
```

### Cálculo de Estadísticas

```jsx
// Filtros por plataforma
const iosTokens = tokens.filter(token => token.platform === 'ios');
const androidTokens = tokens.filter(token => token.platform === 'android');

// Filtros por idioma
const englishTokens = tokens.filter(token => token.language === 'en');
const spanishTokens = tokens.filter(token => token.language === 'es');
const dominicanTokens = tokens.filter(token => token.language === 'dom');

// Conteo objetivo basado en idioma seleccionado
const getTargetCount = () => {
  if (!notification.language) return tokens.length;
  switch (notification.language) {
    case 'en': return englishTokens.length;
    case 'es': return spanishTokens.length;
    case 'dom': return dominicanTokens.length;
    default: return tokens.length;
  }
};
```

### Envío de Notificaciones

```jsx
const handleSendNotification = async () => {
  // Validación
  if (!notification.title.trim() || !notification.body.trim()) {
    toast({
      title: 'Validation Error',
      description: 'Title and body are required',
      status: 'warning'
    });
    return;
  }

  try {
    setSending(true);
    const response = await api.post('/admin/push-notifications/send', notification);
    
    toast({
      title: 'Success',
      description: response.data.message,
      status: 'success'
    });

    // Limpiar formulario
    setNotification({
      title: '',
      body: '',
      data: {},
      language: ''
    });

  } catch (error) {
    toast({
      title: 'Error',
      description: error.response?.data?.message || 'Failed to send notification',
      status: 'error'
    });
  } finally {
    setSending(false);
  }
};
```

## Casos de Uso

### 1. Envío a Todos los Usuarios

```jsx
// Configuración
{
  title: "New Content Available!",
  body: "Check out our latest questions and dares",
  language: "" // Vacío = todos
}

// Resultado esperado
"Notifications sent: 150 successful, 2 failed"
```

### 2. Envío a Usuarios en Español

```jsx
// Configuración
{
  title: "¡Nuevo Contenido Disponible!",
  body: "Revisa nuestras últimas preguntas y retos",
  language: "es"
}

// Resultado esperado
"Notifications sent to es users: 45 successful, 0 failed"
```

### 3. Envío a Usuarios Dominicanos

```jsx
// Configuración
{
  title: "¡Contenido Nuevo!",
  body: "Dale un vistazo a las nuevas preguntas",
  language: "dom"
}

// Resultado esperado
"Notifications sent to dom users: 12 successful, 1 failed"
```

## Manejo de Errores

### Errores de Validación

```jsx
// Campos requeridos vacíos
{
  title: 'Validation Error',
  description: 'Title and body are required',
  status: 'warning'
}
```

### Errores de Red

```jsx
// Error de conexión al backend
{
  title: 'Error',
  description: 'Failed to send notification',
  status: 'error'
}
```

### Sin Dispositivos Objetivo

```jsx
// No hay dispositivos para el idioma seleccionado
{
  message: "No registered devices found for language: dom",
  sent: 0,
  failed: 0,
  language: "dom"
}
```

## Autenticación y Seguridad

### Middleware de Autenticación

```jsx
// Todas las rutas de admin requieren JWT válido
import api from '../services/api';

// api.js incluye automáticamente el token
const response = await api.post('/admin/push-notifications/send', data);
```

### Validación de Permisos

- Solo usuarios con rol de administrador pueden acceder
- Tokens JWT se validan en cada request
- Logs de actividad se registran para auditoría

## Monitoreo y Analytics

### Métricas Disponibles

1. **Total de Dispositivos Registrados**
2. **Distribución por Plataforma** (iOS/Android)
3. **Distribución por Idioma** (EN/ES/DOM)
4. **Tasa de Éxito de Envío**
5. **Dispositivos Activos vs Inactivos**

### Reportes de Envío

```jsx
// Respuesta típica de envío exitoso
{
  message: "Notifications sent to es users: 45 successful, 0 failed",
  sent: 45,
  failed: 0,
  language: "es",
  totalTargeted: 45
}
```

## Mejores Prácticas

### 1. Mensajes Localizados
- Usar idioma apropiado para cada audiencia
- Considerar diferencias culturales (ES vs DOM)
- Mantener mensajes concisos y claros

### 2. Timing de Envío
- Evitar horas de madrugada
- Considerar zonas horarias de usuarios
- No enviar demasiadas notificaciones seguidas

### 3. Segmentación Efectiva
- Usar filtros de idioma apropiadamente
- Considerar contenido relevante por región
- Monitorear tasas de engagement

### 4. Testing
- Probar con dispositivos de prueba primero
- Verificar formato en diferentes plataformas
- Confirmar que links y datos adicionales funcionan
