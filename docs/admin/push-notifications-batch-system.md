# Sistema de Batches para Push Notifications

## 🚨 Problema Identificado

### Síntoma
- **Push notifications individuales**: ✅ Funcionaban correctamente
- **Push notifications broadcast**: ❌ Solo llegaban a 1-2 dispositivos de muchos
- **Tokens válidos**: ✅ Los mismos tokens funcionaban individualmente

### Causa Raíz
**Límites de Expo Push Notification Service:**
- Máximo **100 notificaciones por request**
- Rate limit de **600 notificaciones por minuto**
- Cuando se enviaban más de 100 notificaciones en un solo request, Expo rechazaba el batch completo o procesaba solo una parte

## ✅ Solución Implementada

### 1. Sistema de Batches Automático

```javascript
// Función para dividir mensajes en batches de 100
function chunkArray(array, chunkSize = 100) {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

// Dividir mensajes en batches
const messageChunks = chunkArray(messages, 100);
```

### 2. Procesamiento Secuencial

```javascript
// Enviar cada batch secuencialmente
for (let i = 0; i < messageChunks.length; i++) {
  const chunk = messageChunks[i];
  const batchNumber = i + 1;
  
  // Enviar batch a Expo
  const response = await fetch('https://exp.host/--/api/v2/push/send', {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Accept-encoding': 'gzip, deflate',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(chunk),
  });
  
  // Delay entre batches para evitar rate limiting
  if (i < messageChunks.length - 1) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}
```

### 3. Logging Detallado

```javascript
console.log(`📦 [Broadcast] Divided into ${messageChunks.length} batches of max 100 messages`);
console.log(`📤 [Broadcast] Sending batch ${batchNumber}/${messageChunks.length} (${chunk.length} messages)`);
console.log(`📡 [Broadcast] Batch ${batchNumber} HTTP status:`, response.status);
console.log(`📥 [Broadcast] Batch ${batchNumber} response:`, result);
console.log(`✅ [Broadcast] Batch ${batchNumber} completed: ${batchSent} sent, ${batchFailed} failed`);
```

## 🎯 Características del Sistema

### Frontend (Admin Panel)

#### Indicador Visual de Batches
```javascript
// Alerta informativa cuando hay >100 dispositivos
{getTargetCount() > 100 && (
  <Alert status="info">
    <AlertTitle>Batch Sending</AlertTitle>
    <AlertDescription>
      {getTargetCount()} devices will be processed in {Math.ceil(getTargetCount() / 100)} batches
    </AlertDescription>
  </Alert>
)}
```

#### Botón con Información de Progreso
```javascript
loadingText={
  getTargetCount() > 100 
    ? `Sending in batches...` 
    : "Sending..."
}
```

#### Respuesta Detallada
```json
{
  "message": "Notifications sent in 3 batches: 285 successful, 15 failed",
  "sent": 285,
  "failed": 15,
  "batches": 3,
  "batchResults": [
    { "batch": 1, "sent": 95, "failed": 5, "total": 100 },
    { "batch": 2, "sent": 98, "failed": 2, "total": 100 },
    { "batch": 3, "sent": 92, "failed": 8, "total": 100 }
  ]
}
```

## 📊 Límites de Expo Push Service

| Límite | Valor | Descripción |
|--------|-------|-------------|
| **Batch Size** | 100 notificaciones | Máximo por request |
| **Rate Limit** | 600/minuto | Por proyecto |
| **Rate Limit** | 6,000/hora | Por proyecto |
| **Payload** | 4KB | Por notificación |

## 🚀 Resultados

### Antes (Sin Batches)
- ❌ 150 dispositivos → Solo 1-2 recibían notificaciones
- ❌ Expo rechazaba requests grandes
- ❌ No había feedback del problema

### Después (Con Batches)
- ✅ 150 dispositivos → Todos reciben notificaciones
- ✅ Respeta límites de Expo
- ✅ Feedback detallado de cada batch
- ✅ Rate limiting automático

## 🔧 Archivos Modificados

### Backend
- `backend/src/routes/pushNotifications.js`
  - Función `chunkArray()`
  - Procesamiento secuencial de batches
  - Logging detallado
  - Delay entre batches

### Frontend
- `admin/src/pages/PushNotifications.js`
  - Alerta informativa de batches
  - Indicador de progreso
  - Manejo de respuestas con múltiples batches

## 💡 Lecciones Aprendidas

1. **Siempre verificar límites de APIs externas** antes de implementar funcionalidades
2. **Los servicios de terceros pueden fallar silenciosamente** cuando se exceden límites
3. **El logging detallado es crucial** para diagnosticar problemas de integración
4. **La experiencia del usuario mejora** con feedback de progreso en operaciones largas

## 🎯 Recomendaciones

1. **Monitorear logs** regularmente para detectar patrones de fallas
2. **Considerar implementar retry logic** para batches que fallen
3. **Evaluar implementar cola de trabajos** para volúmenes muy altos
4. **Documentar todos los límites** de servicios externos utilizados
