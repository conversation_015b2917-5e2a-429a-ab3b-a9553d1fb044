# TapTrap API v2 - Technical Implementation Guide

## Architecture Overview

### Directory Structure
```
backend/src/
├── v2/                          # API v2 implementation
│   ├── routes/                  # v2 route handlers
│   │   ├── content.js
│   │   ├── penalty.js
│   │   ├── admin.js
│   │   └── ...
│   └── controllers/             # v2 controllers
│       ├── contentController.js
│       ├── penaltyController.js
│       └── ...
├── models/                      # Shared models (v1 & v2)
│   ├── migrationSettings.js    # New migration model
│   └── ...
├── services/                    # Shared services
│   ├── migrationService.js     # New migration service
│   └── ...
└── controllers/                 # v1 controllers (modified)
    ├── contentController.js    # Now checks migration
    └── penaltyController.js    # Now checks migration
```

### API Routing
```javascript
// backend/src/index.js

// API v1 Routes (legacy - affected by migration)
app.use('/api/content', contentRoutes);
app.use('/api/penalties', penaltyRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/admin/migration', migrationRoutes); // New

// API v2 Routes (current - unaffected by migration)
app.use('/api/v2/content', contentRoutesV2);
app.use('/api/v2/penalties', penaltyRoutesV2);
app.use('/api/v2/admin', adminRoutesV2);
```

## Migration Service Implementation

### Core Service Methods

```javascript
// backend/src/services/migrationService.js

class MigrationService {
  // Check if migration is enabled
  static async isMigrationEnabled() {
    const settings = await this.getCurrentSettings();
    return settings.migrationEnabled;
  }

  // Create migration content response
  static async createMigrationContentResponse() {
    const message = await this.getMigrationMessage();
    const migrationItem = {
      id: 'migration-message',
      text_en: message.field_en,
      text_es: message.field_es,
      text_dom: message.field_dom
    };

    return {
      questions: {
        casual_questions: [migrationItem],
        mild_questions: [migrationItem],
        // ... all categories
      },
      dares: {
        casual_dares: [migrationItem],
        mild_dares: [migrationItem],
        // ... all categories
      }
    };
  }
}
```

### Controller Integration

```javascript
// backend/src/controllers/contentController.js (v1)

exports.getAllContent = async (req, res) => {
  try {
    // Check migration status
    const isMigrationEnabled = await MigrationService.isMigrationEnabled();
    
    if (isMigrationEnabled) {
      // Return migration message
      const migrationContent = await MigrationService.createMigrationContentResponse();
      
      // Apply encryption if enabled
      if (process.env.ENCRYPTION_KEY) {
        const encryptedData = encrypt(migrationContent, process.env.ENCRYPTION_KEY);
        return res.json({ encrypted: true, data: encryptedData });
      }
      
      return res.json(migrationContent);
    }

    // Normal operation - return real content
    const items = await Content.find({ active: true });
    const formattedContent = formatGameContent(items);
    
    // ... rest of normal logic
  } catch (error) {
    // ... error handling
  }
};
```

## Database Schema

### Migration Settings Model

```javascript
// backend/src/models/migrationSettings.js

const migrationSettingsSchemaDefinition = {
  settingsId: {
    type: String,
    required: true,
    unique: true,
    default: 'default'
  },
  migrationEnabled: {
    type: Boolean,
    required: true,
    default: false
  },
  migrationMessage: {
    field_en: {
      type: String,
      required: true,
      default: 'Please download the new version'
    },
    field_es: {
      type: String,
      required: true,
      default: 'Por favor descarga la nueva versión'
    },
    field_dom: {
      type: String,
      required: true,
      default: 'Por favor descarga la nueva versión'
    }
  },
  migrationEnabledAt: { type: Date, default: null },
  migrationDisabledAt: { type: Date, default: null },
  lastModifiedBy: { type: String, default: 'system' }
};
```

### Static Methods

```javascript
// Get current settings (creates default if none exist)
migrationSettingsSchema.statics.getCurrentSettings = async function() {
  let settings = await this.findOne({ settingsId: 'default' });
  
  if (!settings) {
    settings = new this({ settingsId: 'default', migrationEnabled: false });
    await settings.save();
  }
  
  return settings;
};

// Toggle migration with tracking
migrationSettingsSchema.statics.toggleMigration = async function(enabled, modifiedBy = 'admin') {
  const settings = await this.getCurrentSettings();
  settings.migrationEnabled = enabled;
  settings.lastModifiedBy = modifiedBy;
  await settings.save();
  return settings;
};
```

## Admin Panel Integration

### Migration Service

```javascript
// admin/src/services/migration.service.js

export const toggleMigration = async (enabled) => {
  const response = await api.post('/admin/migration/toggle', { enabled });
  return response.data;
};

export const getMigrationSettings = async () => {
  const response = await api.get('/admin/migration');
  return response.data;
};
```

### React Component

```jsx
// admin/src/pages/MigrationSettings.js

const MigrationSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);

  const handleToggleMigration = async () => {
    try {
      const newEnabled = !settings.migrationEnabled;
      const response = await toggleMigration(newEnabled);
      setSettings(response.data);
      
      toast({
        title: 'Success',
        description: `Migration ${newEnabled ? 'enabled' : 'disabled'} successfully`,
        status: 'success'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to toggle migration',
        status: 'error'
      });
    }
  };

  return (
    <Layout>
      <Switch
        isChecked={settings?.migrationEnabled || false}
        onChange={handleToggleMigration}
        colorScheme={settings?.migrationEnabled ? 'red' : 'green'}
      />
    </Layout>
  );
};
```

## API Endpoints

### Migration Management

```javascript
// backend/src/routes/migration.js

// Get current migration settings
router.get('/', migrationController.getMigrationSettings);

// Toggle migration on/off
router.post('/toggle', migrationController.toggleMigration);

// Update migration message
router.put('/message', migrationController.updateMigrationMessage);

// Get migration status (lightweight)
router.get('/status', migrationController.getMigrationStatus);
```

### Controller Implementation

```javascript
// backend/src/controllers/migrationController.js

exports.toggleMigration = async (req, res) => {
  try {
    const { enabled } = req.body;
    const modifiedBy = req.user?.email || 'admin';
    
    if (typeof enabled !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'enabled field must be a boolean value'
      });
    }
    
    const settings = await MigrationService.toggleMigration(enabled, modifiedBy);
    
    res.status(200).json({
      success: true,
      message: `Migration ${enabled ? 'enabled' : 'disabled'} successfully`,
      data: settings
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to toggle migration',
      error: error.message
    });
  }
};
```

## Testing Implementation

### Unit Tests

```javascript
// tests/migration.test.js

describe('Migration Service', () => {
  test('should return false when migration is disabled', async () => {
    const isEnabled = await MigrationService.isMigrationEnabled();
    expect(isEnabled).toBe(false);
  });

  test('should create migration content response', async () => {
    const migrationContent = await MigrationService.createMigrationContentResponse();
    
    expect(migrationContent.questions.casual_questions).toHaveLength(1);
    expect(migrationContent.questions.casual_questions[0].text_en).toBe('Please download the new version');
  });
});
```

### Integration Tests

```javascript
// tests/api.test.js

describe('API v1 Migration', () => {
  test('should return migration message when enabled', async () => {
    // Enable migration
    await MigrationService.toggleMigration(true);
    
    // Test content endpoint
    const response = await request(app).get('/api/content');
    expect(response.body.questions.casual_questions[0].text_en).toBe('Please download the new version');
  });

  test('should return normal content when disabled', async () => {
    // Disable migration
    await MigrationService.toggleMigration(false);
    
    // Test content endpoint
    const response = await request(app).get('/api/content');
    expect(response.body.questions.casual_questions.length).toBeGreaterThan(1);
  });
});
```

## Performance Considerations

### Caching Migration Status

```javascript
// Optional: Cache migration status to reduce DB queries
let migrationStatusCache = null;
let cacheExpiry = 0;

const getCachedMigrationStatus = async () => {
  const now = Date.now();
  
  if (!migrationStatusCache || now > cacheExpiry) {
    migrationStatusCache = await MigrationService.isMigrationEnabled();
    cacheExpiry = now + (30 * 1000); // Cache for 30 seconds
  }
  
  return migrationStatusCache;
};
```

### Database Indexing

```javascript
// Ensure migration settings are indexed for fast retrieval
migrationSettingsSchema.index({ settingsId: 1 });
```

## Security Implementation

### Authentication Middleware

```javascript
// All migration endpoints require admin authentication
router.use(authMiddleware);

// Verify admin role
const authMiddleware = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    if (decoded.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Token is not valid' });
  }
};
```

### Audit Logging

```javascript
// Log all migration changes
exports.toggleMigration = async (req, res) => {
  const { enabled } = req.body;
  const modifiedBy = req.user?.email || 'admin';
  
  console.log(`Migration ${enabled ? 'enabled' : 'disabled'} by ${modifiedBy} at ${new Date()}`);
  
  // ... rest of implementation
};
```

## Deployment Checklist

### Backend Deployment
- [ ] Deploy v2 routes and controllers
- [ ] Deploy migration system (model, service, controller, routes)
- [ ] Update v1 controllers with migration checks
- [ ] Test migration toggle functionality
- [ ] Verify encryption works with migration messages

### Frontend Deployment
- [ ] Update mobile app API service to use v2
- [ ] Deploy admin panel migration settings page
- [ ] Update admin panel navigation
- [ ] Test admin panel migration controls

### Production Verification
- [ ] Verify API v2 endpoints work normally
- [ ] Test migration toggle (carefully)
- [ ] Confirm AdSettings/WhatsNew unaffected
- [ ] Monitor system performance
- [ ] Prepare rollback procedures

---

This technical guide provides the complete implementation details for the TapTrap API v2 migration strategy. Follow the patterns and examples provided to maintain consistency and reliability.
