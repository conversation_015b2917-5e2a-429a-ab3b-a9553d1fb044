# TapTrap Development Guide

This guide provides detailed information for developers working on the TapTrap project. It covers setup, development workflows, best practices, and common tasks.

## Development Environment Setup

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Expo CLI (`npm install -g expo-cli`)
- React Native development environment
- Git
- Code editor (VS Code recommended)
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Initial Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/TapTrap.git
   cd TapTrap
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your API encryption key and other required variables
   ```
   See [Mobile Environment Variables](./mobile/environment-variables.md) for detailed configuration.

3. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

4. Start the development server:
   ```bash
   expo start
   # or
   npm start
   ```

5. Run on a device or emulator:
   - Press 'a' for Android
   - Press 'i' for iOS
   - Scan the QR code with the Expo Go app

### Backend Setup (Optional)

If you need to work with the backend:

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install backend dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your MongoDB connection and admin credentials
   ```

4. Start the backend server:
   ```bash
   npm run dev
   ```

5. Start the admin dashboard:
   ```bash
   cd admin
   npm install
   npm start
   ```

## Project Structure

### Key Directories and Files

- `App.tsx`: Main application component
- `screens/`: Main application screens
  - `HomeScreen.tsx`: Home screen with game mode selection
  - `SelectCategoriesScreen.tsx`: Category selection screen
  - `TouchGameScreen.tsx`: Main game screen
  - `SettingsScreen.tsx`: Settings screen
- `components/`: Reusable UI components
  - `game/`: Game-specific components
  - `ThemedText.tsx`: Text component with styling
- `services/`: API and utility services
  - `contentService.ts`: Content fetching and caching
  - `i18nService.tsx`: Localization service
  - `revenueCatService.ts`: In-app purchase handling
- `assets/`: Static assets
  - `sounds/`: Game sound effects
  - `redesign/`: SVG assets for UI
  - `game_content.json`: Bundled game content
- `context/`: React context providers
  - `AppSettingsContext.tsx`: App settings context

## Development Workflows

### Feature Development

1. Create a new branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Implement your changes
3. Test thoroughly on both Android and iOS
4. Commit your changes:
   ```bash
   git add .
   git commit -m "Description of changes"
   ```

5. Push your branch:
   ```bash
   git push origin feature/your-feature-name
   ```

6. Create a pull request

### Testing

#### Manual Testing

1. Test on multiple devices (phones and tablets)
2. Test on both Android and iOS
3. Test with different language settings
4. Test offline functionality
5. Test with different content categories

#### Automated Testing

If implementing automated tests:

```bash
npm test
# or
yarn test
```

### Building for Production

To create a production build:

```bash
expo build:android  # For Android
expo build:ios      # For iOS
```

For EAS (Expo Application Services) builds:

1. Install EAS CLI:
   ```bash
   npm install -g eas-cli
   ```

2. Configure environment variables in EAS:
   ```bash
   eas secret:create --scope project --name API_ENCRYPTION_KEY --value "your-encryption-key"
   ```
   See [Mobile Environment Variables](./mobile/environment-variables.md) for more details.

3. Build for production:
   ```bash
   eas build --platform android
   eas build --platform ios
   ```

## Common Development Tasks

### Adding a New Screen

1. Create a new file in the `screens/` directory
2. Import necessary components and hooks
3. Add the screen to the navigation stack in `App.tsx`

Example:

```tsx
// screens/NewScreen.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function NewScreen() {
  return (
    <View style={styles.container}>
      <Text>New Screen</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

// In App.tsx
<Stack.Navigator>
  {/* ... other screens */}
  <Stack.Screen name="NewScreen" component={NewScreen} />
</Stack.Navigator>
```

### Adding New Game Content

#### Through the Admin Dashboard (Preferred)

1. Access the admin dashboard at `http://localhost:3000/admin` (local) or `https://taptrap.app/admin` (production)
2. Log in with admin credentials
3. Navigate to the content management section
4. Add or edit content as needed

#### Directly in the Bundled Content

1. Edit `assets/game_content.json`
2. Follow the existing structure:

```json
{
  "questions": {
    "casual_questions": [
      {
        "id": "unique_id",
        "text_en": "Question in English",
        "text_es": "Question in Spanish"
      }
    ],
    // Other categories...
  },
  "dares": {
    // Similar structure...
  }
}
```

### Adding New Translations

1. Update the translation objects in `services/i18nService.tsx`:

```tsx
const translations = {
  en: {
    // Existing translations...
    newKey: 'New English text',
  },
  es: {
    // Existing translations...
    newKey: 'Nuevo texto en Español',
  },
};
```

2. Use the translation in components:

```tsx
const { t } = useLanguage();
<Text>{t('newKey')}</Text>
```

### Adding New Sound Effects

1. Add the sound file to `assets/sounds/`
2. Register the sound in the sound system:

```tsx
// In components/game/playSound.tsx
const soundFiles = {
  // Existing sounds...
  newSound: require('@/assets/sounds/new_sound.mp3'),
};
```

3. Use the sound in components:

```tsx
const { playSound } = useSound();
playSound('newSound');
```

## Best Practices

### Code Style

- Follow the existing code style
- Use TypeScript for type safety
- Use functional components with hooks
- Keep components small and focused
- Use descriptive variable and function names

### Performance

- Memoize expensive calculations with `useMemo`
- Optimize re-renders with `useCallback`
- Use `React.memo` for pure components
- Optimize images and assets
- Test on lower-end devices

### State Management

- Use React Context for global state
- Use local state for component-specific state
- Avoid prop drilling
- Consider state structure carefully

### Error Handling

- Implement proper error boundaries
- Log errors for debugging
- Provide user-friendly error messages
- Handle network errors gracefully

## Troubleshooting

### Common Development Issues

**Metro bundler issues:**
```bash
expo start --clear
# or
rm -rf node_modules && npm install && expo start
```

**iOS build issues:**
```bash
cd ios && pod install && cd ..
```

**Android build issues:**
```bash
cd android && ./gradlew clean && cd ..
```

**Expo errors:**
```bash
expo doctor
```

## Resources

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Expo Documentation](https://docs.expo.dev/)
- [React Navigation Documentation](https://reactnavigation.org/docs/getting-started)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
