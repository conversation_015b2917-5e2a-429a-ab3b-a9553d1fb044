# TapTrap Migration System - Quick Reference

## 🚀 Quick Start

### Enable Migration (Force App Update)
```bash
# Via Admin Panel
1. Go to: taptrap.app/admin/migration-settings
2. Toggle "Start Migration" to ON
3. Users on old app versions will see migration message

# Via API
curl -X POST -H "Authorization: Bearer <token>" \
  -d '{"enabled": true}' \
  https://taptrap-backend.vercel.app/api/admin/migration/toggle
```

### Disable Migration
```bash
# Via Admin Panel
1. Go to: taptrap.app/admin/migration-settings
2. Toggle "Start Migration" to OFF

# Via API
curl -X POST -H "Authorization: Bearer <token>" \
  -d '{"enabled": false}' \
  https://taptrap-backend.vercel.app/api/admin/migration/toggle
```

## 📊 Current System Status

| Component | API Version | Migration Impact |
|-----------|-------------|------------------|
| Mobile App | v2 | ❌ Not affected |
| Admin Panel | v2 | ❌ Not affected |
| Legacy Apps | v1 | ✅ Affected by toggle |

## 🎯 Endpoint Behavior

### When Migration is ENABLED

| Endpoint | Response |
|----------|----------|
| `/api/content` | 🔄 Migration message |
| `/api/penalties` | 🔄 Migration message |
| `/api/admin/ad-settings` | ✅ Normal data |
| `/api/whatsnew` | ✅ Normal data |
| `/api/v2/content` | ✅ Normal data |
| `/api/v2/penalties` | ✅ Normal data |

### When Migration is DISABLED

| Endpoint | Response |
|----------|----------|
| `/api/content` | ✅ Normal data |
| `/api/penalties` | ✅ Normal data |
| All other endpoints | ✅ Normal data |

## 🛠️ Admin Panel Access

### Migration Settings Page
- **URL**: `taptrap.app/admin/migration-settings`
- **Navigation**: Main menu → "Migration Settings" (refresh icon)
- **Features**:
  - Toggle migration on/off
  - Edit migration messages (3 languages)
  - View migration history
  - See current status

### Required Permissions
- Admin authentication required
- JWT token with admin role
- Access to admin panel

## 📝 Migration Messages

### Default Messages
```json
{
  "field_en": "Please download the new version",
  "field_es": "Por favor descarga la nueva versión", 
  "field_dom": "Por favor descarga la nueva versión"
}
```

### Customizing Messages
1. Go to Migration Settings page
2. Edit text in the three language fields
3. Click "Update Message"
4. Changes apply immediately

## 🔍 Testing & Verification

### Local Testing (Safe)
```bash
# Start local backend
cd backend && npm run dev

# Test migration toggle
curl -X POST -H "Authorization: Bearer <token>" \
  -d '{"enabled": true}' \
  http://localhost:5002/api/admin/migration/toggle

# Verify API v1 returns migration message
curl http://localhost:5002/api/content

# Verify API v2 returns normal content  
curl http://localhost:5002/api/v2/content
```

### Production Testing
⚠️ **Be careful with production testing**
- Test during low-usage hours
- Have rollback plan ready
- Monitor user impact
- Disable quickly if issues occur

## 🚨 Emergency Procedures

### Quick Disable Migration
```bash
# Fastest method - Admin Panel
1. Go to taptrap.app/admin/migration-settings
2. Toggle OFF immediately

# API method
curl -X POST -H "Authorization: Bearer <token>" \
  -d '{"enabled": false}' \
  https://taptrap-backend.vercel.app/api/admin/migration/toggle
```

### Database Emergency Disable
```javascript
// Direct MongoDB access (last resort)
db.migrationSettings.updateOne(
  { settingsId: 'default' },
  { $set: { migrationEnabled: false } }
);
```

## 📈 Monitoring

### Key Metrics
- Migration status (enabled/disabled)
- API v1 vs v2 usage
- User app update rates
- Error rates during migration

### Check Migration Status
```bash
# Quick status check
curl -H "Authorization: Bearer <token>" \
  https://taptrap-backend.vercel.app/api/admin/migration/status

# Full settings
curl -H "Authorization: Bearer <token>" \
  https://taptrap-backend.vercel.app/api/admin/migration
```

## 🔐 Security Notes

- All migration changes are logged with admin email
- Migration messages are encrypted (same as normal content)
- Only admin users can modify migration settings
- Changes are atomic and reversible

## 📞 Support

### Common Issues

**Migration not working?**
- Check migration is enabled in admin panel
- Verify you're testing API v1 endpoints (not v2)
- Check authentication token is valid

**Admin panel not loading?**
- Clear browser cache
- Check network connection
- Verify admin credentials

**API v2 affected by migration?**
- This should never happen
- API v2 is designed to be unaffected
- Contact development team if this occurs

### Contact
- **Development Team**: For technical issues
- **Admin Support**: For admin panel access issues
- **Emergency**: Use database emergency disable procedure

## 📋 Checklist for Migration Event

### Pre-Migration
- [ ] Verify migration message is correct
- [ ] Test migration toggle in staging
- [ ] Prepare rollback plan
- [ ] Schedule during low-usage time
- [ ] Notify team of migration event

### During Migration
- [ ] Enable migration via admin panel
- [ ] Monitor API v1 endpoints return migration messages
- [ ] Verify API v2 endpoints unaffected
- [ ] Monitor user feedback/support requests
- [ ] Track app update adoption rates

### Post-Migration
- [ ] Monitor system stability
- [ ] Track user update progress
- [ ] Disable migration when sufficient users updated
- [ ] Document lessons learned
- [ ] Update migration procedures if needed

---

**Remember**: Migration affects only API v1. Current mobile apps use API v2 and are unaffected by the migration toggle.
