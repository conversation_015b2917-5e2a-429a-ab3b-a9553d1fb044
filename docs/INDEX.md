# TapTrap Documentation Index

Welcome to the TapTrap documentation. This index provides links to all available documentation for the TapTrap project.

## Overview Documents

- [README](README.md) - General overview of the TapTrap project
- [Architecture](ARCHITECTURE.md) - Technical architecture and system design
- [User Guide](USER_GUIDE.md) - Guide for end users
- [Development Guide](DEVELOPMENT.md) - Guide for developers
- [API Documentation](API.md) - Backend API reference
- [Admin Panel](ADMIN_PANEL.md) - Admin dashboard documentation

## Additional Resources

- [Backend README](../README-BACKEND.md) - Information about the backend integration
- [Admin Troubleshooting](../admin/TROUBLESHOOTING.md) - Troubleshooting guide for admin dashboard
- [Vercel Deployment](../admin/VERCEL_DEPLOYMENT.md) - Guide for deploying to Vercel
- [Encryption Implementation](../backend/docs/ENCRYPTION.md) - Guide for response encryption

## Quick Links

### For Users
- [Getting Started](USER_GUIDE.md#getting-started)
- [Game Modes](USER_GUIDE.md#game-modes)
- [Playing the Game](USER_GUIDE.md#playing-the-game)
- [Settings and Customization](USER_GUIDE.md#settings-and-customization)
- [Troubleshooting](USER_GUIDE.md#troubleshooting)
- [FAQ](USER_GUIDE.md#faq)

### For Developers
- [Development Environment Setup](DEVELOPMENT.md#development-environment-setup)
- [Project Structure](DEVELOPMENT.md#project-structure)
- [Development Workflows](DEVELOPMENT.md#development-workflows)
- [Common Development Tasks](DEVELOPMENT.md#common-development-tasks)
- [Best Practices](DEVELOPMENT.md#best-practices)

### For Administrators
- [Admin Panel Overview](ADMIN_PANEL.md#overview)
- [Admin Features](ADMIN_PANEL.md#features)
- [AI Content Generation](AI_CONTENT_GENERATION.md#overview)
- [Installation and Setup](ADMIN_PANEL.md#installation-and-setup)
- [Usage Guide](ADMIN_PANEL.md#usage-guide)
- [Deployment](ADMIN_PANEL.md#deployment)
- [Troubleshooting](ADMIN_PANEL.md#troubleshooting)

### For API Users
- [API Overview](API.md#api-overview)
- [Public Endpoints](API.md#public-endpoints)
- [Admin Endpoints](API.md#admin-endpoints)
- [Data Models](API.md#data-models)
- [Response Encryption](../backend/docs/ENCRYPTION.md)

## Document Conventions

Throughout the documentation, you'll find:

- Code blocks for code examples
- Command-line instructions for terminal commands
- Screenshots for visual reference
- Links to external resources

## Contributing to Documentation

If you'd like to improve this documentation:

1. Fork the repository
2. Make your changes
3. Submit a pull request

Please follow these guidelines:
- Use clear, concise language
- Include code examples where appropriate
- Test any instructions or commands
- Update the index when adding new documents

## Version Information

This documentation is for TapTrap version 1.0.0. As the application evolves, the documentation will be updated to reflect changes.

## Contact Information

For questions or support:
- Email: <EMAIL>
- Website: www.taptrap.app
