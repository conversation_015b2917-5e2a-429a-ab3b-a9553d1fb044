# TapTrap Penalty System

## Overview

The TapTrap Penalty System allows players to receive penalties when they refuse to answer a question or complete a dare. This comprehensive system includes backend management, admin panel controls, and mobile app integration.

**Status: ✅ FULLY IMPLEMENTED AND OPERATIONAL**

## Quick Start

1. **Backend**: Running on `http://********:5002` with 16 sample penalties
2. **Mobile App**: Penalty fetching integrated in HomeScreen, settings in SettingsScreen
3. **Admin Panel**: Complete CRUD interface at `/penalties`
4. **API**: Encrypted penalty endpoint at `/api/penalties`

## Features

### Core Functionality
- **Dynamic Penalty Selection**: Players can choose from multiple penalty categories
- **Multi-language Support**: Penalties available in English, Spanish, and Dominican Spanish
- **Category-based Organization**: Penalties organized into drinking, physical, social, silly, and creative categories
- **Random Selection**: System randomly selects penalties based on user preferences
- **Persistent Settings**: User penalty preferences saved across app sessions

### Admin Management
- **CRUD Operations**: Full create, read, update, delete functionality for penalties
- **Category Management**: Organize penalties by type
- **Active/Inactive Status**: Enable or disable penalties
- **Search and Filtering**: Find penalties by text content or category
- **Bulk Operations**: Manage multiple penalties at once

### Mobile Integration
- **Settings Interface**: Easy penalty selection in app settings
- **Dynamic Content**: Penalties fetched once at app launch, cached locally
- **Hourly Refresh**: Content refreshed every hour (same as game content)
- **Offline Support**: Cached penalties work without internet connection
- **Smooth Animations**: Consistent UI animations for penalty dialogs
- **IP-based Connection**: Uses computer's IP address (********) for mobile development

## Architecture

### Backend Components

#### Models
- **Penalty Model** (`backend/src/models/penalty.js`)
  - Multi-language text fields (text_en, text_es, text_dom)
  - Category classification
  - Active/inactive status
  - Timestamps for creation and updates

#### Controllers
- **Penalty Controller** (`backend/src/controllers/penaltyController.js`)
  - Public API for mobile app
  - Encrypted response support
  - Formatted penalty structure

- **Admin Controller** (`backend/src/controllers/adminController.js`)
  - CRUD operations for admin panel
  - Search and filtering capabilities
  - Validation and error handling

#### Services
- **Penalty Service** (`backend/src/services/penalty.service.js`)
  - Business logic for penalty management
  - Statistics and analytics
  - Data formatting utilities

#### Routes
- **Public Routes** (`backend/src/routes/penalty.js`)
  - `GET /api/penalties` - Fetch all active penalties for mobile app

- **Admin Routes** (`backend/src/routes/admin.js`)
  - `GET /api/admin/penalties` - List all penalties with filters
  - `POST /api/admin/penalties` - Create new penalty
  - `GET /api/admin/penalties/:id` - Get penalty by ID
  - `PUT /api/admin/penalties/:id` - Update penalty
  - `DELETE /api/admin/penalties/:id` - Delete penalty

### Mobile App Components

#### Services
- **Penalty Service** (`services/penaltyService.ts`)
  - Fetch and cache penalty content (follows contentService.ts patterns)
  - Hourly refresh logic (integrated with HomeScreen)
  - User preference management
  - Random penalty selection
  - Uses `decryptApiData` for API responses
  - Uses `decryptFromStorage` for cached content

#### Context
- **App Settings Context** (`context/AppSettingsContext.tsx`)
  - Penalty selection state management
  - Persistent storage integration
  - Settings synchronization

#### Screens
- **Settings Screen** (`screens/SettingsScreen.tsx`)
  - Penalty category selection interface
  - Dynamic loading of penalty options
  - User preference persistence

- **Touch Game Screen** (`screens/TouchGameScreen.tsx`)
  - Penalty dialog integration
  - Random penalty text display
  - Refuse button functionality

#### Components
- **Penalty Dialog** (`components/game/PenaltyDialog.tsx`)
  - Modal penalty display
  - Dynamic text content
  - Navigation controls

### Admin Panel Components

#### Pages
- **Penalty List** (`admin/src/pages/PenaltyList.js`)
  - Table view of all penalties
  - Search and filtering interface
  - Bulk operations support

- **Add Penalty** (`admin/src/pages/AddPenalty.js`)
  - Form for creating new penalties
  - Multi-language input fields
  - Real-time preview

- **Edit Penalty** (`admin/src/pages/EditPenalty.js`)
  - Form for updating existing penalties
  - Pre-populated fields
  - Update confirmation

#### Services
- **Penalty Service** (`admin/src/services/penalty.service.js`)
  - API communication layer
  - Data validation utilities
  - Export/import functionality

## API Reference

### Public Endpoints

#### Get All Penalties
```http
GET /api/penalties
```

**Response (Encrypted):**
```json
{
  "encrypted": true,
  "data": "U2FsdGVkX19v2uEO6D2/rnIeOiF4TlcVPvZw+wfkvdzwYwPBplcF7Zj4o8VrWo+R..."
}
```

**Decrypted Structure:**
```json
{
  "drinking": [
    {
      "id": "uuid",
      "text_en": "Take a shot.",
      "text_es": "Toma un trago.",
      "text_dom": "Tómate un trago."
    }
  ],
  "physical": [...],
  "social": [...],
  "silly": [...],
  "creative": [...]
}
```

### Admin Endpoints

#### List Penalties
```http
GET /api/admin/penalties?category=drinking&search=shot&active=true
```

#### Create Penalty
```http
POST /api/admin/penalties
Content-Type: application/json

{
  "text_en": "English penalty text",
  "text_es": "Spanish penalty text",
  "text_dom": "Dominican penalty text",
  "category": "drinking",
  "active": true
}
```

#### Update Penalty
```http
PUT /api/admin/penalties/:id
Content-Type: application/json

{
  "text_en": "Updated English text",
  "text_es": "Updated Spanish text",
  "text_dom": "Updated Dominican text",
  "category": "physical",
  "active": false
}
```

#### Delete Penalty
```http
DELETE /api/admin/penalties/:id
```

## Data Models

### Penalty Schema
```javascript
{
  id: String,           // Unique identifier
  text_en: String,      // English text (required)
  text_es: String,      // Spanish text (required)
  text_dom: String,     // Dominican Spanish text (required)
  category: String,     // Category: drinking, physical, social, silly, creative
  active: Boolean,      // Active status (default: true)
  createdAt: Date,      // Creation timestamp
  updatedAt: Date       // Last update timestamp
}
```

### Categories
- **drinking**: Alcohol-related penalties
- **physical**: Exercise or physical activity penalties
- **social**: Social interaction penalties
- **silly**: Fun, humorous penalties
- **creative**: Artistic or creative penalties

## Usage Examples

### Mobile App - HomeScreen Integration
```typescript
// Penalty fetching happens once at app launch in HomeScreen
const { fetchAndUpdatePenalties } = require('@/services/penaltyService');

// In HomeScreen refreshGameContent function:
const penaltyContent = await fetchAndUpdatePenalties();
if (penaltyContent) {
  const totalPenalties = Object.values(penaltyContent).reduce((sum, arr) => sum + arr.length, 0);
  console.log(`✅ Penalty content refreshed: ${totalPenalties} penalties loaded`);
}
```

### Mobile App - Settings
```typescript
// Load penalty selections (from cache only)
const selections = await loadPenaltySelections();

// Update penalty selections
await updatePenaltySelections(['drinking', 'silly']);

// Get random penalty (from cached content)
const penalty = await getRandomPenalty('en');
```

### Mobile App - Game Integration
```typescript
// Show penalty dialog with dynamic content
const showPenaltyDialog = async () => {
  const penaltyText = await getRandomPenalty(language);
  setCurrentPenaltyText(penaltyText || 'No penalty selected.');
  // Show dialog...
};
```

### Admin Panel - Management
```javascript
// Create new penalty
const penalty = await penaltyService.createPenalty({
  text_en: 'English text',
  text_es: 'Spanish text',
  text_dom: 'Dominican text',
  category: 'drinking',
  active: true
});

// Search penalties
const results = await penaltyService.searchPenalties('shot', {
  category: 'drinking',
  active: true
});
```

## Testing

### Running Tests
```bash
# Run comprehensive test suite
node test-penalty-system.js

# Add sample penalty data
node backend/scripts/addSamplePenalties.js
```

### Test Coverage
- Backend API endpoints
- CRUD operations
- Filtering and search
- Mobile app integration
- Admin panel functionality
- End-to-end penalty flow

## Deployment

### Backend Setup ✅
1. ✅ MongoDB connection configured and working
2. ✅ Penalty model created and functional
3. ✅ Sample penalty data added (16 penalties across 5 categories)
4. ✅ API endpoints accessible at `http://********:5002/api/penalties`
5. ✅ Swagger documentation updated with inline schemas

### Mobile App Setup ✅
1. ✅ Penalty service integrated following contentService.ts patterns
2. ✅ Settings screen includes penalty selection interface
3. ✅ Touch game screen supports penalty dialogs with dynamic content
4. ✅ HomeScreen fetches penalties once at app launch
5. ✅ API_URL updated to use computer's IP address for mobile development

### Admin Panel Setup ✅
1. ✅ Penalty routes integrated in App.js
2. ✅ Navigation menu includes penalty links
3. ✅ All CRUD pages functional (List, Add, Edit)
4. ✅ Admin service handles API communication

## Troubleshooting

### Common Issues ✅ RESOLVED
1. **✅ Network request failed**: Fixed by updating API_URL to use IP address (********) instead of localhost
2. **✅ Swagger resolver errors**: Fixed by updating penalty route to use inline schemas instead of missing PenaltyItem reference
3. **✅ Wrong decryption function**: Fixed by using `decryptApiData` for API responses and `decryptFromStorage` for cached content
4. **✅ Multiple API calls**: Fixed by implementing penalty fetching only once at app launch in HomeScreen
5. **✅ Penalty inconsistency bug**: Fixed penalty selection logic to maintain same penalty per content item
6. **Settings not persisting**: Verify AsyncStorage permissions
7. **Admin panel errors**: Check authentication and API endpoints
8. **Empty penalty dialog**: Ensure user has selected penalty categories

### Bug Fixes

#### Penalty Consistency Fix ✅ RESOLVED
**Issue**: Each time a user tapped "Back to Content" and then "Refuse" again, a different penalty was displayed instead of showing the same penalty consistently for the same content item.

**Root Cause**: The `showPenaltyDialog` function was calling `getRandomPenalty()` every time it was invoked, generating a new random penalty on each penalty dialog opening.

**Solution**: Implemented penalty-per-content tracking system:

```typescript
// New state variables for penalty consistency
const [currentContentPenalty, setCurrentContentPenalty] = useState<string | null>(null);
const [penaltyContentId, setPenaltyContentId] = useState<string | null>(null);

// Modified showPenaltyDialog function
const showPenaltyDialog = async () => {
  const currentQuestionId = question?.id;
  let penaltyText: string;

  if (currentQuestionId && penaltyContentId === currentQuestionId && currentContentPenalty) {
    // Use existing penalty for this content item
    penaltyText = currentContentPenalty;
    console.log('🎯 Using existing penalty for content:', currentQuestionId);
  } else {
    // Generate new penalty only for new content
    const languageKey = language === 'dom' ? 'dom' : language === 'es' ? 'es' : 'en';
    const newPenaltyText = await getRandomPenalty(languageKey);
    penaltyText = newPenaltyText || t('penaltyNotSelected');

    // Store penalty for this content item
    if (currentQuestionId) {
      setCurrentContentPenalty(penaltyText);
      setPenaltyContentId(currentQuestionId);
      console.log('🎯 Generated new penalty for content:', currentQuestionId);
    }
  }

  setCurrentPenaltyText(penaltyText);
  // ... rest of dialog logic
};
```

**Penalty Clearing Logic**: Penalties are automatically cleared when:
- Moving to new content (`proceedToNextQuestion`, initial question setting)
- Showing premium content (premium content doesn't use penalties)
- Resetting the game (`resetGameState`)

**Expected Behavior**:
1. User taps "Refuse" → Penalty generated and stored for that content item
2. User taps "Back to Content" → Returns to content dialog
3. User taps "Refuse" again → Shows the **same penalty** from step 1
4. User moves to next content → Penalty cleared, new one generated for new content

**Files Modified**: `screens/TouchGameScreen.tsx`
**Date Fixed**: December 2024

#### Refuse Button Visibility Control ✅ IMPLEMENTED (December 2024)
**Feature**: Hide the "Refuse" button when penalty is set as "none" to provide a cleaner UI experience.

**Implementation**: Added conditional rendering logic that checks if penalties are enabled:

```typescript
// Check if penalties are enabled (user has selected at least one penalty)
const arePenaltiesEnabled = selectedIndividualPenalties.length > 0;

// Conditional rendering of refuse button
{arePenaltiesEnabled && (
  <TouchableOpacity
    style={styles.refuseButton}
    onPress={async () => {
      // ... refuse button logic
    }}
  >
    {/* Button content */}
  </TouchableOpacity>
)}
```

**Behavior**:
- **When penalties are enabled**: Refuse button is visible and functional
- **When penalties are set to "none"**: Refuse button is completely hidden
- **Layout adjustment**: Remaining buttons (Skip and Reset) automatically adjust to fill available space

**User Experience**: Provides a cleaner interface when penalties are disabled, removing unnecessary UI elements and reducing user confusion.

**Files Modified**: `screens/TouchGameScreen.tsx`
**Date Implemented**: December 2024

### Debug Mode
Enable debug logging in penalty service:
```typescript
console.log('Penalty content loaded:', penalties);
console.log('User selections:', selections);
console.log('Random penalty selected:', penaltyText);
```

## Future Enhancements

### Planned Features
- **Custom Penalties**: Allow users to create custom penalties
- **Penalty History**: Track which penalties have been used
- **Difficulty Levels**: Different penalty intensities
- **Time-based Penalties**: Penalties that last for specific durations
- **Group Penalties**: Penalties that affect multiple players
- **Achievement System**: Rewards for completing penalties

### Technical Improvements
- **Caching Optimization**: Improve penalty content caching
- **Offline Sync**: Better offline/online synchronization
- **Analytics**: Track penalty usage statistics
- **A/B Testing**: Test different penalty sets
- **Localization**: Additional language support
