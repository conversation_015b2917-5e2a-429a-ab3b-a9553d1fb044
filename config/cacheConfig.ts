/**
 * Cache Configuration for TapTrap Services
 * 
 * Centralized configuration for all caching and throttling durations
 * used across different services in the TapTrap application.
 */

// Cache durations in milliseconds
export const CACHE_DURATIONS = {
  // Smart content checking - lightweight check every 12 hours, full fetch only if changes detected
  CONTENT_CHECK: 12 * 60 * 60 * 1000, // 12 hours

  // Ad watch tracking expiration
  AD_WATCH_TRACKING: 15 * 60 * 1000, // 15 minutes

} as const;

// Service-specific cache durations - simplified to only use content check
export const SERVICE_CACHE_DURATIONS = {
  // All content services use the same 12-hour smart checking
  CONTENT: CACHE_DURATIONS.CONTENT_CHECK,
  PENALTY: CACHE_DURATIONS.CONTENT_CHECK,
  AD_SETTINGS: CACHE_DURATIONS.CONTENT_CHECK,
  WHATS_NEW: CACHE_DURATIONS.CONTENT_CHECK,

  // Push notification service
} as const;

// Helper functions for duration calculations
export const DURATION_HELPERS = {
  /**
   * Convert milliseconds to human-readable format
   */
  toHumanReadable: (ms: number): string => {
    const hours = Math.floor(ms / (60 * 60 * 1000));
    const minutes = Math.floor((ms % (60 * 60 * 1000)) / (60 * 1000));
    const seconds = Math.floor((ms % (60 * 1000)) / 1000);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  },
  
  /**
   * Check if a timestamp is expired based on duration
   */
  isExpired: (timestamp: number, duration: number): boolean => {
    return (Date.now() - timestamp) > duration;
  },
  
  /**
   * Get time remaining until expiration
   */
  getTimeRemaining: (timestamp: number, duration: number): number => {
    const elapsed = Date.now() - timestamp;
    return Math.max(0, duration - elapsed);
  },
  
  /**
   * Get time since last action in human-readable format
   */
  getTimeSince: (timestamp: number): string => {
    const elapsed = Date.now() - timestamp;
    return DURATION_HELPERS.toHumanReadable(elapsed);
  },
} as const;

/**
 * Hash generation utilities for change detection
 * Uses MD5 to match backend implementation
 */
export const HASH_HELPERS = {
  /**
   * Generate MD5 hash from any data object
   * Must match backend generateHash function exactly
   */
  generateHash: (data: any): string => {
    try {
      // Import crypto-js for MD5 (same as backend)
      const CryptoJS = require('crypto-js');

      // Convert data to JSON string (same as backend)
      const dataString = typeof data === 'object' ? JSON.stringify(data) : String(data);

      // Generate MD5 hash (same as backend)
      const hash = CryptoJS.MD5(dataString).toString();

      return hash;
    } catch (error) {
      console.error('Error generating MD5 hash:', error);
      // Fallback: use timestamp as hash
      return Date.now().toString(16);
    }
  },

  /**
   * Generate hash specifically for content change detection
   */
  generateContentHash: (content: any): string => {
    return HASH_HELPERS.generateHash(content);
  },

  /**
   * Test hash generation to verify it's working
   */
  testHashGeneration: (): void => {
    try {
      const testData = { test: "data", number: 123 };
      const hash1 = HASH_HELPERS.generateHash(testData);
      const hash2 = HASH_HELPERS.generateHash(testData);

      console.log('🧪 HASH TEST:', {
        testData,
        hash1,
        hash2,
        areEqual: hash1 === hash2,
        hashLength: hash1.length
      });

      if (hash1 === hash2) {
        console.log('✅ Hash generation is consistent');
      } else {
        console.error('❌ Hash generation is inconsistent!');
      }
    } catch (error) {
      console.error('❌ Hash test failed:', error);
    }
  },

  /**
   * Clear all stored hashes for testing purposes
   */
  clearAllStoredHashes: async (): Promise<void> => {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;

      const hashKeys = [
        'taptrap_content_change_hash',
        'penalty_change_hash',
        'taptrap_ad_settings_change_hash',
        'taptrap_whatsnew_change_hash'
      ];

      for (const key of hashKeys) {
        await AsyncStorage.removeItem(key);
        console.log(`🗑️ Cleared hash: ${key}`);
      }

      console.log('✅ All stored hashes cleared for testing');
    } catch (error) {
      console.error('❌ Error clearing stored hashes:', error);
    }
  },
} as const;

// Export individual durations for backward compatibility
export const CONTENT_CHECK_DURATION = SERVICE_CACHE_DURATIONS.CONTENT;
export const AD_WATCH_TRACKING_DURATION = CACHE_DURATIONS.AD_WATCH_TRACKING;

// Type definitions for better TypeScript support
export type CacheDuration = typeof CACHE_DURATIONS[keyof typeof CACHE_DURATIONS];
export type ServiceCacheDuration = typeof SERVICE_CACHE_DURATIONS[keyof typeof SERVICE_CACHE_DURATIONS];

export default {
  CACHE_DURATIONS,
  SERVICE_CACHE_DURATIONS,
  DURATION_HELPERS,
};
