#!/usr/bin/env node

/**
 * Quick integration test for TapTrap Penalty System
 * Tests the key integration points without requiring a full backend setup
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Test file existence and content
function testFileExists(filePath, description) {
  const fullPath = path.join(__dirname, filePath);
  if (fs.existsSync(fullPath)) {
    logSuccess(`${description} exists`);
    return true;
  } else {
    logError(`${description} not found at ${filePath}`);
    return false;
  }
}

function testFileContains(filePath, searchStrings, description) {
  const fullPath = path.join(__dirname, filePath);
  if (!fs.existsSync(fullPath)) {
    logError(`${description} file not found`);
    return false;
  }

  const content = fs.readFileSync(fullPath, 'utf8');
  const missingStrings = searchStrings.filter(str => !content.includes(str));
  
  if (missingStrings.length === 0) {
    logSuccess(`${description} has all required integrations`);
    return true;
  } else {
    logError(`${description} missing: ${missingStrings.join(', ')}`);
    return false;
  }
}

// Main test function
function runIntegrationTests() {
  log('\n🔧 TapTrap Penalty System Integration Test', 'blue');
  log('===========================================\n', 'blue');

  let allTestsPassed = true;

  // Test 1: Backend files
  logInfo('Testing backend files...');
  const backendTests = [
    ['backend/src/models/penalty.js', 'Penalty Model'],
    ['backend/src/controllers/penaltyController.js', 'Penalty Controller'],
    ['backend/src/services/penalty.service.js', 'Penalty Service'],
    ['backend/src/routes/penalty.js', 'Penalty Routes']
  ];

  backendTests.forEach(([file, desc]) => {
    if (!testFileExists(file, desc)) {
      allTestsPassed = false;
    }
  });

  // Test 2: Mobile app files
  logInfo('\nTesting mobile app files...');
  const mobileTests = [
    ['services/penaltyService.ts', 'Mobile Penalty Service'],
    ['components/game/PenaltyDialog.tsx', 'Penalty Dialog Component'],
  ];

  mobileTests.forEach(([file, desc]) => {
    if (!testFileExists(file, desc)) {
      allTestsPassed = false;
    }
  });

  // Test 3: Admin panel files
  logInfo('\nTesting admin panel files...');
  const adminTests = [
    ['admin/src/pages/PenaltyList.js', 'Penalty List Page'],
    ['admin/src/pages/AddPenalty.js', 'Add Penalty Page'],
    ['admin/src/pages/EditPenalty.js', 'Edit Penalty Page'],
    ['admin/src/services/penalty.service.js', 'Admin Penalty Service']
  ];

  adminTests.forEach(([file, desc]) => {
    if (!testFileExists(file, desc)) {
      allTestsPassed = false;
    }
  });

  // Test 4: Integration points
  logInfo('\nTesting integration points...');

  // Test penalty service imports
  if (!testFileContains('services/penaltyService.ts', [
    'API_URL',
    'decryptFromStorage',
    'encryptForStorage',
    'PenaltyCategory',
    'getRandomPenalty'
  ], 'Penalty Service')) {
    allTestsPassed = false;
  }

  // Test TouchGameScreen integration
  if (!testFileContains('screens/TouchGameScreen.tsx', [
    'getRandomPenalty',
    'currentPenaltyText',
    'penaltyText={currentPenaltyText}'
  ], 'TouchGameScreen Integration')) {
    allTestsPassed = false;
  }

  // Test SettingsScreen integration
  if (!testFileContains('screens/SettingsScreen.tsx', [
    'PenaltyCategory',
    'loadPenaltyContent',
    'selectedPenalties',
    'updatePenaltySelections'
  ], 'SettingsScreen Integration')) {
    allTestsPassed = false;
  }

  // Test AppSettingsContext integration
  if (!testFileContains('context/AppSettingsContext.tsx', [
    'PenaltyCategory',
    'selectedPenalties',
    'updatePenaltySelections',
    'savePenaltySelections'
  ], 'AppSettingsContext Integration')) {
    allTestsPassed = false;
  }

  // Test PenaltyDialog updates
  if (!testFileContains('components/game/PenaltyDialog.tsx', [
    'penaltyText?:',
    'penaltyText ||'
  ], 'PenaltyDialog Dynamic Content')) {
    allTestsPassed = false;
  }

  // Test backend route integration
  if (!testFileContains('backend/src/index.js', [
    'penaltyRoutes',
    '/api/penalties'
  ], 'Backend Route Integration')) {
    allTestsPassed = false;
  }

  // Test admin controller integration
  if (!testFileContains('backend/src/controllers/adminController.js', [
    'createPenalty',
    'getAllPenalties',
    'updatePenalty',
    'deletePenalty'
  ], 'Admin Controller Integration')) {
    allTestsPassed = false;
  }

  // Test admin panel routing
  if (!testFileContains('admin/src/App.js', [
    'PenaltyList',
    'AddPenalty',
    'EditPenalty',
    '/penalties'
  ], 'Admin Panel Routing')) {
    allTestsPassed = false;
  }

  // Test admin panel navigation
  if (!testFileContains('admin/src/components/Layout.js', [
    'Penalties',
    'FiAlertTriangle',
    '/penalties'
  ], 'Admin Panel Navigation')) {
    allTestsPassed = false;
  }

  // Test 5: Documentation and scripts
  logInfo('\nTesting documentation and scripts...');
  const docTests = [
    ['docs/penalty-system.md', 'Penalty System Documentation'],
    ['backend/scripts/addSamplePenalties.js', 'Sample Data Script'],
    ['test-penalty-system.js', 'Comprehensive Test Suite']
  ];

  docTests.forEach(([file, desc]) => {
    if (!testFileExists(file, desc)) {
      allTestsPassed = false;
    }
  });

  // Summary
  log('\n📊 Integration Test Results', 'blue');
  log('============================\n', 'blue');

  if (allTestsPassed) {
    logSuccess('🎉 All integration tests passed!');
    logInfo('The penalty system is properly integrated and ready to use.');
    log('\nNext steps:', 'yellow');
    log('1. Start the backend server: cd backend && npm start');
    log('2. Add sample penalty data: node backend/scripts/addSamplePenalties.js');
    log('3. Start the admin panel: cd admin && npm start');
    log('4. Test the mobile app penalty features');
  } else {
    logError('⚠️  Some integration tests failed.');
    logInfo('Please check the missing files or integrations listed above.');
  }

  return allTestsPassed;
}

// Run tests if this script is executed directly
if (require.main === module) {
  const success = runIntegrationTests();
  process.exit(success ? 0 : 1);
}

module.exports = { runIntegrationTests };
