# TapTrap Admin Panel - Troubleshooting Guide

This guide covers common issues you might encounter when deploying the TapTrap Admin Panel and Backend API to Vercel.

## Login Issues

### "Server error during login"

This error usually indicates that there's an error in the backend's login function. To resolve:

1. **Check JWT_SECRET**: Make sure the JWT_SECRET environment variable is set in your Vercel project settings for the backend.

2. **Check MongoDB Connection**: Ensure your MongoDB connection string is correctly set in the environment variables on Vercel.

3. **Check Logs**: In Vercel, go to your backend deployment, navigate to "Deployments" and click on the latest deployment. Then click on "Functions" and look for any `/api/admin/login` requests to see detailed error logs.

### CORS Errors

If you see CORS errors in your browser console:

1. **Check Allowed Origins**: Make sure your admin domain is listed in the allowed origins in the backend's CORS configuration.

2. **Check API URL**: Ensure the admin panel is using the correct API URL (should be `/api` in production).

## "Cannot GET /api/" Error

This error means your backend doesn't have a route handler for the root API path. This is generally expected behavior as the API has specific endpoints like `/api/admin/login` and `/api/content`.

However, if you updated the backend with our recent changes, there should now be a route at `/api` that returns a simple message. If you're still seeing this error:

1. **Redeploy Backend**: Make sure you've redeployed the backend with the updated code that includes the `/api` route.

2. **Check Rewrites**: Ensure your Vercel configuration for the admin panel has the correct rewrite rule that forwards `/api/*` requests to the backend.

## Content Not Loading

If you can log in but content isn't loading:

1. **Check CORS for Content Endpoints**: Make sure the CORS settings allow access to content endpoints.

2. **Check Authentication**: Verify the token is being properly sent with requests to protected endpoints.

3. **Check Network Tab**: In your browser's dev tools, look at the network tab to see what responses you're getting from content requests.

## Environment Variables

Make sure you have these environment variables properly set:

### Backend Environment Variables
- `MONGODB_URI`: Your MongoDB connection string
- `JWT_SECRET`: A secret key for JWT token generation/verification
- `NODE_ENV`: Set to `production` for production deployments

### Admin Panel Environment Variables
- `REACT_APP_API_URL`: Should be set to `/api` for production (already configured in .env.production)

## Vercel Configuration Checklist

1. **Backend Project**:
   - Ensure `vercel.json` is correctly configured
   - Set all required environment variables
   - Deployments are completing successfully

2. **Admin Panel Project**:
   - Verify `vercel.json` has correct rewrites to proxy `/api/*` to backend
   - Build command is set to `npm run vercel-build`
   - Output directory is set to `build`

## Still Having Issues?

If you're still experiencing problems:

1. **Check Browser Console**: Look for detailed error messages
2. **Enable Verbose Logging**: Temporarily add more logging to your code
3. **Development Testing**: Test locally first to isolate if the issue is deployment-specific
4. **Review Network Requests**: Compare network requests between local development and production