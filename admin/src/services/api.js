import axios from 'axios';

// Create axios instance with base URL - Updated to use API v2
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5002/api/v2';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use(
  (config) => {
    // Ensure headers object exists
    config.headers = config.headers || {};

    // Get token from localStorage
    const token = localStorage.getItem('token');

    // Add token to Authorization header if available
    if (token) {
      console.log('Adding auth token to request');
      // Use Bearer token format as per API documentation
      config.headers['Authorization'] = `Bearer ${token}`;

      // Also include x-auth-token for backward compatibility
      config.headers['x-auth-token'] = token;
    } else {
      console.log('No auth token available for request');
    }

    // Log request details (for debugging)
    console.log(`Request: ${config.method?.toUpperCase()} ${config.url}`,
                 token ? 'With Auth' : 'No Auth');

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle 401 responses (unauthorized) - only for specific paths
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.log('API error interceptor:', error.config?.url, error.response?.status);

    // Handle 401 unauthorized errors in all environments
    if (error.response && error.response.status === 401) {
      console.error('Authentication error: Unauthorized access');

      // Clear authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Redirect to login page
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API is now moved to auth.service.js
// Content API is now moved to content.service.js

export default api;