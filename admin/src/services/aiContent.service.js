import api from './api';

/**
 * AI Content Generation Service
 * 
 * This service handles communication with the backend AI content generation endpoints.
 * It provides functions to generate AI content and bulk create content items.
 */

/**
 * Generate AI content for a specific game mode and category
 * 
 * @param {string} gameMode - The game mode (questions or dares)
 * @param {string} category - The content category
 * @returns {Promise<Object>} - Generated content response
 */
export const generateAIContent = async (gameMode, category) => {
  try {
    console.log('AI Content Service: Generating content for', { gameMode, category });
    
    const response = await api.post('/admin/content/generate', {
      gameMode,
      category
    });
    
    console.log('AI Content Service: Content generated successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('AI Content Service: Error generating content:', error);
    
    // Handle specific error cases
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 503) {
        throw new Error(data.details || 'AI service is currently unavailable');
      }
      
      if (status === 400) {
        throw new Error(data.details || 'Invalid request parameters');
      }
      
      throw new Error(data.details || data.message || 'Failed to generate AI content');
    }
    
    throw new Error('Network error: Unable to connect to AI service');
  }
};

/**
 * Bulk create content items from AI generated content
 * 
 * @param {Array} contentItems - Array of content items to create
 * @param {string} gameMode - The game mode (questions or dares)
 * @param {string} category - The content category
 * @returns {Promise<Object>} - Bulk create response
 */
export const bulkCreateAIContent = async (contentItems, gameMode, category) => {
  try {
    console.log('AI Content Service: Bulk creating content items:', {
      count: contentItems.length,
      gameMode,
      category
    });
    
    const response = await api.post('/admin/content/bulk-create', {
      contentItems,
      gameMode,
      category
    });
    
    console.log('AI Content Service: Content items created successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('AI Content Service: Error bulk creating content:', error);
    
    // Handle specific error cases
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 400) {
        throw new Error(data.details || 'Invalid content items');
      }
      
      throw new Error(data.details || data.message || 'Failed to create content items');
    }
    
    throw new Error('Network error: Unable to connect to server');
  }
};

/**
 * Get available game modes for content generation
 * 
 * @returns {Array} - Array of game mode options
 */
export const getGameModeOptions = () => {
  return [
    { value: 'questions', label: 'Questions' },
    { value: 'dares', label: 'Dares' }
  ];
};

/**
 * Get available categories for a specific game mode
 * 
 * @param {string} gameMode - The game mode (questions or dares)
 * @returns {Array} - Array of category options
 */
export const getCategoryOptions = (gameMode) => {
  const baseCategories = [
    { value: 'casual', label: 'Casual', description: 'Light, fun, and appropriate for all audiences' },
    { value: 'mild', label: 'Mild', description: 'Slightly more personal but still comfortable' },
    { value: 'spicy', label: 'Spicy', description: 'More intimate and revealing content' },
    { value: 'no_limits', label: 'No Limits', description: 'Very personal and bold content' },
    { value: 'couple', label: 'Couple', description: 'Content specifically designed for couples and romantic partners' }
  ];
  
  // Convert to the format expected by the backend
  return baseCategories.map(category => ({
    ...category,
    value: `${category.value}_${gameMode}`,
    fullLabel: `${category.label} ${gameMode === 'questions' ? 'Questions' : 'Dares'}`
  }));
};

/**
 * Validate content item structure
 * 
 * @param {Object} item - Content item to validate
 * @returns {boolean} - Whether the item is valid
 */
export const validateContentItem = (item) => {
  return (
    item &&
    typeof item === 'object' &&
    typeof item.text_en === 'string' &&
    typeof item.text_es === 'string' &&
    typeof item.text_dom === 'string' &&
    item.text_en.trim().length > 0 &&
    item.text_es.trim().length > 0 &&
    item.text_dom.trim().length > 0
  );
};

/**
 * Validate array of content items
 * 
 * @param {Array} items - Array of content items to validate
 * @returns {Object} - Validation result with valid items and errors
 */
export const validateContentItems = (items) => {
  if (!Array.isArray(items)) {
    return {
      isValid: false,
      validItems: [],
      errors: ['Content items must be an array']
    };
  }
  
  const validItems = [];
  const errors = [];
  
  items.forEach((item, index) => {
    if (validateContentItem(item)) {
      validItems.push(item);
    } else {
      errors.push(`Item ${index + 1} is missing required text fields`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    validItems,
    errors
  };
};

/**
 * Format category display name for UI
 * 
 * @param {string} category - Category value (e.g., 'casual_questions')
 * @returns {string} - Formatted display name
 */
export const formatCategoryDisplayName = (category) => {
  const categoryMap = {
    'casual_questions': 'Casual Questions',
    'mild_questions': 'Mild Questions',
    'spicy_questions': 'Spicy Questions',
    'no_limits_questions': 'No Limits Questions',
    'couple_questions': 'Couple Questions',
    'casual_dares': 'Casual Dares',
    'mild_dares': 'Mild Dares',
    'spicy_dares': 'Spicy Dares',
    'no_limits_dares': 'No Limits Dares',
    'couple_dares': 'Couple Dares'
  };

  return categoryMap[category] || category;
};

/**
 * Get content type from game mode
 * 
 * @param {string} gameMode - Game mode (questions or dares)
 * @returns {string} - Content type for display
 */
export const getContentTypeLabel = (gameMode) => {
  return gameMode === 'questions' ? 'Questions' : 'Dares';
};
