/**
 * Content Service
 *
 * Provides methods for content management operations.
 */
import api from './api';

/**
 * Get all content with optional filtering
 *
 * @param {Object} params - Query parameters
 * @param {number} params.page - Page number (default: 1)
 * @param {number} params.limit - Items per page (default: 50)
 * @param {string} params.gameMode - Filter by game mode (questions/dares)
 * @param {string} params.category - Filter by category
 * @param {string} params.categoryPattern - Filter by category pattern (e.g., 'casual_')
 * @param {string} params.search - Search term for text_en, text_es, or text_dom
 * @param {boolean} params.active - Filter by active status
 * @returns {Promise<Object>} - Response containing content items and pagination info
 */
export const getAllContent = async (params = {}) => {
  try {
    console.log('Content service: Fetching all content with params:', params);

    // Build query string from params
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.gameMode) queryParams.append('gameMode', params.gameMode);
    if (params.category) queryParams.append('category', params.category);
    if (params.categoryPattern) queryParams.append('categoryPattern', params.categoryPattern);
    if (params.search) queryParams.append('search', params.search);
    if (params.active !== undefined) queryParams.append('active', params.active);

    const queryString = queryParams.toString();
    const url = `/admin/content${queryString ? `?${queryString}` : ''}`;

    console.log(`Content service: API call to ${url}`);
    const response = await api.get(url);

    // Check if the response has the expected structure
    // If the response is an array (old format), convert it to the new format
    if (Array.isArray(response.data)) {
      console.log(`Content service: Received ${response.data.length} items (array format)`);
      return {
        items: response.data,
        totalItems: response.data.length,
        totalPages: 1,
        currentPage: 1
      };
    }

    // If the response is already in the new format
    console.log(`Content service: Received ${response.data.items?.length || 0} items`);
    return response.data;
  } catch (error) {
    console.error('Content service: Error fetching all content:', error);
    throw error;
  }
};

/**
 * Get content by ID
 *
 * @param {string} id - Content ID
 * @returns {Promise<Object>} - Content item
 */
export const getContentById = async (id) => {
  try {
    console.log(`Content service: Fetching content with ID ${id}`);
    const response = await api.get(`/admin/content/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Content service: Error fetching content with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Create new content
 *
 * @param {Object} data - Content data
 * @param {string} data.text_en - English text
 * @param {string} data.text_es - Spanish text
 * @param {string} data.text_dom - Dominican Spanish text
 * @param {string} data.category - Content category
 * @param {string} data.gameMode - Game mode (questions/dares)
 * @param {boolean} data.active - Active status
 * @returns {Promise<Object>} - Created content
 */
export const createContent = async (data) => {
  try {
    console.log('Content service: Creating new content', data);
    const response = await api.post('/admin/content', data);
    console.log('Content service: Content created successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Content service: Error creating content:', error);
    throw error;
  }
};

/**
 * Update existing content
 *
 * @param {string} id - Content ID
 * @param {Object} data - Updated content data
 * @returns {Promise<Object>} - Updated content
 */
export const updateContent = async (id, data) => {
  try {
    console.log(`Content service: Updating content with ID ${id}`, data);
    const response = await api.put(`/admin/content/${id}`, data);
    console.log('Content service: Content updated successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error(`Content service: Error updating content with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Delete content
 *
 * @param {string} id - Content ID
 * @returns {Promise<Object>} - Deletion result
 */
export const deleteContent = async (id) => {
  try {
    console.log(`Content service: Deleting content with ID ${id}`);
    const response = await api.delete(`/admin/content/${id}`);
    console.log('Content service: Content deleted successfully');
    return response.data;
  } catch (error) {
    console.error(`Content service: Error deleting content with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Transform content data for display or editing
 *
 * @param {Object} content - Content data from API
 * @returns {Object} - Transformed content for UI
 */
export const transformContentForUI = (content) => {
  return {
    id: content.id,
    text_en: content.text_en,
    text_es: content.text_es,
    text_dom: content.text_dom,
    category: content.category,
    gameMode: content.gameMode,
    active: content.active,
    createdAt: content.createdAt ? new Date(content.createdAt) : null,
    updatedAt: content.updatedAt ? new Date(content.updatedAt) : null
  };
};

export default {
  getAllContent,
  getContentById,
  createContent,
  updateContent,
  deleteContent,
  transformContentForUI
};
