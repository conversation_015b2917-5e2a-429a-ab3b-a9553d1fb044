import api from './api';

/**
 * Get current ad settings
 * @returns {Promise<Object>} Ad settings object
 */
export const getAdSettings = async () => {
  try {
    console.log('Fetching ad settings from API');
    const response = await api.get('/admin/ad-settings');
    console.log('Ad settings fetched successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching ad settings:', error);
    throw error;
  }
};

/**
 * Update ad settings
 * @param {Object} settings - Settings object with watchAdsButtonEnabled and requiredAdsCount
 * @returns {Promise<Object>} Updated ad settings object
 */
export const updateAdSettings = async (settings) => {
  try {
    console.log('Updating ad settings:', settings);

    // Validate input
    if (typeof settings.watchAdsButtonEnabled !== 'boolean') {
      throw new Error('watchAdsButtonEnabled must be a boolean');
    }

    if (!Number.isInteger(settings.requiredAdsCount) ||
        settings.requiredAdsCount < 1 ||
        settings.requiredAdsCount > 10) {
      throw new Error('requiredAdsCount must be an integer between 1 and 10');
    }

    const response = await api.put('/admin/ad-settings', settings);
    console.log('Ad settings updated successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating ad settings:', error);
    throw error;
  }
};
