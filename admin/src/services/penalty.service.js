import api from './api';

class PenaltyService {
  // Get all penalties with optional filters
  async getAllPenalties(filters = {}) {
    try {
      const params = new URLSearchParams();

      if (filters.category) {
        params.append('category', filters.category);
      }

      if (filters.search) {
        params.append('search', filters.search);
      }

      if (filters.active !== undefined) {
        params.append('active', filters.active);
      }

      const response = await api.get(`/admin/penalties?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching penalties:', error);
      throw error;
    }
  }

  // Get penalty by ID
  async getPenaltyById(id) {
    try {
      const response = await api.get(`/admin/penalties/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching penalty by ID:', error);
      throw error;
    }
  }

  // Create new penalty
  async createPenalty(penaltyData) {
    try {
      const response = await api.post('/admin/penalties', penaltyData);
      return response.data;
    } catch (error) {
      console.error('Error creating penalty:', error);
      throw error;
    }
  }

  // Update penalty
  async updatePenalty(id, penaltyData) {
    try {
      const response = await api.put(`/admin/penalties/${id}`, penaltyData);
      return response.data;
    } catch (error) {
      console.error('Error updating penalty:', error);
      throw error;
    }
  }

  // Delete penalty
  async deletePenalty(id) {
    try {
      const response = await api.delete(`/admin/penalties/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting penalty:', error);
      throw error;
    }
  }

  // Get penalty categories
  getPenaltyCategories() {
    return [
      { value: 'drinking', label: 'Drinking' },
      { value: 'physical', label: 'Physical' },
      { value: 'social', label: 'Social' },
      { value: 'silly', label: 'Silly' },
      { value: 'creative', label: 'Creative' }
    ];
  }

  // Validate penalty data
  validatePenalty(penaltyData) {
    const errors = {};

    if (!penaltyData.text_en || penaltyData.text_en.trim() === '') {
      errors.text_en = 'English text is required';
    }

    if (!penaltyData.text_es || penaltyData.text_es.trim() === '') {
      errors.text_es = 'Spanish text is required';
    }

    if (!penaltyData.text_dom || penaltyData.text_dom.trim() === '') {
      errors.text_dom = 'Dominican text is required';
    }

    if (!penaltyData.category || penaltyData.category.trim() === '') {
      errors.category = 'Category is required';
    }

    // Validate premium and default logic
    if (penaltyData.isPremium && penaltyData.isDefaultFree) {
      errors.isDefaultFree = 'Premium penalties cannot be default for free users';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  // Format penalty for display
  formatPenaltyForDisplay(penalty) {
    return {
      ...penalty,
      categoryLabel: this.getPenaltyCategories().find(cat => cat.value === penalty.category)?.label || penalty.category,
      statusLabel: penalty.active ? 'Active' : 'Inactive',
      createdAtFormatted: new Date(penalty.createdAt).toLocaleDateString(),
      updatedAtFormatted: new Date(penalty.updatedAt).toLocaleDateString()
    };
  }

  // Get penalty statistics
  async getPenaltyStats() {
    try {
      const penalties = await this.getAllPenalties();
      const stats = {
        total: penalties.items.length,
        active: penalties.items.filter(p => p.active).length,
        inactive: penalties.items.filter(p => !p.active).length,
        byCategory: {}
      };

      // Count by category
      this.getPenaltyCategories().forEach(category => {
        stats.byCategory[category.value] = penalties.items.filter(p => p.category === category.value).length;
      });

      return stats;
    } catch (error) {
      console.error('Error getting penalty stats:', error);
      throw error;
    }
  }

  // Bulk operations
  async bulkUpdatePenalties(penaltyIds, updateData) {
    try {
      const promises = penaltyIds.map(id => this.updatePenalty(id, updateData));
      const results = await Promise.allSettled(promises);

      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      return {
        successful,
        failed,
        total: penaltyIds.length
      };
    } catch (error) {
      console.error('Error in bulk update:', error);
      throw error;
    }
  }

  async bulkDeletePenalties(penaltyIds) {
    try {
      const promises = penaltyIds.map(id => this.deletePenalty(id));
      const results = await Promise.allSettled(promises);

      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      return {
        successful,
        failed,
        total: penaltyIds.length
      };
    } catch (error) {
      console.error('Error in bulk delete:', error);
      throw error;
    }
  }

  // Search penalties
  async searchPenalties(searchTerm, filters = {}) {
    try {
      const searchFilters = {
        ...filters,
        search: searchTerm
      };

      return await this.getAllPenalties(searchFilters);
    } catch (error) {
      console.error('Error searching penalties:', error);
      throw error;
    }
  }

  // Export penalties
  async exportPenalties(format = 'json') {
    try {
      const penalties = await this.getAllPenalties();

      if (format === 'csv') {
        return this.convertToCSV(penalties.items);
      }

      return JSON.stringify(penalties.items, null, 2);
    } catch (error) {
      console.error('Error exporting penalties:', error);
      throw error;
    }
  }

  // Convert penalties to CSV format
  convertToCSV(penalties) {
    const headers = ['ID', 'English Text', 'Spanish Text', 'Dominican Text', 'Category', 'Active', 'Created At', 'Updated At'];
    const csvContent = [
      headers.join(','),
      ...penalties.map(penalty => [
        penalty.id,
        `"${penalty.text_en.replace(/"/g, '""')}"`,
        `"${penalty.text_es.replace(/"/g, '""')}"`,
        `"${penalty.text_dom.replace(/"/g, '""')}"`,
        penalty.category,
        penalty.active,
        penalty.createdAt,
        penalty.updatedAt
      ].join(','))
    ].join('\n');

    return csvContent;
  }

  // Import penalties from CSV
  async importPenaltiesFromCSV(csvContent) {
    try {
      const lines = csvContent.split('\n');
      const headers = lines[0].split(',');
      const penalties = [];

      for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim() === '') continue;

        const values = lines[i].split(',');
        const penalty = {
          text_en: values[1]?.replace(/^"|"$/g, '').replace(/""/g, '"'),
          text_es: values[2]?.replace(/^"|"$/g, '').replace(/""/g, '"'),
          text_dom: values[3]?.replace(/^"|"$/g, '').replace(/""/g, '"'),
          category: values[4],
          active: values[5] === 'true'
        };

        const validation = this.validatePenalty(penalty);
        if (validation.isValid) {
          penalties.push(penalty);
        }
      }

      // Create penalties in batch
      const results = await Promise.allSettled(
        penalties.map(penalty => this.createPenalty(penalty))
      );

      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      return {
        successful,
        failed,
        total: penalties.length
      };
    } catch (error) {
      console.error('Error importing penalties:', error);
      throw error;
    }
  }
}

export default new PenaltyService();
