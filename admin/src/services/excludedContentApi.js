import api from './api';

class ExcludedContentAPI {
  /**
   * Exclude content from future AI generations
   * @param {Object} contentData - Content to exclude
   * @param {string} reason - Reason for exclusion
   * @returns {Promise<Object>} - API response
   */
  async excludeContent(contentData, reason = 'Manual exclusion by admin') {
    try {
      const response = await api.post('/admin/excluded-content', {
        contentData,
        reason
      });
      return response.data;
    } catch (error) {
      console.error('Error excluding content:', error);
      throw error;
    }
  }

  /**
   * Get all excluded content or filter by gameMode/category
   * @param {string} gameMode - Optional: questions or dares
   * @param {string} category - Optional: category name
   * @returns {Promise<Array>} - Array of excluded content
   */
  async getExcludedContent(gameMode = null, category = null) {
    try {
      const params = {};
      if (gameMode) params.gameMode = gameMode;
      if (category) params.category = category;

      const response = await api.get('/admin/excluded-content', { params });
      return response.data.data || [];
    } catch (error) {
      console.error('Error getting excluded content:', error);
      throw error;
    }
  }

  /**
   * Remove content from exclusion list
   * @param {string} excludedContentId - ID of excluded content to remove
   * @returns {Promise<Object>} - API response
   */
  async removeExclusion(excludedContentId) {
    try {
      const response = await api.delete(`/admin/excluded-content/${excludedContentId}`);
      return response.data;
    } catch (error) {
      console.error('Error removing exclusion:', error);
      throw error;
    }
  }

  /**
   * Reset all excluded content (clear entire collection)
   * @returns {Promise<Object>} - API response with deletion count
   */
  async resetAllExcludedContent() {
    try {
      const response = await api.delete('/admin/excluded-content');
      return response.data;
    } catch (error) {
      console.error('Error resetting excluded content:', error);
      throw error;
    }
  }

  /**
   * Get excluded content statistics
   * @returns {Promise<Object>} - Statistics about excluded content
   */
  async getExclusionStats() {
    try {
      const response = await api.get('/admin/excluded-content/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error getting exclusion stats:', error);
      throw error;
    }
  }
}

export default new ExcludedContentAPI();
