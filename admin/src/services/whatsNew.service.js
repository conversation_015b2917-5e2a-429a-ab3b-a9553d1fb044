import api from './api';

/**
 * What's New Service
 * 
 * This service handles communication with the backend What's New endpoints.
 * It provides functions to manage What's New logs for the admin panel.
 */

/**
 * Get all What's New logs for admin panel
 * 
 * @returns {Promise<Array>} - Array of What's New logs
 */
export const getAllWhatsNewLogs = async () => {
  try {
    console.log('What\'s New Service: Fetching all logs for admin');
    
    const response = await api.get('/whatsnew/admin');
    
    console.log('What\'s New Service: Successfully fetched logs');
    return response.data;
  } catch (error) {
    console.error('What\'s New Service: Error fetching logs', error);
    throw error;
  }
};

/**
 * Create a new What's New log
 * 
 * @param {Object} logData - What's New log data
 * @param {string} logData.type - Visual theme type (dark, red, cyan, purple)
 * @param {string} logData.date - Display date
 * @param {string} logData.title - Log title
 * @param {string} logData.description - Log description
 * @param {string} logData.videoUrl - Optional video URL
 * @param {string} logData.appVersion - Minimum required app version
 * @returns {Promise<Object>} - Created log data
 */
export const createWhatsNewLog = async (logData) => {
  try {
    console.log('What\'s New Service: Creating new log', logData);
    
    const response = await api.post('/whatsnew/admin', logData);
    
    console.log('What\'s New Service: Successfully created log');
    return response.data;
  } catch (error) {
    console.error('What\'s New Service: Error creating log', error);
    throw error;
  }
};

/**
 * Update an existing What's New log
 * 
 * @param {string} id - Log ID
 * @param {Object} logData - Updated log data
 * @returns {Promise<Object>} - Updated log data
 */
export const updateWhatsNewLog = async (id, logData) => {
  try {
    console.log('What\'s New Service: Updating log', id, logData);
    
    const response = await api.put(`/whatsnew/admin/${id}`, logData);
    
    console.log('What\'s New Service: Successfully updated log');
    return response.data;
  } catch (error) {
    console.error('What\'s New Service: Error updating log', error);
    throw error;
  }
};

/**
 * Delete a What's New log
 * 
 * @param {string} id - Log ID
 * @returns {Promise<Object>} - Deletion confirmation
 */
export const deleteWhatsNewLog = async (id) => {
  try {
    console.log('What\'s New Service: Deleting log', id);
    
    const response = await api.delete(`/whatsnew/admin/${id}`);
    
    console.log('What\'s New Service: Successfully deleted log');
    return response.data;
  } catch (error) {
    console.error('What\'s New Service: Error deleting log', error);
    throw error;
  }
};

export default {
  getAllWhatsNewLogs,
  createWhatsNewLog,
  updateWhatsNewLog,
  deleteWhatsNewLog
};
