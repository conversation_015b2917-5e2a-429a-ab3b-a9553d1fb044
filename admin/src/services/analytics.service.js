import api from './api';

/**
 * Get analytics data for dashboard charts
 *
 * @param {string} timePeriod - Time period filter (thisWeek, lastWeek, thisMonth, lastMonth, last3Months, all)
 * @returns {Promise<Object>} - Analytics data with chart data and summary
 */
export const getAnalytics = async (timePeriod = 'all') => {
  try {
    console.log(`Analytics service: Fetching analytics data for period: ${timePeriod}`);
    
    const response = await api.get(`/admin/analytics?timePeriod=${timePeriod}`);
    
    console.log('Analytics service: Received analytics data:', response.data);
    return response.data;
  } catch (error) {
    console.error('Analytics service: Error fetching analytics data:', error);
    throw error;
  }
};

/**
 * Time period options for the filter dropdown
 */
export const TIME_PERIOD_OPTIONS = [
  { value: 'thisWeek', label: 'This Week' },
  { value: 'lastWeek', label: 'Last Week' },
  { value: 'thisMonth', label: 'This Month' },
  { value: 'lastMonth', label: 'Last Month' },
  { value: 'last3Months', label: 'Last 3 Months' },
  { value: 'all', label: 'All Time' }
];
