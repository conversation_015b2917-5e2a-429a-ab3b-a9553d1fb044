/**
 * Authentication Service
 * 
 * Provides methods for user authentication and token management.
 */
import api from './api';

/**
 * <PERSON><PERSON> with email and password
 * 
 * @param {Object} credentials - User credentials
 * @param {string} credentials.email - User email
 * @param {string} credentials.password - User password
 * @returns {Promise<Object>} - Response containing token and user data
 */
export const login = async (credentials) => {
  try {
    console.log('Auth service: Attempting login with:', credentials.email);
    const response = await api.post('/admin/login', credentials);
    console.log('Auth service: Login successful');
    
    // Store token in localStorage
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      console.log('Auth service: Token and user stored in localStorage');
    }
    
    return response.data;
  } catch (error) {
    console.error('Auth service: Login failed', error);
    const errorMessage = error.response?.data?.message || '<PERSON><PERSON> failed. Please check your credentials.';
    throw new Error(errorMessage);
  }
};

/**
 * Logout the current user
 * Removes token and user data from localStorage
 */
export const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  console.log('Auth service: User logged out, storage cleared');
};

/**
 * Get the current authentication token
 * 
 * @returns {string|null} - The current JWT token or null if not authenticated
 */
export const getToken = () => {
  return localStorage.getItem('token');
};

/**
 * Get the current authenticated user
 * 
 * @returns {Object|null} - The current user object or null if not authenticated
 */
export const getCurrentUser = () => {
  const userStr = localStorage.getItem('user');
  if (!userStr) return null;
  
  try {
    return JSON.parse(userStr);
  } catch (error) {
    console.error('Auth service: Error parsing user from localStorage', error);
    return null;
  }
};

/**
 * Check if user is authenticated
 * 
 * @returns {boolean} - True if user is authenticated, false otherwise
 */
export const isAuthenticated = () => {
  return !!getToken();
};

export default {
  login,
  logout,
  getToken,
  getCurrentUser,
  isAuthenticated
};
