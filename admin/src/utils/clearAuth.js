/**
 * Utility script to clear authentication data
 * 
 * This script can be imported and executed to clear any stored authentication data
 * from localStorage, forcing the user to log in again.
 */

export const clearAuthData = () => {
  console.log('Clearing authentication data from localStorage');
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  console.log('Authentication data cleared');
};

// Auto-execute when imported
clearAuthData();

export default clearAuthData;
