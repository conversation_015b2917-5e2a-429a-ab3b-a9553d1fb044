import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { Spinner, Center, Text, VStack } from '@chakra-ui/react';

const PrivateRoute = ({ children }) => {
  const { isAuthenticated, loading, user } = useAuth();

  useEffect(() => {
    console.log('PrivateRoute authentication state:', { isAuthenticated, loading, user });
  }, [isAuthenticated, loading, user]);

  if (loading) {
    return (
      <Center h="100vh">
        <VStack spacing={4}>
          <Spinner 
            thickness="4px"
            speed="0.65s"
            emptyColor="gray.200"
            color="brand.500"
            size="xl"
          />
          <Text>Loading authentication...</Text>
        </VStack>
      </Center>
    );
  }

  if (!isAuthenticated) {
    console.log('Not authenticated, redirecting to login');
    return <Navigate to="/login" />;
  }

  console.log('Authenticated, showing protected content');
  return children;
};

export default PrivateRoute;