import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>uButton,
  MenuList,
  MenuItem,
  IconButton,
  Toolt<PERSON>
} from '@chakra-ui/react';
import { FiMoreVertical, FiRotateCcw } from 'react-icons/fi';

const MoreMenu = ({ onResetExcludedContent }) => {
  const handleResetClick = () => {
    if (onResetExcludedContent) {
      onResetExcludedContent();
    }
  };

  return (
    <Menu>
      <Tooltip label="More options" hasArrow>
        <MenuButton
          as={IconButton}
          icon={<FiMoreVertical />}
          variant="ghost"
          size="sm"
          aria-label="More options"
        />
      </Tooltip>
      <MenuList>
        <MenuItem
          icon={<FiRotateCcw />}
          onClick={handleResetClick}
        >
          Reset Excluded Content
        </MenuItem>
      </MenuList>
    </Menu>
  );
};

export default MoreMenu;
