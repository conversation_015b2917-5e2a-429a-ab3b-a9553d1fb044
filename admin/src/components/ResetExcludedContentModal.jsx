import React, { useState } from 'react';
import {
  <PERSON>dal,
  Modal<PERSON><PERSON>lay,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  Text,
  Alert,
  AlertIcon,
  VStack,
  HStack,
  Icon,
  useToast
} from '@chakra-ui/react';
import { FiAlertTriangle } from 'react-icons/fi';
import excludedContentApi from '../services/excludedContentApi';

const ResetExcludedContentModal = ({ isOpen, onClose, onReset }) => {
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  const handleReset = async () => {
    try {
      setIsLoading(true);

      const result = await excludedContentApi.resetAllExcludedContent();

      // Show success toast
      toast({
        title: 'Success',
        description: result.message || 'All excluded content has been cleared.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Notify parent component
      if (onReset) {
        onReset(result);
      }

      // Close modal
      onClose();

    } catch (error) {
      console.error('Error resetting excluded content:', error);

      const errorMessage = error.response?.data?.message || 'Failed to reset excluded content';
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 6000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={3}>
            <Icon as={FiAlertTriangle} color="red.500" boxSize={6} />
            <Text>Reset Excluded Content</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton isDisabled={isLoading} />

        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Text fontSize="sm" color="gray.600">
              Are you sure you want to clear all excluded content? This action will:
            </Text>

            <VStack spacing={1} align="stretch" pl={4}>
              <Text fontSize="sm" color="gray.600">• Remove all content from the exclusion list</Text>
              <Text fontSize="sm" color="gray.600">• Allow previously excluded content to be generated again</Text>
              <Text fontSize="sm" color="gray.600">• Cannot be undone</Text>
            </VStack>

            <Alert status="warning" borderRadius="md">
              <AlertIcon />
              <Text fontSize="sm">
                <strong>Warning:</strong> This action cannot be undone. All excluded content will be permanently removed from the exclusion list.
              </Text>
            </Alert>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button
              variant="ghost"
              onClick={onClose}
              isDisabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              colorScheme="red"
              onClick={handleReset}
              isLoading={isLoading}
              loadingText="Resetting..."
            >
              Reset All Excluded Content
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ResetExcludedContentModal;
