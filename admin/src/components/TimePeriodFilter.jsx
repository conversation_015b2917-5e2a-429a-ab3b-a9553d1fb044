import React from 'react';
import {
  Select,
  FormControl,
  FormLabel,
  Box
} from '@chakra-ui/react';
import { TIME_PERIOD_OPTIONS } from '../services/analytics.service';

const TimePeriodFilter = ({ value, onChange, isLoading = false }) => {
  return (
    <Box maxW="200px">
      <FormControl>
        <FormLabel fontSize="sm" mb={1}>Time Period</FormLabel>
        <Select
          value={value}
          onChange={(e) => onChange(e.target.value)}
          isDisabled={isLoading}
          size="sm"
          bg="white"
          borderColor="gray.300"
          _hover={{ borderColor: 'gray.400' }}
          _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
        >
          {TIME_PERIOD_OPTIONS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default TimePeriodFilter;
