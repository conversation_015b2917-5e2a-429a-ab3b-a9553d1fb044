import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  Flex,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useColorModeValue,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText
} from '@chakra-ui/react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { getAnalytics } from '../services/analytics.service';
import TimePeriodFilter from './TimePeriodFilter';

const AnalyticsChart = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [timePeriod, setTimePeriod] = useState('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const fetchAnalytics = async (period) => {
    try {
      setLoading(true);
      setError(null);
      const data = await getAnalytics(period);
      setAnalyticsData(data);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics(timePeriod);
  }, [timePeriod]);

  const handleTimePeriodChange = (newPeriod) => {
    setTimePeriod(newPeriod);
  };

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Box
          bg={cardBg}
          p={3}
          border="1px"
          borderColor={borderColor}
          borderRadius="md"
          boxShadow="lg"
        >
          <Text fontWeight="bold" mb={2}>{label}</Text>
          {payload.map((entry, index) => (
            <Text key={index} color={entry.color} fontSize="sm">
              {entry.dataKey}: {entry.value}
            </Text>
          ))}
        </Box>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card shadow="md" bg={cardBg}>
        <CardBody>
          <Flex justify="center" align="center" minH="400px">
            <Spinner size="xl" color="brand.500" />
          </Flex>
        </CardBody>
      </Card>
    );
  }

  if (error) {
    return (
      <Card shadow="md" bg={cardBg}>
        <CardBody>
          <Alert status="error">
            <AlertIcon />
            <Box>
              <AlertTitle>Error!</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Box>
          </Alert>
        </CardBody>
      </Card>
    );
  }

  if (!analyticsData || !analyticsData.chartData || analyticsData.chartData.length === 0) {
    return (
      <Card shadow="md" bg={cardBg}>
        <CardHeader>
          <Flex justify="space-between" align="center">
            <Heading size="md">Content Analytics</Heading>
            <TimePeriodFilter
              value={timePeriod}
              onChange={handleTimePeriodChange}
              isLoading={loading}
            />
          </Flex>
        </CardHeader>
        <CardBody>
          <Alert status="info">
            <AlertIcon />
            <Box>
              <AlertTitle>No Data Available</AlertTitle>
              <AlertDescription>
                No content found for the selected time period.
              </AlertDescription>
            </Box>
          </Alert>
        </CardBody>
      </Card>
    );
  }

  return (
    <Box>
      {/* Summary Stats */}
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={5} mb={6}>
        <Card shadow="md" bg={cardBg}>
          <CardBody>
            <Stat>
              <StatLabel fontSize="sm">Total Content ({timePeriod})</StatLabel>
              <StatNumber fontSize="2xl">{analyticsData.summary.totalContent}</StatNumber>
              <StatHelpText>All active entries</StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card shadow="md" bg={cardBg}>
          <CardBody>
            <Stat>
              <StatLabel fontSize="sm">Questions</StatLabel>
              <StatNumber fontSize="2xl">{analyticsData.summary.totalQuestions}</StatNumber>
              <StatHelpText>
                {analyticsData.summary.totalContent > 0 
                  ? `${((analyticsData.summary.totalQuestions / analyticsData.summary.totalContent) * 100).toFixed(1)}% of total`
                  : '0% of total'
                }
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card shadow="md" bg={cardBg}>
          <CardBody>
            <Stat>
              <StatLabel fontSize="sm">Dares</StatLabel>
              <StatNumber fontSize="2xl">{analyticsData.summary.totalDares}</StatNumber>
              <StatHelpText>
                {analyticsData.summary.totalContent > 0 
                  ? `${((analyticsData.summary.totalDares / analyticsData.summary.totalContent) * 100).toFixed(1)}% of total`
                  : '0% of total'
                }
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Chart */}
      <Card shadow="md" bg={cardBg}>
        <CardHeader>
          <Flex justify="space-between" align="center">
            <Heading size="md">Content by Category</Heading>
            <TimePeriodFilter
              value={timePeriod}
              onChange={handleTimePeriodChange}
              isLoading={loading}
            />
          </Flex>
        </CardHeader>
        <CardBody>
          <Box h="400px" w="100%">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={analyticsData.chartData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="category" 
                  tick={{ fontSize: 12 }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar 
                  dataKey="questions" 
                  fill="#3182CE" 
                  name="Questions"
                  radius={[2, 2, 0, 0]}
                />
                <Bar 
                  dataKey="dares" 
                  fill="#38A169" 
                  name="Dares"
                  radius={[2, 2, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </CardBody>
      </Card>
    </Box>
  );
};

export default AnalyticsChart;
