import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from '@chakra-ui/react';
import { FiX } from 'react-icons/fi';
import excludedContentApi from '../services/excludedContentApi';

const ExcludeButton = ({
  content,
  gameMode,
  category,
  onExcluded,
  isExcluded = false,
  disabled = false
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleExclude = async () => {
    if (isLoading || disabled || isExcluded) return;

    try {
      setIsLoading(true);

      // Prepare content data for exclusion
      const contentData = {
        text_en: content.text_en,
        text_es: content.text_es,
        text_dom: content.text_dom,
        gameMode: gameMode,
        category: category,
        id: content._id || content.id
      };

      await excludedContentApi.excludeContent(contentData, 'Manual exclusion by admin');

      // Notify parent component
      if (onExcluded) {
        onExcluded(content);
      }

    } catch (error) {
      console.error('Error excluding content:', error);

      // Show error message to user
      const errorMessage = error.response?.data?.message || 'Failed to exclude content';
      alert(`Error: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (isExcluded) {
    return (
      <IconButton
        size="sm"
        variant="ghost"
        colorScheme="gray"
        icon={<FiX />}
        isDisabled={true}
        aria-label="Content excluded"
      />
    );
  }

  return (
    <Tooltip
      label={isLoading ? 'Excluding...' : 'Exclude this content from future generations'}
      hasArrow
    >
      <IconButton
        size="sm"
        variant="ghost"
        colorScheme="red"
        icon={isLoading ? <Spinner size="sm" /> : <FiX />}
        onClick={handleExclude}
        isLoading={isLoading}
        isDisabled={disabled}
        aria-label="Exclude content"
        _hover={{
          bg: 'red.50',
          color: 'red.600'
        }}
      />
    </Tooltip>
  );
};

export default ExcludeButton;
