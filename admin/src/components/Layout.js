import React from 'react';
import { Box, <PERSON>lex, Heading, IconButton, useDisclosure, Drawer, DrawerBody, DrawerHeader,
  DrawerOverlay, DrawerContent, DrawerCloseButton, VStack, Text, Link,
  HStack, Icon, Button } from '@chakra-ui/react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { FiMenu, FiHome, FiList, FiLogOut, FiPlus, FiAlertTriangle, FiSettings, FiBell, FiGift, FiRefreshCw } from 'react-icons/fi';
import { useAuth } from '../hooks/useAuth';

const Layout = ({ children, title }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const menuItems = [
    { name: 'Dashboard', icon: FiHome, path: '/' },
    { name: 'Content', icon: FiList, path: '/content' },
    { name: 'Add Content', icon: FiPlus, path: '/content/add' },
    { name: 'Penalties', icon: FiAlertTriangle, path: '/penalties' },
    { name: 'Add Penalty', icon: FiPlus, path: '/penalties/add' },
    { name: 'What\'s New', icon: FiGift, path: '/whatsnew' },
    { name: 'Ad Settings', icon: FiSettings, path: '/ad-settings' },
    { name: 'Push Notifications', icon: FiBell, path: '/push-notifications' },
    { name: 'Migration Settings', icon: FiRefreshCw, path: '/migration-settings' },
  ];

  return (
    <Box minH="100vh" bg="gray.50">
      {/* Top Navigation */}
      <Flex
        as="nav"
        align="center"
        justify="space-between"
        wrap="wrap"
        padding={4}
        bg="brand.500"
        color="white"
        boxShadow="md"
      >
        <IconButton
          display={{ base: 'flex', md: 'none' }}
          onClick={onOpen}
          variant="outline"
          aria-label="open menu"
          icon={<FiMenu />}
          color="white"
          _hover={{ bg: 'brand.600' }}
        />

        <Heading as="h1" size="lg" letterSpacing="tight">
          TapTrap Admin
        </Heading>

        <Button
          variant="outline"
          onClick={handleLogout}
          leftIcon={<FiLogOut />}
          color="white"
          _hover={{ bg: 'brand.600' }}
        >
          Logout
        </Button>
      </Flex>

      {/* Mobile Drawer */}
      <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
        <DrawerOverlay />
        <DrawerContent>
          <DrawerCloseButton />
          <DrawerHeader borderBottomWidth="1px">TapTrap Admin</DrawerHeader>
          <DrawerBody>
            <VStack spacing={4} align="stretch" mt={4}>
              {menuItems.map((item) => (
                <Link
                  as={RouterLink}
                  to={item.path}
                  key={item.name}
                  onClick={onClose}
                  _hover={{ textDecoration: 'none' }}
                >
                  <HStack spacing={2} p={2} rounded="md" _hover={{ bg: 'brand.50' }}>
                    <Icon as={item.icon} />
                    <Text>{item.name}</Text>
                  </HStack>
                </Link>
              ))}
            </VStack>
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      {/* Main Content */}
      <Flex>
        {/* Desktop Sidebar */}
        <Box
          display={{ base: 'none', md: 'block' }}
          w="240px"
          bg="white"
          boxShadow="md"
          h="calc(100vh - 64px)"
          position="fixed"
          p={4}
        >
          <VStack spacing={4} align="stretch" mt={4}>
            {menuItems.map((item) => (
              <Link
                as={RouterLink}
                to={item.path}
                key={item.name}
                _hover={{ textDecoration: 'none' }}
              >
                <HStack
                  spacing={2}
                  p={2}
                  rounded="md"
                  _hover={{ bg: 'brand.50' }}
                >
                  <Icon as={item.icon} />
                  <Text>{item.name}</Text>
                </HStack>
              </Link>
            ))}
          </VStack>
        </Box>

        {/* Page Content */}
        <Box
          flex="1"
          p={5}
          ml={{ base: 0, md: '240px' }}
          mt={5}
        >
          <Heading mb={6}>{title}</Heading>
          {children}
        </Box>
      </Flex>
    </Box>
  );
};

export default Layout;