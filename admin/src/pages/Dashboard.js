import React, { useState, useEffect } from 'react';
import {
  Box,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatGroup,
  StatArrow,
  Text,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Divider,
  Stack,
  useColorModeValue,
} from '@chakra-ui/react';
import Layout from '../components/Layout';
import AnalyticsChart from '../components/AnalyticsChart';
import { getAllContent } from '../services/content.service';

const Dashboard = () => {
  const [stats, setStats] = useState({
    total: 0,
    questions: 0,
    dares: 0,
    categories: {},
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Get all content without pagination to calculate stats
        const response = await getAllContent({ limit: 1000 });

        // Check if response has the expected structure
        if (!response || !Array.isArray(response.items)) {
          console.error('Unexpected response format:', response);
          return;
        }

        const content = response.items;

        // Calculate stats
        const categoryCounts = {};
        let questionCount = 0;
        let dareCount = 0;

        content.forEach(item => {
          // Count by category
          if (!categoryCounts[item.category]) {
            categoryCounts[item.category] = 0;
          }
          categoryCounts[item.category]++;

          // Count by game mode
          if (item.gameMode === 'questions') {
            questionCount++;
          } else if (item.gameMode === 'dares') {
            dareCount++;
          }
        });

        setStats({
          total: content.length,
          questions: questionCount,
          dares: dareCount,
          categories: categoryCounts,
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const cardBg = useColorModeValue('white', 'gray.700');

  return (
    <Layout title="Dashboard">
      {loading ? (
        <Text>Loading stats...</Text>
      ) : (
        <>
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={5} mb={8}>
            <Card shadow="md" bg={cardBg}>
              <CardBody>
                <Stat>
                  <StatLabel fontSize="lg">Total Content</StatLabel>
                  <StatNumber fontSize="4xl">{stats.total}</StatNumber>
                  <StatHelpText>All active entries</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card shadow="md" bg={cardBg}>
              <CardBody>
                <Stat>
                  <StatLabel fontSize="lg">Questions</StatLabel>
                  <StatNumber fontSize="4xl">{stats.questions}</StatNumber>
                  <StatHelpText>
                    {((stats.questions / stats.total) * 100).toFixed(1)}% of total
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card shadow="md" bg={cardBg}>
              <CardBody>
                <Stat>
                  <StatLabel fontSize="lg">Dares</StatLabel>
                  <StatNumber fontSize="4xl">{stats.dares}</StatNumber>
                  <StatHelpText>
                    {((stats.dares / stats.total) * 100).toFixed(1)}% of total
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Analytics Chart Section */}
          <Box mb={8}>
            <AnalyticsChart />
          </Box>

          <Card shadow="md" bg={cardBg} mb={8}>
            <CardHeader>
              <Heading size="md">Content by Category (Legacy View)</Heading>
            </CardHeader>
            <Divider />
            <CardBody>
              <Stack spacing={4}>
                {Object.entries(stats.categories).map(([category, count]) => (
                  <Box key={category} p={2} borderWidth="1px" borderRadius="md">
                    <StatGroup>
                      <Stat>
                        <StatLabel>{category.replace('_', ' ').toUpperCase()}</StatLabel>
                        <StatNumber>{count}</StatNumber>
                        <StatHelpText>
                          <StatArrow type="increase" />
                          {((count / stats.total) * 100).toFixed(1)}%
                        </StatHelpText>
                      </Stat>
                    </StatGroup>
                  </Box>
                ))}
              </Stack>
            </CardBody>
          </Card>
        </>
      )}
    </Layout>
  );
};

export default Dashboard;