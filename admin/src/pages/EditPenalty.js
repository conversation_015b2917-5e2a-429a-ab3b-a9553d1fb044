import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  Switch,
  VStack,
  HStack,
  useToast,
  FormErrorMessage,
  Card,
  CardBody,
  Heading,
  Text,
  Divider,
  Spinner,
  Center
} from '@chakra-ui/react';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../components/Layout';
import penaltyService from '../services/penalty.service';

const EditPenalty = () => {
  const { id } = useParams();
  const [formData, setFormData] = useState({
    text_en: '',
    text_es: '',
    text_dom: '',
    category: '',
    active: true,
    isPremium: false,
    isDefaultFree: false,
    isDefaultPremium: false
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const toast = useToast();
  const navigate = useNavigate();

  // Load penalty data
  useEffect(() => {
    const loadPenalty = async () => {
      try {
        const penalty = await penaltyService.getPenaltyById(id);
        setFormData({
          text_en: penalty.text_en || '',
          text_es: penalty.text_es || '',
          text_dom: penalty.text_dom || '',
          category: penalty.category || '',
          active: penalty.active !== false,
          isPremium: penalty.isPremium || false,
          isDefaultFree: penalty.isDefaultFree || false,
          isDefaultPremium: penalty.isDefaultPremium || false
        });
      } catch (error) {
        toast({
          title: 'Error loading penalty',
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        navigate('/penalties');
      } finally {
        setInitialLoading(false);
      }
    };

    if (id) {
      loadPenalty();
    }
  }, [id, navigate, toast]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    let newFormData = {
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    };

    // Handle mutual exclusivity for default penalties
    if (name === 'isDefaultFree' && checked) {
      // If setting as default for free users, ensure it's not premium
      newFormData.isPremium = false;
    } else if (name === 'isPremium' && checked) {
      // If setting as premium, ensure it's not default for free users
      newFormData.isDefaultFree = false;
    }

    setFormData(newFormData);

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const validation = penaltyService.validatePenalty(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    try {
      setLoading(true);
      await penaltyService.updatePenalty(id, formData);

      toast({
        title: 'Penalty updated',
        description: 'The penalty has been successfully updated.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      navigate('/penalties');
    } catch (error) {
      toast({
        title: 'Error updating penalty',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Get penalty categories
  const categories = penaltyService.getPenaltyCategories();

  if (initialLoading) {
    return (
      <Layout title="Edit Penalty">
        <Center h="400px">
          <Spinner size="lg" color="brand.500" />
        </Center>
      </Layout>
    );
  }

  return (
    <Layout title="Edit Penalty">
      <Box maxW="800px" mx="auto">
        <Card>
          <CardBody>
            <VStack spacing={6} align="stretch">
              <Box>
                <Heading size="md" mb={2}>Edit Penalty</Heading>
                <Text color="gray.600">
                  Update the penalty information below.
                </Text>
              </Box>

              <Divider />

              <form onSubmit={handleSubmit}>
                <VStack spacing={6} align="stretch">
                  {/* English Text */}
                  <FormControl isRequired isInvalid={!!errors.text_en}>
                    <FormLabel>English Text</FormLabel>
                    <Textarea
                      name="text_en"
                      value={formData.text_en}
                      onChange={handleChange}
                      placeholder="Enter penalty text in English..."
                      rows={3}
                    />
                    <FormErrorMessage>{errors.text_en}</FormErrorMessage>
                  </FormControl>

                  {/* Spanish Text */}
                  <FormControl isRequired isInvalid={!!errors.text_es}>
                    <FormLabel>Spanish Text</FormLabel>
                    <Textarea
                      name="text_es"
                      value={formData.text_es}
                      onChange={handleChange}
                      placeholder="Enter penalty text in Spanish..."
                      rows={3}
                    />
                    <FormErrorMessage>{errors.text_es}</FormErrorMessage>
                  </FormControl>

                  {/* Dominican Text */}
                  <FormControl isRequired isInvalid={!!errors.text_dom}>
                    <FormLabel>Dominican Spanish Text</FormLabel>
                    <Textarea
                      name="text_dom"
                      value={formData.text_dom}
                      onChange={handleChange}
                      placeholder="Enter penalty text in Dominican Spanish..."
                      rows={3}
                    />
                    <FormErrorMessage>{errors.text_dom}</FormErrorMessage>
                  </FormControl>

                  {/* Category */}
                  <FormControl isRequired isInvalid={!!errors.category}>
                    <FormLabel>Category</FormLabel>
                    <Select
                      name="category"
                      value={formData.category}
                      onChange={handleChange}
                      placeholder="Select a category"
                    >
                      {categories.map((category) => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </Select>
                    <FormErrorMessage>{errors.category}</FormErrorMessage>
                  </FormControl>

                  {/* Active Status */}
                  <FormControl>
                    <HStack justify="space-between">
                      <Box>
                        <FormLabel mb={0}>Active Status</FormLabel>
                        <Text fontSize="sm" color="gray.600">
                          Inactive penalties won't be shown to players
                        </Text>
                      </Box>
                      <Switch
                        name="active"
                        isChecked={formData.active}
                        onChange={handleChange}
                        colorScheme="brand"
                        size="lg"
                      />
                    </HStack>
                  </FormControl>

                  {/* Premium Status */}
                  <FormControl>
                    <HStack justify="space-between">
                      <Box>
                        <FormLabel mb={0}>Premium Only</FormLabel>
                        <Text fontSize="sm" color="gray.600">
                          Only premium users can select this penalty
                        </Text>
                      </Box>
                      <Switch
                        name="isPremium"
                        isChecked={formData.isPremium}
                        onChange={handleChange}
                        colorScheme="purple"
                        size="lg"
                      />
                    </HStack>
                  </FormControl>

                  {/* Default for Free Users */}
                  <FormControl>
                    <HStack justify="space-between">
                      <Box>
                        <FormLabel mb={0}>Default for Free Users</FormLabel>
                        <Text fontSize="sm" color="gray.600">
                          Auto-selected for free users (only one penalty can be default)
                        </Text>
                      </Box>
                      <Switch
                        name="isDefaultFree"
                        isChecked={formData.isDefaultFree}
                        onChange={handleChange}
                        colorScheme="green"
                        size="lg"
                        isDisabled={formData.isPremium}
                      />
                    </HStack>
                  </FormControl>

                  {/* Default for Premium Users */}
                  <FormControl>
                    <HStack justify="space-between">
                      <Box>
                        <FormLabel mb={0}>Default for Premium Users</FormLabel>
                        <Text fontSize="sm" color="gray.600">
                          Auto-selected for premium users (only one penalty can be default)
                        </Text>
                      </Box>
                      <Switch
                        name="isDefaultPremium"
                        isChecked={formData.isDefaultPremium}
                        onChange={handleChange}
                        colorScheme="orange"
                        size="lg"
                      />
                    </HStack>
                  </FormControl>

                  <Divider />

                  {/* Form Actions */}
                  <HStack spacing={4} justify="flex-end">
                    <Button
                      variant="outline"
                      onClick={() => navigate('/penalties')}
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      colorScheme="brand"
                      isLoading={loading}
                      loadingText="Updating..."
                    >
                      Update Penalty
                    </Button>
                  </HStack>
                </VStack>
              </form>
            </VStack>
          </CardBody>
        </Card>

        {/* Preview Card */}
        {(formData.text_en || formData.text_es || formData.text_dom) && (
          <Card mt={6}>
            <CardBody>
              <Heading size="sm" mb={4}>Preview</Heading>
              <VStack spacing={3} align="stretch">
                {formData.text_en && (
                  <Box>
                    <Text fontSize="xs" color="gray.500" mb={1}>English:</Text>
                    <Text>{formData.text_en}</Text>
                  </Box>
                )}
                {formData.text_es && (
                  <Box>
                    <Text fontSize="xs" color="gray.500" mb={1}>Spanish:</Text>
                    <Text>{formData.text_es}</Text>
                  </Box>
                )}
                {formData.text_dom && (
                  <Box>
                    <Text fontSize="xs" color="gray.500" mb={1}>Dominican Spanish:</Text>
                    <Text>{formData.text_dom}</Text>
                  </Box>
                )}
                {formData.category && (
                  <Box>
                    <Text fontSize="xs" color="gray.500" mb={1}>Category:</Text>
                    <Text>{categories.find(cat => cat.value === formData.category)?.label}</Text>
                  </Box>
                )}
              </VStack>
            </CardBody>
          </Card>
        )}
      </Box>
    </Layout>
  );
};

export default EditPenalty;
