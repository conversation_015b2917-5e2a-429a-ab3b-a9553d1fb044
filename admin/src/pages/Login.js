import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Heading,
  useToast,
  Alert,
  AlertIcon,
  Container,
  Image,
  Text,
  FormErrorMessage,
} from '@chakra-ui/react';
import { useAuth } from '../hooks/useAuth';

/**
 * Login component for admin authentication
 *
 * Handles user login and form validation
 */
const Login = () => {
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState({
    email: '',
    password: ''
  });

  // Hooks
  const { login, error, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();

  // Get redirect path from location state or default to dashboard
  const from = location.state?.from?.pathname || '/';

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      console.log('Already authenticated, redirecting to:', from);
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  /**
   * Validate form fields
   * @returns {boolean} True if form is valid
   */
  const validateForm = () => {
    const errors = {
      email: '',
      password: ''
    };
    let isValid = true;

    // Email validation
    if (!email) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'Email is invalid';
      isValid = false;
    }

    // Password validation
    if (!password) {
      errors.password = 'Password is required';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  /**
   * Handle form submission
   * @param {Event} e - Form submit event
   */
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    console.log('Submitting login form...');

    try {
      const user = await login(email, password);
      console.log('Login successful, user:', user.email);

      toast({
        title: 'Login successful',
        description: `Welcome back, ${user.email}`,
        status: 'success',
        duration: 3000,
      });

      // Navigate to the intended destination
      navigate(from, { replace: true });
    } catch (err) {
      console.error('Login error:', err);
      toast({
        title: 'Login failed',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container maxW="md" py={12}>
      <Box
        p={8}
        bg="white"
        boxShadow="lg"
        rounded="md"
      >
        <Box mb={8} textAlign="center">
          <Image
            src="/logo.svg"
            alt="TapTrap Logo"
            maxW="100px"
            mx="auto"
            mb={4}
          />
          <Heading size="lg">TapTrap Admin</Heading>
          <Text fontSize="sm" color="gray.500" mt={2}>
            Sign in to access the admin dashboard
          </Text>
        </Box>

        {error && (
          <Alert status="error" mb={4} rounded="md">
            <AlertIcon />
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Stack spacing={4}>
            <FormControl
              id="email"
              isRequired
              isInvalid={!!formErrors.email}
            >
              <FormLabel>Email</FormLabel>
              <Input
                type="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (formErrors.email) {
                    setFormErrors({...formErrors, email: ''});
                  }
                }}
                placeholder="<EMAIL>"
              />
              <FormErrorMessage>{formErrors.email}</FormErrorMessage>
            </FormControl>

            <FormControl
              id="password"
              isRequired
              isInvalid={!!formErrors.password}
            >
              <FormLabel>Password</FormLabel>
              <Input
                type="password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (formErrors.password) {
                    setFormErrors({...formErrors, password: ''});
                  }
                }}
                placeholder="Enter your password"
              />
              <FormErrorMessage>{formErrors.password}</FormErrorMessage>
            </FormControl>

            <Button
              type="submit"
              colorScheme="purple"
              size="lg"
              fontSize="md"
              isLoading={isSubmitting}
              w="full"
              mt={4}
            >
              Sign In
            </Button>
          </Stack>
        </form>
      </Box>
    </Container>
  );
};

export default Login;