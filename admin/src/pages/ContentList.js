import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Flex,
  IconButton,
  useToast,
  Badge,
  Text,
  Input,
  Select,
  Stack,
  HStack,
  useDisclosure,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Spinner,
  Center,
  ButtonGroup,
} from '@chakra-ui/react';
import { FiEdit, FiTrash2, FiPlus, FiChevronLeft, FiChevronRight, FiZap } from 'react-icons/fi';
import Layout from '../components/Layout';
import { getAllContent, deleteContent } from '../services/content.service';
import ContentDTO from '../dtos/ContentDTO';
import { useContentFilters } from '../context/ContentFilterContext';

const ContentList = () => {
  // Import the content filter context
  const {
    searchTerm, setSearchTerm,
    searchInputValue, setSearchInputValue,
    filterCategory, setFilterCategory,
    filterGameMode, setFilterGameMode,
    filterActive, setFilterActive,
    currentPage, setCurrentPage,
    resetFilters
  } = useContentFilters();

  // Content state
  const [content, setContent] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Pagination state
  const [totalPages, setTotalPages] = useState(1);
  // Fixed items per page value instead of a state variable
  const itemsPerPage = 10;

  // Delete dialog state
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [itemToDelete, setItemToDelete] = useState(null);
  const cancelRef = React.useRef();
  const toast = useToast();

  // Handle category filter logic
  const getCategoryFilter = () => {
    // If a specific category is selected with no game mode, we need to handle both question and dare variants
    if (filterCategory && !filterGameMode) {
      if (['casual', 'mild', 'spicy', 'no_limits', 'couple'].includes(filterCategory)) {
        // For generic categories, we need to search for both questions and dares variants
        return undefined; // We'll handle this with a custom filter in the backend
      }
    }
    return filterCategory || undefined;
  };

  // Function to fetch content with current filters
  const fetchContent = async () => {
    setLoading(true);
    setError(null);

    try {
      // Determine the category filter based on selected game mode and category
      const categoryFilter = getCategoryFilter();

      // If we have a generic category without a game mode, we need to construct a special filter
      let categoryPattern;
      if (filterCategory && !filterGameMode && ['casual', 'mild', 'spicy', 'no_limits', 'couple'].includes(filterCategory)) {
        categoryPattern = `${filterCategory}_`;
      }

      console.log('Fetching content with filters:', {
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm || undefined,
        category: categoryFilter,
        categoryPattern: categoryPattern,
        gameMode: filterGameMode || undefined,
        active: filterActive || undefined
      });

      // Call the content service with filters and pagination
      const response = await getAllContent({
        page: currentPage,
        limit: itemsPerPage,
        search: searchTerm || undefined,
        category: categoryFilter,
        categoryPattern: categoryPattern,
        gameMode: filterGameMode || undefined,
        active: filterActive === 'true' ? true :
               filterActive === 'false' ? false : undefined
      });

      console.log('Received content data:', response);

      // Check if response has the expected structure
      if (response && Array.isArray(response.items)) {
        // Transform content items for UI
        const transformedItems = response.items.map(item => ContentDTO.toFrontend(item));

        setContent(transformedItems);
        setTotalItems(response.totalItems || transformedItems.length);
        setTotalPages(response.totalPages || Math.ceil(transformedItems.length / itemsPerPage));

        if (transformedItems.length === 0 && currentPage === 1) {
          console.log('No content items found');
          toast({
            title: 'No content found',
            description: 'No content matches your current filters. Try adjusting your search criteria.',
            status: 'info',
            duration: 5000,
            isClosable: true,
          });
        } else {
          console.log(`Successfully loaded ${transformedItems.length} content items`);
        }
      } else if (Array.isArray(response)) {
        // Handle old response format (direct array)
        const transformedItems = response.map(item => ContentDTO.toFrontend(item));

        setContent(transformedItems);
        setTotalItems(transformedItems.length);
        setTotalPages(Math.ceil(transformedItems.length / itemsPerPage));

        if (transformedItems.length === 0 && currentPage === 1) {
          console.log('No content items found');
          toast({
            title: 'No content found',
            description: 'No content matches your current filters. Try adjusting your search criteria.',
            status: 'info',
            duration: 5000,
            isClosable: true,
          });
        } else {
          console.log(`Successfully loaded ${transformedItems.length} content items`);
        }
      } else {
        console.error('Received unexpected data format:', response);
        setError('The API returned an unexpected data format');
        toast({
          title: 'Invalid content data',
          description: 'The API returned an unexpected data format.',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error fetching content:', error);
      setError(error.message || 'Failed to load content');
      toast({
        title: 'Error fetching content',
        description: error.message || 'Failed to load content',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const memoizedFetchContent = React.useCallback(fetchContent, [
    currentPage,
    searchTerm,
    filterCategory,
    filterGameMode,
    filterActive,
    toast // Include toast in dependencies
  ]);

  useEffect(() => {
    memoizedFetchContent();
  }, [memoizedFetchContent]);

  // Handle pagination
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleDelete = async () => {
    if (!itemToDelete) return;

    try {
      console.log('Deleting content with ID:', itemToDelete.id);
      const result = await deleteContent(itemToDelete.id);
      console.log('Delete result:', result);

      // Refresh the content list after deletion
      // This is better than manually filtering the state as it ensures consistency with the backend

      // If the current page might be empty after deletion, check and adjust
      const currentItemCount = content.length;
      if (currentItemCount === 1 && currentPage > 1) {
        // If we're deleting the last item on a page that's not the first page,
        // go back to the previous page
        setCurrentPage(currentPage - 1);
      } else {
        // Otherwise, just refresh the current page
        memoizedFetchContent();
      }

      toast({
        title: 'Content deleted',
        description: 'The item was successfully removed from the database.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error deleting content:', error);

      let errorMessage = error.message;
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      }

      toast({
        title: 'Error deleting content',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      onClose();
      setItemToDelete(null);
    }
  };

  const confirmDelete = (item) => {
    setItemToDelete(item);
    onOpen();
  };

  const handleResetFilters = () => {
    // Use the resetFilters function from context
    resetFilters();

    // Fetch content without filters
    memoizedFetchContent();
  };

  return (
    <Layout title="Content Management">
      <Flex justifyContent="flex-end" mb={4} gap={3}>
        <Button
          as={RouterLink}
          to="/content/generate"
          colorScheme="blue"
          leftIcon={<FiZap />}
          variant="outline"
        >
          Generate Content
        </Button>
        <Button
          as={RouterLink}
          to="/content/add"
          colorScheme="purple"
          leftIcon={<FiPlus />}
        >
          Add New Content
        </Button>
      </Flex>

      <Box bg="white" p={4} rounded="md" shadow="md" mb={4}>
        <Stack spacing={4}>
          <HStack spacing={2}>
            <Input
              placeholder="Search content..."
              value={searchInputValue}
              onChange={(e) => setSearchInputValue(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  setSearchTerm(searchInputValue);
                  setCurrentPage(1); // Reset to first page when search changes
                }
              }}
            />
            <Button
              colorScheme="blue"
              onClick={() => {
                setSearchTerm(searchInputValue);
                setCurrentPage(1); // Reset to first page when search changes
              }}
            >
              Search
            </Button>
          </HStack>

          <HStack spacing={4} flexWrap="wrap">
            <Select
              placeholder="Game Mode"
              value={filterGameMode}
              onChange={(e) => {
                const newGameMode = e.target.value;
                setFilterGameMode(newGameMode);

                // Preserve category when changing game mode
                if (filterCategory) {
                  // Extract the base category without the game mode suffix
                  const baseCategory = filterCategory.split('_')[0];

                  if (['casual', 'mild', 'spicy', 'no_limits', 'couple'].includes(baseCategory)) {
                    // If we have a valid base category, update it to match the new game mode
                    if (newGameMode === 'questions') {
                      setFilterCategory(`${baseCategory}_questions`);
                    } else if (newGameMode === 'dares') {
                      setFilterCategory(`${baseCategory}_dares`);
                    }
                  }
                }

                setCurrentPage(1); // Reset to first page when filter changes
              }}
              minW="150px"
            >
              <option value="questions">Questions</option>
              <option value="dares">Dares</option>
            </Select>

            <Select
              placeholder="Category"
              value={filterCategory}
              onChange={(e) => {
                const newCategory = e.target.value;
                setFilterCategory(newCategory);

                // If selecting a generic category (without game mode suffix)
                // and a game mode is already selected, convert to specific category
                if (['casual', 'mild', 'spicy', 'no_limits', 'couple'].includes(newCategory) && filterGameMode) {
                  setFilterCategory(`${newCategory}_${filterGameMode}`);
                }

                setCurrentPage(1); // Reset to first page when filter changes
              }}
              minW="200px"
            >
              {!filterGameMode && (
                <>
                  <option value="casual">Casual</option>
                  <option value="mild">Mild</option>
                  <option value="spicy">Spicy</option>
                  <option value="no_limits">No Limits</option>
                  <option value="couple">Couple</option>
                </>
              )}
              {(filterGameMode === 'questions' || !filterGameMode) && (
                <>
                  {filterGameMode && <option value="casual_questions">Casual</option>}
                  {filterGameMode && <option value="mild_questions">Mild</option>}
                  {filterGameMode && <option value="spicy_questions">Spicy</option>}
                  {filterGameMode && <option value="no_limits_questions">No Limits</option>}
                  {filterGameMode && <option value="couple_questions">Couple</option>}
                </>
              )}
              {(filterGameMode === 'dares' || !filterGameMode) && (
                <>
                  {filterGameMode && <option value="casual_dares">Casual</option>}
                  {filterGameMode && <option value="mild_dares">Mild</option>}
                  {filterGameMode && <option value="spicy_dares">Spicy</option>}
                  {filterGameMode && <option value="no_limits_dares">No Limits</option>}
                  {filterGameMode && <option value="couple_dares">Couple</option>}
                </>
              )}
            </Select>

            <Select
              placeholder="Status"
              value={filterActive}
              onChange={(e) => {
                setFilterActive(e.target.value);
                setCurrentPage(1); // Reset to first page when filter changes
              }}
              minW="120px"
            >
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </Select>

            <Button onClick={handleResetFilters} size="md" variant="outline">
              Reset Filters
            </Button>
          </HStack>


        </Stack>
      </Box>

      {loading ? (
        <Center p={8}>
          <Spinner size="xl" color="purple.500" thickness="4px" />
        </Center>
      ) : error ? (
        <Box bg="red.50" p={4} rounded="md" shadow="md">
          <Text color="red.500">{error}</Text>
        </Box>
      ) : (
        <Box bg="white" rounded="md" shadow="md" overflowX="auto">
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>ID</Th>
                <Th>Text (EN)</Th>
                <Th>Category</Th>
                <Th>Game Mode</Th>
                <Th>Status</Th>
                <Th>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {content.length > 0 ? (
                content.map((item) => (
                  <Tr key={item.id}>
                    <Td>{item.id}</Td>
                    <Td>
                      <Text noOfLines={2}>{item.text_en}</Text>
                    </Td>
                    <Td>{ContentDTO.getCategoryDisplayName(item.category)}</Td>
                    <Td>{ContentDTO.getGameModeDisplayName(item.gameMode)}</Td>
                    <Td>
                      <Badge
                        colorScheme={item.active ? 'green' : 'red'}
                        variant="solid"
                        px={2}
                        py={1}
                        rounded="full"
                      >
                        {item.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </Td>
                    <Td>
                      <HStack spacing={2}>
                        <IconButton
                          as={RouterLink}
                          to={`/content/edit/${item.id}`}
                          icon={<FiEdit />}
                          aria-label="Edit"
                          colorScheme="blue"
                          size="sm"
                        />
                        <IconButton
                          icon={<FiTrash2 />}
                          aria-label="Delete"
                          colorScheme="red"
                          size="sm"
                          onClick={() => confirmDelete(item)}
                        />
                      </HStack>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={6} textAlign="center" py={6}>
                    No content found with the current filters.
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>

          {/* Pagination */}
          {totalPages > 1 && (
            <Flex justify="center" p={4} borderTop="1px" borderColor="gray.200">
              <ButtonGroup variant="outline" spacing={2}>
                <Button
                  leftIcon={<FiChevronLeft />}
                  onClick={() => goToPage(currentPage - 1)}
                  isDisabled={currentPage === 1}
                >
                  Previous
                </Button>

                {[...Array(totalPages)].map((_, i) => {
                  const pageNum = i + 1;
                  // Show limited page numbers to avoid cluttering
                  if (
                    pageNum === 1 ||
                    pageNum === totalPages ||
                    (pageNum >= currentPage - 1 && pageNum <= currentPage + 1)
                  ) {
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? 'solid' : 'outline'}
                        colorScheme={currentPage === pageNum ? 'purple' : 'gray'}
                        onClick={() => goToPage(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    );
                  } else if (
                    (pageNum === currentPage - 2 && currentPage > 3) ||
                    (pageNum === currentPage + 2 && currentPage < totalPages - 2)
                  ) {
                    return <Text key={pageNum}>...</Text>;
                  }
                  return null;
                })}

                <Button
                  rightIcon={<FiChevronRight />}
                  onClick={() => goToPage(currentPage + 1)}
                  isDisabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </ButtonGroup>
            </Flex>
          )}

          {/* Pagination info */}
          <Flex justify="center" p={2} fontSize="sm" color="gray.600">
            Showing {content.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} items
          </Flex>
        </Box>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isOpen}
        leastDestructiveRef={cancelRef}
        onClose={onClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Delete Content
            </AlertDialogHeader>

            <AlertDialogBody>
              Are you sure you want to delete this content? This action cannot be undone.
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onClose}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={handleDelete} ml={3}>
                Delete
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Layout>
  );
};

export default ContentList;