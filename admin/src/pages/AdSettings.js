import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardHeader,
  CardBody,
  Heading,
  VStack,
  HStack,
  Text,
  Switch,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Button,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  FormControl,
  FormLabel,
  FormHelperText,
  Divider,
} from '@chakra-ui/react';
import Layout from '../components/Layout';
import { getAdSettings, updateAdSettings } from '../services/adSettings.service';

const AdSettings = () => {
  const [settings, setSettings] = useState({
    watchAdsButtonEnabled: true, // Legacy field for backward compatibility
    watchAdsButtonEnabledIOS: true,
    watchAdsButtonEnabledAndroid: true,
    requiredAdsCount: 1,
    freeContentLimit: 6
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const toast = useToast();

  const loadSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getAdSettings();
      setSettings({
        watchAdsButtonEnabled: data.watchAdsButtonEnabled, // Legacy field
        watchAdsButtonEnabledIOS: data.watchAdsButtonEnabledIOS ?? data.watchAdsButtonEnabled ?? true,
        watchAdsButtonEnabledAndroid: data.watchAdsButtonEnabledAndroid ?? data.watchAdsButtonEnabled ?? true,
        requiredAdsCount: data.requiredAdsCount,
        freeContentLimit: data.freeContentLimit || 6 // Backward compatibility
      });
    } catch (error) {
      console.error('Error loading ad settings:', error);
      setError('Failed to load ad settings. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load ad settings',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const updatedSettings = await updateAdSettings(settings);

      setSettings({
        watchAdsButtonEnabled: updatedSettings.watchAdsButtonEnabled, // Legacy field
        watchAdsButtonEnabledIOS: updatedSettings.watchAdsButtonEnabledIOS,
        watchAdsButtonEnabledAndroid: updatedSettings.watchAdsButtonEnabledAndroid,
        requiredAdsCount: updatedSettings.requiredAdsCount,
        freeContentLimit: updatedSettings.freeContentLimit
      });

      toast({
        title: 'Success',
        description: 'Ad settings updated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error saving ad settings:', error);
      setError(error.response?.data?.message || 'Failed to save ad settings. Please try again.');
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to save ad settings',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setSaving(false);
    }
  };

  const handleToggleWatchAds = (enabled) => {
    setSettings(prev => ({
      ...prev,
      watchAdsButtonEnabled: enabled
    }));
  };

  const handleToggleWatchAdsIOS = (enabled) => {
    setSettings(prev => ({
      ...prev,
      watchAdsButtonEnabledIOS: enabled
    }));
  };

  const handleToggleWatchAdsAndroid = (enabled) => {
    setSettings(prev => ({
      ...prev,
      watchAdsButtonEnabledAndroid: enabled
    }));
  };

  const handleRequiredAdsCountChange = (value) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 1 && numValue <= 10) {
      setSettings(prev => ({
        ...prev,
        requiredAdsCount: numValue
      }));
    }
  };

  const handleFreeContentLimitChange = (value) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 1 && numValue <= 20) {
      setSettings(prev => ({
        ...prev,
        freeContentLimit: numValue
      }));
    }
  };

  if (loading) {
    return (
      <Layout title="Ad Settings">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <Spinner size="xl" />
        </Box>
      </Layout>
    );
  }

  return (
    <Layout title="Ad Settings">
      <VStack spacing={6} align="stretch" maxWidth="800px">
        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Card shadow="md">
          <CardHeader>
            <Heading size="md">Ad-Based Premium Access Settings</Heading>
            <Text fontSize="sm" color="gray.600" mt={2}>
              Configure how the ad-based premium access system works for mobile users
            </Text>
          </CardHeader>
          <CardBody>
            <VStack spacing={6} align="stretch">
              {/* iOS Watch Ads Button Toggle */}
              <FormControl>
                <HStack justify="space-between">
                  <Box>
                    <FormLabel mb={1}>Watch Ads Button - iOS</FormLabel>
                    <FormHelperText>
                      Enable or disable the "Watch Ads" button for iOS users
                    </FormHelperText>
                  </Box>
                  <Switch
                    size="lg"
                    isChecked={settings.watchAdsButtonEnabledIOS}
                    onChange={(e) => handleToggleWatchAdsIOS(e.target.checked)}
                    colorScheme="purple"
                  />
                </HStack>
              </FormControl>

              {/* Android Watch Ads Button Toggle */}
              <FormControl>
                <HStack justify="space-between">
                  <Box>
                    <FormLabel mb={1}>Watch Ads Button - Android</FormLabel>
                    <FormHelperText>
                      Enable or disable the "Watch Ads" button for Android users
                    </FormHelperText>
                  </Box>
                  <Switch
                    size="lg"
                    isChecked={settings.watchAdsButtonEnabledAndroid}
                    onChange={(e) => handleToggleWatchAdsAndroid(e.target.checked)}
                    colorScheme="purple"
                  />
                </HStack>
              </FormControl>

              <Divider />

              {/* Required Ads Count */}
              <FormControl>
                <FormLabel>Required Ads Count</FormLabel>
                <NumberInput
                  value={settings.requiredAdsCount}
                  onChange={handleRequiredAdsCountChange}
                  min={1}
                  max={10}
                  maxWidth="200px"
                >
                  <NumberInputField />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
                <FormHelperText>
                  Number of ads users must watch to unlock premium access (1-10)
                </FormHelperText>
              </FormControl>

              <Divider />

              {/* Free Content Limit */}
              <FormControl>
                <FormLabel>Free Content Limit</FormLabel>
                <NumberInput
                  value={settings.freeContentLimit}
                  onChange={handleFreeContentLimitChange}
                  min={1}
                  max={20}
                  maxWidth="200px"
                >
                  <NumberInputField />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
                <FormHelperText>
                  Number of free premium content items before requiring ads or premium (1-20)
                </FormHelperText>
              </FormControl>

              <Divider />

              {/* Save Button */}
              <HStack justify="flex-end">
                <Button
                  colorScheme="purple"
                  onClick={handleSave}
                  isLoading={saving}
                  loadingText="Saving..."
                  size="lg"
                >
                  Save Settings
                </Button>
              </HStack>
            </VStack>
          </CardBody>
        </Card>

        {/* Information Card */}
        <Card shadow="md" bg="blue.50">
          <CardBody>
            <VStack spacing={3} align="stretch">
              <Heading size="sm" color="blue.700">How These Settings Work</Heading>
              <Text fontSize="sm" color="blue.600">
                <strong>Watch Ads Button:</strong> When enabled, users will see a "Watch Ads" option
                in the unlock dialog. When disabled, only the premium purchase option will be shown.
              </Text>
              <Text fontSize="sm" color="blue.600">
                <strong>Required Ads Count:</strong> The number of ads users must watch in the
                WatchAdsScreen to unlock temporary premium access for the current session.
              </Text>
              <Text fontSize="sm" color="blue.600">
                <strong>Free Content Limit:</strong> The number of premium content items users can
                access for free before being required to watch ads or upgrade to premium. This also
                affects when the store review banner appears for free users.
              </Text>
              <Text fontSize="sm" color="blue.600">
                <strong>Note:</strong> These settings are checked at app launch and cached locally.
                Changes may take up to 1 hour to take effect on user devices.
              </Text>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Layout>
  );
};

export default AdSettings;
