import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Stack,
  HStack,
  useToast,
  FormErrorMessage,
  Switch,
  FormHelperText,
  Heading,
  Card,
  CardBody,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import Layout from '../components/Layout';
import { createContent } from '../services/content.service';
import ContentDTO from '../dtos/ContentDTO';

const AddContent = () => {
  const {
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    watch,
  } = useForm({
    defaultValues: {
      text_en: '',
      text_es: '',
      text_dom: '',
      category: '',
      gameMode: '',
      active: true,
    },
  });
  const toast = useToast();
  const navigate = useNavigate();
  const selectedGameMode = watch('gameMode');

  const [validationErrors, setValidationErrors] = useState(null);

  const onSubmit = async (data) => {
    try {
      console.log('Form submitted with data:', data);

      // Validate data using ContentDTO
      const validation = ContentDTO.validate(data);
      if (!validation.isValid) {
        console.error('Validation errors:', validation.errors);
        setValidationErrors(validation.errors);
        return;
      }

      // Clear any previous validation errors
      setValidationErrors(null);

      // Transform data for backend using ContentDTO
      const contentData = ContentDTO.toBackend(data);
      console.log('Creating content with processed data:', contentData);

      const result = await createContent(contentData);
      console.log('Content creation result:', result);

      toast({
        title: 'Content created successfully',
        description: 'Your content item has been added to the database.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Navigate after a short delay to ensure toast is visible
      setTimeout(() => {
        // Navigate back to content list without resetting filters
        navigate('/content', { replace: true });
      }, 1000);
    } catch (error) {
      console.error('Error creating content:', error);

      let errorMessage = error.message;
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      }

      toast({
        title: 'Error creating content',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Layout title="Add New Content">
      <Card>
        <CardBody>
          {validationErrors && (
            <Alert status="error" mb={4}>
              <AlertIcon />
              <Box>
                <strong>Validation errors:</strong>
                <ul>
                  {Object.entries(validationErrors).map(([field, error]) => (
                    <li key={field}>{error}</li>
                  ))}
                </ul>
              </Box>
            </Alert>
          )}

          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={5}>
              <FormControl isInvalid={errors.text_en} isRequired>
                <FormLabel>Text (English)</FormLabel>
                <Textarea
                  {...register('text_en', {
                    required: 'English text is required',
                    minLength: { value: 3, message: 'Minimum length should be 3 characters' },
                  })}
                  placeholder="Enter content text in English"
                  rows={4}
                />
                <FormErrorMessage>{errors.text_en?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={errors.text_es} isRequired>
                <FormLabel>Text (Spanish)</FormLabel>
                <Textarea
                  {...register('text_es', {
                    required: 'Spanish text is required',
                    minLength: { value: 3, message: 'Minimum length should be 3 characters' },
                  })}
                  placeholder="Enter content text in Spanish"
                  rows={4}
                />
                <FormErrorMessage>{errors.text_es?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={errors.text_dom} isRequired>
                <FormLabel>Text (Dominican Spanish)</FormLabel>
                <Textarea
                  {...register('text_dom', {
                    required: 'Dominican Spanish text is required',
                    minLength: { value: 3, message: 'Minimum length should be 3 characters' },
                  })}
                  placeholder="Enter content text in Dominican Spanish"
                  rows={4}
                />
                <FormErrorMessage>{errors.text_dom?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={errors.gameMode} isRequired>
                <FormLabel>Game Mode</FormLabel>
                <Select
                  {...register('gameMode', {
                    required: 'Game mode is required',
                  })}
                  placeholder="Select game mode"
                >
                  <option value="questions">Questions</option>
                  <option value="dares">Dares</option>
                </Select>
                <FormErrorMessage>{errors.gameMode?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={errors.category} isRequired>
                <FormLabel>Category</FormLabel>
                <Select
                  {...register('category', {
                    required: 'Category is required',
                  })}
                  placeholder="Select category"
                  isDisabled={!selectedGameMode}
                >
                  {selectedGameMode === 'questions' ? (
                    <>
                      <option value="casual_questions">Casual</option>
                      <option value="mild_questions">Mild</option>
                      <option value="spicy_questions">Spicy</option>
                      <option value="no_limits_questions">No Limits</option>
                      <option value="couple_questions">Couple</option>
                    </>
                  ) : selectedGameMode === 'dares' ? (
                    <>
                      <option value="casual_dares">Casual</option>
                      <option value="mild_dares">Mild</option>
                      <option value="spicy_dares">Spicy</option>
                      <option value="no_limits_dares">No Limits</option>
                      <option value="couple_dares">Couple</option>
                    </>
                  ) : null}
                </Select>
                <FormErrorMessage>{errors.category?.message}</FormErrorMessage>
              </FormControl>

              <FormControl>
                <FormLabel htmlFor="active">Active</FormLabel>
                <Switch id="active" {...register('active')} defaultChecked />
                <FormHelperText>
                  Only active content will be shown in the app
                </FormHelperText>
              </FormControl>

              <HStack spacing={4} justify="flex-end" mt={4}>
                <Button
                  variant="outline"
                  onClick={() => navigate('/content', { replace: true })}
                >
                  Cancel
                </Button>
                <Button
                  colorScheme="purple"
                  isLoading={isSubmitting}
                  type="submit"
                  size="lg"
                >
                  Add Content
                </Button>
              </HStack>
            </Stack>
          </form>
        </CardBody>
      </Card>
    </Layout>
  );
};

export default AddContent;