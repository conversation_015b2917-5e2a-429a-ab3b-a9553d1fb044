import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  Switch,
  FormControl,
  FormLabel,
  FormHelperText,
  Button,
  VStack,
  HStack,
  Textarea,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  Center,
  Badge,
  Divider,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText
} from '@chakra-ui/react';
import Layout from '../components/Layout';
import { getMigrationSettings, toggleMigration, updateMigrationMessage } from '../services/migration.service';

const MigrationSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [toggling, setToggling] = useState(false);
  const [updatingMessage, setUpdatingMessage] = useState(false);
  const [migrationMessage, setMigrationMessage] = useState({
    field_en: '',
    field_es: '',
    field_dom: ''
  });
  const toast = useToast();

  // Fetch migration settings on component mount
  useEffect(() => {
    fetchMigrationSettings();
  }, []);

  const fetchMigrationSettings = async () => {
    try {
      setLoading(true);
      const response = await getMigrationSettings();
      setSettings(response.data);
      setMigrationMessage(response.data.migrationMessage);
    } catch (error) {
      console.error('Error fetching migration settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch migration settings',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggleMigration = async () => {
    try {
      setToggling(true);
      const newEnabled = !settings.migrationEnabled;
      
      const response = await toggleMigration(newEnabled);
      setSettings(response.data);
      
      toast({
        title: 'Success',
        description: `Migration ${newEnabled ? 'enabled' : 'disabled'} successfully`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error toggling migration:', error);
      toast({
        title: 'Error',
        description: 'Failed to toggle migration',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setToggling(false);
    }
  };

  const handleUpdateMessage = async () => {
    try {
      setUpdatingMessage(true);
      
      const response = await updateMigrationMessage(migrationMessage);
      setSettings(response.data);
      
      toast({
        title: 'Success',
        description: 'Migration message updated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error updating migration message:', error);
      toast({
        title: 'Error',
        description: 'Failed to update migration message',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setUpdatingMessage(false);
    }
  };

  const handleMessageChange = (field, value) => {
    setMigrationMessage(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <Layout>
        <Center h="200px">
          <Spinner size="xl" color="blue.500" />
        </Center>
      </Layout>
    );
  }

  return (
    <Layout>
      <VStack spacing={6} align="stretch">
        <Box>
          <Heading size="lg" mb={2}>Migration Settings</Heading>
          <Text color="gray.600">
            Control the migration from API v1 to API v2. When migration is enabled, 
            API v1 will return migration messages instead of real content.
          </Text>
        </Box>

        {/* Migration Status Alert */}
        {settings?.migrationEnabled && (
          <Alert status="warning">
            <AlertIcon />
            <Box>
              <AlertTitle>Migration Mode Active!</AlertTitle>
              <AlertDescription>
                API v1 is currently returning migration messages. Users on older app versions 
                will see the migration message instead of real content.
              </AlertDescription>
            </Box>
          </Alert>
        )}

        {/* Migration Toggle Card */}
        <Card>
          <CardHeader>
            <Heading size="md">Migration Control</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <HStack justify="space-between">
                  <Box>
                    <FormLabel mb={1}>Start Migration</FormLabel>
                    <FormHelperText>
                      Enable this to force users to update to the new app version
                    </FormHelperText>
                  </Box>
                  <Switch
                    size="lg"
                    isChecked={settings?.migrationEnabled || false}
                    onChange={handleToggleMigration}
                    isDisabled={toggling}
                    colorScheme={settings?.migrationEnabled ? 'red' : 'green'}
                  />
                </HStack>
              </FormControl>

              {/* Migration Status Info */}
              <Divider />
              <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                <GridItem>
                  <Stat>
                    <StatLabel>Current Status</StatLabel>
                    <StatNumber>
                      <Badge 
                        colorScheme={settings?.migrationEnabled ? 'red' : 'green'}
                        fontSize="md"
                        px={3}
                        py={1}
                      >
                        {settings?.migrationEnabled ? 'ENABLED' : 'DISABLED'}
                      </Badge>
                    </StatNumber>
                    <StatHelpText>
                      {settings?.migrationEnabled ? 'Migration active' : 'Normal operation'}
                    </StatHelpText>
                  </Stat>
                </GridItem>
                <GridItem>
                  <Stat>
                    <StatLabel>Last Modified By</StatLabel>
                    <StatNumber fontSize="md">{settings?.lastModifiedBy || 'system'}</StatNumber>
                    <StatHelpText>
                      {settings?.migrationEnabled 
                        ? `Enabled: ${new Date(settings?.migrationEnabledAt).toLocaleString()}`
                        : settings?.migrationDisabledAt 
                          ? `Disabled: ${new Date(settings?.migrationDisabledAt).toLocaleString()}`
                          : 'Never enabled'
                      }
                    </StatHelpText>
                  </Stat>
                </GridItem>
              </Grid>
            </VStack>
          </CardBody>
        </Card>

        {/* Migration Message Card */}
        <Card>
          <CardHeader>
            <Heading size="md">Migration Message</Heading>
            <Text fontSize="sm" color="gray.600" mt={1}>
              This message will be shown to users when migration is enabled
            </Text>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel>English Message</FormLabel>
                <Textarea
                  value={migrationMessage.field_en}
                  onChange={(e) => handleMessageChange('field_en', e.target.value)}
                  placeholder="Please download the new version"
                  rows={2}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Spanish Message</FormLabel>
                <Textarea
                  value={migrationMessage.field_es}
                  onChange={(e) => handleMessageChange('field_es', e.target.value)}
                  placeholder="Por favor descarga la nueva versión"
                  rows={2}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Dominican Spanish Message</FormLabel>
                <Textarea
                  value={migrationMessage.field_dom}
                  onChange={(e) => handleMessageChange('field_dom', e.target.value)}
                  placeholder="Por favor descarga la nueva versión"
                  rows={2}
                />
              </FormControl>

              <Button
                colorScheme="blue"
                onClick={handleUpdateMessage}
                isLoading={updatingMessage}
                loadingText="Updating..."
                size="md"
                alignSelf="flex-start"
              >
                Update Message
              </Button>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Layout>
  );
};

export default MigrationSettings;
