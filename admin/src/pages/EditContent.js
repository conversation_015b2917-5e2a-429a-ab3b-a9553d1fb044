import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  Stack,
  useToast,
  FormErrorMessage,
  Switch,
  FormHelperText,
  Skeleton,
  Card,
  CardBody,
  HStack,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import Layout from '../components/Layout';
import { getContentById, updateContent } from '../services/content.service';
import ContentDTO from '../dtos/ContentDTO';

const EditContent = () => {
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [originalGameMode, setOriginalGameMode] = useState('');
  const [validationErrors, setValidationErrors] = useState(null);
  const toast = useToast();
  const navigate = useNavigate();

  const {
    handleSubmit,
    register,
    reset,
    watch,
    formState: { errors, isSubmitting },
  } = useForm();

  const selectedGameMode = watch('gameMode', originalGameMode);

  useEffect(() => {
    const fetchContent = async () => {
      setLoading(true);
      setError(null);

      try {
        console.log(`Fetching content with ID: ${id}`);
        const content = await getContentById(id);
        console.log('Received content:', content);

        // Transform content for frontend
        const transformedContent = ContentDTO.toFrontend(content);
        setOriginalGameMode(transformedContent.gameMode);

        // Reset form with fetched values
        reset({
          text_en: transformedContent.text_en,
          text_es: transformedContent.text_es,
          text_dom: transformedContent.text_dom,
          category: transformedContent.category,
          gameMode: transformedContent.gameMode,
          active: transformedContent.active,
        });
      } catch (error) {
        console.error('Error fetching content:', error);
        setError(error.message || 'Failed to load content');

        toast({
          title: 'Error fetching content',
          description: error.message || 'Failed to load content',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });

        // Navigate back to content list after a short delay
        setTimeout(() => {
          navigate('/content');
        }, 2000);
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, [id, reset, toast, navigate]);

  const onSubmit = async (data) => {
    try {
      console.log('Submitting updated content:', data);

      // Validate data using ContentDTO
      const validation = ContentDTO.validate(data);
      if (!validation.isValid) {
        console.error('Validation errors:', validation.errors);
        setValidationErrors(validation.errors);
        return;
      }

      // Clear any previous validation errors
      setValidationErrors(null);

      // Transform data for backend using ContentDTO
      const contentData = ContentDTO.toBackend(data);
      console.log('Processed update data:', contentData);

      const result = await updateContent(id, contentData);
      console.log('Update result:', result);

      toast({
        title: 'Content updated successfully',
        description: 'Your changes have been saved.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Add delay before navigation to ensure toast is visible
      setTimeout(() => {
        console.log('Navigating to content list after update');
        // Navigate back to content list without resetting filters
        navigate('/content', { replace: true });
      }, 1000);
    } catch (error) {
      console.error('Error updating content:', error);

      let errorMessage = error.message;
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      }

      toast({
        title: 'Error updating content',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  if (loading) {
    return (
      <Layout title="Edit Content">
        <Card>
          <CardBody>
            <Stack spacing={5}>
              <Skeleton height="100px" />
              <Skeleton height="100px" />
              <Skeleton height="40px" />
              <Skeleton height="40px" />
              <Skeleton height="40px" />
            </Stack>
          </CardBody>
        </Card>
      </Layout>
    );
  }

  return (
    <Layout title="Edit Content">
      <Card>
        <CardBody>
          {validationErrors && (
            <Alert status="error" mb={4}>
              <AlertIcon />
              <Box>
                <strong>Validation errors:</strong>
                <ul>
                  {Object.entries(validationErrors).map(([field, error]) => (
                    <li key={field}>{error}</li>
                  ))}
                </ul>
              </Box>
            </Alert>
          )}

          {error && (
            <Alert status="error" mb={4}>
              <AlertIcon />
              {error}
            </Alert>
          )}

          <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={5}>
              <FormControl isInvalid={errors.text_en} isRequired>
                <FormLabel>Text (English)</FormLabel>
                <Textarea
                  {...register('text_en', {
                    required: 'English text is required',
                    minLength: { value: 3, message: 'Minimum length should be 3 characters' },
                  })}
                  placeholder="Enter content text in English"
                  rows={4}
                />
                <FormErrorMessage>{errors.text_en?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={errors.text_es} isRequired>
                <FormLabel>Text (Spanish)</FormLabel>
                <Textarea
                  {...register('text_es', {
                    required: 'Spanish text is required',
                    minLength: { value: 3, message: 'Minimum length should be 3 characters' },
                  })}
                  placeholder="Enter content text in Spanish"
                  rows={4}
                />
                <FormErrorMessage>{errors.text_es?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={errors.text_dom} isRequired>
                <FormLabel>Text (Dominican Spanish)</FormLabel>
                <Textarea
                  {...register('text_dom', {
                    required: 'Dominican Spanish text is required',
                    minLength: { value: 3, message: 'Minimum length should be 3 characters' },
                  })}
                  placeholder="Enter content text in Dominican Spanish"
                  rows={4}
                />
                <FormErrorMessage>{errors.text_dom?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={errors.gameMode} isRequired>
                <FormLabel>Game Mode</FormLabel>
                <Select
                  {...register('gameMode', {
                    required: 'Game mode is required',
                  })}
                  placeholder="Select game mode"
                >
                  <option value="questions">Questions</option>
                  <option value="dares">Dares</option>
                </Select>
                <FormErrorMessage>{errors.gameMode?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={errors.category} isRequired>
                <FormLabel>Category</FormLabel>
                <Select
                  {...register('category', {
                    required: 'Category is required',
                  })}
                  placeholder="Select category"
                >
                  {selectedGameMode === 'questions' ? (
                    <>
                      <option value="casual_questions">Casual Questions</option>
                      <option value="mild_questions">Mild Questions</option>
                      <option value="spicy_questions">Spicy Questions</option>
                      <option value="no_limits_questions">No Limits Questions</option>
                      <option value="couple_questions">Couple Questions</option>
                    </>
                  ) : selectedGameMode === 'dares' ? (
                    <>
                      <option value="casual_dares">Casual Dares</option>
                      <option value="mild_dares">Mild Dares</option>
                      <option value="spicy_dares">Spicy Dares</option>
                      <option value="no_limits_dares">No Limits Dares</option>
                      <option value="couple_dares">Couple Dares</option>
                    </>
                  ) : null}
                </Select>
                <FormErrorMessage>{errors.category?.message}</FormErrorMessage>
              </FormControl>

              <FormControl>
                <FormLabel htmlFor="active">Active</FormLabel>
                <Switch id="active" {...register('active')} />
                <FormHelperText>
                  Only active content will be shown in the app
                </FormHelperText>
              </FormControl>

              <HStack spacing={4} justify="flex-end">
                <Button
                  variant="outline"
                  onClick={() => navigate('/content', { replace: true })}
                >
                  Cancel
                </Button>
                <Button
                  colorScheme="purple"
                  isLoading={isSubmitting}
                  type="submit"
                  size="md"
                >
                  Update Content
                </Button>
              </HStack>
            </Stack>
          </form>
        </CardBody>
      </Card>
    </Layout>
  );
};

export default EditContent;