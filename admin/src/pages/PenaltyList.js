import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  IconButton,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  HStack,
  Input,
  Select,
  Text,
  Flex,
  Spacer,
  Spinner,
  Center,
  VStack
} from '@chakra-ui/react';
import { FiEdit, FiTrash2, FiPlus, FiSearch } from 'react-icons/fi';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import penaltyService from '../services/penalty.service';

const PenaltyList = () => {
  const [penalties, setPenalties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [activeFilter, setActiveFilter] = useState('');
  const [deleteId, setDeleteId] = useState(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const cancelRef = React.useRef();
  const toast = useToast();
  const navigate = useNavigate();

  // Load penalties
  const loadPenalties = async () => {
    try {
      setLoading(true);
      const filters = {};

      if (searchTerm) filters.search = searchTerm;
      if (categoryFilter) filters.category = categoryFilter;
      if (activeFilter !== '') filters.active = activeFilter === 'true';

      const response = await penaltyService.getAllPenalties(filters);
      setPenalties(response.items || []);
    } catch (error) {
      toast({
        title: 'Error loading penalties',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Load penalties on component mount and when filters change
  useEffect(() => {
    loadPenalties();
  }, [searchTerm, categoryFilter, activeFilter]);

  // Handle delete penalty
  const handleDelete = async () => {
    try {
      await penaltyService.deletePenalty(deleteId);
      toast({
        title: 'Penalty deleted',
        description: 'The penalty has been successfully deleted.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      loadPenalties();
    } catch (error) {
      toast({
        title: 'Error deleting penalty',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setDeleteId(null);
      onClose();
    }
  };

  // Open delete confirmation
  const openDeleteDialog = (id) => {
    setDeleteId(id);
    onOpen();
  };

  // Get penalty categories for filter
  const categories = penaltyService.getPenaltyCategories();

  // Format penalty text for display (truncate if too long)
  const formatText = (text, maxLength = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Layout title="Penalties">
      <VStack spacing={6} align="stretch">
        {/* Header with Add Button */}
        <Flex>
          <Spacer />
          <Button
            as={RouterLink}
            to="/penalties/add"
            leftIcon={<FiPlus />}
            colorScheme="brand"
            size="md"
          >
            Add New Penalty
          </Button>
        </Flex>

        {/* Filters */}
        <Box bg="white" p={4} borderRadius="md" boxShadow="sm">
          <HStack spacing={4}>
            <Box flex="1">
              <Input
                placeholder="Search penalties..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftElement={<FiSearch />}
              />
            </Box>
            <Select
              placeholder="All Categories"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              w="200px"
            >
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </Select>
            <Select
              placeholder="All Status"
              value={activeFilter}
              onChange={(e) => setActiveFilter(e.target.value)}
              w="150px"
            >
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </Select>
          </HStack>
        </Box>

        {/* Penalties Table */}
        <Box bg="white" borderRadius="md" boxShadow="sm" overflow="hidden">
          {loading ? (
            <Center p={8}>
              <Spinner size="lg" color="brand.500" />
            </Center>
          ) : penalties.length === 0 ? (
            <Center p={8}>
              <Text color="gray.500">No penalties found</Text>
            </Center>
          ) : (
            <Table variant="simple">
              <Thead bg="gray.50">
                <Tr>
                  <Th>English Text</Th>
                  <Th>Spanish Text</Th>
                  <Th>Dominican Text</Th>
                  <Th>Category</Th>
                  <Th>Status</Th>
                  <Th>Premium</Th>
                  <Th>Default</Th>
                  <Th>Created</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {penalties.map((penalty) => (
                  <Tr key={penalty._id}>
                    <Td maxW="200px">
                      <Text title={penalty.text_en}>
                        {formatText(penalty.text_en)}
                      </Text>
                    </Td>
                    <Td maxW="200px">
                      <Text title={penalty.text_es}>
                        {formatText(penalty.text_es)}
                      </Text>
                    </Td>
                    <Td maxW="200px">
                      <Text title={penalty.text_dom}>
                        {formatText(penalty.text_dom)}
                      </Text>
                    </Td>
                    <Td>
                      <Badge colorScheme="blue" variant="subtle">
                        {categories.find(cat => cat.value === penalty.category)?.label || penalty.category}
                      </Badge>
                    </Td>
                    <Td>
                      <Badge
                        colorScheme={penalty.active ? 'green' : 'red'}
                        variant="subtle"
                      >
                        {penalty.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </Td>
                    <Td>
                      {penalty.isPremium ? (
                        <Badge colorScheme="purple" variant="subtle">
                          Premium
                        </Badge>
                      ) : (
                        <Badge colorScheme="gray" variant="outline">
                          Free
                        </Badge>
                      )}
                    </Td>
                    <Td>
                      <VStack spacing={1} align="start">
                        {penalty.isDefaultFree && (
                          <Badge colorScheme="green" variant="subtle" size="sm">
                            Free Default
                          </Badge>
                        )}
                        {penalty.isDefaultPremium && (
                          <Badge colorScheme="orange" variant="subtle" size="sm">
                            Premium Default
                          </Badge>
                        )}
                        {!penalty.isDefaultFree && !penalty.isDefaultPremium && (
                          <Text fontSize="sm" color="gray.400">
                            -
                          </Text>
                        )}
                      </VStack>
                    </Td>
                    <Td>
                      {new Date(penalty.createdAt).toLocaleDateString()}
                    </Td>
                    <Td>
                      <HStack spacing={2}>
                        <IconButton
                          aria-label="Edit penalty"
                          icon={<FiEdit />}
                          size="sm"
                          colorScheme="blue"
                          variant="ghost"
                          onClick={() => navigate(`/penalties/edit/${penalty.id}`)}
                        />
                        <IconButton
                          aria-label="Delete penalty"
                          icon={<FiTrash2 />}
                          size="sm"
                          colorScheme="red"
                          variant="ghost"
                          onClick={() => openDeleteDialog(penalty.id)}
                        />
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          )}
        </Box>

        {/* Summary */}
        {!loading && penalties.length > 0 && (
          <Box bg="white" p={4} borderRadius="md" boxShadow="sm">
            <Text color="gray.600">
              Showing {penalties.length} penalties
            </Text>
          </Box>
        )}
      </VStack>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isOpen}
        leastDestructiveRef={cancelRef}
        onClose={onClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Delete Penalty
            </AlertDialogHeader>

            <AlertDialogBody>
              Are you sure you want to delete this penalty? This action cannot be undone.
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onClose}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={handleDelete} ml={3}>
                Delete
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Layout>
  );
};

export default PenaltyList;
