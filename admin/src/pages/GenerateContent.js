import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Select,
  HStack,
  VStack,
  useToast,
  Heading,
  Card,
  CardBody,
  Alert,
  AlertIcon,
  Text,
  Spinner,
  Center,
  Checkbox,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Divider,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
} from '@chakra-ui/react';
import { FiRefreshCw, FiPlus, FiX } from 'react-icons/fi';
import Layout from '../components/Layout';
import ExcludeButton from '../components/ExcludeButton';
import MoreMenu from '../components/MoreMenu';
import ResetExcludedContentModal from '../components/ResetExcludedContentModal';
import {
  generateAIContent,
  bulkCreateAIContent,
  getGameModeOptions,
  getCategoryOptions,
  formatCategoryDisplayName,
  getContentTypeLabel,
  validateContentItems
} from '../services/aiContent.service';

const GenerateContent = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Form state
  const [gameMode, setGameMode] = useState('');
  const [category, setCategory] = useState('');

  // Generation state
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState([]);
  const [selectedItems, setSelectedItems] = useState(new Set());

  // Bulk create state
  const [isCreating, setIsCreating] = useState(false);

  // Reset excluded content modal state
  const [isResetModalOpen, setIsResetModalOpen] = useState(false);

  // Get options for dropdowns
  const gameModeOptions = getGameModeOptions();
  const categoryOptions = gameMode ? getCategoryOptions(gameMode) : [];

  // Handle game mode change
  const handleGameModeChange = (value) => {
    setGameMode(value);
    setCategory(''); // Reset category when game mode changes
    setGeneratedContent([]); // Clear generated content
    setSelectedItems(new Set()); // Clear selections
  };

  // Handle category change
  const handleCategoryChange = (value) => {
    setCategory(value);
    setGeneratedContent([]); // Clear generated content
    setSelectedItems(new Set()); // Clear selections
  };

  // Handle content exclusion
  const handleContentExcluded = (content) => {
    const contentIndex = generatedContent.findIndex(item =>
      item.text_es === content.text_es &&
      item.text_en === content.text_en
    );

    if (contentIndex !== -1) {
      // Remove the excluded content from the generated content array
      const newGeneratedContent = generatedContent.filter((_, index) => index !== contentIndex);
      setGeneratedContent(newGeneratedContent);

      // Update selected items indices (shift down indices that are after the removed item)
      setSelectedItems(prev => {
        const newSelected = new Set();
        prev.forEach(selectedIndex => {
          if (selectedIndex < contentIndex) {
            // Keep indices that are before the removed item
            newSelected.add(selectedIndex);
          } else if (selectedIndex > contentIndex) {
            // Shift down indices that are after the removed item
            newSelected.add(selectedIndex - 1);
          }
          // Skip the removed item (selectedIndex === contentIndex)
        });
        return newSelected;
      });

      toast({
        title: 'Content Excluded',
        description: 'This content has been excluded and removed from the list.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Handle reset excluded content
  const handleResetExcludedContent = () => {
    setIsResetModalOpen(true);
  };

  // Handle reset confirmation
  const handleResetConfirmed = (result) => {
    toast({
      title: 'Excluded Content Reset',
      description: result.message || 'All excluded content has been cleared.',
      status: 'success',
      duration: 5000,
      isClosable: true,
    });
  };

  // Generate AI content
  const handleGenerateContent = async () => {
    if (!gameMode || !category) {
      toast({
        title: 'Missing Information',
        description: 'Please select both game mode and category before generating content.',
        status: 'warning',
        duration: 4000,
        isClosable: true,
      });
      return;
    }

    setIsGenerating(true);
    try {
      console.log('Generating AI content for:', { gameMode, category });

      const response = await generateAIContent(gameMode, category);

      if (response.success && response.generatedContent) {
        setGeneratedContent(response.generatedContent);
        setSelectedItems(new Set()); // Clear previous selections

        toast({
          title: 'Content Generated Successfully',
          description: `Generated ${response.count} ${getContentTypeLabel(gameMode).toLowerCase()} for ${formatCategoryDisplayName(category)}`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      } else {
        throw new Error('Invalid response format from AI service');
      }
    } catch (error) {
      console.error('Error generating content:', error);
      toast({
        title: 'Generation Failed',
        description: error.message || 'Failed to generate AI content. Please try again.',
        status: 'error',
        duration: 6000,
        isClosable: true,
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle item selection
  const handleItemSelection = (index, isSelected) => {
    const newSelectedItems = new Set(selectedItems);
    if (isSelected) {
      newSelectedItems.add(index);
    } else {
      newSelectedItems.delete(index);
    }
    setSelectedItems(newSelectedItems);
  };

  // Select all items
  const handleSelectAll = () => {
    if (selectedItems.size === generatedContent.length) {
      setSelectedItems(new Set()); // Deselect all
    } else {
      setSelectedItems(new Set(generatedContent.map((_, index) => index))); // Select all
    }
  };

  // Get selected content items
  const getSelectedContentItems = () => {
    return Array.from(selectedItems).map(index => generatedContent[index]);
  };

  // Handle bulk create
  const handleBulkCreate = async () => {
    const selectedContentItems = getSelectedContentItems();

    if (selectedContentItems.length === 0) {
      toast({
        title: 'No Items Selected',
        description: 'Please select at least one item to add to the database.',
        status: 'warning',
        duration: 4000,
        isClosable: true,
      });
      return;
    }

    // Validate selected items
    const validation = validateContentItems(selectedContentItems);
    if (!validation.isValid) {
      toast({
        title: 'Invalid Content Items',
        description: validation.errors.join(', '),
        status: 'error',
        duration: 6000,
        isClosable: true,
      });
      return;
    }

    setIsCreating(true);
    try {
      console.log('Bulk creating content items:', {
        count: selectedContentItems.length,
        gameMode,
        category
      });

      const response = await bulkCreateAIContent(selectedContentItems, gameMode, category);

      if (response.success) {
        toast({
          title: 'Content Added Successfully',
          description: `Successfully added ${response.count} ${getContentTypeLabel(gameMode).toLowerCase()} to the database.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Remove created items from the generated content
        const remainingContent = generatedContent.filter((_, index) => !selectedItems.has(index));
        setGeneratedContent(remainingContent);
        setSelectedItems(new Set());

        onClose(); // Close confirmation modal
      } else {
        throw new Error('Invalid response from bulk create service');
      }
    } catch (error) {
      console.error('Error bulk creating content:', error);
      toast({
        title: 'Creation Failed',
        description: error.message || 'Failed to add content to database. Please try again.',
        status: 'error',
        duration: 6000,
        isClosable: true,
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Layout title="Generate AI Content">
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Card>
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Heading size="lg">AI Content Generation</Heading>
              <Text color="gray.600">
                Use AI to generate new questions and dares for your TapTrap game.
                Select a game mode and category, then review and add the generated content to your database.
              </Text>
            </VStack>
          </CardBody>
        </Card>

        {/* Generation Controls */}
        <Card>
          <CardBody>
            <VStack spacing={5} align="stretch">
              <Heading size="md">Generation Settings</Heading>

              <HStack spacing={4} align="end">
                <FormControl isRequired>
                  <FormLabel>Game Mode</FormLabel>
                  <Select
                    placeholder="Select game mode"
                    value={gameMode}
                    onChange={(e) => handleGameModeChange(e.target.value)}
                  >
                    {gameModeOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </Select>
                </FormControl>

                <FormControl isRequired>
                  <FormLabel>Category</FormLabel>
                  <Select
                    placeholder="Select category"
                    value={category}
                    onChange={(e) => handleCategoryChange(e.target.value)}
                    isDisabled={!gameMode}
                  >
                    {categoryOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.fullLabel}
                      </option>
                    ))}
                  </Select>
                </FormControl>

                <Button
                  leftIcon={<FiRefreshCw />}
                  colorScheme="purple"
                  onClick={handleGenerateContent}
                  isLoading={isGenerating}
                  loadingText="Generating..."
                  isDisabled={!gameMode || !category}
                  size="lg"
                >
                  Generate Content
                </Button>
              </HStack>

              {gameMode && category && (
                <Alert status="info">
                  <AlertIcon />
                  <Text>
                    Generating {getContentTypeLabel(gameMode).toLowerCase()} for{' '}
                    <strong>{formatCategoryDisplayName(category)}</strong> category.
                    The AI will create content appropriate for this difficulty level.
                  </Text>
                </Alert>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Loading State */}
        {isGenerating && (
          <Card>
            <CardBody>
              <Center py={8}>
                <VStack spacing={4}>
                  <Spinner size="xl" color="purple.500" thickness="4px" />
                  <Text>Generating AI content...</Text>
                  <Text fontSize="sm" color="gray.500">
                    This may take a few moments while our AI creates unique content for you.
                  </Text>
                </VStack>
              </Center>
            </CardBody>
          </Card>
        )}

        {/* Generated Content Review */}
        {generatedContent.length > 0 && !isGenerating && (
          <Card>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <HStack justify="space-between" align="center">
                  <Heading size="md">
                    Generated Content ({generatedContent.length} items)
                  </Heading>
                  <HStack spacing={2}>
                    <MoreMenu onResetExcludedContent={handleResetExcludedContent} />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleSelectAll}
                    >
                      {selectedItems.size === generatedContent.length ? 'Deselect All' : 'Select All'}
                    </Button>
                    <Badge colorScheme="purple" px={2} py={1}>
                      {selectedItems.size} selected
                    </Badge>
                  </HStack>
                </HStack>

                <Divider />

                <Box overflowX="auto">
                  <Table variant="simple" size="sm">
                    <Thead>
                      <Tr>
                        <Th width="50px">Select</Th>
                        <Th>English</Th>
                        <Th>Spanish</Th>
                        <Th>Dominican</Th>
                        <Th width="80px">Actions</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {generatedContent.map((item, index) => (
                        <Tr key={index}>
                          <Td>
                            <Checkbox
                              isChecked={selectedItems.has(index)}
                              onChange={(e) => handleItemSelection(index, e.target.checked)}
                            />
                          </Td>
                          <Td>
                            <Text fontSize="sm">{item.text_en}</Text>
                          </Td>
                          <Td>
                            <Text fontSize="sm">{item.text_es}</Text>
                          </Td>
                          <Td>
                            <Text fontSize="sm">{item.text_dom}</Text>
                          </Td>
                          <Td>
                            <ExcludeButton
                              content={item}
                              gameMode={gameMode}
                              category={category}
                              onExcluded={handleContentExcluded}
                              isExcluded={false}
                            />
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>

                <Divider />

                <HStack justify="space-between">
                  <Button
                    variant="outline"
                    onClick={() => navigate('/content')}
                  >
                    Back to Content List
                  </Button>

                  <HStack spacing={2}>
                    <Button
                      leftIcon={<FiX />}
                      variant="outline"
                      colorScheme="red"
                      onClick={() => {
                        setGeneratedContent([]);
                        setSelectedItems(new Set());
                      }}
                    >
                      Clear All
                    </Button>
                    <Button
                      leftIcon={<FiPlus />}
                      colorScheme="green"
                      onClick={onOpen}
                      isDisabled={selectedItems.size === 0}
                    >
                      Add Selected to Database ({selectedItems.size})
                    </Button>
                  </HStack>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        )}
      </VStack>

      {/* Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Confirm Content Creation</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>
              Are you sure you want to add {selectedItems.size} selected{' '}
              {getContentTypeLabel(gameMode).toLowerCase()} to the database?
            </Text>
            <Text mt={2} fontSize="sm" color="gray.600">
              This action cannot be undone. The content will be immediately available in the app.
            </Text>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="green"
              onClick={handleBulkCreate}
              isLoading={isCreating}
              loadingText="Creating..."
            >
              Add to Database
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Reset Excluded Content Modal */}
      <ResetExcludedContentModal
        isOpen={isResetModalOpen}
        onClose={() => setIsResetModalOpen(false)}
        onReset={handleResetConfirmed}
      />
    </Layout>
  );
};

export default GenerateContent;
