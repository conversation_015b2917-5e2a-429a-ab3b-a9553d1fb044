import React from 'react';
import { <PERSON>, <PERSON><PERSON>, But<PERSON>, Container, VStack } from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';

const TestDashboard = () => {
  return (
    <Container maxW="container.xl" py={10}>
      <VStack spacing={8} align="stretch">
        <Heading as="h1" size="xl">TapTrap Admin Dashboard (Test Version)</Heading>
        
        <Box p={5} shadow="md" borderWidth="1px" bg="white">
          <Heading fontSize="xl" mb={4}>Dashboard Overview</Heading>
          <p>This is a test dashboard to bypass authentication issues.</p>
        </Box>
        
        <Box p={5} shadow="md" borderWidth="1px" bg="white">
          <Heading fontSize="xl" mb={4}>Quick Links</Heading>
          <VStack align="stretch" spacing={4}>
            <Button as={RouterLink} to="/content" colorScheme="purple">
              View Content
            </Button>
            <Button as={RouterLink} to="/content/add" colorScheme="green">
              Add New Content
            </Button>
          </VStack>
        </Box>
      </VStack>
    </Container>
  );
};

export default TestDashboard;