import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Input,
  Textarea,
  FormControl,
  FormLabel,
  Select,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Badge,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Icon,
  Switch,
  Tooltip,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel
} from '@chakra-ui/react';
import { FiSmartphone, FiSend, FiBell, FiCopy } from 'react-icons/fi';
import Layout from '../components/Layout';
import api from '../services/api';

const PushNotifications = () => {
  const [tokens, setTokens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [batchProgress, setBatchProgress] = useState(null);
  const [notification, setNotification] = useState({
    title: '',
    body: '',
    data: {},
    language: '', // Empty string means send to all languages
    forcePush: false
  });
  const [specificNotification, setSpecificNotification] = useState({
    title: '',
    body: '',
    data: {},
    deviceId: ''
  });
  const toast = useToast();

  // Fetch push tokens on component mount
  useEffect(() => {
    fetchPushTokens();
  }, []);

  const fetchPushTokens = async () => {
    try {
      setLoading(true);
      const response = await api.get('/admin/push-tokens');
      setTokens(response.data.tokens || []);
    } catch (error) {
      console.error('Error fetching push tokens:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch push tokens',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSendNotification = async () => {
    if (!notification.title.trim() || !notification.body.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Title and body are required',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setSending(true);
      setBatchProgress(null);

      const response = await api.post('/admin/push-notifications/send', notification);

      // Show batch information if available
      let successMessage = response.data.message;
      if (response.data.batches > 1) {
        successMessage += `\n\nBatch Details:`;
        response.data.batchResults?.forEach(batch => {
          successMessage += `\nBatch ${batch.batch}: ${batch.sent} sent, ${batch.failed} failed`;
        });
      }

      toast({
        title: 'Success',
        description: successMessage,
        status: 'success',
        duration: 8000,
        isClosable: true,
      });

      // Clear form after successful send
      setNotification({
        title: '',
        body: '',
        data: {},
        language: '',
        forcePush: false
      });

    } catch (error) {
      console.error('Error sending notification:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to send notification',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setSending(false);
      setBatchProgress(null);
    }
  };


  const handleSendSpecificNotification = async () => {
    if (!specificNotification.title.trim() || !specificNotification.body.trim() || !specificNotification.deviceId.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Title, body, and device ID are required',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setSending(true);
      const response = await api.post('/admin/push-notifications/send-specific', specificNotification);

      toast({
        title: 'Success',
        description: response.data.message,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Clear form after successful send
      setSpecificNotification({
        title: '',
        body: '',
        data: {},
        deviceId: ''
      });

    } catch (error) {
      console.error('Error sending specific notification:', error);
      console.error('Error response:', error.response?.data);
      console.error('Request data:', specificNotification);

      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          `Failed to send notification (${error.response?.status || 'Unknown error'})`;

      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setSending(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getPlatformColor = (platform) => {
    return platform === 'ios' ? 'blue' : 'green';
  };

  const copyDeviceId = (deviceId) => {
    navigator.clipboard.writeText(deviceId);
    toast({
      title: 'Copied!',
      description: `Device ID copied to clipboard: ${deviceId}`,
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  const iosTokens = tokens.filter(token => token.platform === 'ios');
  const androidTokens = tokens.filter(token => token.platform === 'android');

  // Language statistics
  const englishTokens = tokens.filter(token => token.language === 'en');
  const spanishTokens = tokens.filter(token => token.language === 'es');
  const dominicanTokens = tokens.filter(token => token.language === 'dom');

  // Get target count based on selected language and force push setting
  const getTargetCount = () => {
    let targetTokens = tokens;

    // Filter by language if specified
    if (notification.language) {
      targetTokens = tokens.filter(token => token.language === notification.language);
    }

    // If not force push, only count tokens with notifications enabled
    if (!notification.forcePush) {
      targetTokens = targetTokens.filter(token => token.notificationEnabled !== false);
    }

    return targetTokens.length;
  };

  return (
    <Layout title="Push Notifications">
      <VStack spacing={6} align="stretch">
        {/* Statistics */}
        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
          <Stat>
            <StatLabel>
              <HStack>
                <Icon as={FiBell} />
                <Text>Total Devices</Text>
              </HStack>
            </StatLabel>
            <StatNumber>{tokens.length}</StatNumber>
            <StatHelpText>Registered for notifications</StatHelpText>
          </Stat>

          <Stat>
            <StatLabel>
              <HStack>
                <Icon as={FiSmartphone} />
                <Text>iOS Devices</Text>
              </HStack>
            </StatLabel>
            <StatNumber>{iosTokens.length}</StatNumber>
            <StatHelpText>Apple devices</StatHelpText>
          </Stat>

          <Stat>
            <StatLabel>
              <HStack>
                <Icon as={FiSmartphone} />
                <Text>Android Devices</Text>
              </HStack>
            </StatLabel>
            <StatNumber>{androidTokens.length}</StatNumber>
            <StatHelpText>Android devices</StatHelpText>
          </Stat>
        </SimpleGrid>

        {/* Language Statistics */}
        <Card>
          <CardHeader>
            <Heading size="md">Language Distribution</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
              <Stat>
                <StatLabel>English</StatLabel>
                <StatNumber>{englishTokens.length}</StatNumber>
                <StatHelpText>EN users</StatHelpText>
              </Stat>

              <Stat>
                <StatLabel>Spanish</StatLabel>
                <StatNumber>{spanishTokens.length}</StatNumber>
                <StatHelpText>ES users</StatHelpText>
              </Stat>

              <Stat>
                <StatLabel>Dominican</StatLabel>
                <StatNumber>{dominicanTokens.length}</StatNumber>
                <StatHelpText>DOM users</StatHelpText>
              </Stat>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Send Notification Form */}
        <Card>
          <CardHeader>
            <Heading size="md">
              <HStack>
                <Icon as={FiSend} />
                <Text>Send Push Notification</Text>
              </HStack>
            </Heading>
          </CardHeader>
          <CardBody>
            <Tabs variant="enclosed" colorScheme="blue">
              <TabList>
                <Tab>Broadcast to All</Tab>
                <Tab>Send to Specific Device</Tab>
              </TabList>

              <TabPanels>
                {/* Broadcast Tab */}
                <TabPanel px={0}>
            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel>Title</FormLabel>
                <Input
                  placeholder="Enter notification title"
                  value={notification.title}
                  onChange={(e) => setNotification(prev => ({ ...prev, title: e.target.value }))}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Message</FormLabel>
                <Textarea
                  placeholder="Enter notification message"
                  value={notification.body}
                  onChange={(e) => setNotification(prev => ({ ...prev, body: e.target.value }))}
                  rows={4}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Target Language</FormLabel>
                <Select
                  placeholder="All Languages"
                  value={notification.language}
                  onChange={(e) => setNotification(prev => ({ ...prev, language: e.target.value }))}
                >
                  <option value="en">English ({englishTokens.length} devices)</option>
                  <option value="es">Spanish ({spanishTokens.length} devices)</option>
                  <option value="dom">Dominican ({dominicanTokens.length} devices)</option>
                </Select>
              </FormControl>

              <FormControl>
                <HStack justify="space-between" align="center">
                  <VStack align="start" spacing={1}>
                    <FormLabel mb={0}>Force Push</FormLabel>
                    <Text fontSize="sm" color="gray.600">
                      Send even to users who disabled notifications in-app
                    </Text>
                  </VStack>
                  <Tooltip
                    label="When enabled, notifications will be sent to all active devices regardless of user notification preferences"
                    placement="top"
                  >
                    <Switch
                      colorScheme="red"
                      isChecked={notification.forcePush}
                      onChange={(e) => setNotification(prev => ({ ...prev, forcePush: e.target.checked }))}
                    />
                  </Tooltip>
                </HStack>
              </FormControl>

              {getTargetCount() > 100 && (
                <Alert status="info" borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <AlertTitle>Batch Sending</AlertTitle>
                    <AlertDescription>
                      {getTargetCount()} devices will be processed in {Math.ceil(getTargetCount() / 100)} batches of 100 notifications each to comply with Expo limits.
                    </AlertDescription>
                  </Box>
                </Alert>
              )}

              <Button
                colorScheme={notification.forcePush ? "red" : "blue"}
                onClick={handleSendNotification}
                isLoading={sending}
                loadingText={
                  getTargetCount() > 100
                    ? `Sending in batches...`
                    : "Sending..."
                }
                leftIcon={<FiSend />}
                isDisabled={!notification.title.trim() || !notification.body.trim()}
              >
                {notification.forcePush ? "🚨 FORCE PUSH" : "Send"}
                {notification.language
                  ? ` to ${notification.language.toUpperCase()} Users (${getTargetCount()})`
                  : ` to All Devices (${getTargetCount()})`
                }
                {getTargetCount() > 100 && !sending && (
                  <Text fontSize="xs" color="gray.500" ml={2}>
                    ({Math.ceil(getTargetCount() / 100)} batches)
                  </Text>
                )}
              </Button>
            </VStack>
                </TabPanel>

                {/* Specific Device Tab */}
                <TabPanel px={0}>
                  <VStack spacing={4} align="stretch">
                    <FormControl isRequired>
                      <FormLabel>Device ID</FormLabel>
                      <Input
                        placeholder="Enter device ID (e.g., ABC123-DEF456-GHI789)"
                        value={specificNotification.deviceId}
                        onChange={(e) => setSpecificNotification(prev => ({ ...prev, deviceId: e.target.value }))}
                        fontFamily="mono"
                      />
                      <Text fontSize="sm" color="gray.600" mt={1}>
                        You can find device IDs in the table below
                      </Text>
                    </FormControl>

                    <FormControl isRequired>
                      <FormLabel>Title</FormLabel>
                      <Input
                        placeholder="Enter notification title"
                        value={specificNotification.title}
                        onChange={(e) => setSpecificNotification(prev => ({ ...prev, title: e.target.value }))}
                      />
                    </FormControl>

                    <FormControl isRequired>
                      <FormLabel>Message</FormLabel>
                      <Textarea
                        placeholder="Enter notification message"
                        value={specificNotification.body}
                        onChange={(e) => setSpecificNotification(prev => ({ ...prev, body: e.target.value }))}
                        rows={4}
                      />
                    </FormControl>

                    <Button
                      colorScheme="green"
                      onClick={handleSendSpecificNotification}
                      isLoading={sending}
                      loadingText="Sending..."
                      leftIcon={<FiSend />}
                      isDisabled={!specificNotification.title.trim() || !specificNotification.body.trim() || !specificNotification.deviceId.trim()}
                    >
                      Send to Device: {specificNotification.deviceId || 'Enter Device ID'}
                    </Button>
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </CardBody>
        </Card>

        {/* Device List */}
        <Card>
          <CardHeader>
            <Heading size="md">Registered Devices</Heading>
          </CardHeader>
          <CardBody>
            {loading ? (
              <Box textAlign="center" py={8}>
                <Spinner size="lg" />
                <Text mt={4}>Loading devices...</Text>
              </Box>
            ) : tokens.length === 0 ? (
              <Alert status="info">
                <AlertIcon />
                <AlertTitle>No devices registered</AlertTitle>
                <AlertDescription>
                  No devices have registered for push notifications yet.
                </AlertDescription>
              </Alert>
            ) : (
              <Box overflowX="auto">
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Platform</Th>
                      <Th>Language</Th>
                      <Th>Notifications</Th>
                      <Th>Device ID</Th>
                      <Th>Token (Preview)</Th>
                      <Th>Registered</Th>
                      <Th>Last Used</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {tokens.map((token) => (
                      <Tr key={token._id}>
                        <Td>
                          <Badge colorScheme={getPlatformColor(token.platform)}>
                            {token.platform.toUpperCase()}
                          </Badge>
                        </Td>
                        <Td>
                          <Badge
                            colorScheme={
                              token.language === 'en' ? 'blue' :
                              token.language === 'es' ? 'green' :
                              token.language === 'dom' ? 'purple' : 'gray'
                            }
                          >
                            {token.language?.toUpperCase() || 'EN'}
                          </Badge>
                        </Td>
                        <Td>
                          <Badge
                            colorScheme={token.notificationEnabled !== false ? 'green' : 'red'}
                            variant={token.notificationEnabled !== false ? 'solid' : 'outline'}
                          >
                            {token.notificationEnabled !== false ? '✓ ON' : '✗ OFF'}
                          </Badge>
                        </Td>
                        <Td>
                          <HStack spacing={2}>
                            <Text fontSize="sm" fontFamily="mono">
                              {token.deviceId}
                            </Text>
                            <Button
                              size="xs"
                              variant="ghost"
                              onClick={() => copyDeviceId(token.deviceId)}
                              leftIcon={<FiCopy />}
                            >
                              Copy
                            </Button>
                          </HStack>
                        </Td>
                        <Td>
                          <Text fontSize="xs" fontFamily="mono" color="gray.600">
                            {token.token.substring(0, 20)}...
                          </Text>
                        </Td>
                        <Td>
                          <Text fontSize="sm">
                            {formatDate(token.createdAt)}
                          </Text>
                        </Td>
                        <Td>
                          <Text fontSize="sm">
                            {formatDate(token.lastUsed || token.updatedAt)}
                          </Text>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            )}
          </CardBody>
        </Card>
      </VStack>
    </Layout>
  );
};

export default PushNotifications;
