import { useState, useEffect, useCallback } from 'react';
import * as authService from '../services/auth.service';

/**
 * Custom hook for authentication state and operations
 *
 * Provides authentication state and methods for login/logout
 *
 * @returns {Object} Authentication state and methods
 */
export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load user from localStorage on initial render
  useEffect(() => {
    const loadUser = () => {
      try {
        // Get user from auth service
        const currentUser = authService.getCurrentUser();
        const token = authService.getToken();

        console.log('Stored token:', token ? 'exists' : 'none');

        if (currentUser && token) {
          console.log('User found in storage:', currentUser.email);
          setUser(currentUser);
        } else {
          console.log('No authenticated user found');
          // User must log in manually in all environments
        }
      } catch (err) {
        console.error('Error loading user from storage:', err);
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, []);

  /**
   * Login with email and password
   *
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Authenticated user
   */
  const login = useCallback(async (email, password) => {
    setLoading(true);
    setError(null);
    console.log('Login attempt with:', email);

    try {
      // Call auth service login method
      const response = await authService.login({ email, password });
      console.log('Login successful');

      // Update state with user from response
      setUser(response.user);
      return response.user;
    } catch (err) {
      console.error('Login error in hook:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Logout the current user
   */
  const logout = useCallback(() => {
    authService.logout();
    setUser(null);
    setError(null);
  }, []);

  // Check if user is authenticated based on user object
  // This ensures authentication is required in all environments
  const isAuthenticated = !!user;

  return {
    user,
    loading,
    error,
    login,
    logout,
    isAuthenticated
  };
};