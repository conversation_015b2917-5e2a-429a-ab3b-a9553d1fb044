import React, { createContext, useContext, useState } from 'react';

// Create a context for content filters
const ContentFilterContext = createContext();

// Custom hook to use the content filter context
export const useContentFilters = () => {
  const context = useContext(ContentFilterContext);
  if (context === undefined) {
    throw new Error('useContentFilters must be used within a ContentFilterProvider');
  }
  return context;
};

// Provider component for content filters
export const ContentFilterProvider = ({ children }) => {
  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInputValue, setSearchInputValue] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterGameMode, setFilterGameMode] = useState('');
  const [filterActive, setFilterActive] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  // Reset all filters
  const resetFilters = () => {
    setSearchTerm('');
    setSearchInputValue('');
    setFilterCategory('');
    setFilterGameMode('');
    setFilterActive('');
    setCurrentPage(1);
  };

  // Value object to be provided to consumers
  const value = {
    searchTerm,
    setSearchTerm,
    searchInputValue,
    setSearchInputValue,
    filterCategory,
    setFilterCategory,
    filterGameMode,
    setFilterGameMode,
    filterActive,
    setFilterActive,
    currentPage,
    setCurrentPage,
    resetFilters,
  };

  return (
    <ContentFilterContext.Provider value={value}>
      {children}
    </ContentFilterContext.Provider>
  );
};
