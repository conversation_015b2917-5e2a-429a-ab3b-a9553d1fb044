/**
 * User DTOs (Data Transfer Objects)
 * 
 * This file contains interfaces and types for user-related data structures
 * used in the application, particularly for authentication.
 */

/**
 * LoginRequest
 * 
 * Represents the data structure for login requests to the API.
 * 
 * @typedef {Object} LoginRequest
 * @property {string} email - User's email address
 * @property {string} password - User's password
 */

/**
 * LoginResponse
 * 
 * Represents the data structure returned from a successful login.
 * 
 * @typedef {Object} LoginResponse
 * @property {string} token - JWT authentication token
 * @property {User} user - User information
 */

/**
 * User
 * 
 * Represents a user in the system.
 * 
 * @typedef {Object} User
 * @property {string} id - User's unique identifier
 * @property {string} email - User's email address
 * @property {string} role - User's role (e.g., 'admin')
 */

/**
 * AuthState
 * 
 * Represents the authentication state in the application.
 * 
 * @typedef {Object} AuthState
 * @property {User|null} user - The currently authenticated user or null if not authenticated
 * @property {boolean} loading - Whether authentication state is being determined
 * @property {string|null} error - Any error that occurred during authentication
 * @property {boolean} isAuthenticated - Whether a user is currently authenticated
 */

// Export empty object to make this a proper ES module
export default {};
