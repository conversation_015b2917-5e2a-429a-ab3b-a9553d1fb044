/**
 * Content Data Transfer Object
 *
 * Handles data transformation between frontend and backend
 */

class ContentDTO {
  /**
   * Validate content data before sending to backend
   *
   * @param {Object} data - Content data to validate
   * @returns {Object} - Validation result with isValid and errors
   */
  static validate(data) {
    const errors = {};
    let isValid = true;

    // Required fields
    const requiredFields = ['text_en', 'text_es', 'text_dom', 'category', 'gameMode'];
    requiredFields.forEach(field => {
      if (!data[field]) {
        errors[field] = 'This field is required';
        isValid = false;
      }
    });

    // Validate text_en length
    if (data.text_en && data.text_en.length > 500) {
      errors.text_en = 'English text must be less than 500 characters';
      isValid = false;
    }

    // Validate text_es length
    if (data.text_es && data.text_es.length > 500) {
      errors.text_es = 'Spanish text must be less than 500 characters';
      isValid = false;
    }

    // Validate text_dom length
    if (data.text_dom && data.text_dom.length > 500) {
      errors.text_dom = 'Dominican Spanish text must be less than 500 characters';
      isValid = false;
    }

    // Validate category
    const validCategories = [
      'casual_questions', 'mild_questions', 'spicy_questions', 'no_limits_questions', 'couple_questions',
      'casual_dares', 'mild_dares', 'spicy_dares', 'no_limits_dares', 'couple_dares'
    ];

    if (data.category && !validCategories.includes(data.category)) {
      errors.category = `Category must be one of: ${validCategories.join(', ')}`;
      isValid = false;
    }

    // Validate gameMode
    const validGameModes = ['questions', 'dares'];
    if (data.gameMode && !validGameModes.includes(data.gameMode)) {
      errors.gameMode = `Game mode must be one of: ${validGameModes.join(', ')}`;
      isValid = false;
    }

    return { isValid, errors };
  }

  /**
   * Transform content data from frontend to backend format
   *
   * @param {Object} data - Content data from frontend
   * @returns {Object} - Content data for backend
   */
  static toBackend(data) {
    return {
      id: data.id,
      text_en: data.text_en?.trim(),
      text_es: data.text_es?.trim(),
      text_dom: data.text_dom?.trim(),
      category: data.category,
      gameMode: data.gameMode,
      active: data.active === false ? false : true
    };
  }

  /**
   * Transform content data from backend to frontend format
   *
   * @param {Object} data - Content data from backend
   * @returns {Object} - Content data for frontend
   */
  static toFrontend(data) {
    return {
      id: data.id,
      text_en: data.text_en,
      text_es: data.text_es,
      text_dom: data.text_dom,
      category: data.category,
      gameMode: data.gameMode,
      active: data.active,
      createdAt: data.createdAt ? new Date(data.createdAt) : null,
      updatedAt: data.updatedAt ? new Date(data.updatedAt) : null
    };
  }

  /**
   * Get category display name
   *
   * @param {string} category - Category key
   * @returns {string} - Display name
   */
  static getCategoryDisplayName(category) {
    // First check if it's a known category
    const categoryMap = {
      'casual_questions': 'Casual',
      'mild_questions': 'Mild',
      'spicy_questions': 'Spicy',
      'no_limits_questions': 'No Limits',
      'couple_questions': 'Couple',
      'casual_dares': 'Casual',
      'mild_dares': 'Mild',
      'spicy_dares': 'Spicy',
      'no_limits_dares': 'No Limits',
      'couple_dares': 'Couple',
      'casual': 'Casual',
      'mild': 'Mild',
      'spicy': 'Spicy',
      'no_limits': 'No Limits',
      'couple': 'Couple'
    };

    // If it's a known category, return the mapped display name
    if (categoryMap[category]) {
      return categoryMap[category];
    }

    // If it's not in our map, try to extract the base category
    if (category && typeof category === 'string') {
      const parts = category.split('_');
      if (parts.length > 0 && categoryMap[parts[0]]) {
        return categoryMap[parts[0]];
      }
    }

    // Fallback to the original category
    return category;
  }

  /**
   * Get game mode display name
   *
   * @param {string} gameMode - Game mode key
   * @returns {string} - Display name
   */
  static getGameModeDisplayName(gameMode) {
    const gameModeMap = {
      'questions': 'Questions',
      'dares': 'Dares'
    };

    return gameModeMap[gameMode] || gameMode;
  }
}

export default ContentDTO;
