import React from 'react';
import ReactDOM from 'react-dom/client';
import { ChakraProvider, extendTheme } from '@chakra-ui/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { ContentFilterProvider } from './context/ContentFilterContext';
import App from './App';
// Import the clear auth utility to ensure no stale auth data
import './utils/clearAuth';

// Extend the theme to include custom colors, fonts, etc
const theme = extendTheme({
  colors: {
    brand: {
      50: '#f0e4ff',
      100: '#d1beff',
      200: '#b397ff',
      300: '#946fff',
      400: '#7547ff',
      500: '#5a24f0',
      600: '#441bbd',
      700: '#30138a',
      800: '#1d0b58',
      900: '#0a0428',
    },
  },
  fonts: {
    heading: 'Poppins, sans-serif',
    body: 'Inter, sans-serif',
  },
});

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <ChakraProvider theme={theme}>
      <BrowserRouter>
        <AuthProvider>
          <ContentFilterProvider>
            <App />
          </ContentFilterProvider>
        </AuthProvider>
      </BrowserRouter>
    </ChakraProvider>
  </React.StrictMode>
);