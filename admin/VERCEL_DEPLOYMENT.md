# Deploying TapTrap Admin Panel to Vercel

This document outlines the steps needed to deploy the TapTrap Admin Panel to Vercel as a separate project from the backend API.

## Prerequisites

1. A Vercel account
2. Node.js 16.x or higher
3. The TapTrap backend API already deployed to Vercel

## Deployment Steps

### 1. Prepare the Admin Panel for Production

The admin panel is configured to use the `/api` proxy path in production, which will be redirected to the backend API through Vercel's rewrites configuration.

### 2. Set Up Environment Variables in Vercel

When deploying to Vercel, you'll need to set the following environment variables:

- `REACT_APP_API_URL`: Set to `/api` (This is already configured in .env.production)
- `NODE_ENV`: Set to `production`

### 3. Deploy to Vercel

1. Connect your GitHub repository to Vercel
2. Configure the project with the following settings:
   - **Framework Preset**: Create React App
   - **Build Command**: `npm run vercel-build`
   - **Output Directory**: `build`
   - **Development Command**: `npm start`

3. In the project settings, configure the following:
   - Set the environment variables mentioned above
   - Make sure the "Include source files outside of the Root Directory in the Build Step" option is enabled if your project structure requires it

### 4. Configure API Rewrites

The `vercel.json` file is already configured to redirect API requests to the backend:

```json
{
  "version": 2,
  "buildCommand": "npm run vercel-build",
  "outputDirectory": "build",
  "framework": "create-react-app",
  "rewrites": [
    {
      "source": "/api/:path*",
      "destination": "https://taptrap-backend.vercel.app/api/:path*"
    },
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

This configuration:
- Redirects all API requests to the backend API on Vercel
- Routes all other requests to the React app for client-side routing

### 5. Custom Domain (Optional)

If you want to use a custom domain for the admin panel:
1. Go to the Vercel project settings
2. Navigate to the "Domains" section
3. Add your custom domain (e.g., `admin.taptrap.app`)
4. Follow Vercel's instructions to configure DNS settings

## Testing the Deployment

After deployment, make sure to test:
1. Authentication works correctly
2. CRUD operations on game content function properly
3. Navigation within the admin panel works as expected

## Troubleshooting

If you encounter CORS issues:
1. Make sure the backend's CORS configuration includes the admin panel's domain
2. Check that API requests are being properly proxied through the `/api` path

For any other issues, check the Vercel deployment logs for errors.