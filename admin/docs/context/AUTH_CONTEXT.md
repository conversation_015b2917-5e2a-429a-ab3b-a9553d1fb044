# AuthContext

## Overview

The AuthContext provides authentication state management for the TapTrap Admin Panel. It creates a context that stores the current user, authentication status, and authentication functions that can be accessed throughout the application.

## File Location

`admin/src/context/AuthContext.js`

## Dependencies

- React hooks (`createContext`, `useContext`)
- Custom `useAuth` hook

## Context Structure

```jsx
import React, { createContext, useContext } from 'react';
import { useAuth } from '../hooks/useAuth';

// Create the context
const AuthContext = createContext();

// Provider component
export const AuthProvider = ({ children }) => {
  const auth = useAuth();
  
  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the context
export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};
```

## Key Features

1. **Context Creation**:
   - Creates a React context for authentication state
   - Provides a default undefined value

2. **Provider Component**:
   - Wraps the application with the AuthContext.Provider
   - Uses the `useAuth` hook to get authentication state and functions
   - Passes the auth object as the context value

3. **Custom Hook**:
   - Provides a `useAuthContext` hook for consuming the context
   - Includes error checking to ensure the hook is used within a provider
   - Returns the full auth object with state and functions

## Context Value

The context provides the following values from the `useAuth` hook:

| Property | Type | Description |
|----------|------|-------------|
| `user` | Object \| null | The currently authenticated user or null if not authenticated |
| `loading` | boolean | Whether authentication state is being determined |
| `error` | string \| null | Any error that occurred during authentication |
| `login` | function | Function to log in a user with credentials |
| `logout` | function | Function to log out the current user |
| `isAuthenticated` | boolean | Whether a user is currently authenticated |

## Usage

### Provider Setup

```jsx
// In index.js or App.js
import { AuthProvider } from './context/AuthContext';

ReactDOM.render(
  <AuthProvider>
    <App />
  </AuthProvider>,
  document.getElementById('root')
);
```

### Consuming the Context

```jsx
// In a component
import { useAuthContext } from '../context/AuthContext';

const MyComponent = () => {
  const { user, isAuthenticated, login, logout } = useAuthContext();
  
  // Use the authentication state and functions
  return (
    <div>
      {isAuthenticated ? (
        <>
          <p>Welcome, {user.email}</p>
          <button onClick={logout}>Logout</button>
        </>
      ) : (
        <button onClick={() => login('email', 'password')}>Login</button>
      )}
    </div>
  );
};
```

## Development Notes

- The context is a thin wrapper around the `useAuth` hook
- This separation allows for easier testing and potential replacement of the authentication implementation
- The error in `useAuthContext` helps catch cases where the hook is used outside of a provider

## Security Considerations

- The context itself doesn't implement any security measures
- It relies on the `useAuth` hook for actual authentication logic
- Sensitive information like tokens should be handled carefully within the `useAuth` implementation
