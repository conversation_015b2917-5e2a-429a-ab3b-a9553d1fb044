# PrivateRoute Component

## Overview

The PrivateRoute component is a wrapper that protects routes requiring authentication in the TapTrap Admin Panel. It checks the user's authentication status and either renders the protected content or redirects to the login page.

## File Location

`admin/src/components/PrivateRoute.js`

## Dependencies

- React hooks (`useEffect`)
- React Router (`Navigate`)
- Chakra UI components
- Custom `useAuth` hook

## Component Structure

```jsx
const PrivateRoute = ({ children }) => {
  // Auth hook
  const { isAuthenticated, loading, user } = useAuth();

  // Debug logging
  useEffect(() => {
    console.log('PrivateRoute authentication state:', { isAuthenticated, loading, user });
  }, [isAuthenticated, loading, user]);

  // Loading state
  if (loading) {
    return (
      <Center h="100vh">
        <VStack spacing={4}>
          <Spinner 
            thickness="4px"
            speed="0.65s"
            emptyColor="gray.200"
            color="brand.500"
            size="xl"
          />
          <Text>Loading authentication...</Text>
        </VStack>
      </Center>
    );
  }

  // Not authenticated - redirect to login
  if (!isAuthenticated) {
    console.log('Not authenticated, redirecting to login');
    return <Navigate to="/login" />;
  }

  // Authenticated - render children
  console.log('Authenticated, showing protected content');
  return children;
};
```

## Key Features

1. **Authentication Check**:
   - Uses the `useAuth` hook to check authentication status
   - Accesses `isAuthenticated`, `loading`, and `user` from the auth context

2. **Loading State**:
   - Displays a spinner while authentication status is being determined
   - Centered in the viewport for visibility
   - Includes a text message to inform the user

3. **Redirection**:
   - Redirects unauthenticated users to the login page
   - Uses React Router's `Navigate` component for redirection

4. **Debug Logging**:
   - Logs authentication state changes to the console
   - Helps with debugging authentication issues

5. **Children Rendering**:
   - Renders the wrapped component (children) if authentication is successful
   - Preserves all props and functionality of the wrapped component

## Props

| Prop | Type | Description |
|------|------|-------------|
| `children` | React.ReactNode | The component(s) to render if the user is authenticated |

## Usage

```jsx
// In App.js or routing configuration
import PrivateRoute from './components/PrivateRoute';
import Dashboard from './pages/Dashboard';

// In a route definition
<Route 
  path="/dashboard" 
  element={<PrivateRoute><Dashboard /></PrivateRoute>} 
/>
```

## Authentication States

The component handles three possible authentication states:

1. **Loading**: Authentication status is being determined
   - Shows a loading spinner
   - Does not render children or redirect

2. **Not Authenticated**: User is not logged in
   - Redirects to the login page
   - Does not render children

3. **Authenticated**: User is logged in
   - Renders the children components
   - Allows access to the protected route

## Development Notes

- In development mode, the `useAuth` hook may bypass authentication checks
- The component includes detailed console logging to help with debugging
- The loading state prevents flickering between authenticated and unauthenticated states

## Security Considerations

- This component should wrap all routes that require authentication
- It relies on the `useAuth` hook to determine authentication status
- It does not perform any authentication logic itself
