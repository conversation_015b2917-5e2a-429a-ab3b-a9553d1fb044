# Layout Component

## Overview

The Layout component provides a consistent page structure for all authenticated pages in the TapTrap Admin Panel. It includes a responsive navigation system with a sidebar for desktop and a drawer for mobile, as well as a top navigation bar with logout functionality.

## File Location

`admin/src/components/Layout.js`

## Dependencies

- React
- Chakra UI components
- React Router (`Link as RouterLink`, `useNavigate`)
- React Icons (`FiMenu`, `FiHome`, `FiList`, `FiLogOut`, `FiPlus`)
- Custom `useAuth` hook

## Component Structure

```jsx
const Layout = ({ children, title }) => {
  // Drawer state
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  // Auth hook
  const { logout } = useAuth();
  const navigate = useNavigate();

  // Logout handler
  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Navigation items
  const menuItems = [
    { name: 'Dashboard', icon: FiHome, path: '/' },
    { name: 'Content', icon: FiList, path: '/content' },
    { name: 'Add Content', icon: FiPlus, path: '/content/add' },
  ];

  // Component rendering
  return (
    <Box minH="100vh" bg="gray.50">
      {/* Top Navigation */}
      <Flex as="nav">
        {/* Navigation content */}
      </Flex>

      {/* Mobile Drawer */}
      <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
        {/* Drawer content */}
      </Drawer>

      {/* Main Content */}
      <Flex>
        {/* Desktop Sidebar */}
        <Box display={{ base: 'none', md: 'block' }}>
          {/* Sidebar content */}
        </Box>

        {/* Page Content */}
        <Box flex="1" p={5} ml={{ base: 0, md: '240px' }} mt={5}>
          <Heading mb={6}>{title}</Heading>
          {children}
        </Box>
      </Flex>
    </Box>
  );
};
```

## Key Features

1. **Responsive Navigation**:
   - Desktop: Fixed sidebar with navigation links
   - Mobile: Collapsible drawer accessed via hamburger menu
   - Consistent navigation items across both layouts

2. **Top Navigation Bar**:
   - Application title
   - Hamburger menu for mobile
   - Logout button

3. **Navigation Items**:
   - Dashboard link
   - Content list link
   - Add content link
   - Each item includes an icon and text

4. **Content Area**:
   - Page title from props
   - Content from children prop
   - Consistent padding and spacing
   - Responsive margins that adjust based on screen size

5. **Authentication Integration**:
   - Logout functionality via the `useAuth` hook
   - Redirects to login page on logout

## Props

| Prop | Type | Description |
|------|------|-------------|
| `children` | React.ReactNode | The content to be rendered in the main content area |
| `title` | string | The title of the page, displayed above the content |

## Usage

```jsx
// In a page component
import Layout from '../components/Layout';

const SomePage = () => {
  return (
    <Layout title="Page Title">
      {/* Page content */}
    </Layout>
  );
};
```

## Responsive Behavior

- **Desktop** (md breakpoint and above):
  - Fixed sidebar with width of 240px
  - Content area with left margin to accommodate sidebar
  - Full navigation visible at all times

- **Mobile** (below md breakpoint):
  - No sidebar
  - Hamburger menu to access navigation drawer
  - Content area uses full width
  - Drawer slides in from left when activated

## Development Notes

- The component uses Chakra UI's `useDisclosure` hook to manage the mobile drawer state
- Navigation items are defined as an array for easy maintenance and extension
- The component assumes that the `useAuth` hook provides a `logout` function
