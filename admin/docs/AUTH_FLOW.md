# Authentication Flow

## Overview

This document describes the authentication flow in the TapTrap Admin Panel, including the components, services, and data flow involved in the authentication process.

## Components

The authentication system consists of the following components:

1. **Login Component** (`admin/src/pages/Login.js`)
   - Handles user input for email and password
   - Performs form validation
   - Displays error messages
   - Redirects to the dashboard on successful login

2. **Auth Service** (`admin/src/services/auth.service.js`)
   - Provides methods for authentication operations
   - Handles API calls for login
   - Manages token storage and retrieval
   - Provides utility methods for checking authentication status

3. **Auth Hook** (`admin/src/hooks/useAuth.js`)
   - Custom React hook that provides authentication state
   - Manages user state, loading state, and error state
   - Provides login and logout methods
   - Handles authentication persistence

4. **Auth Context** (`admin/src/context/AuthContext.js`)
   - Provides authentication state to the entire application
   - Uses the `useAuth` hook internally
   - Allows components to access authentication state and methods

5. **Private Route Component** (`admin/src/components/PrivateRoute.js`)
   - Protects routes that require authentication
   - Redirects to login page if user is not authenticated
   - Shows loading state while checking authentication

6. **API Service** (`admin/src/services/api.js`)
   - Configures Axios for API requests
   - Adds authentication token to requests
   - Handles authentication errors

## Data Flow

### Login Flow

1. User enters email and password in the Login component
2. Login component validates the input
3. On form submission, the Login component calls the `login` method from `useAuth`
4. The `useAuth` hook calls the `login` method from the Auth Service
5. The Auth Service makes an API call to `/admin/login` with the credentials
6. On successful login, the Auth Service:
   - Stores the JWT token in localStorage
   - Stores the user data in localStorage
   - Returns the response data
7. The `useAuth` hook updates its state with the user data
8. The Login component redirects to the dashboard

### Authentication Check Flow

1. When the application loads, the `useAuth` hook:
   - Checks localStorage for a token and user data
   - If found, sets the user state
   - If not found, sets the user state to null
2. The `isAuthenticated` value is derived from the user state
3. Protected routes use the `isAuthenticated` value to determine if the user can access them

### Logout Flow

1. User clicks the logout button
2. The component calls the `logout` method from `useAuth`
3. The `useAuth` hook calls the `logout` method from the Auth Service
4. The Auth Service removes the token and user data from localStorage
5. The `useAuth` hook sets the user state to null
6. The application redirects to the login page

## JWT Token Handling

- The JWT token is stored in localStorage as `token`
- The token is included in API requests as:
  - `Authorization: Bearer <token>` header (standard JWT method)
  - `x-auth-token: <token>` header (for backward compatibility)
- The token contains the user's ID, email, and role
- The token expires after 24 hours

## Security Considerations

- Credentials are never stored in plain text
- Passwords are not logged to the console
- The JWT token is stored in localStorage, which is vulnerable to XSS attacks
  - This is a trade-off for simplicity and is acceptable for this admin panel
  - For higher security requirements, consider using HttpOnly cookies
- Form validation helps prevent malicious input
- Error messages are generic to avoid information leakage

## Authentication in All Environments

- Authentication is now required in all environments (development and production)
- No automatic authentication or default credentials are used
- Users must log in with valid credentials to access protected routes
- JWT tokens are validated for all API requests
- The same security standards apply in both development and production
