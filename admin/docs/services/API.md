# API Service

## Overview

The API service provides a centralized interface for communicating with the TapTrap backend API. It handles authentication, request formatting, error handling, and provides specific functions for each API endpoint.

## File Location

`admin/src/services/api.js`

## Dependencies

- Axios

## Service Structure

```jsx
import axios from 'axios';

// Create axios instance with base URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5002/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    // Add authentication token to requests
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 unauthorized errors
  }
);

// API functions for specific endpoints
export const login = async (credentials) => {
  // Login implementation
};

export const getAllContent = async () => {
  // Get all content implementation
};

export const getContentById = async (id) => {
  // Get content by ID implementation
};

export const createContent = async (data) => {
  // Create content implementation
};

export const updateContent = async (id, data) => {
  // Update content implementation
};

export const deleteContent = async (id) => {
  // Delete content implementation
};

export default api;
```

## Key Features

1. **Axios Instance Configuration**:
   - Configures base URL from environment variable or default
   - Sets default headers for all requests
   - Creates a reusable API client

2. **Request Interceptor**:
   - Adds authentication token to all requests
   - Gets token from localStorage
   - Special handling for development environment

3. **Response Interceptor**:
   - Handles 401 (Unauthorized) responses
   - Redirects to login page on authentication failures
   - Special handling for development environment

4. **Authentication Functions**:
   - `login`: Authenticates user with credentials

5. **Content Management Functions**:
   - `getAllContent`: Retrieves all content items
   - `getContentById`: Retrieves a specific content item by ID
   - `createContent`: Creates a new content item
   - `updateContent`: Updates an existing content item
   - `deleteContent`: Deletes a content item

6. **Error Handling**:
   - Detailed error logging for debugging
   - Consistent error format for UI feedback
   - Special handling for different error types

## API Endpoints

| Function | HTTP Method | Endpoint | Description |
|----------|-------------|----------|-------------|
| `login` | POST | `/admin/login` | Authenticates an admin user |
| `getAllContent` | GET | `/admin/content` | Retrieves all content items |
| `getContentById` | GET | `/admin/content/:id` | Retrieves a specific content item |
| `createContent` | POST | `/admin/content` | Creates a new content item |
| `updateContent` | PUT | `/admin/content/:id` | Updates an existing content item |
| `deleteContent` | DELETE | `/admin/content/:id` | Deletes a content item |

## Usage

```jsx
import { login, getAllContent, createContent } from '../services/api';

// Authentication
const handleLogin = async (email, password) => {
  try {
    const response = await login({ email, password });
    // Handle successful login
    return response.user;
  } catch (error) {
    // Handle login error
    console.error('Login failed:', error);
    throw error;
  }
};

// Fetching data
const fetchContent = async () => {
  try {
    const data = await getAllContent();
    // Process content data
    return data;
  } catch (error) {
    // Handle fetch error
    console.error('Failed to fetch content:', error);
    throw error;
  }
};

// Creating data
const addNewContent = async (contentData) => {
  try {
    const result = await createContent(contentData);
    // Handle successful creation
    return result;
  } catch (error) {
    // Handle creation error
    console.error('Failed to create content:', error);
    throw error;
  }
};
```

## Development Mode

In development mode (when running on localhost):

- A default auth token is added to requests if none exists
- Authentication errors are logged but don't trigger redirects
- This allows for easier testing without needing to handle authentication

## Error Handling

The service provides consistent error handling:

- All API functions are wrapped in try/catch blocks
- Errors include detailed information from the server when available
- Console logging helps with debugging
- Errors are propagated to the calling component for UI feedback
