# Guía de Instalación del Panel de Administración de TapTrap

Esta guía proporciona instrucciones detalladas para instalar y configurar el Panel de Administración de TapTrap en diferentes entornos.

## Tabla de Contenidos

1. [Requisitos Previos](#requisitos-previos)
2. [Instalación para Desarrollo](#instalación-para-desarrollo)
3. [Configuración de Variables de Entorno](#configuración-de-variables-de-entorno)
4. [Conexión con el Backend](#conexión-con-el-backend)
5. [Construcción para Producción](#construcción-para-producción)
6. [Despliegue](#despliegue)

## Requisitos Previos

Antes de comenzar, asegúrate de tener instalado:

- **Node.js**: Versión 14.x o superior
  - Descarga: [https://nodejs.org/](https://nodejs.org/)
  - Verifica la instalación: `node --version`

- **npm** o **yarn**:
  - npm viene con Node.js
  - Para yarn: `npm install -g yarn`
  - Verifica la instalación: `npm --version` o `yarn --version`

- **Git**:
  - Descarga: [https://git-scm.com/](https://git-scm.com/)
  - Verifica la instalación: `git --version`

## Instalación para Desarrollo

### 1. Clonar el Repositorio

```bash
# Clonar el repositorio completo
git clone https://github.com/tuusuario/TapTrap.git

# Navegar al directorio del panel de administración
cd TapTrap/admin
```

### 2. Instalar Dependencias

Usando npm:

```bash
npm install
```

O usando yarn:

```bash
yarn install
```

### 3. Configurar Variables de Entorno

Crea un archivo `.env.development.local` en el directorio `admin/` con el siguiente contenido:

```
REACT_APP_API_URL=http://localhost:5001/api
```

Ajusta la URL según la configuración de tu backend.

### 4. Iniciar el Servidor de Desarrollo

Usando npm:

```bash
npm start
```

O usando yarn:

```bash
yarn start
```

Esto iniciará el servidor de desarrollo y abrirá automáticamente la aplicación en tu navegador predeterminado en `http://localhost:3000`.

## Configuración de Variables de Entorno

El panel de administración utiliza variables de entorno para configurar diferentes aspectos de la aplicación:

### Variables de Entorno Disponibles

- `REACT_APP_API_URL`: URL base para las solicitudes a la API
  - Desarrollo: `http://localhost:5001/api`
  - Producción: `/api` (cuando se utiliza con rewrites de Vercel)

### Archivos de Configuración

El proyecto utiliza diferentes archivos para diferentes entornos:

- `.env`: Valores predeterminados para todos los entornos
- `.env.development`: Valores específicos para desarrollo
- `.env.production`: Valores específicos para producción
- `.env.local`: Valores locales que sobrescriben los anteriores (no se incluye en el control de versiones)
- `.env.development.local`: Sobrescribe valores de desarrollo (no se incluye en el control de versiones)
- `.env.production.local`: Sobrescribe valores de producción (no se incluye en el control de versiones)

## Conexión con el Backend

El panel de administración necesita comunicarse con el backend de TapTrap para funcionar correctamente.

### Configuración Local

1. Asegúrate de que el backend esté en funcionamiento en `http://localhost:5001` (o el puerto que hayas configurado)
2. Verifica que la variable `REACT_APP_API_URL` apunte a la URL correcta

### Solución de Problemas de Conexión

Si tienes problemas para conectar con el backend:

1. Verifica que el servidor backend esté en funcionamiento
2. Comprueba que no haya errores CORS (el backend debe permitir solicitudes desde `http://localhost:3000`)
3. Asegúrate de que las rutas de la API sean correctas
4. Revisa la consola del navegador para ver errores específicos

## Construcción para Producción

Cuando estés listo para desplegar la aplicación, necesitarás crear una versión optimizada para producción.

### Crear una Construcción de Producción

Usando npm:

```bash
npm run build
```

O usando yarn:

```bash
yarn build
```

Esto creará una versión optimizada de la aplicación en el directorio `build/`.

### Probar la Construcción Localmente

Para verificar que la construcción de producción funciona correctamente antes de desplegarla:

```bash
npx serve -s build
```

Esto iniciará un servidor local que sirve la versión de producción en `http://localhost:5000`.

## Despliegue

El Panel de Administración de TapTrap está diseñado para ser desplegado en Vercel, pero puede desplegarse en cualquier plataforma que soporte aplicaciones React.

### Despliegue en Vercel

1. Crea una cuenta en [Vercel](https://vercel.com/) si aún no tienes una
2. Instala la CLI de Vercel:
   ```bash
   npm install -g vercel
   ```
3. Inicia sesión en Vercel:
   ```bash
   vercel login
   ```
4. Despliega la aplicación:
   ```bash
   vercel
   ```
5. Sigue las instrucciones en pantalla para completar el despliegue

### Configuración de Vercel

Para que la aplicación funcione correctamente en Vercel, necesitarás configurar:

1. **Variables de Entorno**:
   - `REACT_APP_API_URL`: Establece a `/api` para producción

2. **Rewrites**:
   Crea o actualiza el archivo `vercel.json` en el directorio raíz del proyecto:

   ```json
   {
     "version": 2,
     "buildCommand": "npm run vercel-build",
     "outputDirectory": "build",
     "framework": "create-react-app",
     "rewrites": [
       {
         "source": "/api/:path*",
         "destination": "https://tu-backend-url.com/api/:path*"
       },
       {
         "source": "/(.*)",
         "destination": "/index.html"
       }
     ]
   }
   ```

   Reemplaza `https://tu-backend-url.com` con la URL real de tu backend.

Para instrucciones más detalladas sobre el despliegue en Vercel, consulta [VERCEL_DEPLOYMENT.md](../VERCEL_DEPLOYMENT.md).

### Despliegue en Otras Plataformas

El panel de administración puede desplegarse en cualquier plataforma que soporte aplicaciones React, como:

- **Netlify**: Sigue la [documentación de Netlify](https://docs.netlify.com/configure-builds/common-configurations/create-react-app/)
- **GitHub Pages**: Usa [gh-pages](https://github.com/tschaub/gh-pages)
- **Firebase Hosting**: Sigue la [documentación de Firebase](https://firebase.google.com/docs/hosting/quickstart)

En todos los casos, necesitarás configurar correctamente la URL de la API y asegurarte de que las solicitudes a `/api/*` se redirijan al backend.

## Solución de Problemas de Instalación

### Errores Comunes

#### "Module not found"
- Asegúrate de haber ejecutado `npm install` o `yarn install`
- Verifica que no haya errores en la instalación de dependencias

#### "Invalid hook call"
- Puede indicar múltiples versiones de React en el proyecto
- Ejecuta `npm dedupe` o `yarn dedupe`

#### Errores de CORS
- Verifica la configuración CORS en el backend
- Asegúrate de que el backend permita solicitudes desde el origen del frontend

#### "Cannot find module 'react-scripts'"
- Ejecuta `npm install react-scripts` o `yarn add react-scripts`

Para más ayuda con problemas de instalación, consulta la [documentación de Create React App](https://create-react-app.dev/docs/troubleshooting/).
