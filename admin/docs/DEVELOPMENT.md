# Guía de Desarrollo del Panel de Administración de TapTrap

Esta guía proporciona información detallada para desarrolladores que trabajan en el Panel de Administración de TapTrap.

## Tabla de Contenidos

1. [Configuración del Entorno de Desarrollo](#configuración-del-entorno-de-desarrollo)
2. [Estructura del Proyecto](#estructura-del-proyecto)
3. [Flujos de Trabajo de Desarrollo](#flujos-de-trabajo-de-desarrollo)
4. [Componentes Principales](#componentes-principales)
5. [Gestión de Estado](#gestión-de-estado)
6. [Integración con la API](#integración-con-la-api)
7. [Autenticación](#autenticación)
8. [Pruebas](#pruebas)
9. [Mejores Prácticas](#mejores-prácticas)

## Configuración del Entorno de Desarrollo

### Requisitos Previos

- Node.js (v14 o posterior)
- npm o yarn
- Git
- Editor de código (recomendado: VS Code)

### Configuración Inicial

1. Clona el repositorio:
   ```bash
   git clone https://github.com/tuusuario/TapTrap.git
   cd TapTrap/admin
   ```

2. Instala las dependencias:
   ```bash
   npm install
   # o
   yarn install
   ```

3. Configura las variables de entorno:
   - Crea un archivo `.env.development.local` con:
     ```
     REACT_APP_API_URL=http://localhost:5001/api
     ```

4. Inicia el servidor de desarrollo:
   ```bash
   npm start
   # o
   yarn start
   ```

### Extensiones Recomendadas para VS Code

- ESLint
- Prettier
- React Developer Tools
- Debugger for Chrome

## Estructura del Proyecto

```
admin/
├── public/               # Activos estáticos públicos
├── src/                  # Código fuente
│   ├── components/       # Componentes reutilizables
│   │   ├── Layout.js     # Componente de diseño principal
│   │   └── PrivateRoute.js # Componente para rutas protegidas
│   ├── context/          # Contextos de React
│   │   └── AuthContext.js # Contexto de autenticación
│   ├── hooks/            # Hooks personalizados
│   │   └── useAuth.js    # Hook para autenticación
│   ├── pages/            # Componentes de página
│   │   ├── AddContent.js # Página para añadir contenido
│   │   ├── ContentList.js # Página de listado de contenido
│   │   ├── Dashboard.js  # Página de panel de control
│   │   ├── EditContent.js # Página para editar contenido
│   │   └── Login.js      # Página de inicio de sesión
│   ├── services/         # Servicios
│   │   └── api.js        # Cliente API
│   ├── App.js            # Componente principal
│   └── index.js          # Punto de entrada
├── .env                  # Variables de entorno predeterminadas
├── .env.development      # Variables para desarrollo
├── .env.production       # Variables para producción
├── package.json          # Dependencias y scripts
└── vercel.json           # Configuración de despliegue para Vercel
```

## Flujos de Trabajo de Desarrollo

### Desarrollo de Características

1. Crea una nueva rama desde `main`:
   ```bash
   git checkout -b feature/nombre-de-la-caracteristica
   ```

2. Implementa la característica

3. Asegúrate de que el código cumple con los estándares:
   ```bash
   npm run lint
   # o
   yarn lint
   ```

4. Prueba la característica localmente

5. Crea un commit con tus cambios:
   ```bash
   git add .
   git commit -m "Descripción de los cambios"
   ```

6. Envía la rama al repositorio remoto:
   ```bash
   git push origin feature/nombre-de-la-caracteristica
   ```

7. Crea una solicitud de pull request

### Corrección de Errores

1. Crea una rama desde `main`:
   ```bash
   git checkout -b fix/descripcion-del-error
   ```

2. Implementa la corrección

3. Sigue los mismos pasos que para el desarrollo de características

## Componentes Principales

### Layout

El componente `Layout` proporciona la estructura común para todas las páginas autenticadas, incluyendo:

- Barra de navegación
- Menú lateral
- Contenedor principal
- Pie de página

```jsx
// Ejemplo de uso
import Layout from '../components/Layout';

function MiPagina() {
  return (
    <Layout>
      <h1>Contenido de mi página</h1>
    </Layout>
  );
}
```

### PrivateRoute

El componente `PrivateRoute` protege las rutas que requieren autenticación:

```jsx
// Ejemplo de uso en App.js
<Route 
  path="/dashboard" 
  element={<PrivateRoute><Dashboard /></PrivateRoute>} 
/>
```

## Gestión de Estado

### Contexto de Autenticación

El estado de autenticación se gestiona a través de `AuthContext`:

```jsx
// Ejemplo de uso
import { useAuthContext } from '../context/AuthContext';

function MiComponente() {
  const { isAuthenticated, user, login, logout } = useAuthContext();
  
  // Usar estas variables y funciones según sea necesario
}
```

### Estado Local

Para el estado específico de componentes, utiliza los hooks de React:

```jsx
// Ejemplo
function ContentList() {
  const [content, setContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Lógica para cargar contenido
}
```

## Integración con la API

### Cliente API

El archivo `services/api.js` proporciona un cliente Axios configurado para comunicarse con el backend:

```jsx
// Ejemplo de uso
import { getAllContent, createContent } from '../services/api';

// Obtener contenido
const fetchContent = async () => {
  try {
    const data = await getAllContent();
    setContent(data.items);
  } catch (error) {
    setError('Error al cargar el contenido');
  }
};

// Crear contenido
const handleSubmit = async (formData) => {
  try {
    await createContent(formData);
    // Manejar éxito
  } catch (error) {
    // Manejar error
  }
};
```

### Interceptores

El cliente API incluye interceptores para:

1. Añadir el token de autenticación a las solicitudes
2. Manejar errores de autenticación (401)
3. Registrar información de depuración

## Autenticación

### Flujo de Autenticación

1. El usuario introduce credenciales en la página de inicio de sesión
2. Las credenciales se envían al endpoint `/api/admin/login`
3. Si son válidas, el backend devuelve un token JWT
4. El token se almacena en localStorage
5. El estado de autenticación se actualiza en el contexto
6. El usuario es redirigido al panel de control

### Protección de Rutas

Las rutas protegidas utilizan el componente `PrivateRoute` que:

1. Verifica si el usuario está autenticado
2. Si está autenticado, renderiza el componente hijo
3. Si no está autenticado, redirige a la página de inicio de sesión

## Pruebas

### Pruebas Manuales

Para probar manualmente el panel de administración:

1. Inicia el servidor de desarrollo
2. Navega a `http://localhost:3000`
3. Inicia sesión con credenciales válidas
4. Prueba todas las funcionalidades:
   - Ver listado de contenido
   - Filtrar y buscar contenido
   - Añadir nuevo contenido
   - Editar contenido existente
   - Eliminar contenido

### Pruebas Automatizadas

Para implementar pruebas automatizadas:

1. Utiliza Jest y React Testing Library
2. Crea pruebas unitarias para componentes individuales
3. Crea pruebas de integración para flujos completos
4. Utiliza mocks para simular las respuestas de la API

## Mejores Prácticas

### Estilo de Código

- Sigue las convenciones de ESLint y Prettier
- Utiliza nombres descriptivos para variables y funciones
- Escribe comentarios para código complejo
- Mantén los componentes pequeños y enfocados

### Rendimiento

- Utiliza `React.memo` para componentes que no cambian frecuentemente
- Optimiza re-renderizados con `useMemo` y `useCallback`
- Evita cálculos costosos en cada renderizado
- Implementa carga perezosa para componentes grandes

### Seguridad

- No almacenes información sensible en el código
- Utiliza variables de entorno para configuración
- Valida todas las entradas de usuario
- Implementa protección contra CSRF

### Accesibilidad

- Utiliza etiquetas semánticas de HTML
- Añade atributos `aria-*` cuando sea necesario
- Asegúrate de que la aplicación sea navegable con teclado
- Mantén un contraste de color adecuado

## Solución de Problemas de Desarrollo

### Problemas Comunes

- **"Module not found"**: Verifica las importaciones y la instalación de dependencias
- **Errores de CORS**: Configura correctamente el backend para permitir solicitudes desde el origen del frontend
- **Problemas de autenticación**: Verifica el flujo de tokens y la configuración de localStorage

Para más información sobre solución de problemas, consulta [TROUBLESHOOTING.md](../TROUBLESHOOTING.md).
