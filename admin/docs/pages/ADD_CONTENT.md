# Add Content Page

## Overview

The Add Content page provides a form interface for creating new content items in the TapTrap game. It allows administrators to specify text in multiple languages, select game mode and category, and set the active status of the content.

## File Location

`admin/src/pages/AddContent.js`

## Dependencies

- React hooks (`useState`)
- React Router (`useNavigate`)
- Chakra UI components
- React Hook Form
- Layout component
- API service (`createContent`)

## Component Structure

```jsx
const AddContent = () => {
  // React Hook Form setup
  const {
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    watch,
  } = useForm({
    defaultValues: {
      text_en: '',
      text_es: '',
      category: '',
      gameMode: '',
      active: true,
    },
  });
  
  // Hooks
  const toast = useToast();
  const navigate = useNavigate();
  const selectedGameMode = watch('gameMode');

  // Form submission handler
  const onSubmit = async (data) => {
    // Create content logic
  };

  // Component rendering
  return (
    <Layout title="Add New Content">
      {/* Form UI */}
    </Layout>
  );
};
```

## Key Features

1. **Form Fields**:
   - English text (textarea)
   - Spanish text (textarea)
   - Game mode selection (dropdown)
   - Category selection (dynamic dropdown based on game mode)
   - Active status (switch)

2. **Form Validation**:
   - Required field validation
   - Minimum length validation for text fields
   - Dynamic validation based on selected game mode

3. **Dynamic UI**:
   - Category options change based on selected game mode
   - Form layout adapts to different screen sizes

4. **Form Submission**:
   - Data processing before submission
   - API integration for content creation
   - Success/error feedback via toast notifications
   - Navigation to content list on successful submission

5. **UI Components**:
   - Card layout for the form
   - Consistent form styling with the application theme
   - Clear error messages for validation failures
   - Loading state for the submit button

## Data Flow

1. User fills out the form fields
2. React Hook Form handles validation as the user types
3. On form submission, the `onSubmit` function is called with validated data
4. The data is processed (e.g., fallback to English if Spanish is missing)
5. The processed data is sent to the API via the `createContent` function
6. On successful creation, a success toast is shown and the user is redirected
7. On error, an error toast is shown with details

## Form Validation Rules

- **English Text**:
  - Required
  - Minimum length: 3 characters

- **Spanish Text**:
  - Required
  - Minimum length: 3 characters

- **Game Mode**:
  - Required
  - Must be one of: "questions", "dares"

- **Category**:
  - Required
  - Options depend on selected game mode

## Error Handling

- Form validation errors are displayed inline
- API errors are caught and displayed as toast notifications
- Detailed error logging to the console for debugging

## Usage

```jsx
// In App.js or routing configuration
<Route path="/content/add" element={<PrivateRoute><AddContent /></PrivateRoute>} />
```

## Development Notes

- The component uses React Hook Form for form state management and validation
- The `watch` function from React Hook Form is used to reactively update the category options based on the selected game mode
