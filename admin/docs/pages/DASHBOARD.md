# Dashboard Page

## Overview

The Dashboard page serves as the main landing page for authenticated administrators. It provides a high-level overview of the content statistics in the TapTrap game, including total content count, distribution by game mode, and category breakdowns.

## File Location

`admin/src/pages/Dashboard.js`

## Dependencies

- React hooks (`useState`, `useEffect`)
- Chakra UI components
- Layout component
- API service (`getAllContent`)

## Component Structure

```jsx
const Dashboard = () => {
  // State variables
  const [stats, setStats] = useState({
    total: 0,
    questions: 0,
    dares: 0,
    categories: {},
  });
  const [loading, setLoading] = useState(true);

  // Data fetching
  useEffect(() => {
    const fetchStats = async () => {
      // Fetch content and calculate statistics
    };
    
    fetchStats();
  }, []);

  // Component rendering
  return (
    <Layout title="Dashboard">
      {/* Dashboard content */}
    </Layout>
  );
};
```

## Key Features

1. **Content Statistics**:
   - Total content count
   - Breakdown by game mode (questions vs. dares)
   - Distribution by category
   - Percentage calculations

2. **Data Visualization**:
   - Card-based layout for key metrics
   - Visual indicators for content distribution
   - Responsive grid layout

3. **Data Fetching**:
   - Asynchronous data loading from the API
   - Loading state management
   - Error handling

4. **UI Components**:
   - Stat components for numerical data
   - Cards for grouping related information
   - Consistent styling with the application theme

## Data Flow

1. On component mount, the `fetchStats` function is called
2. The function fetches all content items from the API
3. It processes the data to calculate various statistics:
   - Counts total number of content items
   - Categorizes items by game mode (questions/dares)
   - Groups items by category
4. The calculated statistics are stored in component state
5. The UI is updated to reflect the current statistics

## Loading State

- During data fetching, a loading indicator is displayed
- Once data is loaded, the dashboard statistics are shown

## Error Handling

- Errors during data fetching are caught and logged to the console
- The loading state is set to false even if an error occurs

## Usage

```jsx
// In App.js or routing configuration
<Route path="/" element={<PrivateRoute><Dashboard /></PrivateRoute>} />
```

## Development Notes

- The dashboard is designed to be the default landing page after login
- It provides a quick overview of the content database
- All statistics are calculated client-side from the raw content data
