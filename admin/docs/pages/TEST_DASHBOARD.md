# Test Dashboard Page

## Overview

The Test Dashboard page provides a simplified version of the main dashboard for testing purposes. It bypasses authentication requirements and offers quick links to key functionality, making it useful during development and testing phases.

## File Location

`admin/src/pages/TestDashboard.js`

## Dependencies

- React
- Chakra UI components
- React Router (`Link as RouterLink`)

## Component Structure

```jsx
const TestDashboard = () => {
  return (
    <Container maxW="container.xl" py={10}>
      <VStack spacing={8} align="stretch">
        <Heading as="h1" size="xl">TapTrap Admin Dashboard (Test Version)</Heading>
        
        <Box p={5} shadow="md" borderWidth="1px" bg="white">
          <Heading fontSize="xl" mb={4}>Dashboard Overview</Heading>
          <p>This is a test dashboard to bypass authentication issues.</p>
        </Box>
        
        <Box p={5} shadow="md" borderWidth="1px" bg="white">
          <Heading fontSize="xl" mb={4}>Quick Links</Heading>
          <VStack align="stretch" spacing={4}>
            <Button as={RouterLink} to="/content" colorScheme="purple">
              View Content
            </Button>
            <Button as={RouterLink} to="/content/add" colorScheme="green">
              Add New Content
            </Button>
          </VStack>
        </Box>
      </VStack>
    </Container>
  );
};
```

## Key Features

1. **Simplified Interface**:
   - Clean, minimal UI with essential information
   - No data fetching or complex state management
   - Focused on providing quick access to key features

2. **Quick Navigation**:
   - Direct links to content list and content creation
   - Buttons styled with distinct colors for easy identification

3. **Development Purpose**:
   - Designed specifically for testing and development
   - Bypasses authentication requirements
   - Provides clear indication that it's a test version

4. **UI Components**:
   - Container layout with vertical stacking
   - Card-like boxes for content grouping
   - Consistent heading hierarchy
   - Responsive design that works on all screen sizes

## Usage

```jsx
// In App.js or routing configuration
<Route path="/test" element={<TestDashboard />} />
```

## Development Notes

- This component is primarily intended for development and testing
- It should not be accessible in production environments
- It provides a way to quickly access key functionality without going through the authentication flow

## Security Considerations

- In production builds, routes to this component should be disabled
- The component itself does not implement any security checks
- It assumes that access control is handled at the routing level
