# Edit Content Page

## Overview

The Edit Content page provides a form interface for modifying existing content items in the TapTrap game. It allows administrators to update text in multiple languages, change game mode and category, and toggle the active status of the content.

## File Location

`admin/src/pages/EditContent.js`

## Dependencies

- React hooks (`useState`, `useEffect`)
- React Router (`useNavigate`, `useParams`)
- Chakra UI components
- React Hook Form
- Layout component
- API service (`getContentById`, `updateContent`)

## Component Structure

```jsx
const EditContent = () => {
  // URL parameter
  const { id } = useParams();
  
  // State variables
  const [loading, setLoading] = useState(true);
  const [originalGameMode, setOriginalGameMode] = useState('');
  
  // Hooks
  const toast = useToast();
  const navigate = useNavigate();
  
  // React Hook Form setup
  const {
    handleSubmit,
    register,
    reset,
    watch,
    formState: { errors, isSubmitting },
  } = useForm();
  
  const selectedGameMode = watch('gameMode', originalGameMode);

  // Data fetching
  useEffect(() => {
    const fetchContent = async () => {
      // Fetch content and populate form
    };

    fetchContent();
  }, [id, reset, toast, navigate]);

  // Form submission handler
  const onSubmit = async (data) => {
    // Update content logic
  };

  // Component rendering with loading state
  if (loading) {
    return (
      <Layout title="Edit Content">
        {/* Skeleton loading UI */}
      </Layout>
    );
  }

  return (
    <Layout title="Edit Content">
      {/* Form UI */}
    </Layout>
  );
};
```

## Key Features

1. **Content Loading**:
   - Fetches existing content by ID from the API
   - Populates form fields with current values
   - Displays skeleton UI during loading

2. **Form Fields**:
   - English text (textarea)
   - Spanish text (textarea)
   - Game mode selection (dropdown)
   - Category selection (dynamic dropdown based on game mode)
   - Active status (switch)

3. **Form Validation**:
   - Required field validation
   - Minimum length validation for text fields
   - Dynamic validation based on selected game mode

4. **Dynamic UI**:
   - Category options change based on selected game mode
   - Form layout adapts to different screen sizes

5. **Form Submission**:
   - Data processing before submission
   - API integration for content updating
   - Success/error feedback via toast notifications
   - Navigation to content list on successful submission

## Data Flow

1. On component mount, content is fetched by ID from the API
2. The form is populated with the fetched data using the `reset` function
3. User modifies the form fields
4. React Hook Form handles validation as the user types
5. On form submission, the `onSubmit` function is called with validated data
6. The data is processed (e.g., fallback to English if Spanish is missing)
7. The processed data is sent to the API via the `updateContent` function
8. On successful update, a success toast is shown and the user is redirected
9. On error, an error toast is shown with details

## Loading State

- During initial data fetching, a skeleton UI is displayed
- The skeleton mimics the form layout for a smoother visual transition
- Once data is loaded, the actual form is shown

## Error Handling

- Errors during data fetching redirect to the content list with an error toast
- Form validation errors are displayed inline
- API errors during update are caught and displayed as toast notifications
- Detailed error logging to the console for debugging

## Usage

```jsx
// In App.js or routing configuration
<Route path="/content/edit/:id" element={<PrivateRoute><EditContent /></PrivateRoute>} />
```

## Development Notes

- The component uses React Hook Form for form state management and validation
- The `reset` function from React Hook Form is used to populate the form with existing data
- The component tracks the original game mode to handle category options correctly
