# Login Page

## Overview

The Login page provides authentication functionality for the TapTrap Admin Panel. It allows administrators to sign in with their credentials to access the protected areas of the application.

## File Location

`admin/src/pages/Login.js`

## Dependencies

- React hooks (`useState`)
- React Router (`useNavigate`)
- Chakra UI components
- Custom `useAuth` hook

## Component Structure

```jsx
const Login = () => {
  // State variables
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Hooks
  const { login, error } = useAuth();
  const navigate = useNavigate();
  const toast = useToast();

  // Form submission handler
  const handleSubmit = async (e) => {
    // Authentication logic
  };

  // Component rendering
  return (
    <Container>
      <Box>
        {/* Form elements */}
      </Box>
    </Container>
  );
};
```

## Key Features

1. **Form Handling**:
   - Email and password input fields with state management
   - Form submission with preventDefault to handle authentication
   - Loading state during submission

2. **Authentication**:
   - Uses the `login` function from the `useAuth` hook
   - Stores authentication token and user data on successful login
   - Displays error messages on failed login attempts

3. **Navigation**:
   - Redirects to the dashboard on successful login
   - Uses React Router's `useNavigate` hook for redirection

4. **UI Components**:
   - Responsive container layout
   - Chakra UI components for consistent styling
   - Toast notifications for feedback

5. **Development Mode**:
   - Special handling for development environment
   - Hardcoded admin credentials for testing

## Usage

```jsx
// In App.js or routing configuration
<Route path="/login" element={<Login />} />
```

## Form Validation

- Email field: Required, must be valid email format
- Password field: Required

## Error Handling

- Displays authentication errors from the backend
- Shows toast notifications for failed login attempts
- Logs detailed error information to the console for debugging

## Development Notes

- For local development, the component includes special handling <NAME_EMAIL> account
- In production, all credentials are validated against the backend API

## Security Considerations

- Credentials are never stored in plain text
- Authentication token is stored in localStorage
- Passwords are not logged to the console in production
