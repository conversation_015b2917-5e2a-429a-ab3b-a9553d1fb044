# Content List Page

## Overview

The Content List page displays all game content items in a tabular format with filtering, searching, and action capabilities. It serves as the main interface for content management, allowing administrators to view, edit, and delete content items.

## File Location

`admin/src/pages/ContentList.js`

## Dependencies

- React hooks (`useState`, `useEffect`)
- React Router (`Link as RouterLink`)
- Chakra UI components
- Layout component
- API service (`getAllContent`, `deleteContent`)

## Component Structure

```jsx
const ContentList = () => {
  // State variables
  const [content, setContent] = useState([]);
  const [filteredContent, setFilteredContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterGameMode, setFilterGameMode] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [itemToDelete, setItemToDelete] = useState(null);

  // Hooks
  const cancelRef = React.useRef();
  const toast = useToast();

  // Data fetching and filtering
  useEffect(() => {
    // Fetch content from API
  }, []);

  useEffect(() => {
    // Filter content based on search and filter criteria
  }, [content, searchTerm, filterCategory, filterGameMode]);

  // Action handlers
  const handleDelete = async () => {
    // Delete content logic
  };

  const confirmDelete = (item) => {
    // Open confirmation dialog
  };

  const resetFilters = () => {
    // Reset all filters
  };

  // Component rendering
  return (
    <Layout title="Content Management">
      {/* Content list UI */}
    </Layout>
  );
};
```

## Key Features

1. **Content Display**:
   - Tabular layout with columns for ID, text, category, game mode, status, and actions
   - Pagination for large datasets
   - Visual indicators for content status (active/inactive)

2. **Filtering and Searching**:
   - Text search across all content
   - Category filter dropdown
   - Game mode filter dropdown
   - Filter reset functionality

3. **Content Actions**:
   - Edit button linking to the edit page
   - Delete button with confirmation dialog
   - Add new content button
   - **Generate Content button**: AI-powered content generation (lightning bolt icon)

4. **Data Management**:
   - Asynchronous data loading from the API
   - Client-side filtering and searching
   - Optimistic UI updates on deletion

5. **UI Components**:
   - Responsive table with horizontal scrolling on mobile
   - Filter controls in a card layout
   - Action buttons with icons
   - Confirmation dialog for destructive actions

## Data Flow

1. On component mount, all content is fetched from the API
2. The raw content is stored in the `content` state
3. When search or filter criteria change, the content is filtered
4. Filtered results are stored in `filteredContent` state
5. The table displays the filtered content

## Loading State

- During initial data fetching, a loading indicator is displayed
- Once data is loaded, the content table is shown

## Error Handling

- Errors during data fetching are caught and displayed as toast notifications
- Errors during content deletion are handled with appropriate error messages

## Usage

```jsx
// In App.js or routing configuration
<Route path="/content" element={<PrivateRoute><ContentList /></PrivateRoute>} />
```

## Development Notes

- The component uses the `useDisclosure` hook from Chakra UI for managing the delete confirmation dialog
- The filter logic is implemented client-side for better performance with small to medium datasets
