# Documentación de Endpoints de la API de TapTrap

Esta documentación detalla todos los endpoints disponibles en la API de TapTrap que son utilizados por el panel de administración.

## Tabla de Contenidos

1. [Información General](#información-general)
   - [URL Base](#url-base)
   - [Formato de Respuesta](#formato-de-respuesta)
   - [Cabeceras Requeridas](#cabeceras-requeridas)
   - [Encriptación de Respuestas](#encriptación-de-respuestas)
2. [Documentación Swagger](#documentación-swagger)
3. [Configuración de MongoDB](#configuración-de-mongodb)
4. [Autenticación](#autenticación)
5. [Endpoints de Contenido](#endpoints-de-contenido)
   - [Obtener Todo el Contenido (Público)](#obtener-todo-el-contenido-público)
   - [Obtener Todo el Contenido (Admin)](#obtener-todo-el-contenido-admin)
   - [Obtener Contenido por ID](#obtener-contenido-por-id)
   - [Crear Contenido](#crear-contenido)
   - [Actualizar Contenido](#actualizar-contenido)
   - [Eliminar Contenido](#eliminar-contenido)
   - [Generar Contenido con IA](#generar-contenido-con-ia)
   - [Crear Contenido en Lote](#crear-contenido-en-lote)
6. [Códigos de Estado](#códigos-de-estado)
7. [Ejemplos de Uso](#ejemplos-de-uso)

## Información General

### URL Base

- **Desarrollo**: `http://localhost:5002/api`
- **Producción**: `/api` (con redirección a través de Vercel)

### Formato de Respuesta

Todas las respuestas de la API están en formato JSON.

### Cabeceras Requeridas

- `Content-Type: application/json` - Para solicitudes con cuerpo JSON
- `x-auth-token: <token>` - Para endpoints protegidos (método tradicional)
- `Authorization: Bearer <token>` - Para endpoints protegidos (método estándar JWT)

### Encriptación de Respuestas

Algunos endpoints pueden devolver datos encriptados cuando la variable de entorno `ENCRYPTION_KEY` está configurada. En estos casos, la respuesta tendrá el siguiente formato:

```json
{
  "encrypted": true,
  "data": "U2FsdGVkX1+A2KKGrSWUVXDFRNrTsXYeT9Lx3VQ6jWYn9..."
}
```

Para desencriptar estos datos en el cliente, se debe utilizar la misma clave de encriptación:

```javascript
import CryptoJS from 'crypto-js';

// Función para desencriptar datos
const desencriptar = (datosEncriptados, claveSecreta) => {
  const bytes = CryptoJS.AES.decrypt(datosEncriptados, claveSecreta);
  const textoDesencriptado = bytes.toString(CryptoJS.enc.Utf8);
  return JSON.parse(textoDesencriptado);
};

// Ejemplo de uso
const obtenerContenido = async () => {
  const respuesta = await fetch('/api/content');
  const datos = await respuesta.json();

  if (datos.encrypted) {
    // Los datos están encriptados, desencriptar
    return desencriptar(datos.data, 'tu-clave-de-encriptacion');
  } else {
    // Los datos no están encriptados
    return datos;
  }
};

## Documentación Swagger

La API de TapTrap ahora incluye documentación interactiva con Swagger UI, que permite explorar y probar todos los endpoints directamente desde el navegador.

### Acceso a Swagger UI

- **Desarrollo**: `http://localhost:5002/api/docs`
- **Producción**: `/api/docs`

### Características de Swagger UI

- Documentación completa de todos los endpoints
- Interfaz interactiva para probar las solicitudes
- Esquemas detallados de modelos de datos
- Información sobre parámetros, cuerpos de solicitud y respuestas
- Soporte para autenticación JWT

### Autenticación en Swagger UI

Para probar endpoints protegidos en Swagger UI:

1. Primero, ejecuta el endpoint `/admin/login` para obtener un token JWT
2. Haz clic en el botón "Authorize" en la parte superior de la página
3. Tienes dos opciones para autenticarte:
   - **Método Bearer**: Introduce el token en el formato `Bearer <token>` en la sección "bearerAuth"
   - **Método API Key**: Introduce el token directamente (sin "Bearer") en la sección "apiKeyAuth"
4. Ahora puedes probar los endpoints protegidos con cualquiera de los dos métodos

## Configuración de MongoDB

La aplicación utiliza MongoDB como base de datos principal. A continuación se detallan los aspectos más importantes de la configuración.

### Cadena de Conexión

La cadena de conexión a MongoDB se configura a través de la variable de entorno `MONGODB_URI`. Para MongoDB Atlas, la cadena de conexión tiene el siguiente formato:

```
mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
```

Ejemplo:
```
mongodb+srv://eliezerpujols:<EMAIL>/?retryWrites=true&w=majority&appName=TapTrap
```

### Estructura de Servicios

La aplicación utiliza una arquitectura de servicios para interactuar con la base de datos:

1. **DatabaseService**: Gestiona la conexión a MongoDB y proporciona métodos para operaciones comunes.
2. **ContentService**: Maneja operaciones relacionadas con el contenido del juego.
3. **UserService**: Gestiona operaciones relacionadas con usuarios y autenticación.

### Modelos de Datos

Los principales modelos de datos son:

1. **Content**: Almacena el contenido del juego (preguntas y retos).
   ```javascript
   {
     id: String,            // Identificador único
     text_en: String,       // Texto en inglés
     text_es: String,       // Texto en español
     category: String,      // Categoría (casual, mild, spicy, no_limits)
     gameMode: String,      // Modo de juego (questions, dares)
     active: Boolean,       // Estado activo
     createdAt: Date,       // Fecha de creación
     updatedAt: Date        // Fecha de actualización
   }
   ```

2. **User**: Almacena información de usuarios administradores.
   ```javascript
   {
     email: String,         // Correo electrónico (único)
     password: String,      // Contraseña (hash)
     role: String,          // Rol (admin)
     createdAt: Date,       // Fecha de creación
     updatedAt: Date        // Fecha de actualización
   }
   ```

### Opciones de Conexión

La conexión a MongoDB se configura con las siguientes opciones para optimizar el rendimiento:

```javascript
const mongooseOptions = {
  serverSelectionTimeoutMS: 15000, // Tiempo de espera para selección de servidor
  socketTimeoutMS: 30000,         // Tiempo de espera para operaciones de socket
  connectTimeoutMS: 30000,        // Tiempo de espera para conexión inicial
  maxPoolSize: 5,                 // Tamaño máximo del pool para entornos serverless
  minPoolSize: 1,                 // Tamaño mínimo del pool
  maxIdleTimeMS: 10000,           // Tiempo máximo de inactividad
  dbName: 'test'                  // Nombre de la base de datos en MongoDB Atlas
};
```

## Autenticación

### Iniciar Sesión

Autentica a un administrador y devuelve un token JWT.

- **URL**: `/admin/login`
- **Método**: `POST`
- **Autenticación**: No requerida

#### Cuerpo de la Solicitud

```json
{
  "username": "admin",
  "password": "contraseña"
}
```

#### Respuesta Exitosa (200 OK)

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 86400
}
```

#### Respuesta de Error (401 Unauthorized)

```json
{
  "error": "Credenciales inválidas"
}
```

#### Ejemplo de Uso

```javascript
const login = async (credentials) => {
  try {
    const response = await axios.post('/api/admin/login', credentials);
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

## Endpoints de Contenido

### Obtener Todo el Contenido (Público)

Recupera todo el contenido del juego formateado para la app móvil. Este endpoint puede devolver datos encriptados si la variable de entorno `ENCRYPTION_KEY` está configurada en el servidor.

- **URL**: `/content`
- **Método**: `GET`
- **Autenticación**: No requerida

#### Respuesta Exitosa (200 OK) - Sin Encriptación

```json
{
  "questions": {
    "casual_questions": [
      {
        "id": "q1",
        "text_en": "What's your favorite color?",
        "text_es": "¿Cuál es tu color favorito?"
      },
      // Más preguntas...
    ],
    "mild_questions": [...],
    "spicy_questions": [...],
    "no_limits_questions": [...]
  },
  "dares": {
    "casual_dares": [...],
    "mild_dares": [...],
    "spicy_dares": [...],
    "no_limits_dares": [...]
  }
}
```

#### Respuesta Exitosa (200 OK) - Con Encriptación

```json
{
  "encrypted": true,
  "data": "U2FsdGVkX1+A2KKGrSWUVXDFRNrTsXYeT9Lx3VQ6jWYn9..."
}
```

#### Ejemplo de Uso

```javascript
const getGameContent = async () => {
  try {
    const response = await axios.get('/api/content');
    const data = response.data;

    if (data.encrypted) {
      // Los datos están encriptados, desencriptar
      return desencriptar(data.data, 'tu-clave-de-encriptacion');
    } else {
      // Los datos no están encriptados
      return data;
    }
  } catch (error) {
    throw error;
  }
};
```

### Obtener Todo el Contenido (Admin)

Recupera todo el contenido del juego, incluyendo elementos inactivos.

- **URL**: `/admin/content`
- **Método**: `GET`
- **Autenticación**: Requerida

#### Parámetros de Consulta

- `page` (opcional): Número de página (predeterminado: 1)
- `limit` (opcional): Elementos por página (predeterminado: 50)
- `gameMode` (opcional): Filtrar por modo de juego (`questions` o `dares`)
- `category` (opcional): Filtrar por categoría
- `search` (opcional): Término de búsqueda para text_en o text_es
- `active` (opcional): Filtrar por estado activo (`true` o `false`)

#### Respuesta Exitosa (200 OK)

```json
{
  "items": [
    {
      "id": "q1",
      "text_en": "What's your favorite color?",
      "text_es": "¿Cuál es tu color favorito?",
      "category": "casual_questions",
      "gameMode": "questions",
      "active": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    // Más elementos...
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 50,
    "pages": 2
  }
}
```

#### Respuesta de Error (401 Unauthorized)

```json
{
  "error": "Token no proporcionado o inválido"
}
```

#### Ejemplo de Uso

```javascript
const getAllContent = async (filters = {}) => {
  try {
    const response = await api.get('/admin/content', { params: filters });
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### Obtener Contenido por ID

Recupera un elemento de contenido específico por su ID.

- **URL**: `/admin/content/:id`
- **Método**: `GET`
- **Autenticación**: Requerida
- **Parámetros de URL**:
  - `id`: ID del elemento de contenido

#### Respuesta Exitosa (200 OK)

```json
{
  "id": "q1",
  "text_en": "What's your favorite color?",
  "text_es": "¿Cuál es tu color favorito?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

#### Respuesta de Error (404 Not Found)

```json
{
  "error": "Contenido no encontrado"
}
```

#### Ejemplo de Uso

```javascript
const getContentById = async (id) => {
  try {
    const response = await api.get(`/admin/content/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### Crear Contenido

Crea un nuevo elemento de contenido.

- **URL**: `/admin/content`
- **Método**: `POST`
- **Autenticación**: Requerida

#### Cuerpo de la Solicitud

```json
{
  "text_en": "What's your favorite movie?",
  "text_es": "¿Cuál es tu película favorita?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true
}
```

#### Respuesta Exitosa (201 Created)

```json
{
  "id": "q123",
  "text_en": "What's your favorite movie?",
  "text_es": "¿Cuál es tu película favorita?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true,
  "createdAt": "2023-06-01T00:00:00.000Z",
  "updatedAt": "2023-06-01T00:00:00.000Z"
}
```

#### Respuesta de Error (400 Bad Request)

```json
{
  "error": "Datos de contenido inválidos",
  "details": {
    "text_en": "El texto en inglés es requerido"
  }
}
```

#### Ejemplo de Uso

```javascript
const createContent = async (data) => {
  try {
    const response = await api.post('/admin/content', data);
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### Actualizar Contenido

Actualiza un elemento de contenido existente.

- **URL**: `/admin/content/:id`
- **Método**: `PUT`
- **Autenticación**: Requerida
- **Parámetros de URL**:
  - `id`: ID del elemento de contenido

#### Cuerpo de la Solicitud

```json
{
  "text_en": "What's your favorite movie of all time?",
  "text_es": "¿Cuál es tu película favorita de todos los tiempos?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true
}
```

#### Respuesta Exitosa (200 OK)

```json
{
  "id": "q1",
  "text_en": "What's your favorite movie of all time?",
  "text_es": "¿Cuál es tu película favorita de todos los tiempos?",
  "category": "casual_questions",
  "gameMode": "questions",
  "active": true,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-06-01T00:00:00.000Z"
}
```

#### Respuesta de Error (404 Not Found)

```json
{
  "error": "Contenido no encontrado"
}
```

#### Ejemplo de Uso

```javascript
const updateContent = async (id, data) => {
  try {
    const response = await api.put(`/admin/content/${id}`, data);
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### Eliminar Contenido

Elimina un elemento de contenido.

- **URL**: `/admin/content/:id`
- **Método**: `DELETE`
- **Autenticación**: Requerida
- **Parámetros de URL**:
  - `id`: ID del elemento de contenido

#### Respuesta Exitosa (200 OK)

```json
{
  "message": "Contenido eliminado correctamente"
}
```

#### Respuesta de Error (404 Not Found)

```json
{
  "error": "Contenido no encontrado"
}
```

#### Ejemplo de Uso

```javascript
const deleteContent = async (id) => {
  try {
    const response = await api.delete(`/admin/content/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### Generar Contenido con IA

Genera contenido automáticamente utilizando inteligencia artificial (OpenAI).

- **URL**: `/admin/content/generate`
- **Método**: `POST`
- **Autenticación**: Requerida

#### Cuerpo de la Solicitud

```json
{
  "gameMode": "questions",
  "category": "casual_questions"
}
```

#### Respuesta Exitosa (200 OK)

```json
{
  "success": true,
  "gameMode": "questions",
  "category": "casual_questions",
  "generatedContent": [
    {
      "text_en": "What's your favorite childhood memory?",
      "text_es": "¿Cuál es tu recuerdo favorito de la infancia?",
      "text_dom": "¿Cuál e' tu recuerdo favorito de cuando eras pequeño?"
    },
    {
      "text_en": "If you could have dinner with anyone, who would it be?",
      "text_es": "Si pudieras cenar con cualquier persona, ¿quién sería?",
      "text_dom": "Si pudieras cenar con cualquier persona, ¿quién fuera?"
    }
    // ... 6 elementos más
  ],
  "count": 8
}
```

#### Respuesta de Error (503 Service Unavailable)

```json
{
  "message": "AI content generation service is not available",
  "details": "OpenAI API key is not configured. Please contact the administrator."
}
```

#### Respuesta de Error (400 Bad Request)

```json
{
  "message": "Invalid category",
  "details": "Category must be one of: casual_questions, mild_questions, spicy_questions, no_limits_questions, casual_dares, mild_dares, spicy_dares, no_limits_dares"
}
```

#### Ejemplo de Uso

```javascript
const generateAIContent = async (gameMode, category) => {
  try {
    const response = await api.post('/admin/content/generate', {
      gameMode,
      category
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

### Crear Contenido en Lote

Crea múltiples elementos de contenido de una vez.

- **URL**: `/admin/content/bulk-create`
- **Método**: `POST`
- **Autenticación**: Requerida

#### Cuerpo de la Solicitud

```json
{
  "contentItems": [
    {
      "text_en": "What's your favorite childhood memory?",
      "text_es": "¿Cuál es tu recuerdo favorito de la infancia?",
      "text_dom": "¿Cuál e' tu recuerdo favorito de cuando eras pequeño?"
    },
    {
      "text_en": "If you could have dinner with anyone, who would it be?",
      "text_es": "Si pudieras cenar con cualquier persona, ¿quién sería?",
      "text_dom": "Si pudieras cenar con cualquier persona, ¿quién fuera?"
    }
  ],
  "gameMode": "questions",
  "category": "casual_questions"
}
```

#### Respuesta Exitosa (201 Created)

```json
{
  "success": true,
  "message": "Successfully created 2 content items",
  "createdItems": [
    {
      "id": "q456",
      "text_en": "What's your favorite childhood memory?",
      "text_es": "¿Cuál es tu recuerdo favorito de la infancia?",
      "text_dom": "¿Cuál e' tu recuerdo favorito de cuando eras pequeño?",
      "category": "casual_questions",
      "gameMode": "questions",
      "active": true,
      "createdAt": "2023-06-01T00:00:00.000Z",
      "updatedAt": "2023-06-01T00:00:00.000Z"
    },
    {
      "id": "q457",
      "text_en": "If you could have dinner with anyone, who would it be?",
      "text_es": "Si pudieras cenar con cualquier persona, ¿quién sería?",
      "text_dom": "Si pudieras cenar con cualquier persona, ¿quién fuera?",
      "category": "casual_questions",
      "gameMode": "questions",
      "active": true,
      "createdAt": "2023-06-01T00:00:00.000Z",
      "updatedAt": "2023-06-01T00:00:00.000Z"
    }
  ],
  "count": 2
}
```

#### Respuesta de Error (400 Bad Request)

```json
{
  "message": "Content item 1 is missing required text fields",
  "details": "Each item must have text_en, text_es, and text_dom"
}
```

#### Ejemplo de Uso

```javascript
const bulkCreateContent = async (contentItems, gameMode, category) => {
  try {
    const response = await api.post('/admin/content/bulk-create', {
      contentItems,
      gameMode,
      category
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};
```

## Códigos de Estado

La API utiliza los siguientes códigos de estado HTTP:

- `200 OK`: Solicitud exitosa
- `201 Created`: Recurso creado correctamente
- `400 Bad Request`: Parámetros de solicitud inválidos
- `401 Unauthorized`: Autenticación requerida o fallida
- `403 Forbidden`: Permisos insuficientes
- `404 Not Found`: Recurso no encontrado
- `500 Internal Server Error`: Error del servidor

## Ejemplos de Uso

### Flujo Completo de Autenticación y Obtención de Contenido

```javascript
// 1. Iniciar sesión
const loginResponse = await axios.post('/api/admin/login', {
  username: 'admin',
  password: 'contraseña'
});

// 2. Guardar el token
const token = loginResponse.data.token;
localStorage.setItem('token', token);

// 3. Configurar Axios para incluir el token en las solicitudes
// Puedes usar cualquiera de estos dos métodos:

// Método 1: Usando x-auth-token (tradicional)
const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
    'x-auth-token': token
  }
});

// Método 2: Usando Authorization Bearer (estándar JWT)
// const api = axios.create({
//   baseURL: '/api',
//   headers: {
//     'Content-Type': 'application/json',
//     'Authorization': `Bearer ${token}`
//   }
// });

// 4. Obtener contenido
const contentResponse = await api.get('/admin/content');
const content = contentResponse.data.items;

// 5. Mostrar el contenido
console.log('Contenido cargado:', content);
```

### Crear y Actualizar Contenido

```javascript
// Crear nuevo contenido
const newContent = {
  text_en: "What's your favorite book?",
  text_es: "¿Cuál es tu libro favorito?",
  category: "casual_questions",
  gameMode: "questions",
  active: true
};

const createResponse = await api.post('/admin/content', newContent);
const createdContent = createResponse.data;
console.log('Contenido creado:', createdContent);

// Actualizar el contenido
const updatedData = {
  ...createdContent,
  text_en: "What's your favorite book of all time?",
  text_es: "¿Cuál es tu libro favorito de todos los tiempos?"
};

const updateResponse = await api.put(`/admin/content/${createdContent.id}`, updatedData);
const updatedContent = updateResponse.data;
console.log('Contenido actualizado:', updatedContent);
```

### Filtrar y Buscar Contenido

```javascript
// Obtener solo preguntas activas
const questionsResponse = await api.get('/admin/content', {
  params: {
    gameMode: 'questions',
    active: true
  }
});
const questions = questionsResponse.data.items;
console.log('Preguntas activas:', questions);

// Buscar contenido específico
const searchResponse = await api.get('/admin/content', {
  params: {
    search: 'favorite'
  }
});
const searchResults = searchResponse.data.items;
console.log('Resultados de búsqueda:', searchResults);
```
