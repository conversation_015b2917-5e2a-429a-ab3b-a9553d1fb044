# TapTrap Admin Panel - Architecture

## Overview

The TapTrap Admin Panel is a React-based web application that provides an interface for managing game content. It allows administrators to create, read, update, and delete questions and challenges used in the TapTrap mobile app.

## Technology Stack

- **Frontend Framework**: React 18
- **UI Library**: Chakra UI
- **State Management**: React Context API
- **Routing**: React Router v6
- **HTTP Client**: Axios
- **Form Handling**: React Hook Form
- **Authentication**: JWT (JSON Web Tokens)

## Directory Structure

```
admin/
├── public/               # Static public assets
├── src/                  # Source code
│   ├── components/       # Reusable UI components
│   │   ├── Layout.js     # Main layout wrapper
│   │   └── PrivateRoute.js # Authentication wrapper
│   ├── context/          # React context providers
│   │   └── AuthContext.js # Authentication state management
│   ├── hooks/            # Custom React hooks
│   │   └── useAuth.js    # Authentication hook
│   ├── pages/            # Application pages
│   │   ├── AddContent.js # Add new content form
│   │   ├── ContentList.js # Content listing page
│   │   ├── Dashboard.js  # Main dashboard
│   │   ├── EditContent.js # Edit content form
│   │   ├── Login.js      # Login page
│   │   └── TestDashboard.js # Test dashboard page
│   ├── services/         # API and utility services
│   │   └── api.js        # API communication service
│   ├── App.js            # Main application component
│   └── index.js          # Application entry point
```

## Application Flow

1. The application starts at `index.js`, which renders the `App` component wrapped in necessary providers:
   - `ChakraProvider` for UI theming
   - `BrowserRouter` for routing
   - `AuthProvider` for authentication state

2. The `App` component defines routes using React Router:
   - Public routes (like `/login`)
   - Protected routes wrapped in `PrivateRoute` component

3. The `PrivateRoute` component checks authentication status and redirects to login if needed

4. All authenticated pages use the `Layout` component for consistent UI structure

## Authentication Flow

1. User enters credentials on the Login page
2. Credentials are sent to the backend API via the `login` function in `api.js`
3. On successful authentication, the backend returns a JWT token and user data
4. The token and user data are stored in localStorage and in the auth context
5. Protected routes check the auth context to determine if the user is authenticated

## Data Flow

1. Pages fetch data from the backend using functions from `api.js`
2. Data is stored in component state using React's `useState` hook
3. Forms use React Hook Form for validation and submission
4. On form submission, data is sent to the backend via API functions
5. Success/error feedback is provided to the user via toast notifications

## Theme Customization

The application uses a custom Chakra UI theme defined in `index.js`:

- Custom color palette with brand colors
- Custom typography with Poppins for headings and Inter for body text

## Responsive Design

The application is designed to work on both desktop and mobile devices:
- Desktop: Full sidebar navigation
- Mobile: Collapsible drawer navigation

## Development vs Production

The application has special handling for development mode:
- In development (localhost), authentication is bypassed for easier testing
- In production, full authentication flow is enforced
