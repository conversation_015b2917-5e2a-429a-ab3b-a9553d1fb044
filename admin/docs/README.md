# TapTrap Admin Panel

## Descripción General

El Panel de Administración de TapTrap es una aplicación web basada en React que proporciona una interfaz para gestionar el contenido del juego. Permite a los administradores crear, leer, actualizar y eliminar preguntas y desafíos utilizados en la aplicación móvil TapTrap.

## Tabla de Contenidos

1. [Características](#características)
2. [Arquitectura](#arquitectura)
3. [Instalación y Configuración](#instalación-y-configuración)
4. [Guía de Uso](#guía-de-uso)
5. [Despliegue](#despliegue)
6. [Solución de Problemas](#solución-de-problemas)

## Características

- **Autenticación**: Sistema de inicio de sesión seguro para administradores
- **Panel de Control**: Resumen de estadísticas de contenido
- **Gestión de Contenido**: Operaciones CRUD para el contenido del juego
  - Listar todo el contenido con filtrado y búsqueda
  - Añadir nuevas preguntas y desafíos
  - **Generación de Contenido con IA**: Crear contenido automáticamente usando OpenAI
  - Editar contenido existente
  - Eliminar contenido
- **Diseño Responsivo**: Funciona en dispositivos de escritorio y móviles
- **Integración con API**: Se comunica con la API backend de TapTrap

## Arquitectura

### Stack Tecnológico

- **Framework Frontend**: React
- **Biblioteca UI**: Chakra UI
- **Gestión de Estado**: React Context API
- **Enrutamiento**: React Router
- **Cliente HTTP**: Axios
- **Autenticación**: JWT (JSON Web Tokens)

### Estructura de Componentes

```
admin/
├── src/
│   ├── components/       # Componentes UI reutilizables
│   │   ├── Layout.js     # Wrapper de diseño principal
│   │   └── PrivateRoute.js # Wrapper de autenticación
│   ├── context/          # Proveedores de contexto React
│   │   └── AuthContext.js # Gestión del estado de autenticación
│   ├── hooks/            # Hooks personalizados de React
│   │   └── useAuth.js    # Hook de autenticación
│   ├── pages/            # Páginas de la aplicación
│   │   ├── AddContent.js # Formulario para añadir contenido
│   │   ├── ContentList.js # Página de listado de contenido
│   │   ├── GenerateContent.js # Página de generación de contenido con IA
│   │   ├── Dashboard.js  # Panel de control principal
│   │   ├── EditContent.js # Formulario para editar contenido
│   │   └── Login.js      # Página de inicio de sesión
│   ├── services/         # Servicios API y utilidades
│   │   └── api.js        # Servicio de comunicación API
│   ├── App.js            # Componente principal de la aplicación
│   └── index.js          # Punto de entrada de la aplicación
└── public/               # Activos estáticos
```

### Flujo de Autenticación

1. El usuario introduce credenciales en la página de inicio de sesión
2. Las credenciales se envían a la API backend
3. Si son válidas, la API devuelve un token JWT
4. El token se almacena en localStorage
5. Las rutas protegidas verifican la presencia del token
6. Las solicitudes a la API incluyen el token en las cabeceras
7. Si el token expira, el usuario es redirigido al inicio de sesión

## Instalación y Configuración

### Prerrequisitos

- Node.js (v14 o posterior)
- npm o yarn
- API backend de TapTrap en funcionamiento

### Configuración para Desarrollo Local

1. Clonar el repositorio:
   ```bash
   git clone https://github.com/tuusuario/TapTrap.git
   cd TapTrap/admin
   ```

2. Instalar dependencias:
   ```bash
   npm install
   # o
   yarn install
   ```

3. Configurar variables de entorno:
   - Crear un archivo `.env.development.local` con:
     ```
     REACT_APP_API_URL=http://localhost:5001/api
     ```

4. Iniciar el servidor de desarrollo:
   ```bash
   npm start
   # o
   yarn start
   ```

5. Acceder al panel de administración en `http://localhost:3000`

## Guía de Uso

### Iniciar Sesión

1. Navegar a la página de inicio de sesión
2. Introducir las credenciales de administrador
3. Hacer clic en "Login"

### Panel de Control

El panel de control proporciona una visión general de:
- Número total de preguntas y desafíos
- Distribución por categoría
- Actividad reciente

### Gestión de Contenido

#### Ver Contenido

1. Navegar a la sección "Content"
2. Usar filtros para reducir la lista:
   - Modo de juego (Preguntas/Desafíos)
   - Categoría (Casual, Mild, Spicy, No Limits)
   - Estado activo
3. Usar la caja de búsqueda para encontrar contenido específico

#### Añadir Contenido

1. Hacer clic en "Add New Content"
2. Rellenar el formulario:
   - Texto en inglés
   - Texto en español
   - Modo de juego (Preguntas/Desafíos)
   - Categoría
   - Estado activo
3. Hacer clic en "Save"

#### Generar Contenido con IA

1. Hacer clic en "Generate Content" (botón con icono de rayo)
2. Seleccionar modo de juego (Preguntas o Desafíos)
3. Seleccionar categoría (Casual, Mild, Spicy, No Limits)
4. Hacer clic en "Generate Content"
5. Revisar el contenido generado en la tabla
6. Seleccionar los elementos deseados
7. Hacer clic en "Add Selected to Database"
8. Confirmar la acción en el modal

**Nota**: Requiere configurar `OPENAI_API_KEY` en las variables de entorno del backend.

#### Editar Contenido

1. Encontrar el elemento de contenido en la lista
2. Hacer clic en el botón "Edit"
3. Modificar los campos del formulario
4. Hacer clic en "Save"

#### Eliminar Contenido

1. Encontrar el elemento de contenido en la lista
2. Hacer clic en el botón "Delete"
3. Confirmar la eliminación

## Despliegue

El Panel de Administración de TapTrap está diseñado para ser desplegado en Vercel, con las solicitudes a la API redirigidas al backend.

### Despliegue en Vercel

1. Configurar variables de entorno en Vercel:
   - `REACT_APP_API_URL`: Establecer a `/api` para producción

2. Configurar el proyecto en Vercel:
   - Preset de framework: Create React App
   - Comando de construcción: `npm run vercel-build`
   - Directorio de salida: `build`

3. Configurar redirecciones de API en `vercel.json`:
   ```json
   {
     "rewrites": [
       {
         "source": "/api/:path*",
         "destination": "https://tu-url-backend.com/api/:path*"
       }
     ]
   }
   ```

Para instrucciones detalladas de despliegue, consulta [VERCEL_DEPLOYMENT.md](../VERCEL_DEPLOYMENT.md).

## Solución de Problemas

### Problemas Comunes

#### Problemas de Autenticación

- **"Invalid credentials"**: Verificar nombre de usuario y contraseña
- **"Server error during login"**: Revisar logs de la API backend
- **Cierre de sesión automático**: El token JWT puede haber expirado

#### Problemas de Conexión con la API

- **"Network Error"**: Comprobar si la API backend está funcionando
- **Errores CORS**: Asegurar que el backend permite solicitudes desde el origen del panel de administración
- **Errores 404**: Verificar las rutas de los endpoints de la API

#### Problemas de Gestión de Contenido

- **El contenido no carga**: Comprobar autenticación y conexión con la API
- **No se pueden guardar cambios**: Verificar datos del formulario y respuestas de la API
- **Los cambios no se reflejan en la app**: El contenido puede necesitar ser marcado como activo

Para una solución de problemas más detallada, consulta [TROUBLESHOOTING.md](../TROUBLESHOOTING.md).

## Consideraciones de Seguridad

- El panel de administración utiliza JWT para autenticación
- Todas las solicitudes a endpoints protegidos requieren un token válido
- Las contraseñas nunca se almacenan en el frontend
- Las solicitudes a la API se realizan sobre HTTPS en producción
- Se utilizan variables de entorno para configuración sensible

## Directrices de Desarrollo

### Añadir Nuevas Características

1. Crear una nueva rama para tu característica
2. Implementar la característica utilizando los patrones existentes
3. Probar exhaustivamente
4. Enviar una solicitud de pull

### Estilo de Código

- Seguir las mejores prácticas de React
- Usar componentes funcionales con hooks
- Mantener un manejo de errores consistente
- Documentar lógica compleja con comentarios

## Integración con la API

El panel de administración se comunica con la API backend utilizando el servicio definido en `services/api.js`.

### Endpoints Clave de la API

- `POST /api/admin/login`: Autenticación
- `GET /api/admin/content`: Listar todo el contenido
- `GET /api/admin/content/:id`: Obtener contenido por ID
- `POST /api/admin/content`: Crear nuevo contenido
- `POST /api/admin/content/generate`: Generar contenido con IA
- `POST /api/admin/content/bulk-create`: Crear contenido en lote
- `PUT /api/admin/content/:id`: Actualizar contenido
- `DELETE /api/admin/content/:id`: Eliminar contenido
