# useAuth Hook

## Overview

The `useAuth` hook provides authentication functionality for the TapTrap Admin Panel. It manages user authentication state, handles login and logout operations, and provides development mode conveniences for testing.

## File Location

`admin/src/hooks/useAuth.js`

## Dependencies

- React hooks (`useState`, `useEffect`, `useCallback`)
- API service (`login as apiLogin`)

## Hook Structure

```jsx
import { useState, useEffect, useCallback } from 'react';
import { login as apiLogin } from '../services/api';

export const useAuth = () => {
  // State
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load user from localStorage on initial render
  useEffect(() => {
    const loadUser = () => {
      // Load and validate stored authentication
    };
    
    loadUser();
  }, []);

  // Login method
  const login = useCallback(async (email, password) => {
    // Handle login logic
  }, []);

  // Logout method
  const logout = useCallback(() => {
    // Handle logout logic
  }, []);

  // Development mode handling
  const isDevelopment = window.location.hostname === 'localhost';
  const isAuthenticated = isDevelopment ? true : !!user;
  
  // Return auth object
  return {
    user,
    loading,
    error,
    login,
    logout,
    isAuthenticated
  };
};
```

## Key Features

1. **Authentication State Management**:
   - Tracks current user with `user` state
   - Tracks loading state during authentication operations
   - Tracks error state for authentication failures

2. **Persistent Authentication**:
   - Loads user data from localStorage on initial render
   - Stores user data and token in localStorage on successful login
   - Clears localStorage on logout

3. **Authentication Operations**:
   - `login`: Authenticates a user with email and password
   - `logout`: Logs out the current user and clears authentication data

4. **Development Mode**:
   - Special handling for development environment (localhost)
   - Auto-authentication in development for easier testing
   - Default admin user in development when no stored credentials exist

5. **Error Handling**:
   - Captures and formats API errors during login
   - Provides detailed error messages for debugging
   - Exposes error state for UI feedback

## Return Value

The hook returns an object with the following properties:

| Property | Type | Description |
|----------|------|-------------|
| `user` | Object \| null | The currently authenticated user or null if not authenticated |
| `loading` | boolean | Whether authentication state is being determined |
| `error` | string \| null | Any error that occurred during authentication |
| `login` | function | Function to log in a user with credentials |
| `logout` | function | Function to log out the current user |
| `isAuthenticated` | boolean | Whether a user is currently authenticated |

## Usage

```jsx
import { useAuth } from '../hooks/useAuth';

const MyComponent = () => {
  const { user, loading, error, login, logout, isAuthenticated } = useAuth();
  
  const handleLogin = async () => {
    try {
      await login('<EMAIL>', 'password');
      // Handle successful login
    } catch (err) {
      // Handle login error
    }
  };
  
  if (loading) {
    return <div>Loading...</div>;
  }
  
  return (
    <div>
      {isAuthenticated ? (
        <>
          <p>Welcome, {user.email}</p>
          <button onClick={logout}>Logout</button>
        </>
      ) : (
        <>
          {error && <p>Error: {error}</p>}
          <button onClick={handleLogin}>Login</button>
        </>
      )}
    </div>
  );
};
```

## Development Mode

In development mode (when running on localhost):

- Authentication is always considered successful (`isAuthenticated` is true)
- If no user is stored in localStorage, a default admin user is created
- This allows for easier testing without needing to log in repeatedly

## Security Considerations

- Authentication tokens are stored in localStorage, which is accessible to JavaScript
- In production, proper token validation and expiration should be implemented
- The hook assumes that the backend properly validates credentials and returns secure tokens
