#!/usr/bin/env node

/**
 * Comprehensive test script for the TapTrap Penalty System
 * 
 * This script tests:
 * 1. Backend API endpoints
 * 2. Mobile app penalty service
 * 3. Admin panel functionality
 * 4. End-to-end penalty flow
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const API_BASE_URL = 'http://localhost:5002/api';
const ADMIN_API_URL = `${API_BASE_URL}/admin`;
const PENALTY_API_URL = `${API_BASE_URL}/penalties`;

// Test data
const testPenalty = {
  text_en: 'Test penalty in English',
  text_es: 'Penalización de prueba en español',
  text_dom: 'Penalización de prueba en dominicano',
  category: 'silly',
  active: true
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Test functions
async function testBackendHealth() {
  logInfo('Testing backend health...');
  try {
    const response = await axios.get(`${API_BASE_URL}/health`);
    if (response.status === 200) {
      logSuccess('Backend is healthy');
      return true;
    }
  } catch (error) {
    logError(`Backend health check failed: ${error.message}`);
    return false;
  }
}

async function testPenaltyAPI() {
  logInfo('Testing penalty API endpoints...');
  
  try {
    // Test GET /penalties
    const response = await axios.get(PENALTY_API_URL);
    if (response.status === 200) {
      logSuccess('GET /penalties endpoint working');
      
      // Check response structure
      const data = response.data;
      if (data.drinking && data.physical && data.social && data.silly && data.creative) {
        logSuccess('Penalty response has correct structure');
        
        // Log penalty counts
        const counts = Object.keys(data).map(category => 
          `${category}: ${data[category].length}`
        ).join(', ');
        logInfo(`Penalty counts: ${counts}`);
        
        return true;
      } else {
        logError('Penalty response missing expected categories');
        return false;
      }
    }
  } catch (error) {
    logError(`Penalty API test failed: ${error.message}`);
    return false;
  }
}

async function testAdminPenaltyAPI() {
  logInfo('Testing admin penalty API endpoints...');
  
  try {
    // Test GET /admin/penalties
    const response = await axios.get(`${ADMIN_API_URL}/penalties`);
    if (response.status === 200) {
      logSuccess('GET /admin/penalties endpoint working');
      
      const data = response.data;
      if (data.items && Array.isArray(data.items)) {
        logSuccess(`Admin API returned ${data.items.length} penalties`);
        return true;
      } else {
        logError('Admin API response missing items array');
        return false;
      }
    }
  } catch (error) {
    logError(`Admin penalty API test failed: ${error.message}`);
    return false;
  }
}

async function testPenaltyCRUD() {
  logInfo('Testing penalty CRUD operations...');
  
  let createdPenaltyId = null;
  
  try {
    // CREATE
    const createResponse = await axios.post(`${ADMIN_API_URL}/penalties`, testPenalty);
    if (createResponse.status === 201) {
      logSuccess('Penalty created successfully');
      createdPenaltyId = createResponse.data.id;
    } else {
      logError('Failed to create penalty');
      return false;
    }
    
    // READ
    const readResponse = await axios.get(`${ADMIN_API_URL}/penalties/${createdPenaltyId}`);
    if (readResponse.status === 200) {
      logSuccess('Penalty read successfully');
    } else {
      logError('Failed to read penalty');
      return false;
    }
    
    // UPDATE
    const updateData = { ...testPenalty, text_en: 'Updated test penalty' };
    const updateResponse = await axios.put(`${ADMIN_API_URL}/penalties/${createdPenaltyId}`, updateData);
    if (updateResponse.status === 200) {
      logSuccess('Penalty updated successfully');
    } else {
      logError('Failed to update penalty');
      return false;
    }
    
    // DELETE
    const deleteResponse = await axios.delete(`${ADMIN_API_URL}/penalties/${createdPenaltyId}`);
    if (deleteResponse.status === 200) {
      logSuccess('Penalty deleted successfully');
    } else {
      logError('Failed to delete penalty');
      return false;
    }
    
    return true;
  } catch (error) {
    logError(`CRUD test failed: ${error.message}`);
    
    // Cleanup: try to delete the created penalty if it exists
    if (createdPenaltyId) {
      try {
        await axios.delete(`${ADMIN_API_URL}/penalties/${createdPenaltyId}`);
        logInfo('Cleaned up test penalty');
      } catch (cleanupError) {
        logWarning('Failed to cleanup test penalty');
      }
    }
    
    return false;
  }
}

async function testPenaltyFiltering() {
  logInfo('Testing penalty filtering...');
  
  try {
    // Test category filter
    const categoryResponse = await axios.get(`${ADMIN_API_URL}/penalties?category=drinking`);
    if (categoryResponse.status === 200) {
      const drinkingPenalties = categoryResponse.data.items.filter(p => p.category === 'drinking');
      if (drinkingPenalties.length === categoryResponse.data.items.length) {
        logSuccess('Category filtering working correctly');
      } else {
        logError('Category filtering not working correctly');
        return false;
      }
    }
    
    // Test active filter
    const activeResponse = await axios.get(`${ADMIN_API_URL}/penalties?active=true`);
    if (activeResponse.status === 200) {
      const activePenalties = activeResponse.data.items.filter(p => p.active === true);
      if (activePenalties.length === activeResponse.data.items.length) {
        logSuccess('Active filtering working correctly');
      } else {
        logError('Active filtering not working correctly');
        return false;
      }
    }
    
    return true;
  } catch (error) {
    logError(`Filtering test failed: ${error.message}`);
    return false;
  }
}

async function testMobileAppIntegration() {
  logInfo('Testing mobile app integration...');
  
  // Check if penalty service file exists
  const penaltyServicePath = path.join(__dirname, 'services', 'penaltyService.ts');
  if (fs.existsSync(penaltyServicePath)) {
    logSuccess('Penalty service file exists');
  } else {
    logError('Penalty service file not found');
    return false;
  }
  
  // Check if PenaltyDialog component exists
  const penaltyDialogPath = path.join(__dirname, 'components', 'game', 'PenaltyDialog.tsx');
  if (fs.existsSync(penaltyDialogPath)) {
    logSuccess('PenaltyDialog component exists');
  } else {
    logError('PenaltyDialog component not found');
    return false;
  }
  
  // Check if TouchGameScreen has penalty integration
  const touchGameScreenPath = path.join(__dirname, 'screens', 'TouchGameScreen.tsx');
  if (fs.existsSync(touchGameScreenPath)) {
    const content = fs.readFileSync(touchGameScreenPath, 'utf8');
    if (content.includes('getRandomPenalty') && content.includes('penaltyText')) {
      logSuccess('TouchGameScreen has penalty integration');
    } else {
      logError('TouchGameScreen missing penalty integration');
      return false;
    }
  } else {
    logError('TouchGameScreen not found');
    return false;
  }
  
  return true;
}

async function testAdminPanelIntegration() {
  logInfo('Testing admin panel integration...');
  
  // Check if admin penalty pages exist
  const adminPages = [
    'admin/src/pages/PenaltyList.js',
    'admin/src/pages/AddPenalty.js',
    'admin/src/pages/EditPenalty.js',
    'admin/src/services/penalty.service.js'
  ];
  
  for (const pagePath of adminPages) {
    const fullPath = path.join(__dirname, pagePath);
    if (fs.existsSync(fullPath)) {
      logSuccess(`${pagePath} exists`);
    } else {
      logError(`${pagePath} not found`);
      return false;
    }
  }
  
  // Check if App.js has penalty routes
  const appJsPath = path.join(__dirname, 'admin', 'src', 'App.js');
  if (fs.existsSync(appJsPath)) {
    const content = fs.readFileSync(appJsPath, 'utf8');
    if (content.includes('/penalties') && content.includes('PenaltyList')) {
      logSuccess('App.js has penalty routes');
    } else {
      logError('App.js missing penalty routes');
      return false;
    }
  } else {
    logError('App.js not found');
    return false;
  }
  
  return true;
}

// Main test runner
async function runTests() {
  log('\n🧪 TapTrap Penalty System Test Suite', 'blue');
  log('=====================================\n', 'blue');
  
  const tests = [
    { name: 'Backend Health', fn: testBackendHealth },
    { name: 'Penalty API', fn: testPenaltyAPI },
    { name: 'Admin Penalty API', fn: testAdminPenaltyAPI },
    { name: 'Penalty CRUD', fn: testPenaltyCRUD },
    { name: 'Penalty Filtering', fn: testPenaltyFiltering },
    { name: 'Mobile App Integration', fn: testMobileAppIntegration },
    { name: 'Admin Panel Integration', fn: testAdminPanelIntegration }
  ];
  
  const results = [];
  
  for (const test of tests) {
    log(`\n📋 Running ${test.name} test...`, 'yellow');
    const result = await test.fn();
    results.push({ name: test.name, passed: result });
    
    if (result) {
      logSuccess(`${test.name} test passed`);
    } else {
      logError(`${test.name} test failed`);
    }
  }
  
  // Summary
  log('\n📊 Test Results Summary', 'blue');
  log('=======================\n', 'blue');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    log(`${status} ${result.name}`);
  });
  
  log(`\n🎯 Overall: ${passed}/${total} tests passed`, passed === total ? 'green' : 'red');
  
  if (passed === total) {
    log('\n🎉 All tests passed! The penalty system is working correctly.', 'green');
  } else {
    log('\n⚠️  Some tests failed. Please check the implementation.', 'yellow');
  }
  
  return passed === total;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    logError(`Test runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTests };
