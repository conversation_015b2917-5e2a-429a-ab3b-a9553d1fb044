{"type": "service_account", "project_id": "taptrap", "private_key_id": "dff3343853545798d59a992c5764e98b31c65d5f", "private_key": "-----B<PERSON>IN PRIVATE KEY-----\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCgQVe6HK9USQcb\nQPjiEDOMcNyTLQbxu1e9D7gjXsJEOZxzK7i+4A7qM5d7252CSClBJ+KMCuupdFGx\nzbTjJ7MrBw6I5GmbUP2kZJriRNiqAy0puJ9cRLC+ki7qmEU8DqzQl12N2o7cTtoA\nBckYzS7xVqaSBosItcdkVMZe/6G22OD2P0LzMbjAU09lknwruncppHU2qCKiQoR/\nLoFfk6nWon7llwtAY04o65ExO7WxVUbCmYzvPf/nk6nEkJdkxjv1k4isPUAC0YOx\nJUpV7ZYSzaczl+noGu2Hu9kd+g841pDDCAvoXtF1lqRGZ23Ea9Eou42vYSDuL0IU\nWz864cEZAgMBAAECggEAHEI5en+ZaTbxclxcJtztghvawXckrNa4y4VslowcnJQ+\nIs74ZmwzJRhQ443f8fvQjoU+fSxlzv1MdJqOBapX/UPiy7/fYXyAJY96AmFahWxG\n9/7u1wHj2s8DR+nu936s+aFfZWa/X1jmNy/N/Lodx3/coo5V0QTg8q/CRTk1pnzD\nizdPPI4U84r2FjRkNbyGQLenorR8XAl54hJLNiBRnvdFDtXEfjRIKdWcoBJfiD4p\nlVm7DnmoTy8HSlMlG/j1/HASTGntQzU2edErqxe2nB2HmzWe3BGyOVaH/RT5r6jr\nbbekX8akLjZMkUhrYiBtgoXKjGu5rFxph0Ib8JNoKwKBgQDL26CTQew+OxuCpb3e\nclzH50a+3CRBV84F0VLZ4pZAo/KjRoCGU0FYkwfmc1olc9spTuJeWlzM46qJF6fK\nUsKFiTIOaVJz/3xihrGVhrlLVPrGvj+XLeK6kdXZCWTQULlvR2u3NZv68EBsFN5M\nuo+PYovm7x6RUa3AN/KzkUfhbwKBgQDJPqobUjXAIWF0LowA7dxwVL5D5akVPKLF\nr7nYDvAaaHD3vbpkimfwxqPNtGOVH0Hf+AAkSZGwdzzc547uhHB1NcN+v1zBESJA\n2okHJ1xky8xvzITyZ/qWd3txGPsgybrcxnjO24U+xYXUQLXIrEtbgT4JnGDzlOI5\nuuwrJy8x9wKBgCjiq5ndJtx0TlgtlZ7/oMwW/oFY9YyFZ7SS/uLkJu5f81H72dsW\n54w0MOlvSS0px1LJl20FAoe1QYatT+GsJ4dQtNFfHkJVg0Tfbs5mbtNE/+bMUUSo\n10hD53fYAnglOIuw71GXVulFNiZ3KP6KjDBdu2Fdo+vRTd+3WN3azWfRAoGAZdoq\nW/OKsrfyS8v3Hc1FATFSrqiAn/f1ZdofVAOTb7omWqyStqn6Gzld1BGVSPbt7z3k\nTHlTnB1tF1y20W8EP747OF3/wtM6I9q2+YDpWTZwPLElJE6N3DOSrVX4W8rdoTlu\nETu3P2LW7ermVi8lYKWT0WdtBjr3GELLnreRQlcCgYBgdthmQ5c4fZbakIPjH9sZ\nmlpsxPhUxiRPQxwI1dvX5f9T+9yJ6NlroRwyF+wBkI3yQCDS26PIL08ZoU/ICJTj\njbwZHWkhQOn8ytKugr5KXzD8iJZ1dJUZmrthOiSwUClE7pT5o6RkCMAsrdF2SPwZ\n/toilXTzw/7v1tENBn9gzA==\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "109715578236496242963", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/google-play-console-account%40taptrap.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}