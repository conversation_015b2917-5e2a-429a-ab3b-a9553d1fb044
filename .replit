entrypoint = "README.md"
modules = ["nodejs-20"]

[nix]
channel = "stable-24_05"

[[ports]]
localPort = 8081
externalPort = 80

[workflows]
runButton = "Dev Server"

[[workflows.workflow]]
name = "Dev Server"
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "EXPO_PACKAGER_PROXY_URL=https://$REPLIT_DEV_DOMAIN REACT_NATIVE_PACKAGER_HOSTNAME=$REPLIT_DEV_DOMAIN npx expo start"

[[workflows.workflow]]
name = "EAS Init"
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npx eas init"

[[workflows.workflow]]
name = "EAS Update"
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npx eas update --auto"

[[workflows.workflow]]
name = "EAS Publish Preview iOS"
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npx eas build --platform ios --profile preview"

[[workflows.workflow]]
name = "EAS Publish Preview Android"
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npx eas build --platform android --profile preview"
